package sys_dict_group

import (
	"context"

	v1 "admin-server/api/sys_dict_group/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetAll(ctx context.Context, req *v1.GetAllReq) (res *v1.GetAllRes, err error) {
	list, err := service.SysDictGroup().GetAllDictGroups(ctx)
	if err != nil {
		return nil, err
	}

	// 转换为API响应格式
	options := make([]v1.DictGroupOption, len(list))
	for i, group := range list {
		options[i] = v1.DictGroupOption{
			ID:   group.Id,
			Name: group.Name,
			Code: group.Code,
		}
	}

	return &v1.GetAllRes{
		List: options,
	}, nil
}
