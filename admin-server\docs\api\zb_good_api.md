# 套餐管理API - 前端对接文档

## 接口概览

| 接口名称 | 方法 | 路径 | 说明 |
|---------|------|------|------|
| 创建套餐 | POST | `/zb_good` | 创建新的套餐 |
| 更新套餐 | PUT | `/zb_good/{id}` | 更新套餐信息 |
| 删除套餐 | DELETE | `/zb_good/{id}` | 删除套餐（软删除） |
| 获取套餐详情 | GET | `/zb_good/{id}` | 获取单个套餐信息 |
| 获取套餐列表 | GET | `/zb_good/list` | 获取套餐分页列表 |
| 获取所有套餐 | GET | `/zb_good/all` | 获取所有套餐（不分页） |
| 更新状态 | PUT | `/zb_good/{id}/status` | 更新套餐状态 |
| 批量删除 | DELETE | `/zb_good/batch` | 批量删除套餐 |
| 获取可用套餐 | GET | `/zb_good/active` | 获取可用套餐 |

## 数据模型

### ZbGoodInfo

```typescript
interface ZbGoodInfo {
  id: number;                    // 套餐ID
  name: string;                  // 套餐名称
  tag: string;                   // 套餐标签
  original_price: number;        // 原始价格
  price: number;                 // 现时价格
  effective: number;             // 会员有效期（月）
  is_disable: number;            // 是否禁用：0=否，1=是
  is_delete: number;             // 是否删除：0=否，1=是
  created_at: string | null;     // 创建时间
  updated_at: string | null;     // 更新时间
  deleted_at: string | null;     // 删除时间
  discount: number;              // 折扣率（计算字段）
  discount_text: string;         // 折扣文本（计算字段）
}
```

### 状态枚举

```typescript
enum GoodStatus {
  ENABLED = 0,    // 启用
  DISABLED = 1    // 禁用
}

enum DeleteStatus {
  NORMAL = 0,     // 正常
  DELETED = 1     // 已删除
}
```

## 接口详情

### 1. 创建套餐

```typescript
// 请求
interface CreateGoodRequest {
  name: string;                  // 套餐名称
  tag?: string;                  // 套餐标签
  original_price: number;        // 原始价格
  price: number;                 // 现时价格
  effective: number;             // 会员有效期（月）
  is_disable?: number;           // 是否禁用，默认0
}

// 响应
interface CreateGoodResponse {
  id: number;                    // 创建的套餐ID
}
```

### 2. 更新套餐

```typescript
// 请求
interface UpdateGoodRequest {
  id: number;                    // 套餐ID（路径参数）
  name: string;                  // 套餐名称
  tag?: string;                  // 套餐标签
  original_price: number;        // 原始价格
  price: number;                 // 现时价格
  effective: number;             // 会员有效期（月）
  is_disable: number;            // 是否禁用
}

// 响应
interface UpdateGoodResponse {}
```

### 3. 删除套餐

```typescript
// 请求
interface DeleteGoodRequest {
  id: number;                    // 套餐ID（路径参数）
}

// 响应
interface DeleteGoodResponse {}
```

### 4. 获取套餐详情

```typescript
// 请求
interface GetGoodRequest {
  id: number;                    // 套餐ID（路径参数）
}

// 响应
interface GetGoodResponse extends ZbGoodInfo {}
```

### 5. 获取套餐列表

```typescript
// 请求
interface GetGoodListRequest {
  page?: number;                 // 页码，默认1
  page_size?: number;            // 每页数量，默认10
  name?: string;                 // 套餐名称，模糊搜索
  is_disable?: number;           // 是否禁用筛选
  min_price?: number;            // 最低价格筛选
  max_price?: number;            // 最高价格筛选
}

// 响应
interface GetGoodListResponse {
  list: ZbGoodInfo[];            // 套餐列表
  total: number;                 // 总数
  page: number;                  // 当前页码
  page_size: number;             // 每页数量
}
```

### 6. 获取所有套餐

```typescript
// 请求
interface GetAllGoodRequest {
  is_disable?: number;           // 是否禁用筛选
}

// 响应
interface GetAllGoodResponse {
  list: ZbGoodInfo[];            // 套餐列表
}
```

### 7. 更新状态

```typescript
// 请求
interface UpdateGoodStatusRequest {
  id: number;                    // 套餐ID（路径参数）
  is_disable: number;            // 是否禁用：0=否，1=是
}

// 响应
interface UpdateGoodStatusResponse {}
```

### 8. 批量删除

```typescript
// 请求
interface BatchDeleteGoodRequest {
  ids: number[];                 // 套餐ID列表
}

// 响应
interface BatchDeleteGoodResponse {
  count: number;                 // 删除数量
}
```

### 9. 获取可用套餐

```typescript
// 请求
interface GetActiveGoodsRequest {}

// 响应
interface GetActiveGoodsResponse {
  list: ZbGoodInfo[];            // 可用套餐列表
}
```

## Vue 3 + TypeScript 示例

```typescript
import { ref, reactive, computed } from 'vue'
import axios from 'axios'

// 套餐管理组合式函数
export function useZbGood() {
  const goods = ref<ZbGoodInfo[]>([])
  const allGoods = ref<ZbGoodInfo[]>([])
  const activeGoods = ref<ZbGoodInfo[]>([])
  const loading = ref(false)
  const total = ref(0)

  // 获取套餐列表
  const getGoodList = async (params: GetGoodListRequest) => {
    loading.value = true
    try {
      const response = await axios.get('/zb_good/list', { params })
      goods.value = response.data.data.list
      total.value = response.data.data.total
      return response.data.data
    } catch (error) {
      console.error('获取套餐列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取所有套餐
  const getAllGoods = async (params?: GetAllGoodRequest) => {
    loading.value = true
    try {
      const response = await axios.get('/zb_good/all', { params })
      allGoods.value = response.data.data.list
      return response.data.data.list
    } catch (error) {
      console.error('获取所有套餐失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取可用套餐
  const getActiveGoods = async () => {
    loading.value = true
    try {
      const response = await axios.get('/zb_good/active')
      activeGoods.value = response.data.data.list
      return response.data.data.list
    } catch (error) {
      console.error('获取可用套餐失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建套餐
  const createGood = async (data: CreateGoodRequest) => {
    try {
      const response = await axios.post('/zb_good', data)
      return response.data.data
    } catch (error) {
      console.error('创建套餐失败:', error)
      throw error
    }
  }

  // 更新套餐
  const updateGood = async (id: number, data: Omit<UpdateGoodRequest, 'id'>) => {
    try {
      const response = await axios.put(`/zb_good/${id}`, data)
      return response.data.data
    } catch (error) {
      console.error('更新套餐失败:', error)
      throw error
    }
  }

  // 删除套餐
  const deleteGood = async (id: number) => {
    try {
      const response = await axios.delete(`/zb_good/${id}`)
      return response.data.data
    } catch (error) {
      console.error('删除套餐失败:', error)
      throw error
    }
  }

  // 获取套餐详情
  const getGoodDetail = async (id: number) => {
    try {
      const response = await axios.get(`/zb_good/${id}`)
      return response.data.data
    } catch (error) {
      console.error('获取套餐详情失败:', error)
      throw error
    }
  }

  // 更新套餐状态
  const updateGoodStatus = async (id: number, is_disable: number) => {
    try {
      const response = await axios.put(`/zb_good/${id}/status`, { is_disable })
      return response.data.data
    } catch (error) {
      console.error('更新套餐状态失败:', error)
      throw error
    }
  }

  // 批量删除套餐
  const batchDeleteGoods = async (ids: number[]) => {
    try {
      const response = await axios.delete('/zb_good/batch', { data: { ids } })
      return response.data.data
    } catch (error) {
      console.error('批量删除套餐失败:', error)
      throw error
    }
  }

  // 计算折扣信息
  const calculateDiscount = (originalPrice: number, price: number) => {
    if (originalPrice <= 0 || price <= 0 || price >= originalPrice) {
      return { discount: 0, discountText: '无折扣' }
    }
    
    const discount = price / originalPrice
    const discountPercent = discount * 10
    
    let discountText = '无折扣'
    if (discountPercent < 9.5) {
      if (discountPercent >= 9.0) discountText = '9.5折'
      else if (discountPercent >= 8.5) discountText = '9折'
      else if (discountPercent >= 8.0) discountText = '8.5折'
      else if (discountPercent >= 7.5) discountText = '8折'
      else if (discountPercent >= 7.0) discountText = '7.5折'
      else if (discountPercent >= 6.5) discountText = '7折'
      else if (discountPercent >= 6.0) discountText = '6.5折'
      else if (discountPercent >= 5.5) discountText = '6折'
      else if (discountPercent >= 5.0) discountText = '5.5折'
      else discountText = `${discountPercent.toFixed(1)}折`
    }
    
    return { discount, discountText }
  }

  return {
    goods,
    allGoods,
    activeGoods,
    loading,
    total,
    getGoodList,
    getAllGoods,
    getActiveGoods,
    createGood,
    updateGood,
    deleteGood,
    getGoodDetail,
    updateGoodStatus,
    batchDeleteGoods,
    calculateDiscount
  }
}
```

## 表单验证规则

```typescript
// 套餐表单验证规则
export const goodFormRules = {
  name: [
    { required: true, message: '套餐名称不能为空' },
    { min: 1, max: 255, message: '套餐名称长度必须在1-255个字符之间' }
  ],
  tag: [
    { max: 255, message: '套餐标签长度不能超过255个字符' }
  ],
  original_price: [
    { required: true, message: '原始价格不能为空' },
    { type: 'number', min: 0, message: '原始价格不能小于0' }
  ],
  price: [
    { required: true, message: '现时价格不能为空' },
    { type: 'number', min: 0, message: '现时价格不能小于0' },
    { 
      validator: (rule: any, value: number, callback: Function, source: any) => {
        if (value > source.original_price) {
          callback(new Error('现时价格不能大于原始价格'))
        } else {
          callback()
        }
      }
    }
  ],
  effective: [
    { required: true, message: '会员有效期不能为空' },
    { type: 'number', min: 1, message: '会员有效期必须大于0' }
  ],
  is_disable: [
    { required: true, message: '请选择状态' },
    { type: 'enum', enum: [0, 1], message: '状态值必须是0或1' }
  ]
}
```

## 页面组件示例（Vue 3）

```vue
<template>
  <div class="good-management">
    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleCreate">新增套餐</el-button>
      <el-button
        type="danger"
        :disabled="selectedIds.length === 0"
        @click="handleBatchDelete"
      >
        批量删除
      </el-button>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-bar">
      <el-form :model="filters" inline>
        <el-form-item label="套餐名称">
          <el-input v-model="filters.name" placeholder="请输入套餐名称" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.is_disable" placeholder="请选择状态" clearable>
            <el-option label="启用" :value="0" />
            <el-option label="禁用" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="价格范围">
          <el-input-number v-model="filters.min_price" placeholder="最低价格" :min="0" />
          <span style="margin: 0 10px;">-</span>
          <el-input-number v-model="filters.max_price" placeholder="最高价格" :min="0" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 套餐列表 -->
    <el-table
      :data="goods"
      v-loading="loading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="套餐名称" />
      <el-table-column prop="tag" label="标签" width="100">
        <template #default="{ row }">
          <el-tag v-if="row.tag" size="small">{{ row.tag }}</el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="original_price" label="原始价格" width="120">
        <template #default="{ row }">
          ¥{{ row.original_price.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="price" label="现时价格" width="120">
        <template #default="{ row }">
          ¥{{ row.price.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="discount_text" label="折扣" width="80">
        <template #default="{ row }">
          <el-tag v-if="row.discount_text !== '无折扣'" type="success" size="small">
            {{ row.discount_text }}
          </el-tag>
          <span v-else>{{ row.discount_text }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="effective" label="有效期" width="100">
        <template #default="{ row }">
          {{ row.effective }}个月
        </template>
      </el-table-column>
      <el-table-column prop="is_disable" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.is_disable === 0 ? 'success' : 'danger'">
            {{ row.is_disable === 0 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250">
        <template #default="{ row }">
          <el-button size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button
            size="small"
            :type="row.is_disable === 0 ? 'warning' : 'success'"
            @click="handleToggleStatus(row)"
          >
            {{ row.is_disable === 0 ? '禁用' : '启用' }}
          </el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.pageSize"
      :total="total"
      @current-change="handlePageChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useZbGood } from './composables/useZbGood'

const {
  goods,
  loading,
  total,
  getGoodList,
  updateGoodStatus,
  deleteGood,
  batchDeleteGoods
} = useZbGood()

const selectedIds = ref<number[]>([])
const pagination = ref({
  page: 1,
  pageSize: 10
})

const filters = reactive({
  name: '',
  is_disable: undefined as number | undefined,
  min_price: undefined as number | undefined,
  max_price: undefined as number | undefined
})

// 获取列表数据
const fetchData = async () => {
  await getGoodList({
    page: pagination.value.page,
    page_size: pagination.value.pageSize,
    ...filters
  })
}

// 搜索
const handleSearch = () => {
  pagination.value.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.assign(filters, {
    name: '',
    is_disable: undefined,
    min_price: undefined,
    max_price: undefined
  })
  pagination.value.page = 1
  fetchData()
}

// 选择变化
const handleSelectionChange = (selection: ZbGoodInfo[]) => {
  selectedIds.value = selection.map(item => item.id)
}

// 切换状态
const handleToggleStatus = async (row: ZbGoodInfo) => {
  const newStatus = row.is_disable === 0 ? 1 : 0
  await updateGoodStatus(row.id, newStatus)
  await fetchData()
}

// 删除套餐
const handleDelete = async (row: ZbGoodInfo) => {
  await deleteGood(row.id)
  await fetchData()
}

// 批量删除
const handleBatchDelete = async () => {
  await batchDeleteGoods(selectedIds.value)
  selectedIds.value = []
  await fetchData()
}

// 页码变化
const handlePageChange = (page: number) => {
  pagination.value.page = page
  fetchData()
}

// 页大小变化
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.page = 1
  fetchData()
}

onMounted(() => {
  fetchData()
})
</script>
```

## 注意事项

1. **价格验证**：现时价格不能大于原始价格
2. **名称唯一性**：创建或更新套餐时需要检查名称重复
3. **软删除机制**：删除操作为软删除，不会真正删除数据
4. **折扣计算**：系统自动计算折扣率和折扣文本
5. **状态管理**：支持启用/禁用状态切换
6. **批量操作**：支持批量删除功能
7. **权限控制**：根据用户权限显示相应操作按钮
8. **数据筛选**：支持按名称、状态、价格范围筛选数据
9. **前台展示**：使用`/zb_good/active`接口获取可用套餐
```
