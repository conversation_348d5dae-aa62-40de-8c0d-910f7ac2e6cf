package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// 资源分组列表请求
type GetListReq struct {
	g.Meta   `path:"/resource_group/list" method:"get" summary:"获取资源分组列表" tags:"资源分组管理" permission:"resource_group:list"`
	Page     int    `json:"page" v:"min:1" dc:"页码，最小值为1" default:"1"`
	PageSize int    `json:"page_size" v:"min:1|max:100" dc:"每页数量，范围1-100" default:"10"`
	Name     string `json:"name" dc:"分组名称（模糊搜索）"`
	Type     string `json:"type" dc:"资源类型：PIC=图片，VIDEO=视频，AUDIO=音频，OTHER=其它"`
}

type GetListRes struct {
	g.Meta `mime:"application/json"`
	List   []ResourcesGroupInfo `json:"list" dc:"资源分组列表"`
	Total  int                  `json:"total" dc:"总数"`
}

// 资源分组详情请求
type GetOneReq struct {
	g.<PERSON>a `path:"/resource_group/get_one/{id}" method:"get" summary:"获取资源分组详情" tags:"资源分组管理"`
	ID     int64 `json:"id" v:"required|min:1" dc:"资源分组ID"`
}

type GetOneRes struct {
	g.Meta         `mime:"application/json"`
	ResourcesGroup *ResourcesGroupDetail `json:"resources_group" dc:"资源分组详情"`
}

// 创建资源分组请求
type CreateReq struct {
	g.Meta `path:"/resource_group/create" method:"post" summary:"创建资源分组" tags:"资源分组管理" permission:"resource_group:add"`
	Name   string `json:"name" v:"required|length:1,255" dc:"分组名称"`
	Type   string `json:"type" v:"required|in:PIC,VIDEO,AUDIO,OTHER" dc:"资源类型：PIC=图片，VIDEO=视频，AUDIO=音频，OTHER=其它"`
}

type CreateRes struct {
	g.Meta `mime:"application/json"`
}

// 更新资源分组请求
type UpdateReq struct {
	g.Meta `path:"/resource_group/update/{id}" method:"put" summary:"更新资源分组" tags:"资源分组管理" permission:"resource_group:edit"`
	ID     int64  `json:"id" v:"required|min:1" dc:"资源分组ID"`
	Name   string `json:"name" v:"required|length:1,255" dc:"分组名称"`
	Type   string `json:"type" v:"required|in:PIC,VIDEO,AUDIO,OTHER" dc:"资源类型：PIC=图片，VIDEO=视频，AUDIO=音频，OTHER=其它"`
}

type UpdateRes struct {
	g.Meta `mime:"application/json"`
}

// 删除资源分组请求
type DeleteReq struct {
	g.Meta `path:"/resource_group/delete" method:"delete" summary:"删除资源分组" tags:"资源分组管理" permission:"resource_group:del"`
	IDs    []int64 `json:"ids" v:"required|length:1,100" dc:"资源分组ID列表"`
}

type DeleteRes struct {
	g.Meta `mime:"application/json"`
}

// 获取所有资源分组请求（用于下拉选择）
type GetAllReq struct {
	g.Meta `path:"/resource_group/all" method:"get" summary:"获取所有资源分组" tags:"资源分组管理"`
	Type   string `json:"type" dc:"资源类型：PIC=图片，VIDEO=视频，AUDIO=音频，OTHER=其它"`
}

type GetAllRes struct {
	g.Meta `mime:"application/json"`
	List   []ResourcesGroupOption `json:"list" dc:"资源分组选项列表"`
}

// 资源分组信息结构体
type ResourcesGroupInfo struct {
	ID        int64       `json:"id" dc:"主键ID"`
	Name      string      `json:"name" dc:"分组名称"`
	Type      string      `json:"type" dc:"资源类型"`
	IsDelete  int         `json:"is_delete" dc:"是否删除"`
	CreatedAt *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt *gtime.Time `json:"updated_at" dc:"更新时间"`
}

// 资源分组详情结构体
type ResourcesGroupDetail struct {
	ID        int64       `json:"id" dc:"主键ID"`
	Name      string      `json:"name" dc:"分组名称"`
	Type      string      `json:"type" dc:"资源类型"`
	IsDelete  int         `json:"is_delete" dc:"是否删除"`
	CreatedAt *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt *gtime.Time `json:"updated_at" dc:"更新时间"`
}

// 资源分组选项结构体（用于下拉选择）
type ResourcesGroupOption struct {
	ID   int64  `json:"id" dc:"主键ID"`
	Name string `json:"name" dc:"分组名称"`
	Type string `json:"type" dc:"资源类型"`
}
