# 微信公众号授权功能说明

## 功能概述

实现了微信公众号OAuth2.0授权功能，用户访问需要授权的页面时会自动跳转到微信授权页面，授权成功后会根据openid查询或创建用户记录。

## 实现的功能

### 1. 微信授权中间件
- **文件**: `internal/middleware/wechat_auth.go`
- **功能**: 检查用户是否已授权，未授权则跳转到微信授权页面
- **流程**: 
  1. 检查session中是否有用户信息
  2. 如果没有，构建微信授权URL并跳转
  3. 处理微信回调，获取用户信息
  4. 查询或创建用户记录
  5. 将用户信息存储到session

### 2. 用户服务
- **文件**: `internal/service/zb_user.go`
- **功能**: 处理用户的增删改查操作
- **主要方法**:
  - `GetByOpenid`: 根据openid获取用户
  - `CreateFromWechat`: 从微信信息创建用户
  - `UpdateLastLogin`: 更新最后登录信息
  - `CheckUserEffective`: 检查用户是否有效

### 3. 路由配置
- **需要授权的页面**: `/m/detail`
- **授权回调**: `/m/auth/callback`
- **无需授权的页面**: `/m/list`

## 配置要求

### 1. 微信公众号配置
在 `config.yaml` 中添加以下配置：

```yaml
wechat:
  appid: "your_wechat_appid"
  secret: "your_wechat_secret"
  token: "your_wechat_token"
  aes_key: "your_wechat_aes_key"  # 可选
```

### 2. 数据库表
确保 `zb_user` 表已创建，包含以下字段：
- `id`: 用户ID
- `openid`: 微信openid
- `nickname`: 昵称
- `avatar`: 头像URL
- `is_disable`: 是否禁用
- `is_delete`: 是否删除
- `effective_status`: 是否有效
- `effective_start`: 有效开始日期
- `effective_end`: 有效结束日期
- `created_at`: 创建时间
- `updated_at`: 更新时间

## 使用流程

### 1. 用户首次访问
1. 用户访问 `/m/detail`
2. 中间件检测到未授权，跳转到微信授权页面
3. 用户在微信中确认授权
4. 微信回调到应用，获取用户信息
5. 系统根据openid查询用户，如果不存在则创建新用户
6. 将用户信息存储到session
7. 重定向回原始页面

### 2. 用户再次访问
1. 用户访问 `/m/detail`
2. 中间件从session中获取用户信息
3. 直接显示页面内容

## 用户注册逻辑

### 新用户注册
当用户首次授权时：
1. 根据openid查询数据库
2. 如果用户不存在，创建新用户记录：
   - `openid`: 微信openid
   - `nickname`: 微信昵称
   - `avatar`: 微信头像
   - `is_disable`: 0（启用）
   - `is_delete`: 0（未删除）
   - `effective_status`: 0（无效，需要购买套餐）
   - `created_at`: 当前时间

### 已注册用户
当用户已存在时：
1. 检查用户状态（是否禁用、是否删除）
2. 更新最后登录信息
3. 返回用户信息

## 页面模板数据

在需要授权的页面中，可以使用以下模板变量：

```html
{{if .is_logged_in}}
    <p>欢迎，{{.user.nickname}}！</p>
    <img src="{{.user.avatar}}" alt="头像">
    {{if eq .user.effective_status 1}}
        <span>VIP用户</span>
    {{else}}
        <span>普通用户</span>
    {{end}}
{{else}}
    <p>未登录</p>
{{end}}
```

## API接口

### 获取当前用户信息
```go
// 在控制器中获取当前用户
wechatUser := middleware.GetCurrentWechatUser(r)
if wechatUser != nil {
    userID := wechatUser["id"].(int)
    nickname := wechatUser["nickname"].(string)
    // ... 其他用户信息
}
```

## 错误处理

### 1. 配置错误
- 微信AppID未配置：返回500错误
- 微信Secret未配置：无法获取access_token

### 2. 授权错误
- 用户拒绝授权：微信会返回错误码
- 网络错误：无法连接微信API

### 3. 用户状态错误
- 用户被禁用：返回"用户已被禁用"错误
- 用户被删除：返回"用户已被删除"错误

## 安全考虑

### 1. Session安全
- 用户信息存储在服务器端session中
- 避免在客户端存储敏感信息

### 2. 状态验证
- 使用state参数防止CSRF攻击
- 验证回调URL的合法性

### 3. 用户权限
- 检查用户的有效状态
- 根据用户权限控制页面访问

## 扩展功能

### 1. 用户权限管理
```go
// 检查用户是否有效
isEffective, err := service.ZbUser().CheckUserEffective(ctx, userID)
if !isEffective {
    // 跳转到购买页面或显示提示
}
```

### 2. 用户信息更新
```go
// 更新用户信息
err := service.ZbUser().UpdateUserInfo(ctx, userID, newNickname, newAvatar)
```

### 3. 用户统计
```go
// 获取用户统计信息
stats, err := service.ZbUser().GetUserStats(ctx)
```

## 测试方法

### 1. 本地测试
1. 配置微信公众号的授权回调域名
2. 在微信开发者工具中测试授权流程
3. 检查数据库中的用户记录

### 2. 线上测试
1. 部署到有域名的服务器
2. 配置微信公众号的网页授权域名
3. 在微信中访问页面测试

## 注意事项

1. **域名配置**: 微信授权需要配置合法的回调域名
2. **HTTPS要求**: 生产环境建议使用HTTPS
3. **用户隐私**: 遵守用户隐私保护相关法规
4. **错误日志**: 记录授权过程中的错误信息
5. **性能优化**: 考虑用户信息的缓存策略

## 常见问题

### 1. 授权失败
- 检查微信公众号配置是否正确
- 确认回调域名是否已在微信后台配置
- 查看错误日志获取详细信息

### 2. 用户信息获取失败
- 检查access_token是否有效
- 确认用户是否已关注公众号（snsapi_userinfo需要用户关注）

### 3. 重复授权
- 检查session配置是否正确
- 确认用户信息是否正确存储到session
