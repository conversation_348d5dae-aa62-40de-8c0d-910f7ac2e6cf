// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysOperateLog is the golang structure of table sys_operate_log for DAO operations like Where/Data.
type SysOperateLog struct {
	g.Meta    `orm:"table:sys_operate_log, do:true"`
	Id        interface{} //
	RequestId interface{} // 请求id
	AdminId   interface{} // 操作人id
	Username  interface{} // 操作人账号
	Method    interface{} // 请求类型: GET/POST/PUT/DELETE
	Title     interface{} // 操作标题
	Ip        interface{} // 请求ip
	ReqHeader interface{} // 请求头
	ReqBody   interface{} // 请求体
	ResHeader interface{} // 响应头
	ResBody   interface{} // 响应体
	Status    interface{} // 执行状态: 1=成功, 2=失败
	StartTime interface{} // 开始时间，时间戳
	EndTime   interface{} // 结束时间，时间戳
	TaskTime  interface{} // 执行耗时
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 更新时间
}
