# 认证授权API文档

## 概述

认证授权模块提供了完整的管理员登录、token管理和权限验证功能。采用JWT token + refresh token的双token机制，确保安全性和用户体验。

## Token机制说明

### 双Token设计
- **Access Token**: 访问令牌，有效期2小时，用于API访问认证
- **Refresh Token**: 刷新令牌，有效期7天，用于刷新访问令牌

### Token使用流程
1. 用户登录成功后获得access_token和refresh_token
2. 使用access_token访问需要认证的API接口
3. access_token过期后，使用refresh_token获取新的token对
4. refresh_token过期后需要重新登录

## 数据结构

### 管理员信息 (AdminInfo)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 管理员ID |
| username | string | 账号 |
| nickname | string | 昵称 |
| is_super | int | 是否超级管理员 (0=否, 1=是) |

### 角色信息 (RoleInfo)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 角色ID |
| name | string | 角色名称 |

### 权限信息 (PermissionInfo)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 权限ID |
| name | string | 权限名称 |
| permission | string | 权限标识 |
| type | string | 权限类型 |

### 菜单信息 (MenuInfo)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 菜单ID |
| pid | int64 | 父级菜单ID |
| menu_type | string | 菜单类型 (M=目录, C=菜单, A=按钮) |
| menu_name | string | 菜单名称 |
| menu_icon | string | 菜单图标 |
| menu_sort | int | 菜单排序 |
| perms | string | 权限标识 |
| paths | string | 路由地址 |
| component | string | 前端组件 |
| params | string | 路由参数 |
| is_cache | int | 是否缓存 (0=否, 1=是) |
| is_show | int | 是否显示 (0=否, 1=是) |
| children | array | 子菜单列表 |

## API接口

### 1. 管理员登录

**接口地址：** `POST /auth/login`

**请求参数：**
```json
{
  "username": "admin",     // 必填，账号
  "password": "123456"     // 必填，密码
}
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2024-01-01T14:00:00Z",
    "admin_info": {
      "id": 1,
      "username": "admin",
      "nickname": "管理员",
      "is_super": 1
    }
  }
}
```

**错误响应：**
```json
{
  "code": 1,
  "message": "用户名或密码错误",
  "data": null
}
```

### 2. 刷新访问令牌

**接口地址：** `POST /auth/refresh`

**请求参数：**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2024-01-01T16:00:00Z"
  }
}
```

### 3. 获取当前用户权限

**接口地址：** `GET /auth/permissions`

**请求头：**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "permissions": [
      {
        "id": 1,
        "name": "用户管理",
        "permission": "system:user:list",
        "type": "C"
      },
      {
        "id": 2,
        "name": "添加用户",
        "permission": "system:user:add",
        "type": "A"
      }
    ]
  }
}
```

### 4. 获取当前用户信息

**接口地址：** `GET /auth/userinfo`

**请求头：**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "admin_info": {
      "id": 1,
      "username": "admin",
      "nickname": "管理员",
      "is_super": 1
    },
    "roles": [
      {
        "id": 1,
        "name": "系统管理员"
      }
    ],
    "permissions": [
      {
        "id": 1,
        "name": "用户管理",
        "permission": "system:user:list",
        "type": "C"
      }
    ]
  }
}
```

### 5. 获取用户菜单

**接口地址：** `GET /auth/menus`

**请求头：**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "menus": [
      {
        "id": 1,
        "pid": 0,
        "menu_type": "M",
        "menu_name": "系统管理",
        "menu_icon": "system",
        "menu_sort": 1,
        "perms": "",
        "paths": "/system",
        "component": "",
        "params": "",
        "is_cache": 0,
        "is_show": 1,
        "children": [
          {
            "id": 2,
            "pid": 1,
            "menu_type": "C",
            "menu_name": "用户管理",
            "menu_icon": "user",
            "menu_sort": 1,
            "perms": "system:user:list",
            "paths": "/system/user",
            "component": "system/user/index",
            "params": "",
            "is_cache": 1,
            "is_show": 1,
            "children": []
          }
        ]
      }
    ]
  }
}
```

### 6. 退出登录

**接口地址：** `POST /auth/logout`

**请求头：**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

## 权限验证

### 前端权限验证流程

1. **登录验证**：检查是否有有效的access_token
2. **菜单获取**：调用`/auth/menus`获取用户菜单列表，构建左侧导航菜单
3. **权限获取**：调用`/auth/permissions`获取用户权限列表
4. **页面权限**：根据权限标识控制页面访问
5. **按钮权限**：根据权限标识控制按钮显示/隐藏
6. **Token刷新**：access_token过期时自动使用refresh_token刷新

### 权限标识规范

权限标识采用模块:功能:操作的格式，例如：
- `system:user:list` - 用户列表查看权限
- `system:user:add` - 用户添加权限
- `system:user:edit` - 用户编辑权限
- `system:user:delete` - 用户删除权限

### 前端集成示例

```javascript
// 登录
const login = async (username, password) => {
  const response = await fetch('/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ username, password }),
  });
  const data = await response.json();
  
  if (data.code === 0) {
    // 保存token
    localStorage.setItem('access_token', data.data.token);
    localStorage.setItem('refresh_token', data.data.refresh_token);
    return data.data;
  }
  throw new Error(data.message);
};

// 获取权限
const getPermissions = async () => {
  const token = localStorage.getItem('access_token');
  const response = await fetch('/auth/permissions', {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
  const data = await response.json();
  return data.data.permissions;
};

// 获取菜单
const getMenus = async () => {
  const token = localStorage.getItem('access_token');
  const response = await fetch('/auth/menus', {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
  const data = await response.json();
  return data.data.menus;
};

// 权限检查
const hasPermission = (permission, permissions) => {
  return permissions.some(p => p.permission === permission);
};

// Token刷新
const refreshToken = async () => {
  const refreshToken = localStorage.getItem('refresh_token');
  const response = await fetch('/auth/refresh', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ refresh_token: refreshToken }),
  });
  const data = await response.json();
  
  if (data.code === 0) {
    localStorage.setItem('access_token', data.data.token);
    localStorage.setItem('refresh_token', data.data.refresh_token);
    return data.data.token;
  }
  throw new Error(data.message);
};
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1 | 业务错误（具体错误信息见message字段） |
| 401 | 未授权（token无效或过期） |
| 403 | 权限不足 |
| 500 | 服务器内部错误 |

## 注意事项

1. **Token安全**：
   - access_token应存储在内存中，避免XSS攻击
   - refresh_token可存储在httpOnly cookie中
   - 生产环境建议使用HTTPS传输

2. **Token过期处理**：
   - 前端应监听401错误，自动使用refresh_token刷新
   - refresh_token过期时需要重新登录

3. **权限和菜单缓存**：
   - 权限和菜单信息可以缓存在前端，减少API调用
   - 用户角色变更时需要重新获取权限和菜单

4. **安全建议**：
   - 定期轮换JWT密钥
   - 实现token黑名单机制
   - 记录登录日志和异常访问

5. **菜单权限控制**：
   - 超级管理员可以看到所有菜单
   - 普通管理员只能看到分配角色对应的菜单
   - 菜单按照menu_sort字段排序显示
   - 只显示is_show=1的菜单项

6. **并发登录**：
   - 系统支持同一用户多设备登录
   - 可根据业务需求实现单点登录限制
