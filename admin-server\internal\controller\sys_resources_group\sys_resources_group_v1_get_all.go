package sys_resources_group

import (
	"context"

	v1 "admin-server/api/sys_resources_group/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetAll(ctx context.Context, req *v1.GetAllReq) (res *v1.GetAllRes, err error) {
	list, err := service.SysResourcesGroup().GetAllResourcesGroups(ctx, req.Type)
	if err != nil {
		return nil, err
	}

	// 转换为API响应格式
	options := make([]v1.ResourcesGroupOption, len(list))
	for i, group := range list {
		options[i] = v1.ResourcesGroupOption{
			ID:   group.Id,
			Name: group.Name,
			Type: group.Type,
		}
	}

	return &v1.GetAllRes{
		List: options,
	}, nil
}
