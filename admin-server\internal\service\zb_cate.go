package service

import (
	v1 "admin-server/api/zb_cate/v1"
	"context"
)

// 1.定义接口
type IZbCate interface {
	Create(ctx context.Context, req *v1.ZbCateCreateReq) (res *v1.ZbCateCreateRes, err error)
	Update(ctx context.Context, req *v1.ZbCateUpdateReq) (res *v1.ZbCateUpdateRes, err error)
	Delete(ctx context.Context, req *v1.ZbCateDeleteReq) (res *v1.ZbCateDeleteRes, err error)
	GetOne(ctx context.Context, req *v1.ZbCateGetOneReq) (res *v1.ZbCateGetOneRes, err error)
	GetList(ctx context.Context, req *v1.ZbCateGetListReq) (res *v1.ZbCateGetListRes, err error)
	GetAll(ctx context.Context, req *v1.ZbCateGetAllReq) (res *v1.ZbCateGetAllRes, err error)
	GetAllForMobile(ctx context.Context) (res *v1.ZbCateGetAllRes, err error)
	UpdateSort(ctx context.Context, req *v1.ZbCateUpdateSortReq) (res *v1.ZbCateUpdateSortRes, err error)
	UpdateStatus(ctx context.Context, req *v1.ZbCateUpdateStatusReq) (res *v1.ZbCateUpdateStatusRes, err error)
	BatchDelete(ctx context.Context, req *v1.ZbCateBatchDeleteReq) (res *v1.ZbCateBatchDeleteRes, err error)
}

// 2.定义接口变量
var localZbCate IZbCate

// 3.定义一个获取接口实例的函数
func ZbCate() IZbCate {
	if localZbCate == nil {
		panic("IZbCate接口未实现或未注册")
	}
	return localZbCate
}

// 4.定义一个接口实现的注册方法
func RegisterZbCate(i IZbCate) {
	localZbCate = i
}
