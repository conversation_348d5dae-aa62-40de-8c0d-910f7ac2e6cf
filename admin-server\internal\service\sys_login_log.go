package service

import (
	"admin-server/internal/dao"
	"admin-server/internal/model/entity"
	"context"
	"net"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
)

// 1.定义接口
type ISysLoginLog interface {
	RecordLoginSuccess(ctx context.Context, r *ghttp.Request, adminId int64, username string) error
	GetLoginLogList(ctx context.Context, page, pageSize int, username string) (list []entity.SysLoginLog, total int, err error)
	DeleteLoginLog(ctx context.Context, ids []int64) error
	ClearLoginLog(ctx context.Context) error
}

// 2.定义接口变量
var localSysLoginLog ISysLoginLog

// 3.定义一个获取接口实例的函数
func SysLoginLog() ISysLoginLog {
	if localSysLoginLog == nil {
		panic("ISysLoginLog接口未实现或未注册")
	}
	return localSysLoginLog
}

// 4.定义一个接口实现的注册方法
func RegisterSysLoginLog(i ISysLoginLog) {
	localSysLoginLog = i
}

type sSysLoginLog struct{}

func init() {
	RegisterSysLoginLog(New())
}

func New() *sSysLoginLog {
	return &sSysLoginLog{}
}

// RecordLoginSuccess 记录登录成功日志
func (s *sSysLoginLog) RecordLoginSuccess(ctx context.Context, r *ghttp.Request, adminId int64, username string) error {
	// 获取客户端IP
	ip := s.getClientIP(r)

	// 获取User-Agent信息
	userAgent := r.Header.Get("User-Agent")

	// 解析操作系统和浏览器信息
	os, browser := s.parseUserAgent(userAgent)

	// 创建登录日志记录
	loginLog := &entity.SysLoginLog{
		AdminId:   adminId,
		Username:  username,
		Ip:        ip,
		Os:        os,
		Browser:   browser,
		CreatedAt: gtime.Now(),
		UpdatedAt: gtime.Now(),
	}

	// 插入数据库
	_, err := dao.SysLoginLog.Ctx(ctx).Insert(loginLog)
	if err != nil {
		g.Log().Error(ctx, "记录登录日志失败:", err)
		return err
	}

	g.Log().Info(ctx, "记录登录成功日志:", "adminId:", adminId, "username:", username, "ip:", ip)
	return nil
}

// getClientIP 获取客户端真实IP
func (s *sSysLoginLog) getClientIP(r *ghttp.Request) string {
	// 优先从代理头获取真实IP
	ip := r.Header.Get("X-Forwarded-For")
	if ip != "" {
		// X-Forwarded-For可能包含多个IP，取第一个
		ips := strings.Split(ip, ",")
		if len(ips) > 0 {
			return strings.TrimSpace(ips[0])
		}
	}

	// 从X-Real-IP头获取
	ip = r.Header.Get("X-Real-IP")
	if ip != "" {
		return ip
	}

	// 从RemoteAddr获取
	ip = r.GetClientIp()
	if ip != "" {
		return ip
	}

	// 最后从连接地址获取
	host, _, err := net.SplitHostPort(r.RemoteAddr)
	if err != nil {
		return r.RemoteAddr
	}

	return host
}

// parseUserAgent 解析User-Agent获取操作系统和浏览器信息
func (s *sSysLoginLog) parseUserAgent(userAgent string) (os, browser string) {
	if userAgent == "" {
		return "Unknown", "Unknown"
	}

	// 解析操作系统
	os = s.parseOS(userAgent)

	// 解析浏览器
	browser = s.parseBrowser(userAgent)

	return os, browser
}

// parseOS 解析操作系统
func (s *sSysLoginLog) parseOS(userAgent string) string {
	userAgent = strings.ToLower(userAgent)

	switch {
	case strings.Contains(userAgent, "windows nt 10.0"):
		return "Windows 10"
	case strings.Contains(userAgent, "windows nt 6.3"):
		return "Windows 8.1"
	case strings.Contains(userAgent, "windows nt 6.2"):
		return "Windows 8"
	case strings.Contains(userAgent, "windows nt 6.1"):
		return "Windows 7"
	case strings.Contains(userAgent, "windows nt 6.0"):
		return "Windows Vista"
	case strings.Contains(userAgent, "windows nt 5.1"):
		return "Windows XP"
	case strings.Contains(userAgent, "windows"):
		return "Windows"
	case strings.Contains(userAgent, "mac os x"):
		return "Mac OS X"
	case strings.Contains(userAgent, "macintosh"):
		return "Mac OS"
	case strings.Contains(userAgent, "linux"):
		return "Linux"
	case strings.Contains(userAgent, "ubuntu"):
		return "Ubuntu"
	case strings.Contains(userAgent, "android"):
		return "Android"
	case strings.Contains(userAgent, "iphone") || strings.Contains(userAgent, "ipad"):
		return "iOS"
	default:
		return "Unknown"
	}
}

// parseBrowser 解析浏览器
func (s *sSysLoginLog) parseBrowser(userAgent string) string {
	userAgent = strings.ToLower(userAgent)

	switch {
	case strings.Contains(userAgent, "edg/"):
		return "Microsoft Edge"
	case strings.Contains(userAgent, "chrome/") && !strings.Contains(userAgent, "edg/"):
		return "Google Chrome"
	case strings.Contains(userAgent, "firefox/"):
		return "Mozilla Firefox"
	case strings.Contains(userAgent, "safari/") && !strings.Contains(userAgent, "chrome/"):
		return "Safari"
	case strings.Contains(userAgent, "opera/") || strings.Contains(userAgent, "opr/"):
		return "Opera"
	case strings.Contains(userAgent, "msie") || strings.Contains(userAgent, "trident/"):
		return "Internet Explorer"
	default:
		return "Unknown"
	}
}

// GetLoginLogList 获取登录日志列表
func (s *sSysLoginLog) GetLoginLogList(ctx context.Context, page, pageSize int, username string) (list []entity.SysLoginLog, total int, err error) {
	query := dao.SysLoginLog.Ctx(ctx)

	// 按用户名筛选
	if username != "" {
		query = query.WhereLike("username", "%"+username+"%")
	}

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.OrderDesc("created_at").Limit(offset, pageSize).Scan(&list)
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

// DeleteLoginLog 删除登录日志
func (s *sSysLoginLog) DeleteLoginLog(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	_, err := dao.SysLoginLog.Ctx(ctx).WhereIn("id", ids).Delete()
	if err != nil {
		g.Log().Error(ctx, "删除登录日志失败:", err)
		return err
	}

	g.Log().Info(ctx, "删除登录日志成功:", "ids:", ids)
	return nil
}

// ClearLoginLog 清空登录日志
func (s *sSysLoginLog) ClearLoginLog(ctx context.Context) error {
	_, err := dao.SysLoginLog.Ctx(ctx).Delete()
	if err != nil {
		g.Log().Error(ctx, "清空登录日志失败:", err)
		return err
	}

	g.Log().Info(ctx, "清空登录日志成功")
	return nil
}
