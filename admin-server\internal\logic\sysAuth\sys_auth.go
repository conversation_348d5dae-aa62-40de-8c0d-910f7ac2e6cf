package sysAuth

import (
	v1 "admin-server/api/sys_auth/v1"
	"admin-server/internal/consts"
	"admin-server/internal/dao"
	"admin-server/internal/model/do"
	"admin-server/internal/model/entity"
	"admin-server/internal/packed"
	"admin-server/internal/service"
	"context"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/contract"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/messages"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/models"
	serverModels "github.com/ArtisanCloud/PowerWeChat/v3/src/officialAccount/server/handlers/models"
	"github.com/golang-jwt/jwt/v5"
	"io"
	"time"

	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

func init() {
	service.RegisterSysAuth(&SsysAuth{})
}

type SsysAuth struct {
}

func (s SsysAuth) WechatServe(ctx context.Context) (message string, err error) {
	// 1. 获取微信公众号实例
	officialAccount, err := service.WechatConfig().GetOfficialAccount(ctx)
	if err != nil {
		return "", gerror.Wrap(err, "获取微信公众号实例失败")
	}

	// 2. 从上下文中获取请求对象
	gRequest := g.RequestFromCtx(ctx)
	if gRequest == nil {
		return "", gerror.New("无效的请求上下文")
	}

	// 3. 检查是否是URL验证请求
	if gRequest.Get("echostr").String() != "" {
		// URL验证
		rs, err := officialAccount.Server.VerifyURL(gRequest.Request)
		if err != nil {
			g.Log().Error(ctx, "URL验证失败:", err)
			return "", gerror.Wrap(err, "URL验证失败")
		}

		// 读取验证响应
		text, err := io.ReadAll(rs.Body)
		if err != nil {
			g.Log().Error(ctx, "读取验证响应失败:", err)
			return "", gerror.Wrap(err, "读取验证响应失败")
		}

		// 直接写入响应
		gRequest.Response.Write(text)
		return "url_verified", nil
	}

	// 4. 处理消息推送
	rs, err := officialAccount.Server.Notify(gRequest.Request, func(event contract.EventInterface) interface{} {
		g.Log().Info(ctx, "收到微信事件:", "MsgType:", event.GetMsgType())

		// 根据消息类型处理不同的事件
		switch event.GetMsgType() {
		case models.CALLBACK_MSG_TYPE_TEXT:
			// 处理文本消息
			msg := serverModels.MessageText{}
			err := event.ReadMessage(&msg)
			if err != nil {
				g.Log().Error(ctx, "解析文本消息失败:", err)
				return kernel.SUCCESS_EMPTY_RESPONSE
			}

			g.Log().Info(ctx, "收到文本消息:", "Content:", msg.Content, "FromUser:", msg.FromUserName)

			// 回复文本消息
			//return messages.NewText("感谢您的消息：" + msg.Content)
			return kernel.SUCCESS_EMPTY_RESPONSE

		case models.CALLBACK_MSG_TYPE_EVENT:
			g.Log().Info(ctx, "收到事件:", "Event:", event)
			switch event.GetEvent() {
			case "subscribe":
				// 关注事件
				g.Log().Info(ctx, "用户关注:", "FromUser:", event.GetFromUserName())
				// 微信公众号配置
				config, _ := service.WechatConfig().GetConfig(ctx)
				if config.AutoReplyEnabled == 1 {
					// 关注自动回复
					return messages.NewText(config.AutoReplyText)
				}
				// 未开启无需进行回复
				return kernel.SUCCESS_EMPTY_RESPONSE

			case "unsubscribe":
				// 取消关注事件
				g.Log().Info(ctx, "用户取消关注:", "FromUser:", event.GetFromUserName())
				// 取消关注不需要回复消息
				return kernel.SUCCESS_EMPTY_RESPONSE

			case "CLICK":
				// 菜单点击事件
				g.Log().Info(ctx, "菜单点击事件:", "EventKey:", event.GetEventKey(), "FromUser:", event.GetFromUserName())
				//return messages.NewText("您点击了菜单：" + event.GetEventKey())
				return kernel.SUCCESS_EMPTY_RESPONSE

			case "VIEW":
				// 菜单跳转事件
				g.Log().Info(ctx, "菜单跳转事件:", "EventKey:", event.GetEventKey(), "FromUser:", event.GetFromUserName())
				return kernel.SUCCESS_EMPTY_RESPONSE

			default:
				g.Log().Info(ctx, "未处理的事件类型:", "Event:", event.GetEventKey())
				return kernel.SUCCESS_EMPTY_RESPONSE
			}

		case models.CALLBACK_MSG_TYPE_IMAGE:
			// 处理图片消息
			msg := serverModels.MessageImage{}
			err := event.ReadMessage(&msg)
			if err != nil {
				g.Log().Error(ctx, "解析图片消息失败:", err)
				return kernel.SUCCESS_EMPTY_RESPONSE
			}

			g.Log().Info(ctx, "收到图片消息:", "MediaId:", msg.MediaID, "FromUser:", msg.FromUserName)
			//return messages.NewText("收到您发送的图片，谢谢分享！")
			return kernel.SUCCESS_EMPTY_RESPONSE

		case models.CALLBACK_MSG_TYPE_VOICE:
			// 处理语音消息
			msg := serverModels.MessageVoice{}
			err := event.ReadMessage(&msg)
			if err != nil {
				g.Log().Error(ctx, "解析语音消息失败:", err)
				return kernel.SUCCESS_EMPTY_RESPONSE
			}

			g.Log().Info(ctx, "收到语音消息:", "MediaId:", msg.MediaID, "FromUser:", msg.FromUserName)
			//return messages.NewText("收到您的语音消息！")
			return kernel.SUCCESS_EMPTY_RESPONSE

		case models.CALLBACK_MSG_TYPE_LOCATION:
			// 处理位置消息
			msg := serverModels.MessageLocation{}
			err := event.ReadMessage(&msg)
			if err != nil {
				g.Log().Error(ctx, "解析位置消息失败:", err)
				return kernel.SUCCESS_EMPTY_RESPONSE
			}

			g.Log().Info(ctx, "收到位置消息:", "Location_X:", msg.LocationX, "Location_Y:", msg.LocationY, "FromUser:", msg.FromUserName)
			//return messages.NewText(fmt.Sprintf("收到您的位置信息：%s", msg.Label))
			return kernel.SUCCESS_EMPTY_RESPONSE

		default:
			g.Log().Info(ctx, "未处理的消息类型:", "MsgType:", event.GetMsgType())
			return kernel.SUCCESS_EMPTY_RESPONSE
		}
	})

	if err != nil {
		g.Log().Error(ctx, "处理微信消息失败:", err)
		return "", gerror.Wrap(err, "处理微信消息失败")
	}

	// 5. 将响应写入HTTP响应
	if rs != nil && rs.Body != nil {
		responseBody, err := io.ReadAll(rs.Body)
		if err != nil {
			g.Log().Error(ctx, "读取响应内容失败:", err)
			return "", gerror.Wrap(err, "读取响应内容失败")
		}

		// 设置响应头和内容
		gRequest.Response.Header().Set("Content-Type", "application/xml; charset=utf-8")
		gRequest.Response.WriteStatus(200, responseBody)

		g.Log().Info(ctx, "微信响应已发送", "ResponseLength:", len(responseBody))
	}

	return "success", nil
}

// JWT Claims结构
type jwtClaims struct {
	AdminId  int64  `json:"admin_id"`
	Username string `json:"username"`
	Type     string `json:"type"` // "access" 或 "refresh"
	jwt.RegisteredClaims
}

// Token配置常量
const (
	AccessTokenExpire  = 2 * time.Hour      // 访问令牌2小时过期
	RefreshTokenExpire = 7 * 24 * time.Hour // 刷新令牌7天过期
	TokenTypeAccess    = "access"
	TokenTypeRefresh   = "refresh"
)

// Login 管理员登录
func (s SsysAuth) Login(ctx context.Context, req *v1.LoginReq) (res *v1.LoginRes, err error) {
	// 查询管理员
	var admin entity.SysAdmins
	err = dao.SysAdmins.Ctx(ctx).Where("username", req.Username).Where("is_delete", packed.NO_DELETE).Scan(&admin)
	if err != nil {
		return nil, gerror.New("用户名或密码错误")
	}

	if admin.Id == 0 {
		return nil, gerror.New("用户名或密码错误")
	}

	// 验证密码
	if admin.Password != s.encryptPassword(req.Password) {
		return nil, gerror.New("用户名或密码错误")
	}

	// 检查账号状态
	if admin.IsDisable == packed.DISABLE {
		return nil, gerror.New("账号已被禁用")
	}

	// 生成token和refresh_token
	token, refreshToken, expiresAt, err := s.GenerateTokens(ctx, admin.Id)
	if err != nil {
		return nil, err
	}

	// 更新最后登录信息
	clientIP := g.RequestFromCtx(ctx).GetClientIp()
	_, err = dao.SysAdmins.Ctx(ctx).Where("id", admin.Id).Data(do.SysAdmins{
		LastLoginIp:   clientIP,
		LastLoginTime: gtime.Now(),
	}).Update()
	if err != nil {
		g.Log().Error(ctx, "更新登录信息失败:", err)
	}

	// 记录登录成功日志
	request := g.RequestFromCtx(ctx)
	if request != nil {
		err = service.SysLoginLog().RecordLoginSuccess(ctx, request, admin.Id, admin.Username)
		if err != nil {
			g.Log().Error(ctx, "记录登录日志失败:", err)
			// 登录日志记录失败不影响登录流程，只记录错误日志
		}
	}

	return &v1.LoginRes{
		Token:        token,
		RefreshToken: refreshToken,
		ExpiresAt:    gtime.NewFromTimeStamp(expiresAt),
		AdminInfo: &v1.AdminInfo{
			ID:       admin.Id,
			Username: admin.Username,
			Nickname: admin.Nickname,
			IsSuper:  int(admin.IsSuper),
		},
	}, nil
}

// GenerateTokens 生成访问令牌和刷新令牌
func (s SsysAuth) GenerateTokens(ctx context.Context, adminId int64) (token, refreshToken string, expiresAt int64, err error) {
	now := time.Now()

	// 获取管理员信息
	var admin entity.SysAdmins
	err = dao.SysAdmins.Ctx(ctx).Where("id", adminId).Where("is_delete", packed.NO_DELETE).Scan(&admin)
	if err != nil {
		return "", "", 0, err
	}

	// 生成访问令牌
	accessClaims := &jwtClaims{
		AdminId:  adminId,
		Username: admin.Username,
		Type:     TokenTypeAccess,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(AccessTokenExpire)),
			IssuedAt:  jwt.NewNumericDate(now),
		},
	}
	accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims)
	token, err = accessToken.SignedString([]byte(consts.JwtKey))
	if err != nil {
		return "", "", 0, err
	}

	// 生成刷新令牌
	refreshClaims := &jwtClaims{
		AdminId:  adminId,
		Username: admin.Username,
		Type:     TokenTypeRefresh,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(RefreshTokenExpire)),
			IssuedAt:  jwt.NewNumericDate(now),
		},
	}
	refreshTokenJWT := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
	refreshToken, err = refreshTokenJWT.SignedString([]byte(consts.JwtKey))
	if err != nil {
		return "", "", 0, err
	}

	expiresAt = now.Add(AccessTokenExpire).Unix()
	return token, refreshToken, expiresAt, nil
}

// RefreshToken 刷新访问令牌
func (s SsysAuth) RefreshToken(ctx context.Context, refreshToken string) (res *v1.RefreshTokenRes, err error) {
	// 验证刷新令牌
	token, err := jwt.ParseWithClaims(refreshToken, &jwtClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(consts.JwtKey), nil
	})
	if err != nil {
		return nil, gerror.New("刷新令牌无效")
	}

	claims, ok := token.Claims.(*jwtClaims)
	if !ok || !token.Valid {
		return nil, gerror.New("刷新令牌无效")
	}

	// 检查令牌类型
	if claims.Type != TokenTypeRefresh {
		return nil, gerror.New("令牌类型错误")
	}

	// 检查管理员是否存在且未被禁用
	var admin entity.SysAdmins
	err = dao.SysAdmins.Ctx(ctx).Where("id", claims.AdminId).Where("is_delete", packed.NO_DELETE).Scan(&admin)
	if err != nil || admin.Id == 0 {
		return nil, gerror.New("用户不存在")
	}

	if admin.IsDisable == packed.DISABLE {
		return nil, gerror.New("账号已被禁用")
	}

	// 生成新的令牌
	newToken, newRefreshToken, expiresAt, err := s.GenerateTokens(ctx, claims.AdminId)
	if err != nil {
		return nil, err
	}

	return &v1.RefreshTokenRes{
		Token:        newToken,
		RefreshToken: newRefreshToken,
		ExpiresAt:    gtime.NewFromTimeStamp(expiresAt),
	}, nil
}

// ValidateToken 验证访问令牌
func (s SsysAuth) ValidateToken(ctx context.Context, tokenString string) (adminId int64, err error) {
	token, err := jwt.ParseWithClaims(tokenString, &jwtClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(consts.JwtKey), nil
	})
	if err != nil {
		g.Log().Error(ctx, "ValidateToken - JWT parse error:", err)
		return 0, gerror.New("令牌无效")
	}

	claims, ok := token.Claims.(*jwtClaims)
	if !ok || !token.Valid {
		g.Log().Error(ctx, "ValidateToken - Claims invalid:", ok, token.Valid)
		return 0, gerror.New("令牌无效")
	}

	g.Log().Info(ctx, "ValidateToken - Claims:", claims.AdminId, claims.Type, claims.Username)

	// 检查令牌类型（如果Type字段存在）
	if claims.Type != "" && claims.Type != TokenTypeAccess {
		g.Log().Error(ctx, "ValidateToken - Wrong token type:", claims.Type)
		return 0, gerror.New("令牌类型错误")
	}

	return claims.AdminId, nil
}

// GetPermissions 获取管理员权限
func (s SsysAuth) GetPermissions(ctx context.Context, adminId int64) (permissions []*v1.PermissionInfo, err error) {
	// 查询管理员的角色
	var adminRoles []entity.SysAdminRole
	err = dao.SysAdminRole.Ctx(ctx).Where("admin_id", adminId).Scan(&adminRoles)
	if err != nil {
		return nil, err
	}

	if len(adminRoles) == 0 {
		return []*v1.PermissionInfo{}, nil
	}

	// 提取角色ID
	var roleIds []int64
	for _, adminRole := range adminRoles {
		roleIds = append(roleIds, adminRole.RoleId)
	}

	// 查询角色的权限关联
	var roleMenus []entity.SysRoleMenu
	err = dao.SysRoleMenu.Ctx(ctx).Where("role_id", roleIds).Scan(&roleMenus)
	if err != nil {
		return nil, err
	}

	if len(roleMenus) == 0 {
		return []*v1.PermissionInfo{}, nil
	}

	// 提取菜单ID
	var menuIds []int64
	for _, roleMenu := range roleMenus {
		menuIds = append(menuIds, roleMenu.MenuId)
	}

	// 查询菜单权限信息
	var menus []entity.SysMenu
	err = dao.SysMenu.Ctx(ctx).Where("id", menuIds).Where("is_delete", packed.NO_DELETE).Scan(&menus)
	if err != nil {
		return nil, err
	}

	// 转换为权限信息
	permissions = make([]*v1.PermissionInfo, 0, len(menus))
	for _, menu := range menus {
		if menu.Perms != "" {
			permissions = append(permissions, &v1.PermissionInfo{
				ID:         menu.Id,
				Name:       menu.MenuName,
				Permission: menu.Perms,
				Type:       menu.MenuType,
			})
		}
	}

	return permissions, nil
}

// Logout 退出登录
func (s SsysAuth) Logout(ctx context.Context, token string) (err error) {
	// 这里可以实现token黑名单机制
	// 目前简单返回成功，实际项目中可以将token加入黑名单
	return nil
}

// GetUserInfo 获取当前用户信息
func (s SsysAuth) GetUserInfo(ctx context.Context, adminId int64) (res *v1.GetUserInfoRes, err error) {
	// 获取管理员信息
	adminInfo, err := service.SysAdmin().GetOne(ctx, adminId)
	if err != nil {
		return nil, err
	}

	// 获取角色信息
	roles, err := service.SysAdmin().GetRoles(ctx, adminId)
	if err != nil {
		return nil, err
	}

	// 获取权限信息
	permissions, err := s.GetPermissions(ctx, adminId)
	if err != nil {
		return nil, err
	}

	// 转换角色信息格式
	roleInfos := make([]*v1.RoleInfo, len(roles))
	for i, role := range roles {
		roleInfos[i] = &v1.RoleInfo{
			ID:   role.ID,
			Name: role.Name,
		}
	}

	return &v1.GetUserInfoRes{
		AdminInfo: &v1.AdminInfo{
			ID:       adminInfo.ID,
			Username: adminInfo.Username,
			Nickname: adminInfo.Nickname,
			IsSuper:  int(adminInfo.IsSuper),
		},
		Roles:       roleInfos,
		Permissions: permissions,
	}, nil
}

// GetMenus 获取用户菜单
func (s SsysAuth) GetMenus(ctx context.Context, adminId int64) (menus []*v1.MenuInfo, err error) {
	// 检查是否为超级管理员
	var admin entity.SysAdmins
	err = dao.SysAdmins.Ctx(ctx).Where("id", adminId).Where("is_delete", packed.NO_DELETE).Where("is_disable", packed.ENABLE).Scan(&admin)
	if err != nil {
		return nil, err
	}

	if admin.Id == 0 {
		return nil, gerror.New("用户不存在")
	}

	var allMenus []entity.SysMenu

	// 如果是超级管理员，获取所有菜单
	if admin.IsSuper == packed.IS_SUPER {
		err = dao.SysMenu.Ctx(ctx).Where("is_delete", packed.NO_DELETE).Where("is_show", packed.IS_SHOW).Where("is_disable", packed.ENABLE).OrderAsc("menu_sort").Scan(&allMenus)
		if err != nil {
			return nil, err
		}
	} else {
		// 普通管理员，根据角色获取菜单
		// 查询管理员的角色
		var adminRoles []entity.SysAdminRole
		err = dao.SysAdminRole.Ctx(ctx).Where("admin_id", adminId).Scan(&adminRoles)
		if err != nil {
			return nil, err
		}

		if len(adminRoles) == 0 {
			return []*v1.MenuInfo{}, nil
		}

		// 提取角色ID
		var roleIds []int64
		for _, adminRole := range adminRoles {
			roleIds = append(roleIds, adminRole.RoleId)
		}

		// 查询角色的菜单关联
		var roleMenus []entity.SysRoleMenu
		err = dao.SysRoleMenu.Ctx(ctx).Where("role_id", roleIds).Scan(&roleMenus)
		if err != nil {
			return nil, err
		}

		if len(roleMenus) == 0 {
			return []*v1.MenuInfo{}, nil
		}

		// 提取菜单ID
		var menuIds []int64
		for _, roleMenu := range roleMenus {
			menuIds = append(menuIds, roleMenu.MenuId)
		}

		// 查询菜单信息
		err = dao.SysMenu.Ctx(ctx).Where("id", menuIds).Where("is_delete", packed.NO_DELETE).Where("is_show", packed.IS_SHOW).OrderAsc("menu_sort").Scan(&allMenus)
		if err != nil {
			return nil, err
		}
	}

	// 转换为MenuInfo格式并构建树形结构
	menuMap := make(map[int64]*v1.MenuInfo)
	var rootMenus []*v1.MenuInfo

	// 先创建所有菜单项
	for _, menu := range allMenus {
		menuInfo := &v1.MenuInfo{
			ID:        menu.Id,
			Pid:       menu.Pid,
			MenuType:  menu.MenuType,
			MenuName:  menu.MenuName,
			MenuIcon:  menu.MenuIcon,
			MenuSort:  menu.MenuSort,
			Perms:     menu.Perms,
			Paths:     menu.Paths,
			Component: menu.Component,
			Params:    menu.Params,
			IsCache:   menu.IsCache,
			IsShow:    menu.IsShow,
			Children:  []*v1.MenuInfo{},
		}
		menuMap[menu.Id] = menuInfo
	}

	// 构建树形结构
	for _, menuInfo := range menuMap {
		if menuInfo.Pid == 0 {
			// 根菜单
			rootMenus = append(rootMenus, menuInfo)
		} else {
			// 子菜单
			if parent, exists := menuMap[menuInfo.Pid]; exists {
				parent.Children = append(parent.Children, menuInfo)
			}
		}
	}

	return rootMenus, nil
}

func (s *SsysAuth) encryptPassword(password string) string {
	return gmd5.MustEncryptString(password)
}
