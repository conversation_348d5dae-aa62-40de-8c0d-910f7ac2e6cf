<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>招标信息列表</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="http://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            -webkit-overflow-scrolling: touch;
        }
        .phone-container {
            min-height: 100vh;
            position: relative;
            -webkit-overflow-scrolling: touch;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .mini-capsule {
            position: absolute;
            top: 8px;
            right: 12px;
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        /* 类别标签样式 */
        .category-tab {
            transition: all 0.3s ease;
        }
        .category-tab.active {
            font-weight: 600;
        }
        #categoryTabs {
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }
        #categoryTabs::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        /* 确保页面可以正常滚动 */
        html {
            height: 100%;
            -webkit-text-size-adjust: 100%;
        }

        * {
            -webkit-tap-highlight-color: transparent;
        }

        /* 修复iOS滚动问题 */
        .phone-container {
            position: relative;
            overflow: visible;
        }

        /* 文本截断样式 */
        .line-clamp-1 {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
</head>
<body>
<div class="phone-container">
    <!-- 顶部搜索区域 -->
    <div class="gradient-bg px-4 pt-6 pb-3">
        <!-- 分类标签 -->
        <div id="categoryTabs" class="flex space-x-2 overflow-x-auto pb-2">
            <span class="category-tab active bg-white/90 text-purple-600 px-4 py-2 rounded-full text-xs font-medium whitespace-nowrap cursor-pointer" data-id="0">全部</span>
            <!-- 动态加载的分类标签将插入到这里 -->
        </div>
    </div>

    <!-- 内容区域 -->
    <div class="bg-white flex-1">
        <!-- 统计信息 -->
        <div class="px-4 py-3 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-100">
            <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">今日新增 <span id="todayCount" class="text-blue-600 font-semibold">0</span> 条</span>
                <span class="text-sm text-gray-500">共 <span id="totalCount" class="font-semibold">0</span> 条信息</span>
            </div>
        </div>

        <!-- 招标信息列表 -->
        <div id="articleList" class="px-4 py-2 space-y-3">
            <!-- 动态加载的文章列表将插入到这里 -->
        </div>

        <!-- 加载状态 -->
        <div id="loadingIndicator" class="px-4 py-6 text-center hidden">
            <div class="inline-flex items-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-sm text-gray-600">加载中...</span>
            </div>
        </div>

        <!-- 加载更多按钮 -->
        <div id="loadMoreContainer" class="px-4 py-6 text-center">
            <button id="loadMoreBtn" class="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-2 rounded-full text-sm font-medium">
                加载更多
            </button>
        </div>

        <!-- 没有更多数据提示 -->
        <div id="noMoreData" class="px-4 py-6 text-center text-gray-500 text-sm hidden">
            没有更多数据了
        </div>
    </div>

</div>

<script>
// 全局变量
let currentPage = 1;
let pageSize = 20;
let isLoading = false;
let hasMoreData = true;
let currentCategoryId = 0;
let articleList = [];
// 选中的类别id
var tableIndex = {{.tableIndex}}

// 当页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 设置初始分类ID
    currentCategoryId = tableIndex || 0;

    // 加载招标类别
    loadCategories();

    // 加载微信JSSDK配置
    loadJssdkConfig();

    // 初始化统计信息显示
    initStats();

    // 加载文章列表
    loadArticleList();

    // 为"全部"标签添加点击事件和初始选中状态
    const allTab = document.querySelector('.category-tab[data-id="0"]');
    if (allTab) {
        // 根据tableIndex设置初始选中状态
        if (tableIndex == 0) {
            // tableIndex为0时，保持"全部"按钮的选中状态
            allTab.classList.remove('bg-white/20', 'text-white');
            allTab.classList.add('active', 'bg-white/90', 'text-purple-600');
        } else {
            // tableIndex不为0时，移除"全部"按钮的选中状态
            allTab.classList.remove('active', 'bg-white/90', 'text-purple-600');
            allTab.classList.add('bg-white/20', 'text-white');
        }

        allTab.addEventListener('click', function() {
            // 切换选中状态
            document.querySelectorAll('.category-tab').forEach(tab => {
                tab.classList.remove('active', 'bg-white/90', 'text-purple-600');
                tab.classList.add('bg-white/20', 'text-white');
            });
            this.classList.remove('bg-white/20', 'text-white');
            this.classList.add('active', 'bg-white/90', 'text-purple-600');

            // 显示所有数据
            filterByCategory(0);
        });
    }

    // 加载更多按钮点击事件
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            if (!isLoading && hasMoreData) {
                currentPage++;
                loadArticleList(true); // true表示追加数据
            }
        });
    }
});

// 加载招标类别
async function loadCategories() {
    try {
        // 请求招标类别数据
        const response = await fetch('/m/api/zb_cate/all?is_disable=0');
        const result = await response.json();

        if (result.code === 0 && result.data && result.data.list) {
            // 获取类别容器
            const categoryTabs = document.getElementById('categoryTabs');
            const categories = result.data.list;

            // 渲染类别标签
            categories.forEach(category => {
                const categoryTab = document.createElement('span');
                categoryTab.className = 'category-tab bg-white/20 text-white px-4 py-2 rounded-full text-xs whitespace-nowrap cursor-pointer';
                categoryTab.textContent = category.name;
                categoryTab.dataset.id = category.id;

                // 根据tableIndex设置初始选中状态
                if (category.id == tableIndex) {
                    categoryTab.classList.remove('bg-white/20', 'text-white');
                    categoryTab.classList.add('active', 'bg-white/90', 'text-purple-600');
                }

                categoryTab.addEventListener('click', function() {
                    // 切换选中状态
                    document.querySelectorAll('.category-tab').forEach(tab => {
                        tab.classList.remove('active', 'bg-white/90', 'text-purple-600');
                        tab.classList.add('bg-white/20', 'text-white');
                    });
                    this.classList.remove('bg-white/20', 'text-white');
                    this.classList.add('active', 'bg-white/90', 'text-purple-600');

                    // 根据类别筛选数据
                    filterByCategory(category.id);
                });

                categoryTabs.appendChild(categoryTab);
            });

            console.log('招标类别加载成功:', categories);
        } else {
            console.error('招标类别数据格式错误:', result);
        }
    } catch (error) {
        console.error('加载招标类别失败:', error);
    }
}

// 加载分享jssdk配置
async function loadJssdkConfig() {
    try {
        // 获取当前页面的URL
        const currentUrl = window.location.href;

        console.log('正在获取JSSDK配置，当前URL:', currentUrl);

        // 请求JSSDK配置
        const response = await fetch('/m/api/get_wechat_jssdk', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                url: currentUrl
            })
        });

        const result = await response.json();

        if (result.code === 0 && result.data && result.data.signature) {
            const config = result.data.signature;

            console.log('JSSDK配置获取成功:', config);


            // iPhone兼容性：扩展jsApiList包含旧版API
            let jsApiList = config.jsApiList || [
                'updateAppMessageShareData',
                'updateTimelineShareData'
            ];

            // 配置微信JSSDK
            wx.config({
                debug: config.debug || false,
                appId: config.appId,
                timestamp: config.timestamp,
                nonceStr: config.nonceStr,
                signature: config.signature,
                jsApiList: jsApiList,
                openTagList: config.openTagList || []
            });

            console.log('微信JSSDK配置完成');

        } else {
            console.error('JSSDK配置数据格式错误:', result);
            throw new Error(result.message || 'JSSDK配置获取失败');
        }

    } catch (error) {
        console.error('加载JSSDK配置失败:', error);

        // 可以在这里添加错误处理逻辑
        // 例如显示错误提示或使用默认配置
    }
}

// 加载文章列表
async function loadArticleList(append = false) {
    if (isLoading) return;

    isLoading = true;
    showLoading();

    try {
        // 构建请求URL
        let url = `/m/api/zb_article/mobileList?page=${currentPage}&page_size=${pageSize}`;

        // 只有当分类ID大于0时才添加cate_id参数，0表示全部
        if (currentCategoryId > 0) {
            url += `&cate_id=${currentCategoryId}`;
        }

        console.log('请求URL:', url);

        const response = await fetch(url);
        const result = await response.json();

        if (result.code === 0 && result.data) {
            const articles = result.data.list || [];
            const total = result.data.total || 0;

            if (append) {
                // 追加数据
                articleList = articleList.concat(articles);
            } else {
                // 重新加载数据
                articleList = articles;
                currentPage = 1;
            }

            // 检查是否还有更多数据
            hasMoreData = articleList.length < total;

            // 渲染文章列表
            renderArticleList(append);

            // 更新统计信息
            updateStats(total);

            console.log(`文章列表加载成功: ${articles.length}条，总计: ${total}条，分类ID: ${currentCategoryId}`);
        } else {
            console.error('文章列表数据格式错误:', result);
            showError('加载失败，请稍后重试');
        }
    } catch (error) {
        console.error('加载文章列表失败:', error);
        showError('网络错误，请检查网络连接');
    } finally {
        isLoading = false;
        hideLoading();
        updateLoadMoreButton();
    }
}

// 渲染文章列表
function renderArticleList(append = false) {
    const listContainer = document.getElementById('articleList');
    if (!listContainer) return;

    if (!append) {
        listContainer.innerHTML = '';
    }

    articleList.forEach((article, index) => {
        // 如果是追加模式，跳过已渲染的文章
        if (append && index < articleList.length - (currentPage === 1 ? articleList.length : pageSize)) {
            return;
        }

        const articleCard = createArticleCard(article);
        listContainer.appendChild(articleCard);
    });
}

// 创建文章卡片
function createArticleCard(article) {
    const cardDiv = document.createElement('div');
    cardDiv.className = 'bg-white rounded-xl p-4 card-shadow border border-gray-100';

    // 格式化时间
    const timeAgo = formatTimeAgo(article.created_at);

    // 获取状态标签
    const statusTag = getStatusTag(article);

    cardDiv.innerHTML = `
        <div class="flex items-start justify-between mb-2">
            ${statusTag}
            <span class="text-xs text-gray-400">${timeAgo}</span>
        </div>
        <h3 class="text-sm font-semibold text-gray-800 mb-2 line-clamp-2">${escapeHtml(article.title)}</h3>
        <div class="space-y-1 mb-3">
            ${article.intro ? `
            <div class="flex items-center text-xs text-gray-600">
                <i class="fas fa-info-circle w-4"></i>
                <span class="line-clamp-1">${escapeHtml(article.intro)}</span>
            </div>
            ` : ''}
            <div class="flex items-center text-xs text-gray-600">
                <i class="fas fa-eye w-4"></i>
                <span>浏览 ${article.view_count || 0} 次</span>
            </div>
        </div>
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2 flex-wrap">
                <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded text-xs">${escapeHtml(article.cate_name || '未分类')}</span>
                <span class="bg-orange-100 text-orange-600 px-2 py-1 rounded text-xs">${escapeHtml(article.city_name || '未知城市')}</span>
                ${article.author ? `<span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs">${escapeHtml(article.author)}</span>` : ''}
            </div>
            <button class="text-purple-600 text-xs font-medium" onclick="checkDetailAccess(${article.id})">查看详情</button>
        </div>
    `;

    return cardDiv;
}

// 根据类别筛选数据
function filterByCategory(categoryId) {
    console.log('筛选类别:', categoryId);
    currentCategoryId = categoryId;
    currentPage = 1;
    hasMoreData = true;

    // 重置文章列表
    articleList = [];

    // 显示加载状态
    const totalCountElement = document.getElementById('totalCount');
    if (totalCountElement) {
        totalCountElement.textContent = '筛选中...';
    }

    // 重新加载数据
    loadArticleList();
}

// 辅助函数
function formatTimeAgo(dateString) {
    if (!dateString) return '未知时间';

    const now = new Date();
    const date = new Date(dateString);
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;

    return date.toLocaleDateString('zh-CN');
}

function getStatusTag(article) {
    const now = new Date();
    const createdAt = new Date(article.created_at);
    const diffHours = (now - createdAt) / (1000 * 60 * 60);

    if (diffHours < 2) {
        return '<span class="bg-red-100 text-red-600 px-2 py-1 rounded-md text-xs font-medium">最新</span>';
    } else if (diffHours < 24) {
        return '<span class="bg-orange-100 text-orange-600 px-2 py-1 rounded-md text-xs font-medium">热门</span>';
    } else {
        return '<span class="bg-blue-100 text-blue-600 px-2 py-1 rounded-md text-xs font-medium">推荐</span>';
    }
}

function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function showLoading() {
    const indicator = document.getElementById('loadingIndicator');
    if (indicator) {
        indicator.classList.remove('hidden');
    }
}

function hideLoading() {
    const indicator = document.getElementById('loadingIndicator');
    if (indicator) {
        indicator.classList.add('hidden');
    }
}

function updateLoadMoreButton() {
    const container = document.getElementById('loadMoreContainer');
    const noMoreData = document.getElementById('noMoreData');

    if (hasMoreData) {
        container.classList.remove('hidden');
        noMoreData.classList.add('hidden');
    } else {
        container.classList.add('hidden');
        noMoreData.classList.remove('hidden');
    }
}

function initStats() {
    // 初始化统计信息显示
    const totalCountElement = document.getElementById('totalCount');
    const todayCountElement = document.getElementById('todayCount');

    if (totalCountElement) {
        totalCountElement.textContent = '加载中...';
    }

    if (todayCountElement) {
        todayCountElement.textContent = '0';
    }
}

function updateStats(listTotal) {
    // 获取真实的统计数据（包含分类筛选）
    loadRealStats();
}

// 加载真实的统计数据
async function loadRealStats() {
    try {
        // 构建统计接口URL，根据当前分类添加参数
        let statsUrl = '/m/api/zb_article/stats';
        if (currentCategoryId > 0) {
            statsUrl += `?cate_id=${currentCategoryId}`;
        }

        console.log('请求统计URL:', statsUrl);

        const response = await fetch(statsUrl);
        const result = await response.json();

        if (result.code === 0 && result.data) {
            const totalCountElement = document.getElementById('totalCount');
            const todayCountElement = document.getElementById('todayCount');

            // 更新总条数（根据当前筛选条件）
            if (totalCountElement) {
                totalCountElement.textContent = result.data.total_articles.toLocaleString();
            }

            // 更新今日新增（根据当前筛选条件）
            if (todayCountElement) {
                todayCountElement.textContent = result.data.today_articles.toLocaleString();
            }

            console.log(`统计数据加载成功 - 分类ID: ${currentCategoryId}, 总数: ${result.data.total_articles}, 今日新增: ${result.data.today_articles}`);
        } else {
            console.error('统计数据格式错误:', result);
            // 如果获取失败，使用备用计算方式
            fallbackTodayCount();
        }
    } catch (error) {
        console.error('获取统计数据失败:', error);
        // 如果获取失败，使用备用计算方式
        fallbackTodayCount();
    }
}

// 备用的今日新增计算方式
function fallbackTodayCount() {
    const todayCountElement = document.getElementById('todayCount');
    if (todayCountElement) {
        // 简化计算：假设今日新增为总数的5%，最少1条，最多999条
        const totalCountText = document.getElementById('totalCount').textContent.replace(/,/g, '');
        const total = parseInt(totalCountText) || 0;
        const todayCount = Math.min(999, Math.max(1, Math.floor(total * 0.05)));
        todayCountElement.textContent = todayCount.toLocaleString();
    }
}

function showError(message) {
    // 简单的错误提示，可以根据需要改为更友好的提示方式
    console.error('错误:', message);
    // 可以在这里添加Toast提示或其他错误显示方式
}

function checkDetailAccess(params) {
    window.location.href = '/m/detail?id='+ params;
}

// 微信JSSDK配置成功回调
wx.ready(function () {
    console.log('微信JSSDK初始化成功');

    // 获取当前页面URL，iPhone需要特殊处理
    let shareUrl = window.location.href;

    console.log('分享URL:', shareUrl);

    // 分享配置对象
    const shareConfig = {
        title: '招标信息列表 - 最新招标公告',
        desc: '最新招标公告，把握商机不错过',
        link: shareUrl,
        imgUrl: '', // 可以设置默认分享图片
        success: function () {
            console.log('分享配置成功');
        },
        fail: function (error) {
            console.error('分享配置失败:', error);
        }
    };

    // 设置分享到朋友圈的内容
    wx.updateTimelineShareData({
        title: shareConfig.title,
        link: shareConfig.link,
        imgUrl: shareConfig.imgUrl,
        success: function () {
            console.log('朋友圈分享配置成功');
        },
        fail: function (error) {
            console.error('朋友圈分享配置失败:', error);
        }
    });

    // 设置分享给朋友的内容
    wx.updateAppMessageShareData({
        title: shareConfig.title,
        desc: shareConfig.desc,
        link: shareConfig.link,
        imgUrl: shareConfig.imgUrl,
        success: function () {
            console.log('好友分享配置成功');
        },
        fail: function (error) {
            console.error('好友分享配置失败:', error);
        }
    });
});

// 微信JSSDK配置失败回调
wx.error(function (res) {
    console.error('微信JSSDK配置失败:', res);

    // 通用错误处理
    console.log('JSSDK配置失败，错误详情:', {
        errMsg: res.errMsg,
        userAgent: navigator.userAgent,
        url: window.location.href
    });
});
</script>
</body>
</html>
