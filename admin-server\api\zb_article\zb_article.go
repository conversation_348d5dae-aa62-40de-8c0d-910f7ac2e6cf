// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package zb_article

import (
	"context"

	v1 "admin-server/api/zb_article/v1"
)

type IZbArticleV1 interface {
	GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error)
	GetListForMobile(ctx context.Context, req *v1.GetListMobileReq) (res *v1.GetListRes, err error)
	GetOne(ctx context.Context, req *v1.GetOneReq) (res *v1.GetOneRes, err error)
	Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error)
	Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error)
	Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error)
	SetStatus(ctx context.Context, req *v1.SetStatusReq) (res *v1.SetStatusRes, err error)
	GetByCity(ctx context.Context, req *v1.GetByCityReq) (res *v1.GetByCityRes, err error)
	GetByCategory(ctx context.Context, req *v1.GetByCategoryReq) (res *v1.GetByCategoryRes, err error)
	GetHot(ctx context.Context, req *v1.GetHotReq) (res *v1.GetHotRes, err error)
	GetRecent(ctx context.Context, req *v1.GetRecentReq) (res *v1.GetRecentRes, err error)
	GetStats(ctx context.Context, req *v1.GetStatsReq) (res *v1.GetStatsRes, err error)
	Search(ctx context.Context, req *v1.SearchReq) (res *v1.SearchRes, err error)
	IncrementView(ctx context.Context, req *v1.IncrementViewReq) (res *v1.IncrementViewRes, err error)
}
