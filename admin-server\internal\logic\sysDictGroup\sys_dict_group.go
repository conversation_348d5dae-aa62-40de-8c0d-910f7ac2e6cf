package sysDictGroup

import (
	"admin-server/internal/dao"
	"admin-server/internal/model/do"
	"admin-server/internal/model/entity"
	"admin-server/internal/packed"
	"admin-server/internal/service"
	"context"
	"github.com/gogf/gf/v2/errors/gerror"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

type sSysDictGroup struct{}

func init() {
	service.RegisterSysDictGroup(&SsysDictGroup{})
}

type SsysDictGroup struct {
}

func (s *SsysDictGroup) GetAllDictGroups(ctx context.Context) ([]entity.SysDictGroup, error) {
	//TODO implement me
	panic("implement me")
}

// GetDictGroupList 获取字典分组列表
func (s *SsysDictGroup) GetDictGroupList(ctx context.Context, page, pageSize int, name, code string, isDisable int) (list []entity.SysDictGroup, total int, err error) {
	query := dao.SysDictGroup.Ctx(ctx).Where("is_delete", packed.NO_DELETE)

	// 按名称筛选
	if name != "" {
		query = query.WhereLike("name", "%"+name+"%")
	}

	// 按编码筛选
	if code != "" {
		query = query.WhereLike("code", "%"+code+"%")
	}

	// 按状态筛选
	if isDisable >= 0 {
		query = query.Where("is_disable", isDisable)
	}

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.OrderAsc("id").OrderDesc("created_at").Limit(offset, pageSize).Scan(&list)
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

// GetDictGroupDetail 获取字典分组详情
func (s *SsysDictGroup) GetDictGroupDetail(ctx context.Context, id int64) (*entity.SysDictGroup, error) {
	var dictGroup entity.SysDictGroup
	err := dao.SysDictGroup.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Scan(&dictGroup)
	if err != nil {
		return nil, err
	}

	if dictGroup.Id == 0 {
		return nil, nil
	}

	return &dictGroup, nil
}

// CreateDictGroup 创建字典分组
func (s *SsysDictGroup) CreateDictGroup(ctx context.Context, name, code, remark string) error {
	// 检查编码是否已存在
	exists, err := s.CheckDictGroupCodeExists(ctx, code, 0)
	if err != nil {
		return err
	}
	if exists {
		return gerror.New("字典分组编码已存在")
	}

	dictGroup := &do.SysDictGroup{
		Name:      name,
		Code:      code,
		Remark:    remark,
		IsDisable: packed.ENABLE,
		IsDelete:  packed.NO_DELETE,
		IsSystem:  packed.NO_SYSTEM,
		CreatedAt: gtime.Now(),
		UpdatedAt: gtime.Now(),
	}

	_, err = dao.SysDictGroup.Ctx(ctx).Insert(dictGroup)
	if err != nil {
		g.Log().Error(ctx, "创建字典分组失败:", err)
		return err
	}

	g.Log().Info(ctx, "创建字典分组成功:", "name:", name, "code:", code)
	return nil
}

// UpdateDictGroup 更新字典分组
func (s *SsysDictGroup) UpdateDictGroup(ctx context.Context, id int64, name, code, remark string) error {
	// 检查字典分组是否存在
	dictGroup, err := s.GetDictGroupDetail(ctx, id)
	if err != nil {
		return err
	}
	if dictGroup == nil {
		return gerror.New("字典分组不存在")
	}

	// 检查是否为系统保留
	if dictGroup.IsSystem == packed.YES_SYSTEM {
		return gerror.New("系统保留字典分组不允许修改")
	}

	// 检查编码是否已存在（排除自己）
	exists, err := s.CheckDictGroupCodeExists(ctx, code, id)
	if err != nil {
		return err
	}
	if exists {
		return gerror.New("字典分组编码已存在")
	}

	updateData := &do.SysDictGroup{
		Name:      name,
		Code:      code,
		Remark:    remark,
		UpdatedAt: gtime.Now(),
	}

	_, err = dao.SysDictGroup.Ctx(ctx).Where("id", id).Update(updateData)
	if err != nil {
		g.Log().Error(ctx, "更新字典分组失败:", err)
		return err
	}

	g.Log().Info(ctx, "更新字典分组成功:", "id:", id, "name:", name)
	return nil
}

// DeleteDictGroup 删除字典分组
func (s *SsysDictGroup) DeleteDictGroup(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	// 检查是否有系统保留的分组
	var systemGroups []entity.SysDictGroup
	err := dao.SysDictGroup.Ctx(ctx).WhereIn("id", ids).Where("is_system", packed.YES_SYSTEM).Scan(&systemGroups)
	if err != nil {
		return err
	}
	if len(systemGroups) > 0 {
		return gerror.New("系统保留字典分组不允许删除")
	}

	// 检查是否有关联的字典项
	var dictCount int
	dictCount, err = dao.SysDict.Ctx(ctx).WhereIn("group_id", ids).Where("is_delete", packed.NO_DELETE).Count()
	if err != nil {
		return err
	}
	if dictCount > 0 {
		return gerror.New("存在关联的字典项，无法删除")
	}

	// 软删除
	_, err = dao.SysDictGroup.Ctx(ctx).WhereIn("id", ids).Update(do.SysDictGroup{
		IsDelete:  packed.IS_DELETE,
		DeletedAt: gtime.Now(),
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "删除字典分组失败:", err)
		return err
	}

	g.Log().Info(ctx, "删除字典分组成功:", "ids:", ids)
	return nil
}

// SetDictGroupStatus 设置字典分组状态
func (s *SsysDictGroup) SetDictGroupStatus(ctx context.Context, id int64, isDisable int) error {
	// 检查字典分组是否存在
	dictGroup, err := s.GetDictGroupDetail(ctx, id)
	if err != nil {
		return err
	}
	if dictGroup == nil {
		return gerror.New("字典分组不存在")
	}

	// 检查是否为系统保留
	if dictGroup.IsSystem == packed.YES_SYSTEM {
		return gerror.New("系统保留字典分组不允许修改状态")
	}

	_, err = dao.SysDictGroup.Ctx(ctx).Where("id", id).Update(do.SysDictGroup{
		IsDisable: isDisable,
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "设置字典分组状态失败:", err)
		return err
	}

	status := "启用"
	if isDisable == int(packed.DISABLE) {
		status = "禁用"
	}
	g.Log().Info(ctx, "设置字典分组状态成功:", "id:", id, "status:", status)
	return nil
}

// GetAllDictGroups 获取所有字典分组（用于下拉选择）
func (s *sSysDictGroup) GetAllDictGroups(ctx context.Context) ([]entity.SysDictGroup, error) {
	var list []entity.SysDictGroup
	err := dao.SysDictGroup.Ctx(ctx).
		Where("is_delete", packed.NO_DELETE).
		Where("is_disable", packed.DISABLE).
		OrderAsc("id").
		OrderDesc("created_at").
		Scan(&list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

// CheckDictGroupCodeExists 检查字典分组编码是否存在
func (s *SsysDictGroup) CheckDictGroupCodeExists(ctx context.Context, code string, excludeId int64) (bool, error) {
	query := dao.SysDictGroup.Ctx(ctx).Where("code", code).Where("is_delete", packed.NO_DELETE)
	if excludeId > 0 {
		query = query.WhereNot("id", excludeId)
	}

	count, err := query.Count()
	if err != nil {
		return false, err
	}

	return count > 0, nil
}
