package sysConfig

import (
	v1 "admin-server/api/sys_config/v1"
	"admin-server/internal/dao"
	"admin-server/internal/model/do"
	"admin-server/internal/model/entity"
	"admin-server/internal/packed"
	"admin-server/internal/service"
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
)

func init() {
	service.RegisterSysConfig(&SsysConfig{})
}

type SsysConfig struct {
}

func (s SsysConfig) Create(ctx context.Context, req *v1.CreateReq) (insertId int64, err error) {
	// 检查配置分组是否存在
	groupExists, err := service.SysConfigGroup().CheckGroupExists(ctx, req.GroupId)
	if err != nil {
		return 0, err
	}
	if !groupExists {
		return 0, gerror.New("配置分组不存在")
	}

	// 检查配置键名是否已存在
	exists, err := s.CheckConfigKeyExists(ctx, req.Key, 0)
	if err != nil {
		return 0, err
	}
	if exists {
		return 0, gerror.New("配置键名已存在")
	}

	// 处理JSON字段，空字符串转为nil
	var configSelectData interface{}
	if req.ConfigSelectData == "" {
		configSelectData = nil
	} else {
		configSelectData = req.ConfigSelectData
	}

	insertId, err = dao.SysConfig.Ctx(ctx).Data(do.SysConfig{
		GroupId:          req.GroupId,
		Key:              req.Key,
		Value:            req.Value,
		Name:             req.Name,
		Sort:             req.Sort,
		InputType:        req.InputType,
		ConfigSelectData: configSelectData,
		IsSystem:         req.IsSystem,
	}).InsertAndGetId()

	return insertId, err
}

func (s SsysConfig) Update(ctx context.Context, req *v1.UpdateReq) (err error) {
	// 检查配置项是否存在
	exists, err := s.CheckConfigExists(ctx, req.ID)
	if err != nil {
		return err
	}
	if !exists {
		return gerror.New("配置项不存在")
	}

	// 检查配置分组是否存在
	groupExists, err := service.SysConfigGroup().CheckGroupExists(ctx, req.GroupId)
	if err != nil {
		return err
	}
	if !groupExists {
		return gerror.New("配置分组不存在")
	}

	// 检查配置键名是否被其他配置项使用
	keyExists, err := s.CheckConfigKeyExists(ctx, req.Key, req.ID)
	if err != nil {
		return err
	}
	if keyExists {
		return gerror.New("配置键名已被使用")
	}

	// 处理JSON字段，空字符串转为nil
	var configSelectData interface{}
	if req.ConfigSelectData == "" {
		configSelectData = nil
	} else {
		configSelectData = req.ConfigSelectData
	}

	// 更新配置项信息
	_, err = dao.SysConfig.Ctx(ctx).Where("id", req.ID).Data(do.SysConfig{
		GroupId:          req.GroupId,
		Key:              req.Key,
		Value:            req.Value,
		Name:             req.Name,
		Sort:             req.Sort,
		InputType:        req.InputType,
		ConfigSelectData: configSelectData,
		IsSystem:         req.IsSystem,
	}).Update()

	return err
}

func (s SsysConfig) GetList(ctx context.Context, req *v1.GetListReq) (list []*v1.ConfigInfo, total int, err error) {
	// 构建查询条件
	m := dao.SysConfig.Ctx(ctx).Where("is_delete", packed.NO_DELETE)

	// 添加搜索条件
	if req.GroupId > 0 {
		m = m.Where("group_id", req.GroupId)
	}
	if req.Key != "" {
		m = m.WhereLike("key", "%"+req.Key+"%")
	}
	if req.Name != "" {
		m = m.WhereLike("name", "%"+req.Name+"%")
	}
	if req.InputType != "" {
		m = m.Where("input_type", req.InputType)
	}
	if req.IsSystem != nil {
		m = m.Where("is_system", *req.IsSystem)
	}

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 查询配置项数据
	var configs []entity.SysConfig
	err = m.OrderAsc("sort").OrderAsc("id").Scan(&configs)
	if err != nil {
		return nil, 0, err
	}

	// 转换为ConfigInfo格式并获取分组名称
	list = make([]*v1.ConfigInfo, len(configs))
	for i, config := range configs {
		configInfo := s.entityToConfigInfo(&config)

		// 获取分组名称
		groupName, err := dao.SysConfigGroup.Ctx(ctx).Where("id", config.GroupId).Where("is_delete", packed.NO_DELETE).Value("name")
		if err == nil && groupName != nil {
			configInfo.GroupName = groupName.String()
		}

		list[i] = configInfo
	}

	return list, total, nil
}

func (s SsysConfig) GetOne(ctx context.Context, id int64) (config *v1.ConfigInfo, err error) {
	var sysConfig entity.SysConfig
	err = dao.SysConfig.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Scan(&sysConfig)
	if err != nil {
		return nil, err
	}

	if sysConfig.Id == 0 {
		return nil, gerror.New("配置项不存在")
	}

	config = s.entityToConfigInfo(&sysConfig)

	// 获取分组名称
	groupName, err := dao.SysConfigGroup.Ctx(ctx).Where("id", sysConfig.GroupId).Where("is_delete", packed.NO_DELETE).Value("name")
	if err == nil && groupName != nil {
		config.GroupName = groupName.String()
	}

	return config, nil
}

func (s SsysConfig) Delete(ctx context.Context, id int64) (err error) {
	// 检查配置项是否存在
	var config entity.SysConfig
	err = dao.SysConfig.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Scan(&config)
	if err != nil {
		return err
	}
	if config.Id == 0 {
		return gerror.New("配置项不存在")
	}

	// 检查是否为系统保留配置项
	if config.IsSystem == int(packed.YES_SYSTEM) {
		return gerror.New("系统保留配置项不可删除")
	}

	// 软删除配置项
	_, err = dao.SysConfig.Ctx(ctx).Where("id", id).Data(do.SysConfig{
		IsDelete: packed.IS_DELETE,
	}).Update()

	return err
}

func (s SsysConfig) GetByGroup(ctx context.Context, groupId int64) (list []*v1.ConfigInfo, err error) {
	// 检查配置分组是否存在
	groupExists, err := service.SysConfigGroup().CheckGroupExists(ctx, groupId)
	if err != nil {
		return nil, err
	}
	if !groupExists {
		return nil, gerror.New("配置分组不存在")
	}

	var configs []entity.SysConfig
	err = dao.SysConfig.Ctx(ctx).
		Where("group_id", groupId).
		Where("is_delete", packed.NO_DELETE).
		OrderAsc("sort").
		OrderAsc("id").
		Scan(&configs)
	if err != nil {
		return nil, err
	}

	// 获取分组名称
	groupName, err := dao.SysConfigGroup.Ctx(ctx).Where("id", groupId).Where("is_delete", packed.NO_DELETE).Value("name")
	var groupNameStr string
	if err == nil && groupName != nil {
		groupNameStr = groupName.String()
	}

	// 转换为ConfigInfo格式
	list = make([]*v1.ConfigInfo, len(configs))
	for i, config := range configs {
		configInfo := s.entityToConfigInfo(&config)
		configInfo.GroupName = groupNameStr
		list[i] = configInfo
	}

	return list, nil
}

func (s SsysConfig) UpdateValue(ctx context.Context, id int64, value string) (err error) {
	// 检查配置项是否存在
	exists, err := s.CheckConfigExists(ctx, id)
	if err != nil {
		return err
	}
	if !exists {
		return gerror.New("配置项不存在")
	}

	// 更新配置项值
	_, err = dao.SysConfig.Ctx(ctx).Where("id", id).Data(do.SysConfig{
		Value: value,
	}).Update()

	return err
}

func (s SsysConfig) GetByKey(ctx context.Context, key string) (config *v1.ConfigInfo, err error) {
	var sysConfig entity.SysConfig
	err = dao.SysConfig.Ctx(ctx).Where("key", key).Where("is_delete", packed.NO_DELETE).Scan(&sysConfig)
	if err != nil {
		return nil, err
	}

	if sysConfig.Id == 0 {
		return nil, gerror.New("配置项不存在")
	}

	config = s.entityToConfigInfo(&sysConfig)

	// 获取分组名称
	groupName, err := dao.SysConfigGroup.Ctx(ctx).Where("id", sysConfig.GroupId).Where("is_delete", packed.NO_DELETE).Value("name")
	if err == nil && groupName != nil {
		config.GroupName = groupName.String()
	}

	return config, nil
}

func (s SsysConfig) CheckConfigExists(ctx context.Context, id int64) (exists bool, err error) {
	count, err := dao.SysConfig.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s SsysConfig) CheckConfigKeyExists(ctx context.Context, key string, excludeId int64) (exists bool, err error) {
	m := dao.SysConfig.Ctx(ctx).Where("key", key).Where("is_delete", packed.NO_DELETE)
	if excludeId > 0 {
		m = m.Where("id !=", excludeId)
	}

	count, err := m.Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// 辅助方法：将entity转换为ConfigInfo
func (s SsysConfig) entityToConfigInfo(config *entity.SysConfig) *v1.ConfigInfo {
	return &v1.ConfigInfo{
		ID:               config.Id,
		GroupId:          config.GroupId,
		GroupName:        "", // entity中没有GroupName字段
		Key:              config.Key,
		Value:            config.Value,
		Name:             config.Name,
		Sort:             config.Sort,
		InputType:        config.InputType,
		ConfigSelectData: config.ConfigSelectData,
		IsSystem:         packed.System(config.IsSystem),
		CreatedAt:        config.CreatedAt,
		UpdatedAt:        config.UpdatedAt,
	}
}
