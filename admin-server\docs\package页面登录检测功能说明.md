# package页面登录检测功能说明

## 功能概述

为package.html页面添加了用户登录检测功能，参考detail.html的实现，确保用户在开通会员套餐前必须先登录。

## 实现的功能

### 1. 登录状态检测
- 页面加载时检测用户是否已登录
- 根据登录状态显示不同的界面
- 未登录用户显示登录弹窗

### 2. 登录弹窗
- 美观的登录提示界面
- 会员特权展示
- 一键跳转微信登录

### 3. 支付流程控制
- 登录用户可以正常进行支付
- 未登录用户点击支付时显示登录提示
- 支付前验证城市和套餐选择

## 代码实现

### 1. 登录弹窗HTML结构
```html
<!-- 登录弹窗 -->
{{if not .is_logged_in}}
    <div id="loginModal" class="vip-modal">
        <div class="vip-modal-content">
            <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-user text-white text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-3">请先登录</h3>
            <p class="text-gray-600 text-sm mb-6 leading-relaxed">
                登录后即可开通会员套餐<br>
                享受更多专业服务
            </p>

            <!-- 登录优势 -->
            <div class="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6">
                <div class="space-y-2">
                    <div class="flex items-center text-sm text-gray-700">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>专享会员特权</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-700">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>多城市招标信息</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-700">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>个性化推荐服务</span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <button class="w-full bg-gradient-to-r from-purple-500 to-blue-600 text-white py-3 rounded-lg text-sm font-medium mb-3" onclick="goToLogin()">
                立即登录
            </button>
            <p class="text-xs text-gray-500">登录即可享受更多服务</p>
        </div>
    </div>
{{end}}
```

### 2. 登录弹窗CSS样式
```css
/* 登录弹窗样式 */
.vip-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.vip-modal-content {
    background: white;
    border-radius: 20px;
    padding: 30px 20px;
    margin: 20px;
    text-align: center;
    max-width: 320px;
    width: 100%;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from { transform: translateY(50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
```

### 3. 支付按钮控制
```html
<!-- 支付按钮添加点击事件 -->
<button class="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-3 rounded-lg text-sm font-medium flex items-center justify-center space-x-2" onclick="handlePayment()">
    <i class="fab fa-weixin text-lg"></i>
    <span>微信支付</span>
</button>
```

### 4. JavaScript功能实现
```javascript
// 微信登录
function goToLogin() {
    // 获取当前页面URL作为回调地址
    const currentURL = window.location.pathname + window.location.search;
    // 跳转到微信授权登录
    window.location.href = '/m/auth/login?callback=' + encodeURIComponent(currentURL);
    
    console.log('跳转到微信登录，回调URL:', currentURL);
}

// 处理支付按钮点击
function handlePayment() {
    {{if .is_logged_in}}
        // 用户已登录，执行支付逻辑
        console.log('用户已登录，开始支付流程');
        
        // 检查是否选择了城市和套餐
        if (selectedCities.length === 0) {
            alert('请先选择城市');
            return;
        }
        
        if (!selectedPackage) {
            alert('请先选择套餐');
            return;
        }
        
        // 获取支付详情
        const paymentDetails = getPriceDetails();
        console.log('支付详情:', paymentDetails);
        
        // TODO: 这里可以添加实际的支付逻辑
        alert(`即将支付 ¥${paymentDetails.totalPrice}\n城市：${paymentDetails.selectedCities.join('、')}\n套餐：${paymentDetails.selectedPackage}`);
        
    {{else}}
        // 用户未登录，显示登录提示
        console.log('用户未登录，显示登录弹窗');
        // 登录弹窗已经通过模板显示，这里不需要额外操作
    {{end}}
}

// 检查用户登录状态
function checkLoginStatus() {
    {{if .is_logged_in}}
        console.log('用户已登录');
        return true;
    {{else}}
        console.log('用户未登录');
        return false;
    {{end}}
}
```

## 控制器实现

### PackageView控制器方法
```go
func (c *ControllerMobile) PackageView(r *ghttp.Request) {
    // 调试：检查Session ID
    sessionId, _ := r.Session.Id()
    g.Log().Info(r.GetCtx(), "Detail页面Session ID:", sessionId)

    // 调试：直接从Session获取用户信息
    sessionUser := r.Session.MustGet("wechat_user")
    g.Log().Info(r.GetCtx(), "Session中的wechat_user:", g.Map{
        "is_nil":   sessionUser == nil || sessionUser.IsNil(),
        "raw_data": sessionUser,
    })

    // 获取当前微信用户信息
    wechatUser := middleware.GetCurrentWechatUser(r)

    // 调试日志
    g.Log().Info(r.GetCtx(), "GetCurrentWechatUser结果:", g.Map{
        "is_nil":    wechatUser == nil,
        "user_data": wechatUser,
    })

    // 构建模板数据
    templateData := g.Map{
        "title": "会员列表",
        "page":  "package",
    }

    // 如果有用户信息，添加到模板数据中
    if wechatUser != nil {
        fmt.Println("已登录")
        templateData["user"] = wechatUser
        templateData["is_logged_in"] = true
    } else {
        fmt.Println("未登录")
        templateData["is_logged_in"] = false
    }

    // 渲染详情页面
    r.Response.WriteTpl("mobile/package.html", templateData)
}
```

## 用户交互流程

### 1. 未登录用户访问流程
1. 用户访问 `/m/vip` 页面
2. 控制器检测用户未登录，设置 `is_logged_in = false`
3. 页面渲染时显示登录弹窗
4. 用户看到登录提示和会员特权介绍
5. 点击"立即登录"按钮跳转到微信授权

### 2. 已登录用户访问流程
1. 用户访问 `/m/vip` 页面
2. 控制器检测用户已登录，设置 `is_logged_in = true`
3. 页面正常显示，不显示登录弹窗
4. 用户可以正常选择城市和套餐
5. 点击支付按钮进行支付流程

### 3. 支付流程控制
1. 用户点击"微信支付"按钮
2. `handlePayment()` 函数检查登录状态
3. **已登录**：验证城市和套餐选择，执行支付逻辑
4. **未登录**：显示登录弹窗（已通过模板显示）

## 路由配置

### 相关路由
```go
// 手机端页面路由
mobileController := mobile.NewMobile()
group.GET("/vip", mobileController.PackageView)  // 会员套餐页面

// 微信授权相关路由
group.GET("/auth/login", mobileController.WechatLogin)     // 主动授权登录
group.GET("/auth/callback", mobileController.AuthCallback) // 授权回调
```

### URL说明
- 套餐页面：`/m/vip`
- 微信登录：`/m/auth/login?callback=/m/vip`
- 授权回调：`/m/auth/callback`

## 登录状态检测机制

### 1. Session检测
- 通过 `middleware.GetCurrentWechatUser(r)` 获取用户信息
- 检查Session中的 `wechat_user` 数据
- 根据用户信息存在与否判断登录状态

### 2. 模板变量传递
```go
if wechatUser != nil {
    templateData["user"] = wechatUser
    templateData["is_logged_in"] = true
} else {
    templateData["is_logged_in"] = false
}
```

### 3. 前端状态使用
```javascript
{{if .is_logged_in}}
    // 已登录用户的逻辑
{{else}}
    // 未登录用户的逻辑
{{end}}
```

## 测试验证

### 1. 未登录状态测试
1. 清除浏览器Session/Cookie
2. 访问 `http://localhost:8000/m/vip`
3. 应该看到登录弹窗
4. 点击"立即登录"应该跳转到微信授权

### 2. 已登录状态测试
1. 完成微信授权登录
2. 访问 `http://localhost:8000/m/vip`
3. 应该看到正常的套餐选择页面
4. 不应该显示登录弹窗

### 3. 支付流程测试
1. **未登录**：点击支付按钮，应该显示登录弹窗
2. **已登录但未选择城市**：提示"请先选择城市"
3. **已登录但未选择套餐**：提示"请先选择套餐"
4. **已登录且选择完整**：显示支付详情

### 4. 调试日志检查
```
[INFO] Detail页面Session ID: session_id_value
[INFO] Session中的wechat_user: {is_nil: false, raw_data: {...}}
[INFO] GetCurrentWechatUser结果: {is_nil: false, user_data: {...}}
已登录
```

## 与detail.html的对比

### 相同点
1. 使用相同的登录检测机制
2. 相同的登录弹窗样式和结构
3. 相同的微信授权流程
4. 相同的Session处理逻辑

### 不同点
1. **页面用途**：detail.html是查看详情，package.html是开通会员
2. **弹窗内容**：package.html强调会员特权和服务
3. **业务逻辑**：package.html有支付流程控制
4. **回调处理**：登录后回到当前套餐选择页面

## 安全考虑

### 1. 前端验证
- JavaScript中的登录检测只是UI控制
- 不能作为安全验证的唯一手段

### 2. 后端验证
- 实际支付接口需要在后端再次验证登录状态
- 使用Session或Token验证用户身份

### 3. 数据保护
- 敏感的支付信息不在前端处理
- 价格计算在后端进行最终验证

## 后续优化建议

### 1. 用户体验优化
- 添加登录成功后的提示
- 优化登录弹窗的动画效果
- 支持记住用户选择的城市和套餐

### 2. 功能扩展
- 添加游客模式（部分功能可用）
- 支持多种登录方式
- 添加用户信息显示

### 3. 错误处理
- 网络异常时的处理
- 登录失败的重试机制
- 支付异常的处理流程
