package zb_city

import (
	v1 "admin-server/api/zb_city/v1"
	"context"
)

type IZbCityV1 interface {
	Create(ctx context.Context, req *v1.ZbCityCreateReq) (res *v1.ZbCityCreateRes, err error)
	Update(ctx context.Context, req *v1.ZbCityUpdateReq) (res *v1.ZbCityUpdateRes, err error)
	Delete(ctx context.Context, req *v1.ZbCityDeleteReq) (res *v1.ZbCityDeleteRes, err error)
	GetOne(ctx context.Context, req *v1.ZbCityGetOneReq) (res *v1.ZbCityGetOneRes, err error)
	GetList(ctx context.Context, req *v1.ZbCityGetListReq) (res *v1.ZbCityGetListRes, err error)
	GetTree(ctx context.Context, req *v1.ZbCityGetTreeReq) (res *v1.ZbCityGetTreeRes, err error)
	UpdateSort(ctx context.Context, req *v1.ZbCityUpdateSortReq) (res *v1.ZbCityUpdateSortRes, err error)
	UpdateStatus(ctx context.Context, req *v1.ZbCityUpdateStatusReq) (res *v1.ZbCityUpdateStatusRes, err error)
}
