package test

import (
	"admin-server/internal/controller/zb_user"
	"context"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestZbUserController 测试会员控制器
func TestZbUserController(t *testing.T) {
	controller := zb_user.NewV1()

	gtest.C(t, func(t *gtest.T) {
		// 测试获取会员列表
		req := &zb_user.GetListReq{
			Page:     1,
			PageSize: 10,
		}
		
		res, err := controller.GetList(context.Background(), req)
		t.AssertNil(err)
		t.AssertNE(res, nil)
		t.AssertGE(res.Total, 0)
		t.AssertEQ(res.Page, 1)
		t.Assert<PERSON>Q(res.PageSize, 10)
	})

	gtest.C(t, func(t *gtest.T) {
		// 测试获取会员详情（假设ID为1的会员存在）
		req := &zb_user.GetDetailReq{
			Id: 1,
		}
		
		res, err := controller.GetDetail(context.Background(), req)
		// 如果会员不存在，会返回错误，这是正常的
		if err == nil {
			t.AssertNE(res, nil)
			t.AssertNE(res.User, nil)
			t.AssertEQ(res.User.Id, 1)
		}
	})

	gtest.C(t, func(t *gtest.T) {
		// 测试更新会员状态（假设ID为1的会员存在）
		req := &zb_user.UpdateStatusReq{
			Id:        1,
			IsDisable: 0,
		}
		
		res, err := controller.UpdateStatus(context.Background(), req)
		// 如果会员不存在，会返回错误，这是正常的
		if err == nil {
			t.AssertNE(res, nil)
			t.AssertContains(res.Message, "成功")
		}
	})

	gtest.C(t, func(t *gtest.T) {
		// 测试更新VIP有效期（假设ID为1的会员存在）
		req := &zb_user.UpdateVipPeriodReq{
			Id:             1,
			EffectiveStart: "2025-01-01",
			EffectiveEnd:   "2025-12-31",
		}
		
		res, err := controller.UpdateVipPeriod(context.Background(), req)
		// 如果会员不存在，会返回错误，这是正常的
		if err == nil {
			t.AssertNE(res, nil)
			t.AssertContains(res.Message, "成功")
		}
	})
}

// BenchmarkZbUserGetList 性能测试 - 获取会员列表
func BenchmarkZbUserGetList(b *testing.B) {
	controller := zb_user.NewV1()
	req := &zb_user.GetListReq{
		Page:     1,
		PageSize: 10,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = controller.GetList(context.Background(), req)
	}
}

// TestZbUserValidation 测试参数验证
func TestZbUserValidation(t *testing.T) {
	controller := zb_user.NewV1()

	gtest.C(t, func(t *gtest.T) {
		// 测试无效的页码
		req := &zb_user.GetListReq{
			Page:     0, // 无效页码
			PageSize: 10,
		}
		
		res, err := controller.GetList(context.Background(), req)
		// 应该自动修正为1
		if err == nil {
			t.AssertEQ(res.Page, 1)
		}
	})

	gtest.C(t, func(t *gtest.T) {
		// 测试无效的会员ID
		req := &zb_user.GetDetailReq{
			Id: -1, // 无效ID
		}
		
		_, err := controller.GetDetail(context.Background(), req)
		// 应该返回错误
		t.AssertNE(err, nil)
	})

	gtest.C(t, func(t *gtest.T) {
		// 测试无效的日期格式
		req := &zb_user.UpdateVipPeriodReq{
			Id:             1,
			EffectiveStart: "invalid-date", // 无效日期格式
			EffectiveEnd:   "2025-12-31",
		}
		
		_, err := controller.UpdateVipPeriod(context.Background(), req)
		// 应该返回错误
		t.AssertNE(err, nil)
	})

	gtest.C(t, func(t *gtest.T) {
		// 测试结束日期早于开始日期
		req := &zb_user.UpdateVipPeriodReq{
			Id:             1,
			EffectiveStart: "2025-12-31",
			EffectiveEnd:   "2025-01-01", // 结束日期早于开始日期
		}
		
		_, err := controller.UpdateVipPeriod(context.Background(), req)
		// 应该返回错误
		t.AssertNE(err, nil)
	})
}

// TestZbUserBatchOperations 测试批量操作
func TestZbUserBatchOperations(t *testing.T) {
	controller := zb_user.NewV1()

	gtest.C(t, func(t *gtest.T) {
		// 测试空的ID列表
		req := &zb_user.BatchDeleteReq{
			Ids: []int{}, // 空列表
		}
		
		_, err := controller.BatchDelete(context.Background(), req)
		// 应该返回错误
		t.AssertNE(err, nil)
	})

	gtest.C(t, func(t *gtest.T) {
		// 测试批量删除（假设这些ID不存在，但不应该报错）
		req := &zb_user.BatchDeleteReq{
			Ids: []int{999, 998, 997}, // 不存在的ID
		}
		
		res, err := controller.BatchDelete(context.Background(), req)
		// 即使ID不存在，批量删除也应该成功（影响行数为0）
		if err == nil {
			t.AssertNE(res, nil)
			t.AssertContains(res.Message, "成功")
		}
	})
}
