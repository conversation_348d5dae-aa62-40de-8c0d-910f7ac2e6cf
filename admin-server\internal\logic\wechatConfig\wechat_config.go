package wechatConfig

import (
	v1 "admin-server/api/wechat_config/v1"
	"admin-server/internal/dao"
	"admin-server/internal/model/do"
	"admin-server/internal/model/entity"
	"admin-server/internal/service"
	"context"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"

	"github.com/ArtisanCloud/PowerWeChat/v3/src/officialAccount"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

func init() {
	service.RegisterWechatConfig(&sWechatConfig{})
}

type sWechatConfig struct{}

// GetConfig 获取微信公众号配置
func (s *sWechatConfig) GetConfig(ctx context.Context) (res *v1.WechatConfigGetRes, err error) {
	var entity *entity.WechatConfig
	err = dao.WechatConfig.Ctx(ctx).OrderDesc("id").Scan(&entity)
	if err != nil {
		g.Log().Error(ctx, "查询微信配置失败:", err)
		return nil, gerror.New("查询微信配置失败")
	}

	config := &v1.WechatConfigInfo{}
	if entity != nil {
		if err = gconv.Struct(entity, config); err != nil {
			g.Log().Error(ctx, "数据转换失败:", err)
			return nil, gerror.New("数据转换失败")
		}
	} else {
		// 如果没有配置，返回默认值
		config = &v1.WechatConfigInfo{
			Id:               0,
			Appid:            "",
			AppSecret:        "",
			AutoReplyEnabled: 0,
			AutoReplyText:    "",
			ServerUrl:        "",
			Token:            "",
			EncodingAesKey:   "",
			EncryptMode:      "safe",
			CreatedAt:        nil,
			UpdatedAt:        nil,
		}
	}

	res = &v1.WechatConfigGetRes{
		WechatConfigInfo: config,
	}
	return res, nil
}

// SaveConfig 保存微信公众号配置
func (s *sWechatConfig) SaveConfig(ctx context.Context, req *v1.WechatConfigSaveReq) (success bool, message string, err error) {
	// 检查是否已存在配置
	var existingEntity *entity.WechatConfig
	err = dao.WechatConfig.Ctx(ctx).OrderDesc("id").Scan(&existingEntity)
	if err != nil {
		g.Log().Error(ctx, "查询现有配置失败:", err)
		return false, "查询现有配置失败", gerror.New("查询现有配置失败")
	}

	now := gtime.Now()
	configData := do.WechatConfig{
		Appid:            req.Appid,
		AppSecret:        req.AppSecret,
		AutoReplyEnabled: req.AutoReplyEnabled,
		AutoReplyText:    req.AutoReplyText,
		ServerUrl:        req.ServerUrl,
		Token:            req.Token,
		EncodingAesKey:   req.EncodingAesKey,
		EncryptMode:      req.EncryptMode,
		UpdatedAt:        now,
	}

	if existingEntity != nil {
		// 更新现有配置
		_, err = dao.WechatConfig.Ctx(ctx).Where("id", existingEntity.Id).Update(configData)
		if err != nil {
			g.Log().Error(ctx, "更新微信配置失败:", err)
			return false, "更新微信配置失败", gerror.New("更新微信配置失败")
		}
		g.Log().Info(ctx, "更新微信配置成功:", "id:", existingEntity.Id)
		return true, "微信公众号配置更新成功", nil
	} else {
		// 创建新配置
		configData.CreatedAt = now
		insertResult, err := dao.WechatConfig.Ctx(ctx).Insert(configData)
		if err != nil {
			g.Log().Error(ctx, "创建微信配置失败:", err)
			return false, "创建微信配置失败", gerror.New("创建微信配置失败")
		}

		insertId, err := insertResult.LastInsertId()
		if err != nil {
			g.Log().Error(ctx, "获取插入ID失败:", err)
			return false, "获取插入ID失败", gerror.New("获取插入ID失败")
		}

		g.Log().Info(ctx, "创建微信配置成功:", "id:", insertId)
		return true, "微信公众号配置保存成功", nil
	}
}

// GetOfficialAccount 获取微信公众号实例
func (s *sWechatConfig) GetOfficialAccount(ctx context.Context) (*officialAccount.OfficialAccount, error) {
	// 读取数据库配置得微信公众号配置
	var entity *entity.WechatConfig
	err := dao.WechatConfig.Ctx(ctx).OrderDesc("id").Scan(&entity)
	if err != nil {
		g.Log().Error(ctx, "查询微信配置失败:", err)
		return nil, gerror.New("查询微信配置失败")
	}

	// 从配置文件读取微信公众号配置
	appID := entity.Appid
	appSecret := entity.AppSecret
	Token := entity.Token
	AESKey := entity.EncodingAesKey

	if appID == "" || appSecret == "" {
		g.Log().Error(ctx, "微信公众号配置不完整: appID或appSecret为空")
		return nil, gerror.New("微信公众号配置不完整")
	}

	// 创建公众号实例
	officialAccountApp, err := officialAccount.NewOfficialAccount(&officialAccount.UserConfig{
		AppID:  appID,
		Secret: appSecret,
		Token:  Token,
		AESKey: AESKey,
		// 可选，不传默认走程序内存
		Cache: kernel.NewRedisClient(&kernel.UniversalOptions{
			Addrs:    []string{g.Cfg().MustGet(ctx, "offiaccount.redisaddr").String()},
			Password: g.Cfg().MustGet(ctx, "offiaccount.redispass").String(),
			DB:       g.Cfg().MustGet(ctx, "offiaccount.redisdb").Int(),
		}),
	})
	if err != nil {
		g.Log().Error(ctx, "创建微信公众号实例失败:", err)
		return nil, gerror.New("创建微信公众号实例失败")
	}

	return officialAccountApp, nil
}
