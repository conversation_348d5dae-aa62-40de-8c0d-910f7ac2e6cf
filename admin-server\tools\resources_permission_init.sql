-- 素材中心管理权限初始化SQL脚本

-- 添加素材中心管理菜单
INSERT INTO `sys_menu` (`id`, `pid`, `menu_type`, `menu_name`, `menu_icon`, `menu_sort`, `perms`, `paths`, `component`, `params`, `is_cache`, `is_show`, `is_delete`, `created_at`, `updated_at`) VALUES
-- 素材中心主菜单
(100, 1, 'M', '素材中心', 'folder-open', 10, '', '/system/resources', '', '', 0, 1, 0, NOW(), NOW()),

-- 资源分组管理
(101, 100, 'C', '资源分组', 'folder', 1, 'system:resourcesgroup:list', '/system/resources/group', 'system/resources/group/index', '', 1, 1, 0, NOW(), NOW()),
(102, 101, 'A', '查看分组', '', 1, 'system:resourcesgroup:list', '', '', '', 0, 0, 0, NOW(), NOW()),
(103, 101, 'A', '查看详情', '', 2, 'system:resourcesgroup:view', '', '', '', 0, 0, 0, NOW(), NOW()),
(104, 101, 'A', '新增分组', '', 3, 'system:resourcesgroup:create', '', '', '', 0, 0, 0, NOW(), NOW()),
(105, 101, 'A', '修改分组', '', 4, 'system:resourcesgroup:update', '', '', '', 0, 0, 0, NOW(), NOW()),
(106, 101, 'A', '删除分组', '', 5, 'system:resourcesgroup:delete', '', '', '', 0, 0, 0, NOW(), NOW()),

-- 资源管理
(107, 100, 'C', '资源管理', 'file-image', 2, 'system:resources:list', '/system/resources/file', 'system/resources/file/index', '', 1, 1, 0, NOW(), NOW()),
(108, 107, 'A', '查看资源', '', 1, 'system:resources:list', '', '', '', 0, 0, 0, NOW(), NOW()),
(109, 107, 'A', '查看详情', '', 2, 'system:resources:view', '', '', '', 0, 0, 0, NOW(), NOW()),
(110, 107, 'A', '新增资源', '', 3, 'system:resources:create', '', '', '', 0, 0, 0, NOW(), NOW()),
(111, 107, 'A', '修改资源', '', 4, 'system:resources:update', '', '', '', 0, 0, 0, NOW(), NOW()),
(112, 107, 'A', '删除资源', '', 5, 'system:resources:delete', '', '', '', 0, 0, 0, NOW(), NOW()),
(113, 107, 'A', '上传文件', '', 6, 'system:resources:upload', '', '', '', 0, 0, 0, NOW(), NOW());

-- 为系统管理员角色分配素材中心管理权限
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
-- 素材中心主菜单
(1, 100),
-- 资源分组管理
(1, 101), (1, 102), (1, 103), (1, 104), (1, 105), (1, 106),
-- 资源管理
(1, 107), (1, 108), (1, 109), (1, 110), (1, 111), (1, 112), (1, 113);

-- 为普通管理员角色分配查看权限
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`) VALUES
-- 素材中心主菜单
(2, 100),
-- 资源分组查看权限
(2, 101), (2, 102), (2, 103),
-- 资源查看和上传权限
(2, 107), (2, 108), (2, 109), (2, 113);

-- 权限说明：
-- 资源分组权限：
-- system:resourcesgroup:list   - 查看资源分组列表
-- system:resourcesgroup:view   - 查看资源分组详情
-- system:resourcesgroup:create - 创建资源分组
-- system:resourcesgroup:update - 更新资源分组
-- system:resourcesgroup:delete - 删除资源分组

-- 资源权限：
-- system:resources:list   - 查看资源列表
-- system:resources:view   - 查看资源详情
-- system:resources:create - 创建资源
-- system:resources:update - 更新资源
-- system:resources:delete - 删除资源
-- system:resources:upload - 上传文件

-- API接口权限映射：
-- 资源分组接口：
-- GET    /sys_resources_group/list        -> system:resourcesgroup:list
-- GET    /sys_resources_group/get_one/{id} -> system:resourcesgroup:view
-- GET    /sys_resources_group/all         -> system:resourcesgroup:list
-- POST   /sys_resources_group/create      -> system:resourcesgroup:create
-- PUT    /sys_resources_group/update/{id} -> system:resourcesgroup:update
-- DELETE /sys_resources_group/delete      -> system:resourcesgroup:delete

-- 资源接口：
-- GET    /sys_resources/list              -> system:resources:list
-- GET    /sys_resources/get_one/{id}      -> system:resources:view
-- GET    /sys_resources/group/{group_id}  -> system:resources:list
-- POST   /sys_resources/create            -> system:resources:create
-- POST   /sys_resources/upload            -> system:resources:upload
-- PUT    /sys_resources/update/{id}       -> system:resources:update
-- DELETE /sys_resources/delete            -> system:resources:delete
