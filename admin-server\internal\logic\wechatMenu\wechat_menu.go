package wechatMenu

import (
	v1 "admin-server/api/wechat_menu/v1"
	"admin-server/internal/dao"
	"admin-server/internal/model/do"
	"admin-server/internal/model/entity"
	"admin-server/internal/service"
	"context"
	"fmt"

	"github.com/ArtisanCloud/PowerWeChat/v3/src/officialAccount/menu/request"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

func init() {
	service.RegisterWechatMenu(&sWechatMenu{})
}

type sWechatMenu struct{}

// Create 创建微信菜单
func (s *sWechatMenu) Create(ctx context.Context, req *v1.WechatMenuCreateReq) (insertId int, err error) {
	// 验证菜单数据
	if err = s.ValidateMenuData(ctx, req); err != nil {
		return 0, err
	}

	// 如果不是顶级菜单，检查父菜单是否存在
	if req.Pid != 0 {
		if err = s.checkParentExists(ctx, req.Pid); err != nil {
			return 0, err
		}
	}

	// 计算菜单层级
	level := 1
	if req.Pid != 0 {
		level = 2
	}

	// 插入数据
	insertResult, err := dao.WechatMenu.Ctx(ctx).Insert(do.WechatMenu{
		Pid:       req.Pid,
		MenuName:  req.MenuName,
		MenuType:  req.MenuType,
		MenuKey:   req.MenuKey,
		MenuUrl:   req.MenuUrl,
		Appid:     req.Appid,
		Pagepath:  req.Pagepath,
		Sort:      req.Sort,
		Level:     level,
		IsDisable: 0,
		CreatedAt: gtime.Now(),
		UpdatedAt: gtime.Now(),
	})

	if err != nil {
		g.Log().Error(ctx, "创建微信菜单失败:", err)
		return 0, gerror.New("创建微信菜单失败")
	}

	insertId64, err := insertResult.LastInsertId()
	if err != nil {
		g.Log().Error(ctx, "获取插入ID失败:", err)
		return 0, gerror.New("获取插入ID失败")
	}

	insertId = int(insertId64)
	g.Log().Info(ctx, "创建微信菜单成功:", "id:", insertId)
	return insertId, nil
}

// Update 更新微信菜单
func (s *sWechatMenu) Update(ctx context.Context, req *v1.WechatMenuUpdateReq) (err error) {
	// 验证菜单数据
	if err = s.ValidateMenuData(ctx, req); err != nil {
		return err
	}

	// 检查菜单是否存在
	if err = s.checkMenuExists(ctx, req.Id); err != nil {
		return err
	}

	// 如果不是顶级菜单，检查父菜单是否存在
	if req.Pid != 0 {
		if err = s.checkParentExists(ctx, req.Pid); err != nil {
			return err
		}
	}

	// 计算菜单层级
	level := 1
	if req.Pid != 0 {
		level = 2
	}

	// 更新数据
	_, err = dao.WechatMenu.Ctx(ctx).Where("id", req.Id).Update(do.WechatMenu{
		Pid:       req.Pid,
		MenuName:  req.MenuName,
		MenuType:  req.MenuType,
		MenuKey:   req.MenuKey,
		MenuUrl:   req.MenuUrl,
		Appid:     req.Appid,
		Pagepath:  req.Pagepath,
		Sort:      req.Sort,
		Level:     level,
		UpdatedAt: gtime.Now(),
	})

	if err != nil {
		g.Log().Error(ctx, "更新微信菜单失败:", err)
		return gerror.New("更新微信菜单失败")
	}

	g.Log().Info(ctx, "更新微信菜单成功:", "id:", req.Id)
	return nil
}

// Delete 删除微信菜单
func (s *sWechatMenu) Delete(ctx context.Context, id int) (err error) {
	// 检查菜单是否存在
	if err = s.checkMenuExists(ctx, id); err != nil {
		return err
	}

	// 检查是否有子菜单
	children, err := s.GetChildren(ctx, id)
	if err != nil {
		return err
	}
	if len(children) > 0 {
		return gerror.New("该菜单下存在子菜单，无法删除")
	}

	// 删除菜单
	_, err = dao.WechatMenu.Ctx(ctx).Where("id", id).Delete()
	if err != nil {
		g.Log().Error(ctx, "删除微信菜单失败:", err)
		return gerror.New("删除微信菜单失败")
	}

	g.Log().Info(ctx, "删除微信菜单成功:", "id:", id)
	return nil
}

// GetOne 获取单个微信菜单
func (s *sWechatMenu) GetOne(ctx context.Context, id int) (menu *v1.WechatMenuInfo, err error) {
	var entity *entity.WechatMenu
	err = dao.WechatMenu.Ctx(ctx).Where("id", id).Scan(&entity)
	if err != nil {
		g.Log().Error(ctx, "查询微信菜单失败:", err)
		return nil, gerror.New("查询微信菜单失败")
	}

	if entity == nil {
		return nil, gerror.New("菜单不存在")
	}

	menu = &v1.WechatMenuInfo{}
	if err = gconv.Struct(entity, menu); err != nil {
		g.Log().Error(ctx, "数据转换失败:", err)
		return nil, gerror.New("数据转换失败")
	}

	return menu, nil
}

// GetList 获取微信菜单列表
func (s *sWechatMenu) GetList(ctx context.Context, req *v1.WechatMenuGetListReq) (list []*v1.WechatMenuInfo, total int, err error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	model := dao.WechatMenu.Ctx(ctx)

	if req.MenuName != "" {
		model = model.WhereLike("menu_name", "%"+req.MenuName+"%")
	}
	if req.MenuType != "" {
		model = model.Where("menu_type", req.MenuType)
	}
	if req.IsDisable != nil {
		model = model.Where("is_disable", *req.IsDisable)
	}
	if req.Pid != nil {
		model = model.Where("pid", *req.Pid)
	}

	// 查询总数
	total, err = model.Count()
	if err != nil {
		g.Log().Error(ctx, "查询微信菜单总数失败:", err)
		return nil, 0, gerror.New("查询微信菜单总数失败")
	}

	// 查询列表
	var entities []*entity.WechatMenu
	err = model.Order("sort ASC, id ASC").
		Limit((req.Page-1)*req.PageSize, req.PageSize).
		Scan(&entities)

	if err != nil {
		g.Log().Error(ctx, "查询微信菜单列表失败:", err)
		return nil, 0, gerror.New("查询微信菜单列表失败")
	}

	// 转换数据
	list = make([]*v1.WechatMenuInfo, 0, len(entities))
	for _, entity := range entities {
		info := &v1.WechatMenuInfo{}
		if err = gconv.Struct(entity, info); err != nil {
			g.Log().Error(ctx, "数据转换失败:", err)
			continue
		}
		list = append(list, info)
	}

	return list, total, nil
}

// GetTree 获取微信菜单树形结构
func (s *sWechatMenu) GetTree(ctx context.Context, req *v1.WechatMenuGetTreeReq) (tree []*v1.WechatMenuTreeInfo, err error) {
	// 构建查询条件
	model := dao.WechatMenu.Ctx(ctx)

	if req.IsDisable != nil {
		model = model.Where("is_disable", *req.IsDisable)
	}

	// 查询所有菜单
	var entities []*entity.WechatMenu
	err = model.Order("sort ASC, id ASC").Scan(&entities)
	if err != nil {
		g.Log().Error(ctx, "查询微信菜单失败:", err)
		return nil, gerror.New("查询微信菜单失败")
	}

	// 转换为菜单信息
	menus := make([]*v1.WechatMenuInfo, 0, len(entities))
	for _, entity := range entities {
		info := &v1.WechatMenuInfo{}
		if err = gconv.Struct(entity, info); err != nil {
			g.Log().Error(ctx, "数据转换失败:", err)
			continue
		}
		menus = append(menus, info)
	}

	// 构建树形结构
	tree, err = s.BuildTree(ctx, menus, 0)
	if err != nil {
		return nil, err
	}

	return tree, nil
}

// UpdateSort 更新微信菜单排序
func (s *sWechatMenu) UpdateSort(ctx context.Context, id int, sort int) error {
	// 检查菜单是否存在
	if err := s.checkMenuExists(ctx, id); err != nil {
		return err
	}

	_, err := dao.WechatMenu.Ctx(ctx).Where("id", id).Update(do.WechatMenu{
		Sort:      sort,
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "更新微信菜单排序失败:", err)
		return gerror.New("更新微信菜单排序失败")
	}

	g.Log().Info(ctx, "更新微信菜单排序成功:", "id:", id, "sort:", sort)
	return nil
}

// UpdateStatus 更新微信菜单状态
func (s *sWechatMenu) UpdateStatus(ctx context.Context, id int, isDisable int) error {
	// 检查菜单是否存在
	if err := s.checkMenuExists(ctx, id); err != nil {
		return err
	}

	_, err := dao.WechatMenu.Ctx(ctx).Where("id", id).Update(do.WechatMenu{
		IsDisable: isDisable,
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "更新微信菜单状态失败:", err)
		return gerror.New("更新微信菜单状态失败")
	}

	g.Log().Info(ctx, "更新微信菜单状态成功:", "id:", id, "isDisable:", isDisable)
	return nil
}

// BuildTree 构建微信菜单树形结构
func (s *sWechatMenu) BuildTree(ctx context.Context, menus []*v1.WechatMenuInfo, pid int) (tree []*v1.WechatMenuTreeInfo, err error) {
	tree = make([]*v1.WechatMenuTreeInfo, 0)

	for _, menu := range menus {
		if menu.Pid == pid {
			treeNode := &v1.WechatMenuTreeInfo{
				WechatMenuInfo: menu,
			}

			// 递归查找子菜单
			children, err := s.BuildTree(ctx, menus, menu.Id)
			if err != nil {
				return nil, err
			}
			if len(children) > 0 {
				treeNode.Children = children
			}

			tree = append(tree, treeNode)
		}
	}

	return tree, nil
}

// GetChildren 获取子菜单
func (s *sWechatMenu) GetChildren(ctx context.Context, pid int) (children []*v1.WechatMenuInfo, err error) {
	var entities []*entity.WechatMenu
	err = dao.WechatMenu.Ctx(ctx).Where("pid", pid).Scan(&entities)
	if err != nil {
		g.Log().Error(ctx, "查询子菜单失败:", err)
		return nil, gerror.New("查询子菜单失败")
	}

	children = make([]*v1.WechatMenuInfo, 0, len(entities))
	for _, entity := range entities {
		info := &v1.WechatMenuInfo{}
		if err = gconv.Struct(entity, info); err != nil {
			g.Log().Error(ctx, "数据转换失败:", err)
			continue
		}
		children = append(children, info)
	}

	return children, nil
}

// ValidateMenuData 验证菜单数据
func (s *sWechatMenu) ValidateMenuData(ctx context.Context, req interface{}) error {
	switch r := req.(type) {
	case *v1.WechatMenuCreateReq:
		return s.validateMenuFields(ctx, r.MenuType, r.MenuKey, r.MenuUrl, r.Appid, r.Pagepath)
	case *v1.WechatMenuUpdateReq:
		return s.validateMenuFields(ctx, r.MenuType, r.MenuKey, r.MenuUrl, r.Appid, r.Pagepath)
	default:
		return gerror.New("不支持的请求类型")
	}
}

// validateMenuFields 验证菜单字段
func (s *sWechatMenu) validateMenuFields(ctx context.Context, menuType, menuKey, menuUrl, appid, pagepath string) error {
	switch menuType {
	case "click":
		if menuKey == "" {
			return gerror.New("click类型菜单必须填写菜单KEY值")
		}
	case "view":
		if menuUrl == "" {
			return gerror.New("view类型菜单必须填写菜单链接")
		}
	case "miniprogram":
		if appid == "" {
			return gerror.New("miniprogram类型菜单必须填写小程序AppID")
		}
		if pagepath == "" {
			return gerror.New("miniprogram类型菜单必须填写小程序页面路径")
		}
	default:
		return gerror.New("不支持的菜单类型")
	}
	return nil
}

// checkMenuExists 检查菜单是否存在
func (s *sWechatMenu) checkMenuExists(ctx context.Context, id int) error {
	count, err := dao.WechatMenu.Ctx(ctx).Where("id", id).Count()
	if err != nil {
		g.Log().Error(ctx, "检查菜单是否存在失败:", err)
		return gerror.New("检查菜单是否存在失败")
	}
	if count == 0 {
		return gerror.New("菜单不存在")
	}
	return nil
}

// checkParentExists 检查父菜单是否存在
func (s *sWechatMenu) checkParentExists(ctx context.Context, pid int) error {
	count, err := dao.WechatMenu.Ctx(ctx).Where("id", pid).Count()
	if err != nil {
		g.Log().Error(ctx, "检查父菜单是否存在失败:", err)
		return gerror.New("检查父菜单是否存在失败")
	}
	if count == 0 {
		return gerror.New(fmt.Sprintf("父菜单(ID:%d)不存在", pid))
	}
	return nil
}

// Publish 发布微信菜单到微信服务器
func (s *sWechatMenu) Publish(ctx context.Context) (success bool, message string, err error) {
	// 获取所有启用的菜单
	var entities []*entity.WechatMenu
	err = dao.WechatMenu.Ctx(ctx).Where("is_disable", 0).Order("sort ASC, id ASC").Scan(&entities)
	if err != nil {
		g.Log().Error(ctx, "查询微信菜单失败:", err)
		return false, "查询菜单数据失败", gerror.New("查询菜单数据失败")
	}

	if len(entities) == 0 {
		return false, "没有可发布的菜单", gerror.New("没有可发布的菜单")
	}

	// 转换为菜单信息
	menus := make([]*v1.WechatMenuInfo, 0, len(entities))
	for _, entity := range entities {
		info := &v1.WechatMenuInfo{}
		if err = gconv.Struct(entity, info); err != nil {
			g.Log().Error(ctx, "数据转换失败:", err)
			continue
		}
		menus = append(menus, info)
	}

	// 转换为PowerWeChat格式
	buttons, err := s.convertToWeChatMenuFormat(ctx, menus)
	if err != nil {
		g.Log().Error(ctx, "菜单格式转换失败:", err)
		return false, "菜单格式转换失败", err
	}

	// 获取微信公众号实例
	officialAccountApp, err := service.WechatConfig().GetOfficialAccount(ctx)
	if err != nil {
		g.Log().Error(ctx, "获取微信公众号实例失败:", err)
		return false, "微信配置错误", err
	}

	// 调用微信API创建菜单
	response, err := officialAccountApp.Menu.Create(ctx, buttons)
	if err != nil {
		g.Log().Error(ctx, "调用微信API创建菜单失败:", err)
		return false, "发布菜单到微信服务器失败: " + err.Error(), err
	}

	g.Log().Info(ctx, "微信菜单发布成功:", response)
	return true, "菜单发布成功", nil
}

// convertToWeChatMenuFormat 将数据库菜单数据转换为微信菜单格式
func (s *sWechatMenu) convertToWeChatMenuFormat(ctx context.Context, menus []*v1.WechatMenuInfo) ([]*request.Button, error) {
	// 先构建树形结构
	tree, err := s.BuildTree(ctx, menus, 0)
	if err != nil {
		return nil, err
	}

	buttons := make([]*request.Button, 0, len(tree))
	for _, treeNode := range tree {
		button := &request.Button{
			Name: treeNode.MenuName,
		}

		// 如果有子菜单，设置子菜单
		if len(treeNode.Children) > 0 {
			subButtons := make([]request.SubButton, 0, len(treeNode.Children))
			for _, child := range treeNode.Children {
				subButton := request.SubButton{
					Name: child.MenuName,
				}

				// 根据菜单类型设置相应字段
				switch child.MenuType {
				case "click":
					subButton.Type = "click"
					subButton.Key = child.MenuKey
				case "view":
					subButton.Type = "view"
					subButton.URL = child.MenuUrl
				case "miniprogram":
					subButton.Type = "miniprogram"
					subButton.URL = child.MenuUrl
					subButton.AppID = child.Appid
					subButton.PagePath = child.Pagepath
				}

				subButtons = append(subButtons, subButton)
			}
			button.SubButtons = subButtons
		} else {
			// 一级菜单，根据类型设置相应字段
			switch treeNode.MenuType {
			case "click":
				button.Type = "click"
				button.Key = treeNode.MenuKey
			case "view":
				button.Type = "view"
				button.URL = treeNode.MenuUrl
			case "miniprogram":
				button.Type = "miniprogram"
				button.URL = treeNode.MenuUrl
				button.AppID = treeNode.Appid
				button.PagePath = treeNode.Pagepath
			}
		}

		buttons = append(buttons, button)
	}

	return buttons, nil
}
