// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SysOperateLogDao is the data access object for the table sys_operate_log.
type SysOperateLogDao struct {
	table    string               // table is the underlying table name of the DAO.
	group    string               // group is the database configuration group name of the current DAO.
	columns  SysOperateLogColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler   // handlers for customized model modification.
}

// SysOperateLogColumns defines and stores column names for the table sys_operate_log.
type SysOperateLogColumns struct {
	Id        string //
	RequestId string // 请求id
	AdminId   string // 操作人id
	Username  string // 操作人账号
	Method    string // 请求类型: GET/POST/PUT/DELETE
	Title     string // 操作标题
	Ip        string // 请求ip
	ReqHeader string // 请求头
	ReqBody   string // 请求体
	ResHeader string // 响应头
	ResBody   string // 响应体
	Status    string // 执行状态: 1=成功, 2=失败
	StartTime string // 开始时间，时间戳
	EndTime   string // 结束时间，时间戳
	TaskTime  string // 执行耗时
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
}

// sysOperateLogColumns holds the columns for the table sys_operate_log.
var sysOperateLogColumns = SysOperateLogColumns{
	Id:        "id",
	RequestId: "request_id",
	AdminId:   "admin_id",
	Username:  "username",
	Method:    "method",
	Title:     "title",
	Ip:        "ip",
	ReqHeader: "req_header",
	ReqBody:   "req_body",
	ResHeader: "res_header",
	ResBody:   "res_body",
	Status:    "status",
	StartTime: "start_time",
	EndTime:   "end_time",
	TaskTime:  "task_time",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// NewSysOperateLogDao creates and returns a new DAO object for table data access.
func NewSysOperateLogDao(handlers ...gdb.ModelHandler) *SysOperateLogDao {
	return &SysOperateLogDao{
		group:    "default",
		table:    "sys_operate_log",
		columns:  sysOperateLogColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SysOperateLogDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SysOperateLogDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SysOperateLogDao) Columns() SysOperateLogColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SysOperateLogDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SysOperateLogDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SysOperateLogDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
