# Session调试说明

## 问题描述

`GetCurrentWechatUser` 一直返回未登录状态，即使用户已经完成了微信授权。

## 可能的原因

1. **Session存储失败**：用户信息没有正确存储到Session中
2. **Session获取失败**：从Session中获取用户信息时出现问题
3. **Session配置问题**：Session的存储方式或配置有问题
4. **重定向问题**：在重定向过程中Session丢失
5. **State参数问题**：微信回调时state参数处理不正确

## 已添加的调试功能

### 1. AuthCallback中的调试日志
```go
// 用户信息已存储到Session
g.Log().Info(ctx, "用户信息已存储到Session:", g.Map{
    "user_id":  userInfo["id"],
    "nickname": userInfo["nickname"],
    "openid":   userInfo["openid"],
})

// 验证Session是否正确存储
testUser := r.Session.MustGet("wechat_user")
if testUser != nil && !testUser.IsNil() {
    g.<PERSON>().Info(ctx, "Session存储验证成功")
} else {
    g.Log().Error(ctx, "Session存储验证失败")
}
```

### 2. Detail方法中的调试日志
```go
// 检查Session ID
sessionId, _ := r.Session.Id()
g.Log().Info(r.GetCtx(), "Detail页面Session ID:", sessionId)

// 直接从Session获取用户信息
sessionUser := r.Session.MustGet("wechat_user")
g.Log().Info(r.GetCtx(), "Session中的wechat_user:", g.Map{
    "is_nil":    sessionUser == nil || sessionUser.IsNil(),
    "raw_data":  sessionUser,
})

// GetCurrentWechatUser结果
wechatUser := middleware.GetCurrentWechatUser(r)
g.Log().Info(r.GetCtx(), "GetCurrentWechatUser结果:", g.Map{
    "is_nil":     wechatUser == nil,
    "user_data":  wechatUser,
})
```

### 3. 测试Session功能
添加了测试路由 `/m/test/session` 来验证Session基本功能：

```go
func (c *ControllerMobile) TestSession(r *ghttp.Request) {
    // 测试设置Session
    r.Session.Set("test_key", "test_value")
    
    // 测试获取Session
    testValue := r.Session.MustGet("test_key")
    
    // 返回测试结果
    r.Response.WriteJson(g.Map{
        "code":       0,
        "message":    "Session测试完成",
        "session_id": sessionId,
        "test_value": testValue,
    })
}
```

## 修复的问题

### 1. State参数传递问题
**问题**：在微信授权URL中使用固定的"STATE"，导致无法正确回调到原始页面

**修复前**：
```go
params := map[string]string{
    "state": "STATE", // 固定值
}
```

**修复后**：
```go
// WechatLogin中传递原始回调URL作为state
authURL, err := buildWechatAuthURL(wechatCallbackURL, callbackURL)

// buildWechatAuthURL中使用传递的state
params := map[string]string{
    "state": state, // 使用传递的回调URL
}
```

### 2. 回调URL处理
**修复前**：直接将用户访问的URL作为微信回调URL
**修复后**：固定使用 `/m/auth/callback` 作为微信回调URL，原始URL作为state参数

## 调试步骤

### 1. 测试Session基本功能
访问：`http://localhost:8000/m/test/session`

检查返回结果：
- `session_id` 是否有值
- `test_value` 是否正确返回

### 2. 测试授权流程
1. 清除浏览器Session/Cookie
2. 访问：`http://localhost:8000/m/detail`
3. 点击微信授权登录
4. 查看控制台日志

### 3. 检查日志输出
在授权回调时查看以下日志：

```
用户信息已存储到Session: {user_id: 1, nickname: "用户名", openid: "openid"}
Session存储验证成功
准备重定向到: /m/detail
```

在Detail页面查看以下日志：

```
Detail页面Session ID: session_id_value
Session中的wechat_user: {is_nil: false, raw_data: {...}}
GetCurrentWechatUser结果: {is_nil: false, user_data: {...}}
```

## 可能的解决方案

### 1. 如果Session基本功能不工作
检查GoFrame的Session配置：

```yaml
# config.yaml
server:
  sessionMaxAge: "24h"
  sessionStorage: "file"  # 或 "memory", "redis"
```

### 2. 如果Session在重定向后丢失
可能是Cookie域名或路径问题：

```go
// 检查Cookie设置
r.Cookie.SetCookie("test", "value", "/", "localhost")
```

### 3. 如果数据类型转换问题
检查存储到Session的数据类型：

```go
// 确保存储的是可序列化的数据
userInfo := map[string]interface{}{
    "id":       user.Id,
    "nickname": user.Nickname,
    // ...
}
r.Session.Set("wechat_user", userInfo)
```

## 排查清单

- [ ] Session基本功能是否正常（访问 `/m/test/session`）
- [ ] AuthCallback中是否正确存储用户信息
- [ ] Session存储验证是否成功
- [ ] Detail页面是否能获取到Session ID
- [ ] Session中是否有 `wechat_user` 数据
- [ ] `GetCurrentWechatUser` 是否正确解析数据
- [ ] 重定向前后Session ID是否一致
- [ ] 浏览器Cookie是否正确设置

## 临时解决方案

如果Session问题难以解决，可以临时使用其他方式：

### 1. 使用URL参数传递
```go
// 在回调中将用户信息作为URL参数
redirectURL := fmt.Sprintf("/m/detail?user_id=%d&nickname=%s", userID, nickname)
r.Response.RedirectTo(redirectURL)
```

### 2. 使用数据库临时存储
```go
// 生成临时token
token := generateRandomToken()
// 存储到数据库
saveUserToken(userID, token)
// 重定向时携带token
redirectURL := fmt.Sprintf("/m/detail?token=%s", token)
```

## 下一步调试

1. 启动服务并访问 `/m/test/session` 测试Session基本功能
2. 查看授权流程的完整日志输出
3. 对比AuthCallback和Detail中的Session ID是否一致
4. 如果Session功能正常但数据丢失，检查数据序列化问题
5. 如果Session功能异常，检查GoFrame配置和环境
