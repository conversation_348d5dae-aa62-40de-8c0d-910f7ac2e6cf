package service

import (
	v1 "admin-server/api/wechat_menu/v1"
	"context"
)

// 1.定义接口
type IWechatMenu interface {
	Create(ctx context.Context, req *v1.WechatMenuCreateReq) (insertId int, err error)
	Update(ctx context.Context, req *v1.WechatMenuUpdateReq) (err error)
	Delete(ctx context.Context, id int) (err error)
	GetOne(ctx context.Context, id int) (menu *v1.WechatMenuInfo, err error)
	GetList(ctx context.Context, req *v1.WechatMenuGetListReq) (list []*v1.WechatMenuInfo, total int, err error)
	GetTree(ctx context.Context, req *v1.WechatMenuGetTreeReq) (tree []*v1.WechatMenuTreeInfo, err error)
	UpdateSort(ctx context.Context, id int, sort int) error
	UpdateStatus(ctx context.Context, id int, isDisable int) error
	BuildTree(ctx context.Context, menus []*v1.WechatMenuInfo, pid int) (tree []*v1.WechatMenuTreeInfo, err error)
	GetChildren(ctx context.Context, pid int) (children []*v1.WechatMenuInfo, err error)
	ValidateMenuData(ctx context.Context, req interface{}) error
	Publish(ctx context.Context) (success bool, message string, err error)
}

// 2.定义接口变量
var localWechatMenu IWechatMenu

// 3.定义一个获取接口实例的函数
func WechatMenu() IWechatMenu {
	if localWechatMenu == nil {
		panic("IWechatMenu接口未实现或未注册")
	}
	return localWechatMenu
}

// 4.定义一个接口实现的注册方法
func RegisterWechatMenu(i IWechatMenu) {
	localWechatMenu = i
}
