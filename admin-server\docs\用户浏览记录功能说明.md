# 用户浏览记录功能说明

## 📋 功能概述

实现用户浏览记录功能，当用户访问 `/m/detail` 文章详情页面时，如果用户已登录，则自动创建或更新浏览记录。同时提供我的浏览记录API接口供用户查询。

## 🔧 功能实现

### 1. 数据库表结构
```sql
CREATE TABLE `zb_user_browser` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int NOT NULL COMMENT '会员id',
  `article_id` int NOT NULL COMMENT '招标信息id',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_article_id` (`article_id`),
  UNIQUE KEY `uk_user_article` (`user_id`,`article_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户浏览记录表';
```

### 2. 核心功能

#### 浏览记录创建逻辑
```go
func CreateBrowserRecord(ctx context.Context, userId int64, articleId int64) error {
    // 检查是否已存在相同的浏览记录
    count, err := dao.ZbUserBrowser.Ctx(ctx).
        Where("user_id", userId).
        Where("article_id", articleId).
        Count()
    
    if count > 0 {
        // 如果已存在，更新浏览时间
        _, err = dao.ZbUserBrowser.Ctx(ctx).
            Where("user_id", userId).
            Where("article_id", articleId).
            Data(g.Map{"updated_at": "NOW()"}).Update()
    } else {
        // 如果不存在，创建新记录
        _, err = dao.ZbUserBrowser.Ctx(ctx).Data(g.Map{
            "user_id":    userId,
            "article_id": articleId,
        }).Insert()
    }
    
    return err
}
```

#### 文章详情页面集成
```go
// 在 /m/detail 控制器中添加
if wechatUser != nil {
    // 用户已登录，获取用户信息
    userInfo, err := service.ZbUser().GetUserByOpenid(ctx, openid)
    if err == nil {
        // 创建用户浏览记录
        err = service.ZbUserBrowser().CreateBrowserRecord(ctx, userInfo.Id, articleId)
        if err != nil {
            g.Log().Error(ctx, "创建浏览记录失败:", err)
        }
    }
}
```

### 3. API接口

#### 我的浏览记录列表
- **URL**: `GET /m/api/zb_user_browser/my-list`
- **参数**: 
  - `openid`: 用户OpenID (必填)
  - `page`: 页码 (默认1)
  - `page_size`: 每页数量 (默认10，最大50)

#### 请求示例
```bash
curl "http://localhost:8000/m/api/zb_user_browser/my-list?openid=oXXXXXXXXXXXXXXXXXXXXXXXXXXXX&page=1&page_size=10"
```

#### 响应示例
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "article_id": 123,
                "article_title": "某某项目招标公告",
                "category_name": "工程建设",
                "city_name": "北京市",
                "browse_time": "2025-01-23T15:30:00Z"
            },
            {
                "id": 2,
                "article_id": 124,
                "article_title": "设备采购招标公告",
                "category_name": "设备采购",
                "city_name": "上海市",
                "browse_time": "2025-01-23T14:20:00Z"
            }
        ],
        "total": 2
    }
}
```

## 📊 数据关联

### 关联查询逻辑
```sql
SELECT 
    zb_user_browser.*,
    a.title as article_title,
    c.name as category_name,
    city.name as city_name
FROM zb_user_browser
LEFT JOIN zb_article a ON zb_user_browser.article_id = a.id
LEFT JOIN zb_cate c ON a.cate_id = c.id
LEFT JOIN zb_city city ON a.city_id = city.id
WHERE zb_user_browser.user_id = ?
ORDER BY zb_user_browser.updated_at DESC
LIMIT 10 OFFSET 0;
```

### 响应字段说明
| 字段 | 类型 | 说明 | 数据来源 |
|------|------|------|----------|
| `id` | int64 | 浏览记录ID | zb_user_browser.id |
| `article_id` | int64 | 文章ID | zb_user_browser.article_id |
| `article_title` | string | 文章标题 | zb_article.title |
| `category_name` | string | 文章分类名称 | zb_cate.name |
| `city_name` | string | 文章城市名称 | zb_city.name |
| `browse_time` | datetime | 浏览时间 | zb_user_browser.updated_at |

## 🎯 业务特性

### 1. 去重机制
- 同一用户浏览同一文章只保存一条记录
- 重复浏览时更新 `updated_at` 时间
- 使用唯一索引 `uk_user_article(user_id, article_id)` 保证数据唯一性

### 2. 时间记录
- `created_at`: 首次浏览时间
- `updated_at`: 最后浏览时间
- API返回 `updated_at` 作为浏览时间

### 3. 用户验证
- 只有登录用户才会创建浏览记录
- 通过OpenID获取用户信息
- 支持微信用户和普通用户

## 🧪 测试用例

### 测试用例1：首次浏览文章
```bash
# 1. 用户登录后访问文章详情
GET /m/detail?id=123

# 2. 验证浏览记录创建
SELECT * FROM zb_user_browser WHERE user_id = 1 AND article_id = 123;

# 3. 验证记录存在且created_at = updated_at
```

### 测试用例2：重复浏览文章
```bash
# 1. 用户再次访问同一文章
GET /m/detail?id=123

# 2. 验证浏览记录更新
SELECT * FROM zb_user_browser WHERE user_id = 1 AND article_id = 123;

# 3. 验证updated_at时间更新，created_at不变
```

### 测试用例3：查询浏览记录
```bash
# 1. 调用浏览记录API
GET /m/api/zb_user_browser/my-list?openid=test_openid&page=1&page_size=10

# 2. 验证返回数据包含所需字段
# 3. 验证分页功能正常
```

## ⚠️ 注意事项

### 1. 性能考虑
- 使用唯一索引避免重复数据
- 建议在 `user_id` 和 `article_id` 上建立索引
- 大数据量时考虑分页查询优化

### 2. 数据清理
- 可以定期清理过期的浏览记录
- 建议保留最近6个月的浏览记录

### 3. 隐私保护
- 用户只能查看自己的浏览记录
- 通过OpenID验证用户身份
- 不暴露其他用户的浏览信息

## 📈 扩展功能

### 1. 浏览统计
```sql
-- 统计用户浏览最多的分类
SELECT c.name, COUNT(*) as browse_count
FROM zb_user_browser ub
JOIN zb_article a ON ub.article_id = a.id
JOIN zb_cate c ON a.cate_id = c.id
WHERE ub.user_id = ?
GROUP BY c.id
ORDER BY browse_count DESC;
```

### 2. 推荐系统
基于浏览记录实现个性化推荐：
```go
func GetRecommendArticles(ctx context.Context, userId int64) ([]Article, error) {
    // 根据用户浏览的分类和城市推荐相关文章
    // 排除已浏览的文章
    // 按相关度排序
}
```

### 3. 浏览热度
```sql
-- 统计文章浏览热度
SELECT article_id, COUNT(*) as browse_count
FROM zb_user_browser
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY article_id
ORDER BY browse_count DESC;
```

---

**开发状态**: ✅ 已完成  
**功能特性**: 自动记录、去重机制、分页查询  
**安全级别**: 高（用户数据隔离）  
**性能优化**: 索引优化、分页查询  
**文档版本**: v1.0  
**完成时间**: 2025-01-23
