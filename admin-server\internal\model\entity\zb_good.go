// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ZbGood is the golang structure for table zb_good.
type ZbGood struct {
	Id            int         `json:"id"            orm:"id"             description:""`               //
	Name          string      `json:"name"          orm:"name"           description:"名称"`             // 名称
	Tag           string      `json:"tag"           orm:"tag"            description:"标签"`             // 标签
	OriginalPrice float64     `json:"originalPrice" orm:"original_price" description:"原始价格"`           // 原始价格
	Price         float64     `json:"price"         orm:"price"          description:"现时价格"`           // 现时价格
	Effective     int         `json:"effective"     orm:"effective"      description:"会员有效期；单位月"`      // 会员有效期；单位月
	IsDisable     int         `json:"isDisable"     orm:"is_disable"     description:"是否禁用: 0=否, 1=是"` // 是否禁用: 0=否, 1=是
	IsDelete      int         `json:"isDelete"      orm:"is_delete"      description:"是否删除: 0=否, 1=是"` // 是否删除: 0=否, 1=是
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"     description:"创建时间"`           // 创建时间
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"     description:"更新时间"`           // 更新时间
	DeletedAt     *gtime.Time `json:"deletedAt"     orm:"deleted_at"     description:"删除时间"`           // 删除时间
}
