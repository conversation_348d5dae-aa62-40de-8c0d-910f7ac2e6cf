package mobile

import (
	"admin-server/internal/middleware"
	"admin-server/internal/model/entity"
	"admin-server/internal/service"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gctx"
)

type ControllerMobile struct{}

func NewMobile() *ControllerMobile {
	return &ControllerMobile{}
}

// List 手机端列表页面
func (c *ControllerMobile) List(r *ghttp.Request) {
	// 构建模板数据
	templateData := g.Map{
		"title": "招标列表",
		"page":  "list",
	}

	if r.Get("tableIndex") != nil {
		// 点击了指定类别列表
		templateData["tableIndex"] = r.Get("tableIndex").String()
	} else {
		templateData["tableIndex"] = "0"
	}

	// 渲染列表页面
	r.Response.WriteTpl("mobile/list.html", templateData)
}

// Detail 手机端详情页面（需要微信授权）
func (c *ControllerMobile) Detail(r *ghttp.Request) {
	// 获取ID参数
	articleId := r.Get("id").Int64()

	articleInfo, err := service.ZbArticle().GetArticleDetailWithNames(r.GetCtx(), articleId)
	if err != nil {
		r.Response.WriteTpl("mobile/error.html", g.Map{
			"title": "错误",
			"error": "获取信息详情失败，或信息已下架",
		})
		return
	}

	// 增加浏览量
	_ = service.ZbArticle().IncrementViewCount(r.GetCtx(), articleId)

	// 调试：检查Session ID
	sessionId, _ := r.Session.Id()
	g.Log().Info(r.GetCtx(), "Detail页面Session ID:", sessionId)

	// 调试：直接从Session获取用户信息
	sessionUser := r.Session.MustGet("wechat_user")
	g.Log().Info(r.GetCtx(), "Session中的wechat_user:", g.Map{
		"is_nil":   sessionUser == nil || sessionUser.IsNil(),
		"raw_data": sessionUser,
	})

	// 获取当前微信用户信息
	wechatUser := middleware.GetCurrentWechatUser(r)

	// 调试日志
	g.Log().Info(r.GetCtx(), "GetCurrentWechatUser结果:", g.Map{
		"is_nil":    wechatUser == nil,
		"user_data": wechatUser,
	})

	// 构建模板数据
	templateData := g.Map{
		"title": "招标详情",
		"page":  "detail",
		"id":    articleId,
	}

	// 构建内容数据模板
	articleContent := g.Map{
		"id":              articleInfo.Id,
		"city_name":       articleInfo.CityName,
		"cate_name":       articleInfo.CateName,
		"title":           articleInfo.Title,
		"author":          articleInfo.Author,
		"pic":             articleInfo.Pic,
		"viewCount":       articleInfo.ViewCount + 1,
		"seo_title":       articleInfo.SeoTitle,
		"seo_keywords":    articleInfo.SeoKeywords,
		"seo_description": articleInfo.SeoDescription,
		"created_at":      articleInfo.CreatedAt,
	}

	// 如果有用户信息，添加到模板数据中
	if wechatUser != nil {
		fmt.Println("已登录")
		templateData["user"] = wechatUser
		templateData["is_logged_in"] = true
		// 根据openid查询用户信息
		userInfo, err := service.ZbUser().GetUserByOpenid(r.GetCtx(), wechatUser["openid"].(string))
		if err != nil {
			templateData["is_vip"] = 0                            // 查询失败，设为无效
			articleContent["content"] = articleInfo.ShieidContent // 已登录不是vip显示屏蔽内容
			templateData["content"] = articleContent
			g.Log().Error(r.GetCtx(), "查询用户信息失败:", err)
		} else {
			// 创建用户浏览记录
			err = service.ZbUserBrowser().CreateBrowserRecord(r.GetCtx(), userInfo.Id, articleId)
			if err != nil {
				g.Log().Error(r.GetCtx(), "创建浏览记录失败:", err)
			}

			// 验证VIP有效期
			isVip := c.validateVipStatus(userInfo)
			templateData["is_vip"] = isVip
			if isVip == 1 {
				articleContent["content"] = articleInfo.FullContent // 已登录，是vip显示完整内容
				templateData["content"] = articleContent
			} else {
				articleContent["content"] = articleInfo.ShieidContent // 已登录，不是vip显示屏蔽内容
				templateData["content"] = articleContent
			}

			// 添加VIP相关信息到模板数据
			if userInfo.EffectiveStart != nil {
				templateData["vip_start"] = userInfo.EffectiveStart.Format("2006-01-02")
			}
			if userInfo.EffectiveEnd != nil {
				templateData["vip_end"] = userInfo.EffectiveEnd.Format("2006-01-02")
			}

			g.Log().Info(r.GetCtx(), "用户VIP状态验证完成:", g.Map{
				"user_id":         userInfo.Id,
				"is_vip":          isVip,
				"effective_start": userInfo.EffectiveStart,
				"effective_end":   userInfo.EffectiveEnd,
			})
		}
		g.Log().Info(r.GetCtx(), "用户已登录，传递用户信息到模板")
	} else {
		fmt.Println("未登录")
		templateData["is_logged_in"] = false
		templateData["is_vip"] = 0                            // 是否购买套餐
		articleContent["content"] = articleInfo.ShieidContent // 未登录显示屏蔽内容
		templateData["content"] = articleContent
		g.Log().Info(r.GetCtx(), "用户未登录，设置is_logged_in为false")
	}

	// 渲染详情页面
	r.Response.WriteTpl("mobile/detail.html", templateData)
}

// OrderView 订单列表页面
func (c *ControllerMobile) OrderView(r *ghttp.Request) {
	// 调试：检查Session ID
	sessionId, _ := r.Session.Id()
	g.Log().Info(r.GetCtx(), "Detail页面Session ID:", sessionId)

	// 调试：直接从Session获取用户信息
	sessionUser := r.Session.MustGet("wechat_user")
	g.Log().Info(r.GetCtx(), "Session中的wechat_user:", g.Map{
		"is_nil":   sessionUser == nil || sessionUser.IsNil(),
		"raw_data": sessionUser,
	})

	// 获取当前微信用户信息
	wechatUser := middleware.GetCurrentWechatUser(r)

	// 调试日志
	g.Log().Info(r.GetCtx(), "GetCurrentWechatUser结果:", g.Map{
		"is_nil":    wechatUser == nil,
		"user_data": wechatUser,
	})

	// 构建模板数据
	templateData := g.Map{
		"title": "订单列表",
		"page":  "order",
	}

	// 如果有用户信息，添加到模板数据中
	if wechatUser != nil {
		fmt.Println("已登录")
		templateData["user"] = wechatUser
		templateData["is_logged_in"] = true
	} else {
		fmt.Println("未登录")
		templateData["is_logged_in"] = false
	}

	// 渲染详情页面
	r.Response.WriteTpl("mobile/order.html", templateData)
}

// HistoryView 浏览记录页面
func (c *ControllerMobile) HistoryView(r *ghttp.Request) {
	// 调试：检查Session ID
	sessionId, _ := r.Session.Id()
	g.Log().Info(r.GetCtx(), "Detail页面Session ID:", sessionId)

	// 调试：直接从Session获取用户信息
	sessionUser := r.Session.MustGet("wechat_user")
	g.Log().Info(r.GetCtx(), "Session中的wechat_user:", g.Map{
		"is_nil":   sessionUser == nil || sessionUser.IsNil(),
		"raw_data": sessionUser,
	})

	// 获取当前微信用户信息
	wechatUser := middleware.GetCurrentWechatUser(r)

	// 调试日志
	g.Log().Info(r.GetCtx(), "GetCurrentWechatUser结果:", g.Map{
		"is_nil":    wechatUser == nil,
		"user_data": wechatUser,
	})

	// 构建模板数据
	templateData := g.Map{
		"title": "浏览记录",
		"page":  "history",
	}

	// 如果有用户信息，添加到模板数据中
	if wechatUser != nil {
		fmt.Println("已登录")
		templateData["user"] = wechatUser
		templateData["is_logged_in"] = true
	} else {
		fmt.Println("未登录")
		templateData["is_logged_in"] = false
	}

	// 渲染详情页面
	r.Response.WriteTpl("mobile/history.html", templateData)
}

// PackageView 套餐列表页面
func (c *ControllerMobile) PackageView(r *ghttp.Request) {
	// 调试：检查Session ID
	sessionId, _ := r.Session.Id()
	g.Log().Info(r.GetCtx(), "Detail页面Session ID:", sessionId)

	// 调试：直接从Session获取用户信息
	sessionUser := r.Session.MustGet("wechat_user")
	g.Log().Info(r.GetCtx(), "Session中的wechat_user:", g.Map{
		"is_nil":   sessionUser == nil || sessionUser.IsNil(),
		"raw_data": sessionUser,
	})

	// 获取当前微信用户信息
	wechatUser := middleware.GetCurrentWechatUser(r)

	// 调试日志
	g.Log().Info(r.GetCtx(), "GetCurrentWechatUser结果:", g.Map{
		"is_nil":    wechatUser == nil,
		"user_data": wechatUser,
	})

	// 构建模板数据
	templateData := g.Map{
		"title": "会员列表",
		"page":  "package",
	}

	// 如果有用户信息，添加到模板数据中
	if wechatUser != nil {
		fmt.Println("已登录")
		templateData["user"] = wechatUser
		templateData["is_logged_in"] = true
	} else {
		fmt.Println("未登录")
		templateData["is_logged_in"] = false
	}

	// 渲染详情页面
	r.Response.WriteTpl("mobile/package.html", templateData)
}

// SearchView 搜索页面
func (c *ControllerMobile) SearchView(r *ghttp.Request) {
	// 构建模板数据
	templateData := g.Map{
		"title": "搜索页面",
		"page":  "search",
	}

	// 渲染详情页面
	r.Response.WriteTpl("mobile/search.html", templateData)
}

// validateVipStatus 验证用户VIP状态
func (c *ControllerMobile) validateVipStatus(userInfo *entity.ZbUser) int {
	// 如果用户被禁用或删除，直接返回无效
	if userInfo.IsDisable == 1 || userInfo.IsDelete == 1 {
		return 0
	}

	// 如果没有设置有效期，返回无效
	if userInfo.EffectiveStart == nil || userInfo.EffectiveEnd == nil {
		g.Log().Info(context.Background(), "VIP验证失败: 有效期字段为空", g.Map{
			"effective_start_nil": userInfo.EffectiveStart == nil,
			"effective_end_nil":   userInfo.EffectiveEnd == nil,
		})
		return 0
	}

	// 检查时间是否为零值
	if userInfo.EffectiveStart.IsZero() || userInfo.EffectiveEnd.IsZero() {
		g.Log().Info(context.Background(), "VIP验证失败: 有效期为零值", g.Map{
			"effective_start": userInfo.EffectiveStart.String(),
			"effective_end":   userInfo.EffectiveEnd.String(),
		})
		return 0
	}

	// 获取当前时间
	now := time.Now()

	// 获取当前日期（格式：2006-01-02）
	currentDateStr := now.Format("2006-01-02")

	// 使用Time()方法获取标准time.Time，然后格式化
	startDateStr := userInfo.EffectiveStart.Time.Format("2006-01-02")
	endDateStr := userInfo.EffectiveEnd.Time.Format("2006-01-02")

	// 调试日志：打印详细的日期比较信息
	g.Log().Info(context.Background(), "VIP状态验证详情:", g.Map{
		"current_time":        now.Format("2006-01-02 15:04:05"),
		"current_date":        currentDateStr,
		"effective_start_raw": userInfo.EffectiveStart.String(),
		"effective_end_raw":   userInfo.EffectiveEnd.String(),
		"effective_start":     startDateStr,
		"effective_end":       endDateStr,
		"start_comparison":    fmt.Sprintf("%s >= %s = %v", currentDateStr, startDateStr, currentDateStr >= startDateStr),
		"end_comparison":      fmt.Sprintf("%s <= %s = %v", currentDateStr, endDateStr, currentDateStr <= endDateStr),
	})

	// 使用字符串比较日期（YYYY-MM-DD格式可以直接字符串比较）
	if currentDateStr >= startDateStr && currentDateStr <= endDateStr {
		return 1 // VIP有效
	}

	return 0 // VIP无效
}

// WechatLogin 微信授权登录
func (c *ControllerMobile) WechatLogin(r *ghttp.Request) {
	// 获取回调URL参数
	callbackURL := r.Get("callback").String()
	if callbackURL == "" {
		callbackURL = "/m/detail" // 默认回调到详情页
	}

	// 构建完整的微信回调URL（固定为/m/auth/callback）
	scheme := "http"
	if r.TLS != nil || r.Header.Get("X-Forwarded-Proto") == "https" {
		scheme = "https"
	}

	host := r.Host
	if host == "" {
		host = r.Header.Get("Host")
	}

	// 微信回调固定到 /m/auth/callback，原始回调URL作为state参数
	wechatCallbackURL := fmt.Sprintf("%s://%s/m/auth/callback", scheme, host)

	// 构建微信授权URL，将原始回调URL作为state参数
	authURL, err := buildWechatAuthURL(wechatCallbackURL, callbackURL)
	if err != nil {
		g.Log().Error(r.GetCtx(), "构建微信授权URL失败:", err)
		r.Response.WriteJson(g.Map{
			"code":    1,
			"message": "授权失败，请稍后重试",
		})
		return
	}

	// 重定向到微信授权页面
	r.Response.RedirectTo(authURL)
}

// buildWechatAuthURL 构建微信授权URL
func buildWechatAuthURL(redirectURL, state string) (string, error) {
	ctx := gctx.New()
	wechatConfig, err := service.WechatConfig().GetConfig(ctx)
	if err != nil {
		return "", fmt.Errorf("微信AppID未配置")
	}
	// 从配置中获取微信应用信息
	appID := wechatConfig.Appid
	if appID == "" {
		return "", fmt.Errorf("微信AppID未配置")
	}

	// 如果没有提供state，使用默认值
	if state == "" {
		state = "STATE"
	}

	// 构建授权URL
	baseURL := "https://open.weixin.qq.com/connect/oauth2/authorize"
	params := map[string]string{
		"appid":         appID,
		"redirect_uri":  redirectURL,
		"response_type": "code",
		"scope":         "snsapi_userinfo", // 获取用户基本信息
		"state":         state,             // 传递原始回调URL
	}

	// 构建查询字符串
	var queryParts []string
	for key, value := range params {
		queryParts = append(queryParts, fmt.Sprintf("%s=%s", key, value))
	}

	authURL := fmt.Sprintf("%s?%s#wechat_redirect", baseURL, strings.Join(queryParts, "&"))
	return authURL, nil
}

// AuthCallback 微信授权回调
func (c *ControllerMobile) AuthCallback(r *ghttp.Request) {
	ctx := r.GetCtx()
	code := r.Get("code").String()

	if code == "" {
		r.Response.WriteTpl("mobile/error.html", g.Map{
			"title": "错误",
			"error": "授权失败：缺少授权码",
		})
		return
	}

	// 使用PowerWeChat获取用户信息
	officialAccount, err := service.WechatConfig().GetOfficialAccount(ctx)
	if err != nil {
		g.Log().Error(ctx, "获取公众号配置失败:", err)
		r.Response.WriteTpl("mobile/error.html", g.Map{
			"title": "错误",
			"error": "系统配置错误",
		})
		return
	}

	// 通过code获取token
	tokenResponse, err := officialAccount.OAuth.TokenFromCode(code)
	if err != nil {
		g.Log().Error(ctx, "获取access_token失败:", err)
		r.Response.WriteTpl("mobile/error.html", g.Map{
			"title": "错误",
			"error": "授权失败Code无效",
		})
		return
	}

	accessToken := (*tokenResponse)["access_token"].(string)
	openID := (*tokenResponse)["openid"].(string)

	// 获取用户信息
	user, err := officialAccount.OAuth.UserFromToken(accessToken, openID)
	if err != nil {
		g.Log().Error(ctx, "获取用户信息失败:", err)
		r.Response.WriteTpl("mobile/error.html", g.Map{
			"title": "错误",
			"error": "获取用户信息失败",
		})
		return
	}

	// 解析用户信息
	userRaw := user.Attributes.Get("raw")
	var nickname, avatar string

	if jsonStr, ok := userRaw.(string); ok {
		var rawMap map[string]interface{}
		if err := json.Unmarshal([]byte(jsonStr), &rawMap); err == nil {
			if n, exists := rawMap["nickname"]; exists {
				nickname = n.(string)
			}
			if a, exists := rawMap["headimgurl"]; exists {
				avatar = a.(string)
			}
		}
	}

	// 处理用户注册或登录
	userInfo, err := c.handleUserRegisterOrLogin(ctx, openID, nickname, avatar, r.GetClientIp())
	if err != nil {
		g.Log().Error(ctx, "处理用户注册或登录失败:", err)
		r.Response.WriteTpl("mobile/error.html", g.Map{
			"title": "错误",
			"error": "您的账号已被禁用",
		})
		return
	}

	// 将用户信息存储到session
	r.Session.Set("wechat_user", userInfo)

	// 调试日志：确认Session存储
	g.Log().Info(ctx, "用户信息已存储到Session:", g.Map{
		"user_id":  userInfo["id"],
		"nickname": userInfo["nickname"],
		"openid":   userInfo["openid"],
	})

	// 验证Session是否正确存储
	testUser := r.Session.MustGet("wechat_user")
	if testUser != nil && !testUser.IsNil() {
		g.Log().Info(ctx, "Session存储验证成功")
	} else {
		g.Log().Error(ctx, "Session存储验证失败")
	}

	// 重定向到原始页面
	callbackURL := r.Get("state").String()
	if callbackURL == "" {
		callbackURL = "/m/detail"
	}

	g.Log().Info(ctx, "准备重定向到:", callbackURL)
	r.Response.RedirectTo(callbackURL)
}

// handleUserRegisterOrLogin 处理用户注册或登录
func (c *ControllerMobile) handleUserRegisterOrLogin(ctx g.Ctx, openid, nickname, avatar, clientIP string) (map[string]interface{}, error) {
	// 查询用户是否已存在
	existingUser, err := service.ZbUser().GetUserByOpenid(ctx, openid)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %v", err)
	}

	if existingUser != nil {
		// 用户已存在，更新最后登录信息
		err = service.ZbUser().UpdateLastLogin(ctx, existingUser.Id, clientIP)
		if err != nil {
			g.Log().Warning(ctx, "更新用户最后登录信息失败:", err)
		}

		// 检查用户状态
		if existingUser.IsDisable == 1 {
			return nil, fmt.Errorf("用户已被禁用")
		}

		if existingUser.IsDelete == 1 {
			return nil, fmt.Errorf("用户已被删除")
		}

		// 返回用户信息
		return map[string]interface{}{
			"id":       existingUser.Id,
			"nickname": existingUser.Nickname,
			"avatar":   existingUser.Avatar,
			"openid":   existingUser.Openid,
		}, nil
	}

	// 用户不存在，创建新用户
	newUser, err := service.ZbUser().CreateFromWechat(ctx, openid, nickname, avatar, clientIP)
	if err != nil {
		return nil, fmt.Errorf("创建用户失败: %v", err)
	}

	return map[string]interface{}{
		"id":       newUser.Id,
		"nickname": newUser.Nickname,
		"avatar":   newUser.Avatar,
		"openid":   newUser.Openid,
	}, nil
}

// GetWechatJssdk 获取微信JSSDK配置
func (c *ControllerMobile) GetWechatJssdk(r *ghttp.Request) {
	ctx := r.GetCtx()
	url := r.Get("url").String()
	wechatConfig, _ := service.WechatConfig().GetConfig(ctx)
	if wechatConfig == nil {
		r.Response.WriteStatus(500, "系统配置错误")
		return
	}

	if url == "" {
		r.Response.WriteStatus(400, "缺少参数")
		return
	}

	officialAccount, err := service.WechatConfig().GetOfficialAccount(ctx)
	if err != nil {
		g.Log().Error(ctx, "获取公众号配置失败:", err)
		r.Response.WriteStatus(500, "系统配置错误")
		return
	}

	// 生成签名
	signature, _ := officialAccount.JSSDK.BuildConfig(ctx, []string{"updateAppMessageShareData", "updateTimelineShareData"}, false, false, []string{}, url)

	// 返回签名信息
	r.Response.WriteJson(g.Map{
		"code":    0,
		"message": "获取成功",
		"data": g.Map{
			"signature": signature,
		}})
}
