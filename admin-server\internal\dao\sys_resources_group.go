// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-server/internal/dao/internal"
)

// sysResourcesGroupDao is the data access object for the table sys_resources_group.
// You can define custom methods on it to extend its functionality as needed.
type sysResourcesGroupDao struct {
	*internal.SysResourcesGroupDao
}

var (
	// SysResourcesGroup is a globally accessible object for table sys_resources_group operations.
	SysResourcesGroup = sysResourcesGroupDao{internal.NewSysResourcesGroupDao()}
)

// Add your custom methods and functionality below.
