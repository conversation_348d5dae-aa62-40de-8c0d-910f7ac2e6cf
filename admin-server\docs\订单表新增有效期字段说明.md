# 订单表新增有效期字段说明

## 📋 修改概述

为 `zb_order` 表新增 `effective` 字段，用于记录会员有效期（单位：月），在创建订单时自动从套餐信息中获取有效期并保存到订单中。

## 🔧 数据库修改

### 1. 新增字段
```sql
ALTER TABLE `zb_order` ADD COLUMN `effective` int NOT NULL DEFAULT 0 COMMENT '会员有效期；单位月' AFTER `good_price`;
```

### 2. 字段说明
- **字段名**: `effective`
- **类型**: `int`
- **默认值**: `0`
- **位置**: 在 `good_price` 字段后面
- **用途**: 记录会员有效期，单位为月
- **数据来源**: 从套餐表 `zb_good.effective` 字段获取

### 3. 字段关系
- `zb_order.effective` ← `zb_good.effective`
- 创建订单时根据 `good_id` 获取套餐的有效期
- 用于计算用户的会员到期日期

## 💻 代码修改

### 1. Entity模型更新
```go
// ZbOrder 订单实体
type ZbOrder struct {
    Id            int64       `json:"id"`
    OrderSn       string      `json:"orderSn"`
    GoodId        int64       `json:"goodId"`
    GoodName      string      `json:"goodName"`
    GoodPrice     float64     `json:"goodPrice"`
    Effective     int         `json:"effective"`     // 新增：会员有效期
    CityCount     int         `json:"cityCount"`
    UserId        int64       `json:"userId"`
    // ... 其他字段
}
```

### 2. DAO层更新
```go
// ZbOrderColumns 字段定义
type ZbOrderColumns struct {
    Id            string // 主键ID
    OrderSn       string // 订单编号
    GoodId        string // 套餐id
    GoodName      string // 套餐名称
    GoodPrice     string // 套餐单价
    Effective     string // 会员有效期；单位月 (新增)
    CityCount     string // 选择的城市数量
    // ... 其他字段
}

// 字段映射
var zbOrderColumns = ZbOrderColumns{
    Id:            "id",
    OrderSn:       "order_sn",
    GoodId:        "good_id",
    GoodName:      "good_name",
    GoodPrice:     "good_price",
    Effective:     "effective",  // 新增
    CityCount:     "city_count",
    // ... 其他字段
}
```

### 3. API响应结构更新
```go
// OrderInfo 订单信息结构
type OrderInfo struct {
    Id            int64       `json:"id"`
    OrderSn       string      `json:"order_sn"`
    GoodId        int64       `json:"good_id"`
    GoodName      string      `json:"good_name"`
    GoodPrice     float64     `json:"good_price"`
    Effective     int         `json:"effective"`     // 新增：会员有效期
    CityCount     int         `json:"city_count"`
    UserId        int64       `json:"user_id"`
    UserNickname  string      `json:"user_nickname"`
    // ... 其他字段
}
```

### 4. Logic层修改

#### 创建订单时保存有效期
```go
// Create 创建订单
result, err := dao.ZbOrder.Ctx(ctx).Data(g.Map{
    "order_sn":   orderSn,
    "good_id":    good.Id,
    "good_name":  good.Name,
    "good_price": good.Price,
    "effective":  good.Effective,        // 新增：保存套餐有效期
    "city_count": len(req.CityIds),
    "user_id":    user.Id,
    "price":      totalPrice,
    "amount":     0,
    "pay_status": 0,
    "remark":     req.Remark,
}).Insert()
```

#### 查询时返回有效期
```go
// GetList、GetDetail、GetMyList 方法
orderInfo := v1.OrderInfo{
    Id:            orderWithUser.Id,
    OrderSn:       orderWithUser.OrderSn,
    GoodId:        orderWithUser.GoodId,
    GoodName:      orderWithUser.GoodName,
    GoodPrice:     orderWithUser.GoodPrice,
    Effective:     orderWithUser.Effective,  // 新增：返回有效期
    CityCount:     orderWithUser.CityCount,
    // ... 其他字段
}
```

## 📊 API响应示例

### 1. 订单列表响应
```json
{
    "code": 0,
    "data": {
        "list": [
            {
                "id": 1,
                "order_sn": "ZB20250123150405XXXX",
                "good_id": 1,
                "good_name": "VIP套餐",
                "good_price": 100.00,
                "effective": 12,
                "city_count": 3,
                "user_id": 123,
                "user_nickname": "张三",
                "price": 300.00,
                "amount": 300.00,
                "pay_status": 1
            }
        ],
        "total": 1
    }
}
```

### 2. 有效期字段说明
```json
{
    "effective": 12,         // 会员有效期12个月
    "pay_at": "2025-01-23T15:30:00Z",  // 支付时间
    // 会员到期时间 = pay_at + effective个月
    // 即：2025-01-23 + 12个月 = 2026-01-23
}
```

## 🎯 业务价值

### 1. 会员管理
- 记录每个订单对应的会员有效期
- 支持不同套餐的不同有效期设置
- 便于计算用户的会员到期时间

### 2. 数据分析
- 统计不同有效期套餐的销售情况
- 分析用户对不同时长套餐的偏好
- 支持会员续费策略制定

### 3. 业务扩展
- 支持灵活的会员有效期设置
- 为后续会员到期提醒功能提供数据基础
- 支持会员等级和权限管理

## 🧪 测试验证

### 1. 数据库验证
```sql
-- 查看新增字段
DESCRIBE zb_order;

-- 验证数据
SELECT 
    id,
    order_sn,
    good_name,
    good_price,
    effective,
    city_count,
    price,
    pay_status
FROM zb_order 
ORDER BY created_at DESC 
LIMIT 5;
```

### 2. 创建订单测试
```bash
# 测试创建订单（会自动获取套餐有效期）
curl -X POST "http://localhost:8000/m/api/zb_order/create" \
     -H "Content-Type: application/json" \
     -d '{
       "openid": "test_openid_123",
       "good_id": 1,
       "good_name": "VIP套餐",
       "price": 100.00,
       "city_ids": [1, 2, 3],
       "remark": "测试订单"
     }'

# 验证响应中包含effective字段
```

### 3. 查询订单测试
```bash
# 测试订单列表
curl "http://localhost:8000/api/zb_order/list?page=1&page_size=10" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 验证响应中包含effective字段
```

## ⚠️ 注意事项

### 1. 数据一致性
- 确保 `effective` 字段与套餐表中的有效期一致
- 创建订单时必须从套餐表获取最新的有效期
- 套餐有效期变更不影响已创建的订单

### 2. 业务逻辑
- 有效期单位统一为月
- 支付成功后才开始计算会员有效期
- 会员到期时间 = 支付时间 + 有效期（月）

### 3. 历史数据
- 新增字段后，历史订单的 `effective` 为 `0`
- 可以通过脚本更新历史数据：
```sql
UPDATE zb_order o 
JOIN zb_good g ON o.good_id = g.id 
SET o.effective = g.effective 
WHERE o.effective = 0;
```

## 📈 后续扩展

### 1. 会员到期计算
可以添加计算会员到期时间的方法：
```go
func CalculateExpireTime(payAt time.Time, effective int) time.Time {
    return payAt.AddDate(0, effective, 0)
}
```

### 2. 会员状态管理
可以基于有效期实现会员状态管理：
```go
func GetMemberStatus(payAt time.Time, effective int) string {
    expireTime := payAt.AddDate(0, effective, 0)
    if time.Now().After(expireTime) {
        return "expired"  // 已过期
    }
    return "active"      // 有效
}
```

### 3. 到期提醒功能
可以基于有效期实现到期提醒：
```sql
-- 查询即将到期的会员（7天内）
SELECT o.*, u.nickname 
FROM zb_order o 
JOIN zb_user u ON o.user_id = u.id 
WHERE o.pay_status = 1 
  AND DATE_ADD(o.pay_at, INTERVAL o.effective MONTH) 
      BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY);
```

---

**修改状态**: ✅ 已完成  
**新增字段**: effective (会员有效期)  
**数据来源**: 从套餐表自动获取  
**影响范围**: Entity、DAO、API、Logic层  
**向后兼容**: 是  
**文档版本**: v1.0  
**修改时间**: 2025-01-23
