// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-server/internal/dao/internal"
)

// sysDictGroupDao is the data access object for the table sys_dict_group.
// You can define custom methods on it to extend its functionality as needed.
type sysDictGroupDao struct {
	*internal.SysDictGroupDao
}

var (
	// SysDictGroup is a globally accessible object for table sys_dict_group operations.
	SysDictGroup = sysDictGroupDao{internal.NewSysDictGroupDao()}
)

// Add your custom methods and functionality below.
