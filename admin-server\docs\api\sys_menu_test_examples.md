# 系统菜单 API 测试示例

## 测试环境

- **服务器地址**: `http://localhost:8000`
- **认证方式**: <PERSON><PERSON>（需要先登录获取）

## 测试用例

### 1. 创建顶级目录（权限标识可选）

```bash
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pid": 0,
    "menu_type": "M",
    "menu_name": "系统管理",
    "menu_icon": "system",
    "menu_sort": 1,
    "perms": "",
    "paths": "/system",
    "component": "Layout",
    "params": "",
    "is_cache": 0,
    "is_show": 1,
    "is_disable": 0
  }'
```

### 1.1. 创建带权限标识的目录

```bash
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pid": 0,
    "menu_type": "M",
    "menu_name": "系统管理",
    "menu_icon": "system",
    "menu_sort": 1,
    "perms": "system:manage",
    "paths": "/system",
    "component": "Layout",
    "params": "",
    "is_cache": 0,
    "is_show": 1,
    "is_disable": 0
  }'
```

**预期响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1
  }
}
```

### 2. 创建子菜单

```bash
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pid": 1,
    "menu_type": "C",
    "menu_name": "用户管理",
    "menu_icon": "user",
    "menu_sort": 1,
    "perms": "system:user:list",
    "paths": "/system/user",
    "component": "system/user/index",
    "params": "",
    "is_cache": 1,
    "is_show": 1,
    "is_disable": 0
  }'
```

### 3. 创建按钮权限

```bash
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pid": 2,
    "menu_type": "A",
    "menu_name": "新增用户",
    "menu_icon": "",
    "menu_sort": 1,
    "perms": "system:user:create",
    "paths": "",
    "component": "",
    "params": "",
    "is_cache": 0,
    "is_show": 1,
    "is_disable": 0
  }'
```

### 4. 获取菜单列表

```bash
curl -X GET "http://localhost:8000/sys_menu/list?page=1&page_size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**预期响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "pid": 0,
        "menu_type": "M",
        "menu_name": "系统管理",
        "menu_icon": "system",
        "menu_sort": 1,
        "perms": "system:manage",
        "paths": "/system",
        "component": "Layout",
        "params": "",
        "is_cache": 0,
        "is_show": 1,
        "is_disable": 0,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 3,
    "page": 1,
    "page_size": 10
  }
}
```

### 5. 获取菜单树

```bash
curl -X GET "http://localhost:8000/sys_menu/tree?is_show=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**预期响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "tree": [
      {
        "id": 1,
        "pid": 0,
        "menu_type": "M",
        "menu_name": "系统管理",
        "menu_icon": "system",
        "menu_sort": 1,
        "perms": "system:manage",
        "paths": "/system",
        "component": "Layout",
        "params": "",
        "is_cache": 0,
        "is_show": 1,
        "is_disable": 0,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z",
        "children": [
          {
            "id": 2,
            "pid": 1,
            "menu_type": "C",
            "menu_name": "用户管理",
            "menu_icon": "user",
            "menu_sort": 1,
            "perms": "system:user:list",
            "paths": "/system/user",
            "component": "system/user/index",
            "params": "",
            "is_cache": 1,
            "is_show": 1,
            "is_disable": 0,
            "created_at": "2024-01-01T10:00:00Z",
            "updated_at": "2024-01-01T10:00:00Z",
            "children": [
              {
                "id": 3,
                "pid": 2,
                "menu_type": "A",
                "menu_name": "新增用户",
                "menu_icon": "",
                "menu_sort": 1,
                "perms": "system:user:create",
                "paths": "",
                "component": "",
                "params": "",
                "is_cache": 0,
                "is_show": 1,
                "is_disable": 0,
                "created_at": "2024-01-01T10:00:00Z",
                "updated_at": "2024-01-01T10:00:00Z",
                "children": []
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 6. 获取单个菜单信息

```bash
curl -X GET http://localhost:8000/sys_menu/1 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 7. 更新菜单信息

```bash
curl -X PUT http://localhost:8000/sys_menu/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pid": 0,
    "menu_type": "M",
    "menu_name": "系统管理（更新）",
    "menu_icon": "system-updated",
    "menu_sort": 2,
    "perms": "system:manage",
    "paths": "/system",
    "component": "Layout",
    "params": "",
    "is_cache": 0,
    "is_show": 1,
    "is_disable": 0
  }'
```

### 8. 删除菜单

```bash
curl -X DELETE http://localhost:8000/sys_menu/3 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 搜索测试

### 按菜单名称搜索

```bash
curl -X GET "http://localhost:8000/sys_menu/list?menu_name=用户" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 按菜单类型筛选

```bash
curl -X GET "http://localhost:8000/sys_menu/list?menu_type=M" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 筛选显示状态

```bash
curl -X GET "http://localhost:8000/sys_menu/list?is_show=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 筛选禁用状态

```bash
curl -X GET "http://localhost:8000/sys_menu/list?is_disable=0" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 错误测试

### 1. 创建重复权限标识

```bash
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pid": 0,
    "menu_type": "M",
    "menu_name": "重复权限测试",
    "perms": "system:manage"
  }'
```

**预期响应**:
```json
{
  "code": 1,
  "message": "权限标识已存在",
  "data": null
}
```

### 2. 设置不存在的父菜单

```bash
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pid": 999,
    "menu_type": "C",
    "menu_name": "测试菜单",
    "perms": "test:menu"
  }'
```

**预期响应**:
```json
{
  "code": 1,
  "message": "父菜单不存在",
  "data": null
}
```

### 3. 删除有子菜单的菜单

```bash
curl -X DELETE http://localhost:8000/sys_menu/1 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**预期响应**:
```json
{
  "code": 1,
  "message": "存在子菜单，无法删除",
  "data": null
}
```

### 4. 菜单类型权限标识为空

```bash
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pid": 1,
    "menu_type": "C",
    "menu_name": "用户管理",
    "perms": ""
  }'
```

**预期响应**:
```json
{
  "code": 1,
  "message": "菜单和按钮类型的权限标识不能为空",
  "data": null
}
```

### 5. 按钮类型权限标识为空

```bash
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pid": 2,
    "menu_type": "A",
    "menu_name": "新增用户",
    "perms": ""
  }'
```

**预期响应**:
```json
{
  "code": 1,
  "message": "菜单和按钮类型的权限标识不能为空",
  "data": null
}
```

### 6. 设置自己为父菜单

```bash
curl -X PUT http://localhost:8000/sys_menu/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pid": 1,
    "menu_type": "M",
    "menu_name": "系统管理",
    "perms": "system:manage"
  }'
```

**预期响应**:
```json
{
  "code": 1,
  "message": "不能设置自己为父菜单",
  "data": null
}
```

## 参数验证测试

### 1. 菜单名称长度验证

```bash
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pid": 0,
    "menu_type": "M",
    "menu_name": "",
    "perms": "test"
  }'
```

### 2. 权限标识长度验证

```bash
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pid": 0,
    "menu_type": "M",
    "menu_name": "测试",
    "perms": ""
  }'
```

### 3. 菜单类型验证

```bash
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "pid": 0,
    "menu_type": "X",
    "menu_name": "测试",
    "perms": "test"
  }'
```

## 注意事项

1. 替换 `YOUR_TOKEN` 为实际的认证令牌
2. 确保服务器已启动并监听8000端口
3. 测试前请确保数据库连接正常
4. 建议按照层级关系依次创建菜单（先创建父菜单，再创建子菜单）
5. 删除菜单时需要先删除子菜单，再删除父菜单
6. 权限标识建议使用有意义的命名规范
