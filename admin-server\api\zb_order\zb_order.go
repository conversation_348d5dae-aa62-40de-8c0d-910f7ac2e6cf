package zb_order

import (
	v1 "admin-server/api/zb_order/v1"
	"context"
)

// 订单管理相关接口
type IZbOrderV1 interface {
	Create(ctx context.Context, req *v1.CreateReq) (res *v1.<PERSON>reate<PERSON><PERSON>, err error)
	GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error)
	GetDetail(ctx context.Context, req *v1.GetDetailReq) (res *v1.GetDetailRes, err error)
	UpdatePayStatus(ctx context.Context, req *v1.UpdatePayStatusReq) (res *v1.UpdatePayStatusRes, err error)
	Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error)
	GetMyList(ctx context.Context, req *v1.GetMyListReq) (res *v1.GetMyListRes, err error)
	GetMyStats(ctx context.Context, req *v1.GetMyStatsReq) (res *v1.GetMyStatsRes, err error)
}
