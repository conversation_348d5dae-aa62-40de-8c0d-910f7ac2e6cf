package sys_resources

import (
	"context"

	v1 "admin-server/api/sys_resources/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error) {
	list, total, err := service.SysResources().GetResourcesList(
		ctx,
		req.Page,
		req.PageSize,
		req.GroupId,
		req.OriginName,
		req.MimeType,
		req.StorageMode,
	)
	if err != nil {
		return nil, err
	}

	// 转换为API响应格式
	resources := make([]v1.ResourcesInfo, len(list))
	for i, resource := range list {
		resources[i] = v1.ResourcesInfo{
			ID:          resource.Id,
			GroupId:     resource.GroupId,
			StorageMode: int(resource.StorageMode),
			OriginName:  resource.OriginName,
			ObjectName:  resource.ObjectName,
			Hash:        resource.Hash,
			MimeType:    resource.MimeType,
			StoragePath: resource.StoragePath,
			Suffix:      resource.Suffix,
			SizeByte:    resource.SizeByte,
			SizeInfo:    resource.SizeInfo,
			Url:         resource.Url,
			Remark:      resource.Remark,
			IsDelete:    int(resource.IsDelete),
			CreatedAt:   resource.CreatedAt,
			UpdatedAt:   resource.UpdatedAt,
		}
	}

	return &v1.GetListRes{
		List:  resources,
		Total: total,
	}, nil
}
