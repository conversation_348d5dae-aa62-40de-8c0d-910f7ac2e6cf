package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// 资源列表请求
type GetListReq struct {
	g.Meta      `path:"/resource/list" method:"get" summary:"获取资源列表" tags:"资源管理" permission:"system:resource:list"`
	Page        int    `json:"page" v:"min:1" dc:"页码，最小值为1" default:"1"`
	PageSize    int    `json:"page_size" v:"min:1|max:100" dc:"每页数量，范围1-100" default:"10"`
	GroupId     int64  `json:"group_id" dc:"资源分组ID"`
	OriginName  string `json:"origin_name" dc:"源文件名（模糊搜索）"`
	MimeType    string `json:"mime_type" dc:"资源类型"`
	StorageMode int    `json:"storage_mode" dc:"存储模式：1=本地，2=阿里云，3=七牛云，4=腾讯云"`
}

type GetListRes struct {
	g.Meta `mime:"application/json"`
	List   []ResourcesInfo `json:"list" dc:"资源列表"`
	Total  int             `json:"total" dc:"总数"`
}

// 资源详情请求
type GetOneReq struct {
	g.Meta `path:"/resource/get_one/{id}" method:"get" summary:"获取资源详情" tags:"资源管理"`
	ID     int64 `json:"id" v:"required|min:1" dc:"资源ID"`
}

type GetOneRes struct {
	g.Meta    `mime:"application/json"`
	Resources *ResourcesDetail `json:"resources" dc:"资源详情"`
}

// 创建资源请求
type CreateReq struct {
	g.Meta      `path:"/resource/create" method:"post" summary:"创建资源" tags:"资源管理" permission:"system:resource:add"`
	GroupId     int64  `json:"group_id" v:"required|min:1" dc:"资源分组ID"`
	StorageMode int    `json:"storage_mode" v:"required|in:1,2,3,4" dc:"存储模式：1=本地，2=阿里云，3=七牛云，4=腾讯云"`
	OriginName  string `json:"origin_name" v:"required|length:1,255" dc:"源文件名"`
	ObjectName  string `json:"object_name" v:"required|length:1,255" dc:"新文件名"`
	Hash        string `json:"hash" v:"length:0,255" dc:"文件hash"`
	MimeType    string `json:"mime_type" v:"length:0,255" dc:"资源类型"`
	StoragePath string `json:"storage_path" v:"length:0,255" dc:"存储目录"`
	Suffix      string `json:"suffix" v:"length:0,30" dc:"文件后缀"`
	SizeByte    string `json:"size_byte" v:"length:0,50" dc:"字节数"`
	SizeInfo    string `json:"size_info" v:"length:0,255" dc:"文件大小"`
	Url         string `json:"url" v:"required|length:1,255" dc:"url地址"`
	Remark      string `json:"remark" v:"length:0,255" dc:"备注"`
}

type CreateRes struct {
	g.Meta `mime:"application/json"`
}

// 更新资源请求
type UpdateReq struct {
	g.Meta      `path:"/resource/update/{id}" method:"put" summary:"更新资源" tags:"资源管理" permission:"system:resource:edit"`
	ID          int64  `json:"id" v:"required|min:1" dc:"资源ID"`
	GroupId     int64  `json:"group_id" v:"required|min:1" dc:"资源分组ID"`
	StorageMode int    `json:"storage_mode" v:"required|in:1,2,3,4" dc:"存储模式：1=本地，2=阿里云，3=七牛云，4=腾讯云"`
	OriginName  string `json:"origin_name" v:"required|length:1,255" dc:"源文件名"`
	ObjectName  string `json:"object_name" v:"required|length:1,255" dc:"新文件名"`
	Hash        string `json:"hash" v:"length:0,255" dc:"文件hash"`
	MimeType    string `json:"mime_type" v:"length:0,255" dc:"资源类型"`
	StoragePath string `json:"storage_path" v:"length:0,255" dc:"存储目录"`
	Suffix      string `json:"suffix" v:"length:0,30" dc:"文件后缀"`
	SizeByte    string `json:"size_byte" v:"length:0,50" dc:"字节数"`
	SizeInfo    string `json:"size_info" v:"length:0,255" dc:"文件大小"`
	Url         string `json:"url" v:"required|length:1,255" dc:"url地址"`
	Remark      string `json:"remark" v:"length:0,255" dc:"备注"`
}

type UpdateRes struct {
	g.Meta `mime:"application/json"`
}

// 删除资源请求
type DeleteReq struct {
	g.Meta `path:"/resource/delete" method:"delete" summary:"删除资源" tags:"资源管理" permission:"system:resource:del"`
	IDs    []int64 `json:"ids" v:"required|length:1,100" dc:"资源ID列表"`
}

type DeleteRes struct {
	g.Meta `mime:"application/json"`
}

// 根据分组获取资源请求
type GetByGroupReq struct {
	g.Meta   `path:"/resource/group/{group_id}" method:"get" summary:"根据分组获取资源" tags:"资源管理" permission:"system:resource:list"`
	GroupId  int64 `json:"group_id" v:"required|min:1" dc:"资源分组ID"`
	Page     int   `json:"page" v:"min:1" dc:"页码，最小值为1" default:"1"`
	PageSize int   `json:"page_size" v:"min:1|max:100" dc:"每页数量，范围1-100" default:"10"`
}

type GetByGroupRes struct {
	g.Meta `mime:"application/json"`
	List   []ResourcesInfo `json:"list" dc:"资源列表"`
	Total  int             `json:"total" dc:"总数"`
}

// 图片文件上传请求
type UploadReq struct {
	g.Meta  `path:"/resource/upload" method:"post" summary:"图片文件上传" tags:"资源管理" permission:"system:resource:upload"`
	GroupId int64 `json:"group_id" v:"required|min:1" dc:"资源分组ID"`
}

// 文件上传请求
type UploadFileReq struct {
	g.Meta  `path:"/resource/upload-file" method:"post" summary:"文件上传" tags:"资源管理" permission:"system:resource:upload"`
	GroupId int64 `json:"group_id" v:"required|min:1" dc:"资源分组ID"`
}

type UploadRes struct {
	g.Meta `mime:"application/json"`
	ID     int64  `json:"id" dc:"资源ID"`
	Url    string `json:"url" dc:"资源URL"`
}

// 资源信息结构体
type ResourcesInfo struct {
	ID          int64       `json:"id" dc:"主键ID"`
	GroupId     int64       `json:"group_id" dc:"资源分组ID"`
	StorageMode int         `json:"storage_mode" dc:"存储模式"`
	OriginName  string      `json:"origin_name" dc:"源文件名"`
	ObjectName  string      `json:"object_name" dc:"新文件名"`
	Hash        string      `json:"hash" dc:"文件hash"`
	MimeType    string      `json:"mime_type" dc:"资源类型"`
	StoragePath string      `json:"storage_path" dc:"存储目录"`
	Suffix      string      `json:"suffix" dc:"文件后缀"`
	SizeByte    string      `json:"size_byte" dc:"字节数"`
	SizeInfo    string      `json:"size_info" dc:"文件大小"`
	Url         string      `json:"url" dc:"url地址"`
	Remark      string      `json:"remark" dc:"备注"`
	IsDelete    int         `json:"is_delete" dc:"是否删除"`
	CreatedAt   *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt   *gtime.Time `json:"updated_at" dc:"更新时间"`
}

// 资源详情结构体
type ResourcesDetail struct {
	ID          int64       `json:"id" dc:"主键ID"`
	GroupId     int64       `json:"group_id" dc:"资源分组ID"`
	StorageMode int         `json:"storage_mode" dc:"存储模式"`
	OriginName  string      `json:"origin_name" dc:"源文件名"`
	ObjectName  string      `json:"object_name" dc:"新文件名"`
	Hash        string      `json:"hash" dc:"文件hash"`
	MimeType    string      `json:"mime_type" dc:"资源类型"`
	StoragePath string      `json:"storage_path" dc:"存储目录"`
	Suffix      string      `json:"suffix" dc:"文件后缀"`
	SizeByte    string      `json:"size_byte" dc:"字节数"`
	SizeInfo    string      `json:"size_info" dc:"文件大小"`
	Url         string      `json:"url" dc:"url地址"`
	Remark      string      `json:"remark" dc:"备注"`
	IsDelete    int         `json:"is_delete" dc:"是否删除"`
	CreatedAt   *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt   *gtime.Time `json:"updated_at" dc:"更新时间"`
}
