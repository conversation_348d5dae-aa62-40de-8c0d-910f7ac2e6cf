package service

import (
	v1 "admin-server/api/sys_config_group/v1"
	"context"
)

// 1.定义接口
type ISysConfigGroup interface {
	GetList(ctx context.Context, req *v1.GetListReq) (list []*v1.ConfigGroupInfo, total int, err error)
	GetOne(ctx context.Context, id int64) (configGroup *v1.ConfigGroupInfo, err error)
	Create(ctx context.Context, req *v1.CreateReq) (insertId int64, err error)
	Update(ctx context.Context, req *v1.UpdateReq) (err error)
	Delete(ctx context.Context, id int64) (err error)
	ToggleStatus(ctx context.Context, id int64) (err error)
	CheckGroupExists(ctx context.Context, id int64) (exists bool, err error)
	CheckGroupCodeExists(ctx context.Context, code string, excludeId int64) (exists bool, err error)
	GetConfigCount(ctx context.Context, groupId int64) (count int, err error)
}

// 2.定义接口变量
var localSysConfigGroup ISysConfigGroup

// 3.定义一个获取接口实例的函数
func SysConfigGroup() ISysConfigGroup {
	if localSysConfigGroup == nil {
		panic("ISysConfigGroup接口未实现或未注册")
	}
	return localSysConfigGroup
}

// 4.定义一个接口实现的注册方法
func RegisterSysConfigGroup(i ISysConfigGroup) {
	localSysConfigGroup = i
}
