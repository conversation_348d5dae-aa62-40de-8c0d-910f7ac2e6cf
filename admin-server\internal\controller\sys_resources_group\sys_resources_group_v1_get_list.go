package sys_resources_group

import (
	"context"

	v1 "admin-server/api/sys_resources_group/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error) {
	list, total, err := service.SysResourcesGroup().GetResourcesGroupList(
		ctx,
		req.Page,
		req.PageSize,
		req.Name,
		req.Type,
	)
	if err != nil {
		return nil, err
	}

	// 转换为API响应格式
	resourcesGroups := make([]v1.ResourcesGroupInfo, len(list))
	for i, group := range list {
		resourcesGroups[i] = v1.ResourcesGroupInfo{
			ID:        group.Id,
			Name:      group.Name,
			Type:      group.Type,
			IsDelete:  int(group.IsDelete),
			CreatedAt: group.CreatedAt,
			UpdatedAt: group.UpdatedAt,
		}
	}

	return &v1.GetListRes{
		List:  resourcesGroups,
		Total: total,
	}, nil
}
