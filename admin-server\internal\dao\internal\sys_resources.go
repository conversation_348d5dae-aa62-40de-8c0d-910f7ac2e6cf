// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SysResourcesDao is the data access object for the table sys_resources.
type SysResourcesDao struct {
	table    string              // table is the underlying table name of the DAO.
	group    string              // group is the database configuration group name of the current DAO.
	columns  SysResourcesColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler  // handlers for customized model modification.
}

// SysResourcesColumns defines and stores column names for the table sys_resources.
type SysResourcesColumns struct {
	Id          string //
	GroupId     string // 系统资源分组id
	StorageMode string // 存储模式 (1 本地 2 阿里云 3 七牛云 4 腾讯云)
	OriginName  string // 源文件名
	ObjectName  string // 新文件名
	Hash        string // 文件hash;用来去重
	MimeType    string // 资源类型
	StoragePath string // 存储目录
	Suffix      string // 文件后缀
	SizeByte    string // 字节数
	SizeInfo    string // 文件大小
	Url         string // url地址
	Remark      string // 备注
	IsDelete    string // 是否删除: 0=否, 1=是
	CreatedAt   string // 创建时间
	UpdatedAt   string // 更新时间
	DeletedAt   string // 删除时间
}

// sysResourcesColumns holds the columns for the table sys_resources.
var sysResourcesColumns = SysResourcesColumns{
	Id:          "id",
	GroupId:     "group_id",
	StorageMode: "storage_mode",
	OriginName:  "origin_name",
	ObjectName:  "object_name",
	Hash:        "hash",
	MimeType:    "mime_type",
	StoragePath: "storage_path",
	Suffix:      "suffix",
	SizeByte:    "size_byte",
	SizeInfo:    "size_info",
	Url:         "url",
	Remark:      "remark",
	IsDelete:    "is_delete",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
	DeletedAt:   "deleted_at",
}

// NewSysResourcesDao creates and returns a new DAO object for table data access.
func NewSysResourcesDao(handlers ...gdb.ModelHandler) *SysResourcesDao {
	return &SysResourcesDao{
		group:    "default",
		table:    "sys_resources",
		columns:  sysResourcesColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SysResourcesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SysResourcesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SysResourcesDao) Columns() SysResourcesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SysResourcesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SysResourcesDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SysResourcesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
