package wechat_menu

import (
	"context"

	"admin-server/api/wechat_menu/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetOne(ctx context.Context, req *v1.WechatMenuGetOneReq) (res *v1.WechatMenuGetOneRes, err error) {
	menu, err := service.WechatMenu().GetOne(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	res = &v1.WechatMenuGetOneRes{
		WechatMenuInfo: menu,
	}
	return res, nil
}
