// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-server/internal/dao/internal"
)

// sysResourcesDao is the data access object for the table sys_resources.
// You can define custom methods on it to extend its functionality as needed.
type sysResourcesDao struct {
	*internal.SysResourcesDao
}

var (
	// SysResources is a globally accessible object for table sys_resources operations.
	SysResources = sysResourcesDao{internal.NewSysResourcesDao()}
)

// Add your custom methods and functionality below.
