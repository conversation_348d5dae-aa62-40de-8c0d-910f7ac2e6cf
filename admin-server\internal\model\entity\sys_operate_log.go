// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SysOperateLog is the golang structure for table sys_operate_log.
type SysOperateLog struct {
	Id        int         `json:"id"        orm:"id"         description:""`                          //
	RequestId string      `json:"requestId" orm:"request_id" description:"请求id"`                      // 请求id
	AdminId   int64       `json:"adminId"   orm:"admin_id"   description:"操作人id"`                     // 操作人id
	Username  string      `json:"username"  orm:"username"   description:"操作人账号"`                     // 操作人账号
	Method    string      `json:"method"    orm:"method"     description:"请求类型: GET/POST/PUT/DELETE"` // 请求类型: GET/POST/PUT/DELETE
	Title     string      `json:"title"     orm:"title"      description:"操作标题"`                      // 操作标题
	Ip        string      `json:"ip"        orm:"ip"         description:"请求ip"`                      // 请求ip
	ReqHeader string      `json:"reqHeader" orm:"req_header" description:"请求头"`                       // 请求头
	ReqBody   string      `json:"reqBody"   orm:"req_body"   description:"请求体"`                       // 请求体
	ResHeader string      `json:"resHeader" orm:"res_header" description:"响应头"`                       // 响应头
	ResBody   string      `json:"resBody"   orm:"res_body"   description:"响应体"`                       // 响应体
	Status    int         `json:"status"    orm:"status"     description:"执行状态: 1=成功, 2=失败"`          // 执行状态: 1=成功, 2=失败
	StartTime int         `json:"startTime" orm:"start_time" description:"开始时间，时间戳"`                  // 开始时间，时间戳
	EndTime   int         `json:"endTime"   orm:"end_time"   description:"结束时间，时间戳"`                  // 结束时间，时间戳
	TaskTime  int         `json:"taskTime"  orm:"task_time"  description:"执行耗时"`                      // 执行耗时
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`                      // 创建时间
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:"更新时间"`                      // 更新时间
}
