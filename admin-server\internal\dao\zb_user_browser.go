// =================================================================================
// This file is auto-generated by GoFrame CLI tool only once. Fill this file with your own code.
// =================================================================================

package dao

import (
	"admin-server/internal/dao/internal"
	"context"

	"github.com/gogf/gf/v2/database/gdb"
)

// internalZbUserBrowserDao is internal type for wrapping internal DAO implements.
type internalZbUserBrowserDao = *internal.ZbUserBrowserDao

// zbUserBrowserDao is the data access object for table zb_user_browser.
// You can define custom methods on it to extend its functionality as you wish.
type zbUserBrowserDao struct {
	internalZbUserBrowserDao
}

var (
	// ZbUserBrowser is globally public accessible object for table zb_user_browser operations.
	ZbUserBrowser = zbUserBrowserDao{
		internal.NewZbUserBrowserDao(),
	}
)

// ZbUserBrowserColumns defines and stores column names for table zb_user_browser.
type ZbUserBrowserColumns struct {
	Id        string // 主键ID
	UserId    string // 会员id
	ArticleId string // 招标信息id
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
}

// zbUserBrowserColumns holds the columns for table zb_user_browser.
var zbUserBrowserColumns = ZbUserBrowserColumns{
	Id:        "id",
	UserId:    "user_id",
	ArticleId: "article_id",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// Columns returns all column names of current dao.
func (dao *zbUserBrowserDao) Columns() ZbUserBrowserColumns {
	return zbUserBrowserColumns
}

// Group returns the configuration group name of database of current dao.
func (dao *zbUserBrowserDao) Group() string {
	return dao.internalZbUserBrowserDao.Group()
}

// Table returns the table name of current dao.
func (dao *zbUserBrowserDao) Table() string {
	return dao.internalZbUserBrowserDao.Table()
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *zbUserBrowserDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.internalZbUserBrowserDao.Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *zbUserBrowserDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.internalZbUserBrowserDao.Transaction(ctx, f)
}
