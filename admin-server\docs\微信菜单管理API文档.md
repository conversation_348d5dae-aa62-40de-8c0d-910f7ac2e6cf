# 微信菜单管理API文档

## 概述

微信菜单管理模块提供了完整的微信自定义菜单CRUD操作，支持一级菜单和二级菜单的管理，包括点击事件、跳转链接和小程序跳转等多种菜单类型。

## 基础信息

- **模块名称**: 微信菜单管理
- **基础路径**: `/wechat/menu`
- **认证方式**: JWT Token
- **权限验证**: 需要相应的菜单权限

## 菜单类型说明

| 类型 | 值 | 说明 | 必填字段 |
|------|----|----|----------|
| 点击推事件 | `click` | 用户点击菜单后，微信会把点击事件推送给开发者 | `menu_key` |
| 跳转URL | `view` | 用户点击菜单后，会跳转到指定的网页 | `menu_url` |
| 跳转小程序 | `miniprogram` | 用户点击菜单后，会跳转到指定的小程序 | `appid`, `pagepath` |

## API接口列表

### 1. 创建微信菜单

**接口地址**: `POST /wechat/menu`

**接口描述**: 创建新的微信菜单项

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pid | int | 否 | 父菜单ID，0表示一级菜单，默认为0 |
| menu_name | string | 是 | 菜单名称，长度1-255个字符 |
| menu_type | string | 是 | 菜单类型：click/view/miniprogram |
| menu_key | string | 否 | 菜单KEY值，click类型必填，长度0-255个字符 |
| menu_url | string | 否 | 菜单链接，view类型必填，长度0-255个字符 |
| appid | string | 否 | 小程序AppID，miniprogram类型必填，长度0-255个字符 |
| pagepath | string | 否 | 小程序页面路径，miniprogram类型必填，长度0-255个字符 |
| sort | int | 否 | 排序值，数字越小越靠前，默认为0 |

**请求示例**:

```json
{
  "pid": 0,
  "menu_name": "产品中心",
  "menu_type": "click",
  "menu_key": "product_center",
  "sort": 1
}
```

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| id | int | 创建成功的菜单ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1
  }
}
```

### 2. 更新微信菜单

**接口地址**: `PUT /wechat/menu/{id}`

**接口描述**: 更新指定的微信菜单项

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 菜单ID |

**请求参数**: 同创建接口，所有字段都需要传递

**请求示例**:

```json
{
  "pid": 0,
  "menu_name": "产品中心",
  "menu_type": "view",
  "menu_url": "https://example.com/products",
  "sort": 1
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 3. 删除微信菜单

**接口地址**: `DELETE /wechat/menu/{id}`

**接口描述**: 删除指定的微信菜单项

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 菜单ID |

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 4. 获取单个微信菜单

**接口地址**: `GET /wechat/menu/{id}`

**接口描述**: 获取指定的微信菜单详情

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 菜单ID |

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| id | int | 菜单ID |
| pid | int | 父菜单ID |
| menu_name | string | 菜单名称 |
| menu_type | string | 菜单类型 |
| menu_key | string | 菜单KEY值 |
| menu_url | string | 菜单链接 |
| appid | string | 小程序AppID |
| pagepath | string | 小程序页面路径 |
| sort | int | 排序 |
| level | int | 菜单层级 |
| is_disable | int | 是否禁用 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "pid": 0,
    "menu_name": "产品中心",
    "menu_type": "click",
    "menu_key": "product_center",
    "menu_url": "",
    "appid": "",
    "pagepath": "",
    "sort": 1,
    "level": 1,
    "is_disable": 0,
    "created_at": "2025-01-12 10:00:00",
    "updated_at": "2025-01-12 10:00:00"
  }
}
```

### 5. 获取微信菜单列表

**接口地址**: `GET /wechat/menu/list`

**接口描述**: 分页获取微信菜单列表

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认10，最大100 |
| menu_name | string | 否 | 菜单名称，模糊搜索 |
| menu_type | string | 否 | 菜单类型筛选 |
| is_disable | int | 否 | 是否禁用：0=否，1=是 |
| pid | int | 否 | 父菜单ID筛选 |

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| list | array | 菜单列表 |
| total | int | 总数 |
| page | int | 当前页码 |
| page_size | int | 每页数量 |

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "pid": 0,
        "menu_name": "产品中心",
        "menu_type": "click",
        "menu_key": "product_center",
        "menu_url": "",
        "appid": "",
        "pagepath": "",
        "sort": 1,
        "level": 1,
        "is_disable": 0,
        "created_at": "2025-01-12 10:00:00",
        "updated_at": "2025-01-12 10:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10
  }
}
```

### 6. 获取微信菜单树形结构

**接口地址**: `GET /wechat/menu/tree`

**接口描述**: 获取微信菜单的树形结构，用于前端展示层级关系

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| is_disable | int | 否 | 是否禁用：0=否，1=是 |

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| list | array | 菜单树形列表 |

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "pid": 0,
        "menu_name": "产品中心",
        "menu_type": "click",
        "menu_key": "product_center",
        "sort": 1,
        "level": 1,
        "is_disable": 0,
        "children": [
          {
            "id": 2,
            "pid": 1,
            "menu_name": "产品列表",
            "menu_type": "view",
            "menu_url": "https://example.com/products",
            "sort": 1,
            "level": 2,
            "is_disable": 0
          }
        ]
      }
    ]
  }
}
```

### 7. 更新微信菜单排序

**接口地址**: `PUT /wechat/menu/{id}/sort`

**接口描述**: 更新指定微信菜单的排序值

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 菜单ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sort | int | 是 | 排序值，数字越小越靠前 |

**请求示例**:

```json
{
  "sort": 5
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 8. 更新微信菜单状态

**接口地址**: `PUT /wechat/menu/{id}/status`

**接口描述**: 更新指定微信菜单的启用/禁用状态

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 菜单ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| is_disable | int | 是 | 是否禁用：0=否，1=是 |

**请求示例**:

```json
{
  "is_disable": 1
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 9. 发布微信菜单

**接口地址**: `POST /wechat/menu/publish`

**接口描述**: 将数据库中的菜单发布到微信服务器，使菜单在微信公众号中生效

**请求参数**: 无

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 发布是否成功 |
| message | string | 发布结果消息 |

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "message": "菜单发布成功"
  }
}
```

**错误响应示例**:

```json
{
  "code": 400,
  "message": "没有可发布的菜单",
  "data": {
    "success": false,
    "message": "没有可发布的菜单"
  }
}
```

**功能说明**:

1. 该接口会读取数据库中所有启用状态（`is_disable = 0`）的菜单
2. 将菜单数据转换为微信API要求的格式
3. 调用微信公众号API创建自定义菜单
4. 菜单发布后，用户在微信公众号中即可看到新的菜单

**注意事项**:

- 只有启用状态的菜单才会被发布
- 菜单按照排序值（sort）和ID进行排序
- 发布前请确保微信公众号配置正确（AppID、AppSecret等）
- 微信自定义菜单最多支持3个一级菜单，每个一级菜单最多5个二级菜单
- 菜单发布后可能需要24小时才能在所有用户端生效

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 业务规则

### 菜单层级规则

1. **一级菜单**: `pid = 0`，`level = 1`
2. **二级菜单**: `pid > 0`，`level = 2`
3. 微信自定义菜单最多支持2级菜单
4. 一级菜单最多3个，二级菜单每个一级菜单下最多5个

### 菜单类型规则

1. **click类型**: 必须填写`menu_key`，用于接收点击事件
2. **view类型**: 必须填写`menu_url`，用户点击后跳转到指定网页
3. **miniprogram类型**: 必须填写`appid`和`pagepath`，用户点击后跳转到小程序

### 删除规则

1. 删除菜单前会检查是否存在子菜单
2. 如果存在子菜单，需要先删除所有子菜单才能删除父菜单

### 排序规则

1. 排序值越小越靠前
2. 相同排序值按ID升序排列
3. 排序值可以为负数

## 使用示例

### 创建一级菜单

```bash
curl -X POST "http://localhost:8000/wechat/menu" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 0,
    "menu_name": "关于我们",
    "menu_type": "click",
    "menu_key": "about_us",
    "sort": 1
  }'
```

### 创建二级菜单

```bash
curl -X POST "http://localhost:8000/wechat/menu" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 1,
    "menu_name": "公司介绍",
    "menu_type": "view",
    "menu_url": "https://example.com/about",
    "sort": 1
  }'
```

### 获取菜单树形结构

```bash
curl -X GET "http://localhost:8000/wechat/menu/tree" \
  -H "Authorization: Bearer your_token"
```

### 更新菜单状态

```bash
curl -X PUT "http://localhost:8000/wechat/menu/1/status" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "is_disable": 1
  }'
```

## 注意事项

1. 所有接口都需要JWT Token认证
2. 需要相应的菜单管理权限
3. 菜单名称不能为空，长度限制在255个字符以内
4. 菜单类型必须是`click`、`view`、`miniprogram`之一
5. 根据菜单类型，相应的字段必须填写
6. 删除菜单时会检查是否有子菜单，有子菜单的情况下无法删除
7. 排序值建议使用正整数，便于管理
```
