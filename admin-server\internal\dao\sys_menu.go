// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-server/internal/dao/internal"
)

// sysMenuDao is the data access object for the table sys_menu.
// You can define custom methods on it to extend its functionality as needed.
type sysMenuDao struct {
	*internal.SysMenuDao
}

var (
	// SysMenu is a globally accessible object for table sys_menu operations.
	SysMenu = sysMenuDao{internal.NewSysMenuDao()}
)

// Add your custom methods and functionality below.
