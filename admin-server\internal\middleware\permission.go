package middleware

import (
	"admin-server/internal/dao"
	"admin-server/internal/model/entity"
	"admin-server/internal/packed"
	"context"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// Permission 基于g.Meta标签的权限验证中间件
func Permission(r *ghttp.Request) {
	// 获取当前请求的路径和方法
	path := r.URL.Path
	method := r.Method

	// 从上下文中获取管理员ID（由Auth中间件设置）
	adminId := r.GetCtxVar("admin_id").Int64()
	if adminId == 0 {
		r.Response.WriteJsonExit(g.Map{
			"code":    401,
			"message": "未登录或登录已过期",
			"data":    nil,
		})
		return
	}

	// 检查是否为排除路径（不需要权限验证的路径）
	if isExcludedPath(path) {
		r.Middleware.Next()
		return
	}

	// 从路由映射中获取权限标识
	requiredPermission := getPermissionFromHandler(r)
	if requiredPermission == "" {
		// 如果没有配置权限要求，记录警告但允许访问（可根据业务需求调整）
		g.Log().Warning(r.Context(), "路由未配置权限标识:", method, path)
		r.Middleware.Next()
		return
	}

	g.Log().Info(r.Context(), "Required permission:", requiredPermission, "for", method, path)

	// 检查权限
	hasPermission, err := checkUserPermissionByIdentifier(r.Context(), adminId, requiredPermission)
	if err != nil {
		g.Log().Error(r.Context(), "权限检查失败:", err, "adminId:", adminId, "permission:", requiredPermission)
		r.Response.WriteJsonExit(g.Map{
			"code":    500,
			"message": "权限验证失败",
			"data":    nil,
		})
		return
	}

	if !hasPermission {
		g.Log().Warning(r.Context(), "权限不足:", "adminId:", adminId, "permission:", requiredPermission, "path:", path)
		r.Response.WriteJsonExit(g.Map{
			"code":    403,
			"message": "权限不足",
			"data":    nil,
		})
		return
	}

	// 权限验证通过，记录访问日志
	g.Log().Debug(r.Context(), "权限验证通过:", "adminId:", adminId, "permission:", requiredPermission, "path:", path)

	// 权限验证通过，继续执行
	r.Middleware.Next()
}

// getPermissionFromHandler 从路由处理器中获取权限标识
func getPermissionFromHandler(r *ghttp.Request) string {
	// 获取当前路由信息
	var (
		routes = r.Server.GetRoutes() // 获取所有路由信息
		path   = r.URL.Path           // 当前请求的路径
		method = r.Method             // 当前请求的方法
	)

	// 处理路由信息，查找匹配的路由
	for _, route := range routes {
		// 跳过一些特殊路由，如中间件、钩子函数等
		if !route.IsServiceHandler {
			continue
		}

		// 检查HTTP方法是否匹配
		if route.Method != "*" && route.Method != method {
			continue
		}

		// 精确匹配
		if route.Route == path {
			if permissionTag := route.Handler.GetMetaTag("permission"); permissionTag != "" {
				return permissionTag
			}
		}

		// 模式匹配（支持带参数的路径，如 /sys_admin/{id}）
		if matchRoute(route.Route, path) {
			if permissionTag := route.Handler.GetMetaTag("permission"); permissionTag != "" {
				return permissionTag
			}
		}
	}

	return ""
}

// matchRoute 检查路由模式是否匹配请求路径
func matchRoute(routePattern, requestPath string) bool {
	// 简单的路由匹配逻辑，支持 {id} 这样的参数
	if !strings.Contains(routePattern, "{") {
		return routePattern == requestPath
	}

	routeParts := strings.Split(routePattern, "/")
	pathParts := strings.Split(requestPath, "/")

	if len(routeParts) != len(pathParts) {
		return false
	}

	for i, routePart := range routeParts {
		if strings.HasPrefix(routePart, "{") && strings.HasSuffix(routePart, "}") {
			// 这是一个参数，跳过检查
			continue
		}
		if routePart != pathParts[i] {
			return false
		}
	}

	return true
}

// checkUserPermissionByIdentifier 根据权限标识检查用户权限
func checkUserPermissionByIdentifier(ctx context.Context, adminId int64, requiredPermission string) (bool, error) {
	// 查询管理员信息
	var admin entity.SysAdmins
	err := dao.SysAdmins.Ctx(ctx).Where("id", adminId).Where("is_delete", packed.NO_DELETE).Scan(&admin)
	if err != nil {
		return false, err
	}

	if admin.Id == 0 {
		return false, nil
	}

	// 超级管理员拥有所有权限
	if admin.IsSuper == packed.IS_SUPER {
		g.Log().Info(ctx, "超级管理员访问:", requiredPermission)
		return true, nil
	}

	// 普通管理员需要检查具体权限
	return checkRolePermissionByIdentifier(ctx, adminId, requiredPermission)
}

// checkRolePermissionByIdentifier 根据权限标识检查角色权限
func checkRolePermissionByIdentifier(ctx context.Context, adminId int64, requiredPermission string) (bool, error) {
	// 使用联表查询优化性能，一次查询获取所有相关权限
	sql := `
		SELECT DISTINCT m.perms, m.menu_name
		FROM sys_menu m
		INNER JOIN sys_role_menu rm ON m.id = rm.menu_id
		INNER JOIN sys_admin_role ar ON rm.role_id = ar.role_id
		WHERE ar.admin_id = ?
		AND m.is_delete = ?
		AND m.perms != ''
	`

	type MenuPermission struct {
		Perms    string `json:"perms"`
		MenuName string `json:"menu_name"`
	}

	var permissions []MenuPermission
	err := g.DB().Ctx(ctx).Raw(sql, adminId, packed.NO_DELETE).Scan(&permissions)
	if err != nil {
		return false, err
	}

	if len(permissions) == 0 {
		g.Log().Info(ctx, "管理员无有效权限:", adminId)
		return false, nil
	}

	// 检查权限匹配
	for _, permission := range permissions {
		if matchPermissionIdentifier(permission.Perms, requiredPermission) {
			g.Log().Info(ctx, "权限匹配成功:", permission.MenuName, permission.Perms, "->", requiredPermission)
			return true, nil
		}
	}

	g.Log().Info(ctx, "权限验证失败:", adminId, requiredPermission)
	return false, nil
}

// matchPermissionIdentifier 权限标识匹配
func matchPermissionIdentifier(userPermission, requiredPermission string) bool {
	if userPermission == "" || requiredPermission == "" {
		return false
	}

	// 1. 精确匹配
	if userPermission == requiredPermission {
		return true
	}

	// 2. 通配符匹配（例如：system:* 可以匹配 system:admin:list）
	if strings.HasSuffix(userPermission, ":*") {
		prefix := strings.TrimSuffix(userPermission, "*")
		if strings.HasPrefix(requiredPermission, prefix) {
			return true
		}
	}

	// 3. 父级权限匹配（例如：system:admin 可以匹配 system:admin:list）
	if strings.HasPrefix(requiredPermission, userPermission+":") {
		return true
	}

	// 4. 超级权限匹配（例如：* 可以匹配任何权限）
	if userPermission == "*" {
		return true
	}

	// 5. 模块级通配符匹配（例如：system:admin:* 可以匹配 system:admin:list）
	if strings.HasSuffix(userPermission, "*") {
		prefix := strings.TrimSuffix(userPermission, "*")
		if strings.HasPrefix(requiredPermission, prefix) {
			return true
		}
	}

	return false
}

// isExcludedPath 检查是否为排除路径（不需要权限验证的路径）
func isExcludedPath(path string) bool {
	excludedPaths := []string{
		"/auth/login",
		"/auth/refresh",
		"/favicon.ico",
	}

	for _, excludedPath := range excludedPaths {
		if path == excludedPath {
			return true
		}
	}

	// 排除静态资源路径
	if strings.HasPrefix(path, "/static/") ||
		strings.HasPrefix(path, "/assets/") ||
		strings.HasPrefix(path, "/uploads/") ||
		strings.HasPrefix(path, "/public/") {
		return true
	}

	return false
}
