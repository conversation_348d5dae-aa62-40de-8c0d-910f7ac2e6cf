package wechat_menu

import (
	"context"

	"admin-server/api/wechat_menu/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetList(ctx context.Context, req *v1.WechatMenuGetListReq) (res *v1.WechatMenuGetListRes, err error) {
	list, total, err := service.WechatMenu().GetList(ctx, req)
	if err != nil {
		return nil, err
	}

	res = &v1.WechatMenuGetListRes{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}
	return res, nil
}
