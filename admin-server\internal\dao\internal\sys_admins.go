// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SysAdminsDao is the data access object for the table sys_admins.
type SysAdminsDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  SysAdminsColumns   // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// SysAdminsColumns defines and stores column names for the table sys_admins.
type SysAdminsColumns struct {
	Id            string //
	Username      string // 账号
	Password      string // 密码
	Nickname      string // 昵称
	IsSuper       string // 是否是超级管理员1是0否
	IsDisable     string // 是否禁用: 0=否, 1=是
	IsDelete      string // 是否删除: 0=否, 1=是
	LastLoginIp   string // 最后登录ip
	LastLoginTime string // 最后登录时间
	CreatedAt     string // 创建时间
	UpdatedAt     string // 更新时间
	DeletedAt     string // 删除时间
}

// sysAdminsColumns holds the columns for the table sys_admins.
var sysAdminsColumns = SysAdminsColumns{
	Id:            "id",
	Username:      "username",
	Password:      "password",
	Nickname:      "nickname",
	IsSuper:       "is_super",
	IsDisable:     "is_disable",
	IsDelete:      "is_delete",
	LastLoginIp:   "last_login_ip",
	LastLoginTime: "last_login_time",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
	DeletedAt:     "deleted_at",
}

// NewSysAdminsDao creates and returns a new DAO object for table data access.
func NewSysAdminsDao(handlers ...gdb.ModelHandler) *SysAdminsDao {
	return &SysAdminsDao{
		group:    "default",
		table:    "sys_admins",
		columns:  sysAdminsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SysAdminsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SysAdminsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SysAdminsDao) Columns() SysAdminsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SysAdminsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SysAdminsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SysAdminsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
