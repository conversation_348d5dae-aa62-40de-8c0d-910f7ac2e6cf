package sys_auth

import (
	"context"

	v1 "admin-server/api/sys_auth/v1"
	"admin-server/internal/service"
	"github.com/gogf/gf/v2/frame/g"
)

func (c *ControllerV1) Logout(ctx context.Context, req *v1.LogoutReq) (res *v1.<PERSON>goutRes, err error) {
	// 从请求头获取token
	token := g.RequestFromCtx(ctx).Header.Get("Authorization")
	if token != "" {
		// 移除Bearer前缀
		if len(token) > 7 && token[:7] == "Bearer " {
			token = token[7:]
		}
		err = service.SysAuth().Logout(ctx, token)
		if err != nil {
			return nil, err
		}
	}

	return &v1.LogoutRes{}, nil
}
