# 移动端详情页visibility字段移除说明

## 修改概述

根据需求，移除了对 `visibility` 字段的判断，现在所有字段都会显示，不再根据 `visibility` 是否为 `"show"` 来决定是否渲染。

## 修改内容

### 1. 基本信息渲染
**修改前**:
```javascript
if (field.visibility === 'show' && field.value && field.value.trim()) {
    // 渲染字段
}
```

**修改后**:
```javascript
if (field.value && field.value.trim()) {
    // 渲染字段
}
```

### 2. 项目概况渲染
**修改前**:
```javascript
const projectContentField = basicInfoSection.fields.find(field => 
    field.label.includes('项目内容') && field.visibility === 'show'
);
```

**修改后**:
```javascript
const projectContentField = basicInfoSection.fields.find(field => 
    field.label.includes('项目内容')
);
```

### 3. 详细内容渲染
**修改前**:
```javascript
if (field.visibility === 'show' && field.value && field.value.trim()) {
    // 渲染字段
}
```

**修改后**:
```javascript
if (field.value && field.value.trim()) {
    // 渲染字段
}
```

### 4. 分享描述生成
**修改前**:
```javascript
const projectContent = basicInfo.fields.find(field => 
    field.label.includes('项目内容') && field.visibility === 'show'
);
```

**修改后**:
```javascript
const projectContent = basicInfo.fields.find(field => 
    field.label.includes('项目内容')
);
```

## 影响范围

### 1. 显示内容变化
- **之前**: 只显示 `visibility: "show"` 的字段
- **现在**: 显示所有有内容的字段，包括 `visibility: "hide"` 的字段

### 2. 敏感信息显示
- 之前被隐藏的敏感信息（如预算金额等）现在会显示出来
- 例如：`"预算金额：[已屏蔽]"` 这样的字段现在也会显示

### 3. 数据完整性
- 用户现在可以看到更完整的项目信息
- 不再有信息被系统自动过滤

## 示例对比

### 原始数据
```javascript
{
    "label": "预算金额：",
    "order": 5,
    "value": "[已屏蔽]",
    "visibility": "hide"
}
```

### 显示效果
- **修改前**: 不显示此字段
- **修改后**: 显示 "预算金额：[已屏蔽]"

## 注意事项

1. **数据安全**: 现在所有字段都会显示，需要确保后端数据中不包含真正敏感的信息
2. **用户体验**: 显示 "[已屏蔽]" 这样的内容可能会影响用户体验
3. **信息完整性**: 用户现在可以看到更完整的信息，包括之前被隐藏的部分

## 建议

如果需要控制某些信息的显示，建议：
1. 在后端数据处理时就移除不需要显示的字段
2. 或者使用其他字段名来控制显示逻辑
3. 对于敏感信息，在后端就进行脱敏处理

---

**修改状态**: ✅ 已完成  
**影响范围**: 移动端详情页所有字段显示  
**文档版本**: v1.1  
**最后更新**: 2025-01-23
