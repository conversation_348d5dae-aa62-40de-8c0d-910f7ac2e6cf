package wechat_config

import (
	"context"

	"admin-server/api/wechat_config/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) SaveConfig(ctx context.Context, req *v1.WechatConfigSaveReq) (res *v1.WechatConfigSaveRes, err error) {
	success, message, err := service.WechatConfig().SaveConfig(ctx, req)
	if err != nil {
		return nil, err
	}

	res = &v1.WechatConfigSaveRes{
		Success: success,
		Message: message,
	}
	return res, nil
}
