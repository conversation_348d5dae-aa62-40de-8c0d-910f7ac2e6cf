-- 修复权限标识唯一性约束问题
-- 问题：perms字段设置为NOT NULL UNIQUE，导致多个目录无法使用空字符串

-- 方案一：修改字段为允许NULL，并重建唯一索引（推荐）

-- 1. 删除现有的唯一索引
ALTER TABLE `sys_menu` DROP INDEX `perms`;

-- 2. 修改字段允许NULL
ALTER TABLE `sys_menu` MODIFY COLUMN `perms` VARCHAR(255) NULL COMMENT '权限标识';

-- 3. 将现有的空字符串更新为NULL
UPDATE `sys_menu` SET `perms` = NULL WHERE `perms` = '';

-- 4. 创建新的唯一索引，只对非NULL值生效
CREATE UNIQUE INDEX `idx_perms_unique` ON `sys_menu` (`perms`);

-- 方案二：使用复合唯一索引（备选方案）
-- 如果不想修改字段为NULL，可以使用复合索引
-- ALTER TABLE `sys_menu` DROP INDEX `perms`;
-- CREATE UNIQUE INDEX `idx_perms_menu_type` ON `sys_menu` (`perms`, `menu_type`);

-- 验证修改结果
-- 查看表结构
DESCRIBE `sys_menu`;

-- 查看索引
SHOW INDEX FROM `sys_menu` WHERE Key_name = 'idx_perms_unique';
