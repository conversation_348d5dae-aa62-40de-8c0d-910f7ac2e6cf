# 操作日志API文档

## 概述

操作日志系统提供完整的用户操作记录和审计功能，自动记录管理员的所有操作行为，包括请求详情、响应结果、执行时间等信息。

## 基础信息

- **Base URL**: `http://localhost:8000`
- **认证方式**: Bearer Token
- **Content-Type**: `application/json`

## API接口列表

### 1. 获取操作日志列表

**接口地址**: `GET /sys_operate_log/list`

**接口描述**: 分页获取操作日志列表，支持多条件筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码，最小值为1 |
| page_size | int | 否 | 10 | 每页数量，范围1-100 |
| username | string | 否 | - | 操作人账号（模糊搜索） |
| title | string | 否 | - | 操作标题（模糊搜索） |
| method | string | 否 | - | 请求方法：GET/POST/PUT/DELETE |
| status | int | 否 | - | 执行状态：1=成功，2=失败 |
| start_date | string | 否 | - | 开始日期，格式：2024-01-01 |
| end_date | string | 否 | - | 结束日期，格式：2024-01-31 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/sys_operate_log/list?page=1&page_size=10&username=admin&status=1" \
  -H "Authorization: Bearer your_token"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "request_id": "abc123-def456-ghi789",
        "admin_id": 1,
        "username": "admin",
        "method": "POST",
        "title": "新增管理员",
        "ip": "*************",
        "status": 1,
        "task_time": 150,
        "created_at": "2024-01-01 10:00:00"
      }
    ],
    "total": 1
  }
}
```

### 2. 获取操作日志详情

**接口地址**: `GET /sys_operate_log/{id}`

**接口描述**: 获取指定操作日志的详细信息，包括请求体、响应体等完整数据

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 操作日志ID |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/sys_operate_log/1" \
  -H "Authorization: Bearer your_token"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "operate_log": {
      "id": 1,
      "request_id": "abc123-def456-ghi789",
      "admin_id": 1,
      "username": "admin",
      "method": "POST",
      "title": "新增管理员",
      "ip": "*************",
      "req_header": {
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0...",
        "Authorization": "***"
      },
      "req_body": {
        "username": "test_user",
        "nickname": "测试用户",
        "password": "***"
      },
      "res_header": null,
      "res_body": {
        "status": 200,
        "note": "响应体内容已省略"
      },
      "status": 1,
      "start_time": 1704067200,
      "end_time": 1704067200,
      "task_time": 150,
      "created_at": "2024-01-01 10:00:00",
      "updated_at": "2024-01-01 10:00:00"
    }
  }
}
```

### 3. 删除操作日志

**接口地址**: `DELETE /sys_operate_log/delete`

**接口描述**: 批量删除指定的操作日志记录

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | array | 是 | 操作日志ID列表 |

**请求示例**:
```bash
curl -X DELETE "http://localhost:8000/sys_operate_log/delete" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [1, 2, 3]
  }'
```

**响应示例**:
```json
{
  "code": 0,
  "message": "删除成功",
  "data": {}
}
```

### 4. 清空操作日志

**接口地址**: `DELETE /sys_operate_log/clear`

**接口描述**: 清空所有操作日志记录（谨慎操作）

**请求示例**:
```bash
curl -X DELETE "http://localhost:8000/sys_operate_log/clear" \
  -H "Authorization: Bearer your_token"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "清空成功",
  "data": {}
}
```

## 数据结构说明

### OperateLogInfo (列表项)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 主键ID |
| request_id | string | 请求唯一标识 |
| admin_id | int64 | 操作人ID |
| username | string | 操作人账号 |
| method | string | HTTP请求方法 |
| title | string | 操作标题 |
| ip | string | 请求IP地址 |
| status | int | 执行状态：1=成功，2=失败 |
| task_time | int64 | 执行耗时（毫秒） |
| created_at | datetime | 操作时间 |

### OperateLogDetail (详情)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 主键ID |
| request_id | string | 请求唯一标识 |
| admin_id | int64 | 操作人ID |
| username | string | 操作人账号 |
| method | string | HTTP请求方法 |
| title | string | 操作标题 |
| ip | string | 请求IP地址 |
| req_header | object | 请求头信息（敏感信息已脱敏） |
| req_body | object | 请求体内容（敏感信息已脱敏） |
| res_header | object | 响应头信息 |
| res_body | object | 响应体内容 |
| status | int | 执行状态：1=成功，2=失败 |
| start_time | int64 | 开始时间戳 |
| end_time | int64 | 结束时间戳 |
| task_time | int64 | 执行耗时（毫秒） |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

## 权限说明

| 权限标识 | 权限名称 | 说明 |
|----------|----------|------|
| system:operatelog:list | 查看操作日志 | 获取操作日志列表 |
| system:operatelog:view | 查看日志详情 | 获取操作日志详细信息 |
| system:operatelog:delete | 删除操作日志 | 删除指定的操作日志 |
| system:operatelog:clear | 清空操作日志 | 清空所有操作日志 |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 401 | 未登录或登录已过期 |
| 403 | 权限不足 |
| 404 | 记录不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. **敏感信息脱敏**: 系统会自动对密码、token等敏感信息进行脱敏处理
2. **异步记录**: 操作日志采用异步记录方式，不影响主业务流程
3. **存储限制**: 请求体和响应体内容有长度限制，超长内容会被截断
4. **权限控制**: 所有接口都需要相应的权限才能访问
5. **数据保留**: 建议定期清理过期的操作日志以节省存储空间

## 自动记录规则

操作日志会自动记录以下类型的操作：

- **POST请求**: 新增操作
- **PUT请求**: 修改操作  
- **DELETE请求**: 删除操作
- **PATCH请求**: 部分更新操作
- **特殊GET请求**: 导出、下载等操作

排除的路径：
- 登录相关接口
- 健康检查接口
- 静态资源请求
- OPTIONS请求

## 使用建议

1. **定期清理**: 建议定期清理30天以前的操作日志
2. **监控告警**: 可基于操作日志实现异常操作监控
3. **审计分析**: 利用操作日志进行安全审计和行为分析
4. **性能优化**: 大量数据时建议使用分页查询和条件筛选
