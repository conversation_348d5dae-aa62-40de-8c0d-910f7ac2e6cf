package sys_resources

import (
	"context"

	v1 "admin-server/api/sys_resources/v1"
	"admin-server/internal/service"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

func (c *ControllerV1) UploadFile(ctx context.Context, req *v1.UploadFileReq) (res *v1.UploadRes, err error) {
	// 获取HTTP请求对象
	r := ghttp.RequestFromCtx(ctx)

	// 获取group_id参数
	groupId := r.GetForm("group_id").Int64()
	if groupId <= 0 {
		groupId = req.GroupId // 如果表单中没有，使用请求结构体中的
	}
	if groupId <= 0 {
		return nil, gerror.New("请提供有效的分组ID")
	}

	// 获取上传的文件
	file := r.GetUploadFile("file")
	if file == nil {
		return nil, gerror.New("请选择要上传的文件")
	}

	// 添加调试信息
	g.Log().Infof(ctx, "文件类型: %T, 文件名: %s, 文件大小: %d", file, file.Filename, file.Size)

	// 调用上传服务
	resource, err := service.SysResources().FileUpload(ctx, groupId, file)
	if err != nil {
		return nil, err
	}

	return &v1.UploadRes{
		ID:  resource.Id,
		Url: resource.Url,
	}, nil
}
