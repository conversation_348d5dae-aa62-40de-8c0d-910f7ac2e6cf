package zb_article

import (
	"context"
	"encoding/json"

	v1 "admin-server/api/zb_article/v1"
	"admin-server/internal/service"
)

// parseJSONFieldForResponse 解析JSON字段用于响应
func parseJSONFieldForResponse(jsonStr string) interface{} {
	if jsonStr == "" {
		return nil
	}

	var result interface{}
	err := json.Unmarshal([]byte(jsonStr), &result)
	if err != nil {
		return jsonStr // 如果解析失败，返回原字符串
	}

	return result
}

func (c *ControllerV1) GetOne(ctx context.Context, req *v1.GetOneReq) (res *v1.GetOneRes, err error) {
	article, err := service.ZbArticle().GetArticleDetailWithNames(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	// 转换为API响应格式
	articleDetail := &v1.ArticleDetail{
		ID:             article.Id,
		CityId:         article.CityId,
		CityName:       article.CityName,
		CateId:         article.CateId,
		CateName:       article.CateName,
		Title:          article.Title,
		Intro:          article.Intro,
		FullContent:    parseJSONFieldForResponse(article.FullContent),
		ShieidContent:  parseJSONFieldForResponse(article.ShieidContent),
		ViewCount:      article.ViewCount,
		SeoTitle:       article.SeoTitle,
		SeoKeywords:    article.SeoKeywords,
		SeoDescription: article.SeoDescription,
		Pic:            article.Pic,
		Uid:            article.Uid,
		Author:         article.Author,
		Ip:             article.Ip,
		IsDisable:      int(article.IsDisable),
		IsDelete:       int(article.IsDelete),
		CreatedAt:      article.CreatedAt,
		UpdatedAt:      article.UpdatedAt,
		DeletedAt:      article.DeletedAt,
	}

	return &v1.GetOneRes{
		Article: articleDetail,
	}, nil
}
