# 开通城市管理API文档

## 概述

开通城市管理模块提供了城市信息的完整CRUD功能，支持层级结构管理、状态控制、排序等功能。该模块采用树形结构设计，支持多级城市管理。

## 基础信息

- **模块名称**: 开通城市管理
- **基础路径**: `/zb/city`
- **认证方式**: JWT Token
- **权限验证**: 需要相应的城市管理权限

## 数据字段说明

| 字段名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| id | int | 否 | 城市ID，自增主键 | `1` |
| pid | int | 是 | 父级ID，0表示顶级城市 | `0` |
| name | string | 是 | 城市名称，1-255个字符 | `北京市` |
| sort | int | 否 | 排序值，数字越小越靠前 | `1` |
| is_disable | int | 否 | 是否禁用：0=否，1=是 | `0` |
| is_delete | int | 否 | 是否删除：0=否，1=是 | `0` |
| created_at | datetime | 否 | 创建时间 | `2025-01-15 10:00:00` |
| updated_at | datetime | 否 | 更新时间 | `2025-01-15 10:00:00` |
| deleted_at | datetime | 否 | 删除时间 | `null` |

## API接口列表

### 1. 创建开通城市

**接口地址**: `POST /zb/city`

**接口描述**: 创建新的开通城市

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pid | int | 是 | 父级ID，0表示顶级城市 |
| name | string | 是 | 城市名称，1-255个字符 |
| sort | int | 否 | 排序值，默认1 |
| is_disable | int | 否 | 是否禁用：0=否，1=是，默认0 |

**请求示例**:
```json
{
  "pid": 0,
  "name": "北京市",
  "sort": 1,
  "is_disable": 0
}
```

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| id | int | 创建的城市ID |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1
  }
}
```

### 2. 更新开通城市

**接口地址**: `PUT /zb/city/{id}`

**接口描述**: 更新指定的开通城市信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 城市ID（路径参数） |
| pid | int | 是 | 父级ID，0表示顶级城市 |
| name | string | 是 | 城市名称，1-255个字符 |
| sort | int | 否 | 排序值 |
| is_disable | int | 否 | 是否禁用：0=否，1=是 |

**请求示例**:
```json
{
  "pid": 0,
  "name": "北京市",
  "sort": 1,
  "is_disable": 0
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 3. 删除开通城市

**接口地址**: `DELETE /zb/city/{id}`

**接口描述**: 删除指定的开通城市（软删除）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 城市ID（路径参数） |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 4. 获取单个开通城市

**接口地址**: `GET /zb/city/{id}`

**接口描述**: 获取指定的开通城市详细信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 城市ID（路径参数） |

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| id | int | 城市ID |
| pid | int | 父级ID |
| name | string | 城市名称 |
| sort | int | 排序值 |
| is_disable | int | 是否禁用 |
| is_delete | int | 是否删除 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |
| deleted_at | string | 删除时间 |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "pid": 0,
    "name": "北京市",
    "sort": 1,
    "is_disable": 0,
    "is_delete": 0,
    "created_at": "2025-01-15 10:00:00",
    "updated_at": "2025-01-15 10:00:00",
    "deleted_at": null
  }
}
```

### 5. 获取开通城市列表

**接口地址**: `GET /zb/city/list`

**接口描述**: 获取开通城市分页列表

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认10，最大100 |
| name | string | 否 | 城市名称，模糊搜索 |
| is_disable | int | 否 | 是否禁用：0=否，1=是 |
| pid | int | 否 | 父级ID筛选 |

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| list | array | 城市列表 |
| total | int | 总数 |
| page | int | 当前页码 |
| page_size | int | 每页数量 |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "pid": 0,
        "name": "北京市",
        "sort": 1,
        "is_disable": 0,
        "is_delete": 0,
        "created_at": "2025-01-15 10:00:00",
        "updated_at": "2025-01-15 10:00:00",
        "deleted_at": null
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10
  }
}
```

### 6. 获取开通城市树形结构

**接口地址**: `GET /zb/city/tree`

**接口描述**: 获取开通城市的树形结构数据

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| is_disable | int | 否 | 是否禁用：0=否，1=是 |

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| list | array | 城市树形列表 |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "pid": 0,
        "name": "北京市",
        "sort": 1,
        "is_disable": 0,
        "is_delete": 0,
        "created_at": "2025-01-15 10:00:00",
        "updated_at": "2025-01-15 10:00:00",
        "deleted_at": null,
        "children": [
          {
            "id": 2,
            "pid": 1,
            "name": "朝阳区",
            "sort": 1,
            "is_disable": 0,
            "is_delete": 0,
            "created_at": "2025-01-15 10:00:00",
            "updated_at": "2025-01-15 10:00:00",
            "deleted_at": null
          }
        ]
      }
    ]
  }
}
```

### 7. 更新开通城市排序

**接口地址**: `PUT /zb/city/{id}/sort`

**接口描述**: 更新指定城市的排序值

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 城市ID（路径参数） |
| sort | int | 是 | 排序值 |

**请求示例**:
```json
{
  "sort": 10
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 8. 更新开通城市状态

**接口地址**: `PUT /zb/city/{id}/status`

**接口描述**: 更新指定城市的启用/禁用状态

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 城市ID（路径参数） |
| is_disable | int | 是 | 是否禁用：0=否，1=是 |

**请求示例**:
```json
{
  "is_disable": 1
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 常见错误示例

### 参数验证错误
```json
{
  "code": 400,
  "message": "城市名称不能为空",
  "data": null
}
```

### 业务逻辑错误
```json
{
  "code": 400,
  "message": "同级城市名称已存在",
  "data": null
}
```

### 资源不存在
```json
{
  "code": 404,
  "message": "城市不存在",
  "data": null
}
```

## 业务规则

1. **层级关系**：
   - 支持多级城市结构
   - pid=0表示顶级城市
   - 不能设置自己为父级城市

2. **名称唯一性**：
   - 同级城市名称不能重复
   - 不同级别可以有相同名称

3. **删除限制**：
   - 有子城市的城市不能删除
   - 删除采用软删除方式

4. **状态控制**：
   - 支持启用/禁用状态切换
   - 禁用的城市不影响子城市状态

5. **排序规则**：
   - 按sort字段升序排列
   - sort相同时按id升序排列

## 使用示例

### 创建城市
```bash
curl -X POST "http://localhost:8000/zb/city" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 0,
    "name": "北京市",
    "sort": 1,
    "is_disable": 0
  }'
```

### 获取城市列表
```bash
curl -X GET "http://localhost:8000/zb/city/list?page=1&page_size=10" \
  -H "Authorization: Bearer your_token"
```

### 获取城市树形结构
```bash
curl -X GET "http://localhost:8000/zb/city/tree" \
  -H "Authorization: Bearer your_token"
```

### 更新城市状态
```bash
curl -X PUT "http://localhost:8000/zb/city/1/status" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "is_disable": 1
  }'
```
