package task

import (
	"admin-server/internal/service"
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcron"
)

// 初始化搜索趋势更新定时任务
func InitSearchTrendTask() {
	// 每小时的第2分钟执行
	cronId, err := gcron.Add(context.Background(), "0 0 * * * *", func(ctx context.Context) {
		updateSearchTrend(ctx)
	})

	if err != nil {
		g.Log().Error(context.Background(), "搜索趋势定时任务启动失败:", err)
	} else {
		g.Log().Info(context.Background(), "搜索趋势定时任务启动成功, CronID:", cronId)

		// 打印当前所有定时任务
		entries := gcron.Entries()
		g.Log().Info(context.Background(), "当前定时任务数量:", len(entries))
		for i, entry := range entries {
			g.Log().Info(context.Background(), "定时任务", i+1, ":", entry)
		}
	}
}

// 手动触发搜索趋势更新（用于测试）
func TriggerSearchTrendUpdate() {
	g.Log().Info(context.Background(), "手动触发搜索趋势更新...")
	updateSearchTrend(context.Background())
}

// 更新搜索趋势
func updateSearchTrend(ctx context.Context) {
	g.Log().Info(ctx, "开始更新搜索趋势...")

	err := service.Search().UpdateKeywordTrend(ctx)
	if err != nil {
		g.Log().Error(ctx, "更新搜索趋势失败:", err)
	} else {
		g.Log().Info(ctx, "搜索趋势更新完成")
	}
}
