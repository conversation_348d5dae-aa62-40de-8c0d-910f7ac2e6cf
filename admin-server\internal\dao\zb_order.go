// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package dao

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// internalZbOrderDao is internal type for wrapping dao logic.
type internalZbOrderDao struct {
	table   string              // table is the underlying table name of the DAO.
	group   string              // group is the database configuration group name of current DAO.
	columns ZbOrderColumns      // columns contains all the column names of Table for convenient usage.
}

// ZbOrderColumns defines and stores column names for table zb_order.
type ZbOrderColumns struct {
	Id            string // 主键ID
	OrderSn       string // 订单编号
	GoodId        string // 套餐id
	GoodName      string // 套餐名称
	GoodPrice     string // 套餐单价
	Effective     string // 会员有效期；单位月
	CityCount     string // 选择的城市数量
	UserId        string // 会员id
	Price         string // 需支付金额
	Amount        string // 支付金额
	PayStatus     string // 支付状态 1已支付 0未支付
	TransactionId string // 微信支付订单号
	TradeType     string // 交易类型
	TradeState    string // 交易状态
	PayResult     string // 支付返回信息json格式
	Remark        string // 订单备注
	PayAt         string // 支付时间
	CreatedAt     string // 创建时间
	UpdatedAt     string // 更新时间
}

// zbOrderColumns holds the columns for table zb_order.
var zbOrderColumns = ZbOrderColumns{
	Id:            "id",
	OrderSn:       "order_sn",
	GoodId:        "good_id",
	GoodName:      "good_name",
	GoodPrice:     "good_price",
	Effective:     "effective",
	CityCount:     "city_count",
	UserId:        "user_id",
	Price:         "price",
	Amount:        "amount",
	PayStatus:     "pay_status",
	TransactionId: "transaction_id",
	TradeType:     "trade_type",
	TradeState:    "trade_state",
	PayResult:     "pay_result",
	Remark:        "remark",
	PayAt:         "pay_at",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
}

// NewZbOrderDao creates and returns a new DAO object for table data access.
func NewZbOrderDao() *internalZbOrderDao {
	return &internalZbOrderDao{
		group:   "default",
		table:   "zb_order",
		columns: zbOrderColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *internalZbOrderDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *internalZbOrderDao) Table() string {
	return dao.table
}

// Columns returns the columns of current dao.
func (dao *internalZbOrderDao) Columns() ZbOrderColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *internalZbOrderDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *internalZbOrderDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *internalZbOrderDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}

// ZbOrder is the dao object for table zb_order.
var ZbOrder = NewZbOrderDao()
