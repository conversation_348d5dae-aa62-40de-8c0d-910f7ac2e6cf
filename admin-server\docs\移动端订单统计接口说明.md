# 移动端订单统计接口说明

## 📋 接口概述

为移动端新增订单统计接口，用于统计用户的订单数据，包括已支付订单数、已支付总金额、未支付订单数和总订单数。

## 🔧 接口详情

### API定义

#### 请求结构
```go
// GetMyStatsReq 获取我的订单统计请求
type GetMyStatsReq struct {
    g.Meta `path:"/zb_order/my-stats" tags:"Order" method:"get" summary:"获取我的订单统计"`
    OpenId string `json:"openid" v:"required|length:1,100" dc:"用户OpenID"`
}
```

#### 响应结构
```go
// GetMyStatsRes 获取我的订单统计响应
type GetMyStatsRes struct {
    PaidOrderCount   int     `json:"paid_order_count" dc:"已支付订单数"`
    PaidTotalAmount  float64 `json:"paid_total_amount" dc:"已支付总金额"`
    UnpaidOrderCount int     `json:"unpaid_order_count" dc:"未支付订单数"`
    TotalOrderCount  int     `json:"total_order_count" dc:"总订单数"`
}
```

### 业务逻辑

```go
func (s *sZbOrder) GetMyStats(ctx context.Context, req *v1.GetMyStatsReq) (*v1.GetMyStatsRes, error) {
    // 1. 根据OpenID获取用户信息
    user, err := service.ZbUser().GetUserByOpenid(ctx, req.OpenId)
    if err != nil {
        return nil, fmt.Errorf("用户不存在或获取用户信息失败")
    }
    
    // 2. 构建查询条件
    model := dao.ZbOrder.Ctx(ctx).Where("user_id", user.Id)
    
    // 3. 统计总订单数
    totalOrderCount, err := model.Count()
    
    // 4. 统计已支付订单数
    paidOrderCount, err := model.Where("pay_status", 1).Count()
    
    // 5. 统计未支付订单数
    unpaidOrderCount := totalOrderCount - paidOrderCount
    
    // 6. 统计已支付总金额
    paidTotalAmount, err := model.Where("pay_status", 1).Sum("amount")
    
    return &v1.GetMyStatsRes{
        PaidOrderCount:   paidOrderCount,
        PaidTotalAmount:  paidTotalAmount,
        UnpaidOrderCount: unpaidOrderCount,
        TotalOrderCount:  totalOrderCount,
    }, nil
}
```

### 接口信息

- **URL**: `GET /m/api/zb_order/my-stats`
- **权限**: 无需token验证，通过openid获取用户
- **参数**: `openid` (必填)
- **返回**: 用户订单统计数据

### 统计维度

1. **已支付订单数** (`paid_order_count`)
   - 统计条件: `pay_status = 1`
   - 数据类型: `int`

2. **已支付总金额** (`paid_total_amount`)
   - 统计条件: `pay_status = 1`
   - 统计字段: `amount` (实际支付金额)
   - 数据类型: `float64`

3. **未支付订单数** (`unpaid_order_count`)
   - 计算方式: `总订单数 - 已支付订单数`
   - 数据类型: `int`

4. **总订单数** (`total_order_count`)
   - 统计条件: 该用户的所有订单
   - 数据类型: `int`

## 📊 API使用示例

### 请求示例
```bash
curl "http://localhost:8000/m/api/zb_order/my-stats?openid=oXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
```

### 响应示例

#### 成功响应
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "paid_order_count": 5,
        "paid_total_amount": 1500.00,
        "unpaid_order_count": 2,
        "total_order_count": 7
    }
}
```

#### 用户不存在
```json
{
    "code": 1,
    "message": "用户不存在或获取用户信息失败"
}
```

#### 参数错误
```json
{
    "code": 1,
    "message": "openid不能为空"
}
```

### 数据解读

以上示例数据表示：
- 用户总共有 **7** 个订单
- 其中 **5** 个订单已支付，总金额 **1500.00** 元
- 还有 **2** 个订单未支付

## 🎯 业务场景

### 1. 移动端个人中心
```javascript
// 获取用户订单统计
async function getUserOrderStats(openid) {
    const response = await fetch(`/m/api/zb_order/my-stats?openid=${openid}`);
    const result = await response.json();
    
    if (result.code === 0) {
        const stats = result.data;
        // 显示统计数据
        document.getElementById('paid-count').textContent = stats.paid_order_count;
        document.getElementById('paid-amount').textContent = `¥${stats.paid_total_amount}`;
        document.getElementById('unpaid-count').textContent = stats.unpaid_order_count;
        document.getElementById('total-count').textContent = stats.total_order_count;
    }
}
```

### 2. 数据展示组件
```html
<div class="order-stats">
    <div class="stat-item">
        <div class="stat-number" id="total-count">0</div>
        <div class="stat-label">总订单</div>
    </div>
    <div class="stat-item">
        <div class="stat-number" id="paid-count">0</div>
        <div class="stat-label">已支付</div>
    </div>
    <div class="stat-item">
        <div class="stat-number" id="unpaid-count">0</div>
        <div class="stat-label">未支付</div>
    </div>
    <div class="stat-item">
        <div class="stat-amount">¥<span id="paid-amount">0.00</span></div>
        <div class="stat-label">已支付金额</div>
    </div>
</div>
```

## 🛡️ 安全特性

### 1. 用户验证
- **OpenID验证**: 通过OpenID获取用户身份
- **数据隔离**: 只能查看自己的订单统计
- **参数校验**: 严格的OpenID格式验证

### 2. 性能优化
- **单次查询**: 一次请求获取所有统计数据
- **索引优化**: 建议在 `user_id` 和 `pay_status` 上建立复合索引
- **缓存策略**: 可考虑添加短期缓存

### 3. 数据准确性
- **实时统计**: 基于数据库实时计算
- **事务一致性**: 确保统计数据的准确性
- **错误处理**: 完善的错误处理机制

## 🧪 测试用例

### 1. 正常用户测试
```bash
# 测试有订单的用户
curl "http://localhost:8000/m/api/zb_order/my-stats?openid=valid_openid_with_orders"

# 预期响应：返回正确的统计数据
{
    "code": 0,
    "data": {
        "paid_order_count": 3,
        "paid_total_amount": 900.00,
        "unpaid_order_count": 1,
        "total_order_count": 4
    }
}
```

### 2. 新用户测试
```bash
# 测试没有订单的用户
curl "http://localhost:8000/m/api/zb_order/my-stats?openid=valid_openid_no_orders"

# 预期响应：所有统计数据为0
{
    "code": 0,
    "data": {
        "paid_order_count": 0,
        "paid_total_amount": 0.00,
        "unpaid_order_count": 0,
        "total_order_count": 0
    }
}
```

### 3. 无效用户测试
```bash
# 测试不存在的OpenID
curl "http://localhost:8000/m/api/zb_order/my-stats?openid=invalid_openid"

# 预期响应：用户不存在错误
{
    "code": 1,
    "message": "用户不存在或获取用户信息失败"
}
```

### 4. 参数验证测试
```bash
# 测试空OpenID
curl "http://localhost:8000/m/api/zb_order/my-stats?openid="

# 预期响应：参数验证错误
{
    "code": 1,
    "message": "openid不能为空"
}
```

## 📈 性能优化建议

### 1. 数据库索引
```sql
-- 建议添加复合索引
ALTER TABLE zb_order ADD INDEX idx_user_pay_status (user_id, pay_status);

-- 或者分别添加索引
ALTER TABLE zb_order ADD INDEX idx_user_id (user_id);
ALTER TABLE zb_order ADD INDEX idx_pay_status (pay_status);
```

### 2. 缓存策略
```go
// 可以添加Redis缓存
func (s *sZbOrder) GetMyStatsWithCache(ctx context.Context, req *v1.GetMyStatsReq) (*v1.GetMyStatsRes, error) {
    // 1. 先从缓存获取
    cacheKey := fmt.Sprintf("user_order_stats:%s", req.OpenId)
    
    // 2. 缓存未命中时查询数据库
    stats := s.GetMyStats(ctx, req)
    
    // 3. 设置缓存（5分钟过期）
    // redis.Set(cacheKey, stats, 5*time.Minute)
    
    return stats, nil
}
```

### 3. 批量查询优化
```go
// 对于高并发场景，可以考虑批量查询
func (s *sZbOrder) GetMultiUserStats(ctx context.Context, openIds []string) (map[string]*v1.GetMyStatsRes, error) {
    // 批量获取用户ID
    // 批量统计订单数据
    // 返回映射结果
}
```

## 📁 文件结构

```
admin-server/
├── api/zb_order/
│   ├── zb_order.go                           # 新增GetMyStats接口
│   └── v1/zb_order.go                        # 新增请求响应结构
├── internal/
│   ├── controller/zb_order/
│   │   └── zb_order_v1_get_my_stats.go       # 统计控制器
│   ├── logic/zb_order/
│   │   └── zb_order.go                       # 新增GetMyStats方法
│   ├── service/
│   │   └── zb_order.go                       # 新增接口定义
│   └── cmd/
│       └── cmd.go                            # 新增路由配置
└── docs/
    └── 移动端订单统计接口说明.md              # 本文档
```

## ⚠️ 注意事项

### 1. 数据准确性
- 统计数据基于 `amount` 字段（实际支付金额）
- 确保支付状态的准确性
- 注意处理数据类型转换

### 2. 性能考虑
- 大量订单时查询可能较慢
- 建议添加适当的数据库索引
- 考虑添加缓存机制

### 3. 业务逻辑
- 未支付订单数 = 总订单数 - 已支付订单数
- 只统计实际支付金额，不包含优惠等
- 支持实时数据更新

---

**开发状态**: ✅ 已完成  
**接口类型**: 移动端统计接口  
**权限要求**: 无需token，通过openid验证  
**缓存建议**: 5分钟短期缓存  
**文档版本**: v1.0  
**完成时间**: 2025-01-23
