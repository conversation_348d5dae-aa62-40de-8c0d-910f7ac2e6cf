package middleware

import (
	"admin-server/internal/dao"
	"admin-server/internal/model/entity"
	"admin-server/internal/packed"
	"admin-server/internal/service"
	"context"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// OperateLogConfig 操作日志配置
type OperateLogConfig struct {
	// 是否启用操作日志
	Enabled bool
	// 排除的路径
	ExcludePaths []string
	// 排除的方法
	ExcludeMethods []string
	// 是否记录请求体
	LogRequestBody bool
	// 是否记录响应体
	LogResponseBody bool
	// 响应体最大长度
	MaxResponseBodySize int
}

// 默认配置
var defaultConfig = OperateLogConfig{
	Enabled: true,
	ExcludePaths: []string{
		"/auth/login",
		"/auth/refresh",
		"/health",
		"/metrics",
		"/favicon.ico",
	},
	ExcludeMethods: []string{
		"OPTIONS",
	},
	LogRequestBody:      true,
	LogResponseBody:     true,
	MaxResponseBodySize: 10240, // 10KB
}

// OperateLog 操作日志中间件
func OperateLog(config ...OperateLogConfig) func(r *ghttp.Request) {
	cfg := defaultConfig
	if len(config) > 0 {
		cfg = config[0]
	}

	return func(r *ghttp.Request) {
		// 检查是否启用
		if !cfg.Enabled {
			r.Middleware.Next()
			return
		}

		// 检查是否排除该路径
		if isExcludedOperateLogPath(r.URL.Path, cfg.ExcludePaths) {
			r.Middleware.Next()
			return
		}

		// 检查是否排除该方法
		if isExcludedOperateLogMethod(r.Method, cfg.ExcludeMethods) {
			r.Middleware.Next()
			return
		}

		// 检查是否需要记录操作日志的接口
		if !shouldLogOperation(r.URL.Path, r.Method) {
			r.Middleware.Next()
			return
		}

		// 记录开始时间
		startTime := time.Now()

		// 获取管理员信息
		adminId := r.GetCtxVar("admin_id").Int64()

		// 如果没有管理员信息，跳过记录
		if adminId == 0 {
			r.Middleware.Next()
			return
		}

		// 通过adminId获取用户名
		username := getUsernameByAdminId(r.Context(), adminId)

		// 获取操作标题
		title := getOperationTitle(r.URL.Path, r.Method)

		// 执行下一个中间件/处理器
		r.Middleware.Next()

		// 获取响应状态
		var responseStatus int = 1 // 默认成功
		if r.Response.Status >= 400 {
			responseStatus = 2 // 失败
		}

		// 简化响应体处理 - 不记录响应体内容，避免复杂的拦截逻辑
		var responseData interface{}
		if cfg.LogResponseBody {
			// 只记录基本的响应信息
			responseData = map[string]interface{}{
				"status": r.Response.Status,
				"note":   "响应体内容已省略",
			}
		}

		// 异步记录操作日志
		go func() {
			err := service.SysOperateLog().RecordOperateLog(
				r.Context(),
				r,
				adminId,
				username,
				title,
				startTime,
				responseStatus,
				responseData,
			)
			if err != nil {
				g.Log().Error(r.Context(), "记录操作日志失败:", err)
			}
		}()
	}
}

// 响应写入器相关代码已移除，使用简化的响应处理方式

// isExcludedOperateLogPath 检查是否为排除路径
func isExcludedOperateLogPath(path string, excludePaths []string) bool {
	for _, excludePath := range excludePaths {
		if strings.HasPrefix(path, excludePath) {
			return true
		}
	}
	return false
}

// isExcludedOperateLogMethod 检查是否为排除方法
func isExcludedOperateLogMethod(method string, excludeMethods []string) bool {
	for _, excludeMethod := range excludeMethods {
		if method == excludeMethod {
			return true
		}
	}
	return false
}

// shouldLogOperation 判断是否应该记录操作日志
func shouldLogOperation(path, method string) bool {
	// 只记录对数据有影响的操作
	switch method {
	case "POST", "PUT", "DELETE", "PATCH":
		return true
	case "GET":
		// 某些GET操作也需要记录，比如导出
		if strings.Contains(path, "export") || strings.Contains(path, "download") {
			return true
		}
		return false
	default:
		return false
	}
}

// getOperationTitle 获取操作标题
func getOperationTitle(path, method string) string {
	// 根据路径和方法生成操作标题
	pathParts := strings.Split(strings.Trim(path, "/"), "/")
	if len(pathParts) == 0 {
		return "未知操作"
	}

	// 提取模块名
	module := pathParts[0]
	moduleName := getModuleName(module)

	// 根据方法和路径生成操作描述
	switch method {
	case "POST":
		if strings.Contains(path, "create") || len(pathParts) == 1 {
			return "新增" + moduleName
		}
		return "操作" + moduleName
	case "PUT":
		if strings.Contains(path, "update") || (len(pathParts) >= 2 && isOperateLogNumeric(pathParts[1])) {
			return "修改" + moduleName
		}
		if strings.Contains(path, "roles") {
			return "分配" + moduleName + "角色"
		}
		if strings.Contains(path, "menus") {
			return "分配" + moduleName + "权限"
		}
		if strings.Contains(path, "disable") {
			return "禁用" + moduleName
		}
		if strings.Contains(path, "enable") {
			return "启用" + moduleName
		}
		return "更新" + moduleName
	case "DELETE":
		if strings.Contains(path, "clear") {
			return "清空" + moduleName
		}
		return "删除" + moduleName
	case "GET":
		if strings.Contains(path, "export") {
			return "导出" + moduleName
		}
		if strings.Contains(path, "download") {
			return "下载" + moduleName
		}
		return "查看" + moduleName
	default:
		return "操作" + moduleName
	}
}

// getModuleName 获取模块中文名
func getModuleName(module string) string {
	moduleNames := map[string]string{
		"sys_admin":           "管理员",
		"sys_role":            "角色",
		"sys_menu":            "菜单",
		"sys_config":          "配置",
		"sys_config_group":    "配置分组",
		"sys_login_log":       "登录日志",
		"sys_operate_log":     "操作日志",
		"sys_dict_group":      "字典分组",
		"sys_dict":            "字典项",
		"sys_resources_group": "素材分组",
		"sys_resources":       "素材",
		"wechat_menu":         "微信公众号菜单",
		"zb_city":             "开通城市",
		"zb_cate":             "招标类别",
		"zb_good":             "套餐管理",
		"zb_user":             "会员管理",
		"zb_article":          "招标信息",
		"zb_order":            "订单",
	}

	if name, exists := moduleNames[module]; exists {
		return name
	}

	// 默认处理：去掉sys_前缀，首字母大写
	if strings.HasPrefix(module, "sys_") {
		module = strings.TrimPrefix(module, "sys_")
	}

	return strings.Title(module)
}

// isOperateLogNumeric 检查字符串是否为数字
func isOperateLogNumeric(s string) bool {
	if len(s) == 0 {
		return false
	}
	for _, r := range s {
		if r < '0' || r > '9' {
			return false
		}
	}
	return true
}

// getUsernameByAdminId 通过管理员ID获取用户名
func getUsernameByAdminId(ctx context.Context, adminId int64) string {
	var admin entity.SysAdmins
	err := dao.SysAdmins.Ctx(ctx).Where("id", adminId).Where("is_delete", packed.NO_DELETE).Scan(&admin)
	if err != nil {
		g.Log().Error(ctx, "获取管理员信息失败:", err)
		return "unknown"
	}

	if admin.Id == 0 {
		return "unknown"
	}

	return admin.Username
}
