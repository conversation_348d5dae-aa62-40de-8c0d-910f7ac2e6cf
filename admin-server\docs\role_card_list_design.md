# 角色卡片列表API设计说明

## 设计目标

根据前端卡片式角色列表的展示需求，优化角色列表API的返回格式，提供更丰富的统计信息以支持卡片式展示。

## 卡片展示需求分析

### 卡片内容要素
1. **角色名称** - 主标题显示
2. **排序信息** - 角色优先级
3. **权限数量** - 该角色拥有的菜单权限数量
4. **用户数量** - 使用该角色的用户数量
5. **主要权限** - 显示前5个主要权限名称
6. **状态信息** - 启用/禁用状态
7. **操作按钮** - 编辑、删除、权限分配等

### 界面样式特点
- 卡片式布局，每个角色一张卡片
- 不同角色类型用不同颜色区分
- 显示统计数字（权限数、用户数）
- 权限列表以标签形式展示
- 状态用颜色和文字标识

## API优化方案

### 新增字段说明

#### RoleInfo结构体扩展

```go
type RoleInfo struct {
    ID          int64          `json:"id" dc:"角色ID"`
    Name        string         `json:"name" dc:"角色名称"`
    Sort        int            `json:"sort" dc:"排序"`
    Remark      string         `json:"remark" dc:"备注"`
    IsDisable   packed.Disable `json:"is_disable" dc:"是否禁用"`
    CreatedAt   *gtime.Time    `json:"created_at" dc:"创建时间"`
    UpdatedAt   *gtime.Time    `json:"updated_at" dc:"更新时间"`
    MenuCount   int            `json:"menu_count" dc:"权限数量"`      // 新增
    UserCount   int            `json:"user_count" dc:"用户数量"`      // 新增
    MainMenus   []string       `json:"main_menus" dc:"主要权限列表"`   // 新增
}
```

#### 新增字段详细说明

1. **MenuCount (权限数量)**
   - 类型: `int`
   - 说明: 该角色拥有的菜单权限总数
   - 计算方式: 统计 `sys_role_menu` 表中该角色的记录数
   - 用途: 在卡片上显示权限数量统计

2. **UserCount (用户数量)**
   - 类型: `int`
   - 说明: 使用该角色的用户总数
   - 计算方式: 统计 `sys_admin_role` 表中该角色的记录数
   - 用途: 在卡片上显示用户数量统计

3. **MainMenus (主要权限列表)**
   - 类型: `[]string`
   - 说明: 该角色的主要权限名称列表（最多5个）
   - 计算方式: 查询该角色的前5个菜单权限名称
   - 用途: 在卡片上以标签形式展示主要权限

## 实现细节

### 数据查询优化

#### GetRoleStatistics方法
```go
func (s SsysRole) GetRoleStatistics(ctx context.Context, roleId int64) (menuCount int, userCount int, mainMenus []string, err error) {
    // 1. 获取权限数量
    menuCount, err = dao.SysRoleMenu.Ctx(ctx).Where("role_id", roleId).Count()
    
    // 2. 获取用户数量
    userCount, err = dao.SysAdminRole.Ctx(ctx).Where("role_id", roleId).Count()
    
    // 3. 获取主要权限（前5个）
    var roleMenus []entity.SysRoleMenu
    err = dao.SysRoleMenu.Ctx(ctx).Where("role_id", roleId).Limit(5).Scan(&roleMenus)
    
    // 4. 查询菜单名称
    if len(roleMenus) > 0 {
        menuIds := make([]int64, len(roleMenus))
        for i, rm := range roleMenus {
            menuIds[i] = rm.MenuId
        }
        
        var menus []entity.SysMenu
        err = dao.SysMenu.Ctx(ctx).Where("id", menuIds).Where("is_delete", packed.NO_DELETE).Scan(&menus)
        
        mainMenus = make([]string, len(menus))
        for i, menu := range menus {
            mainMenus[i] = menu.MenuName
        }
    }
    
    return menuCount, userCount, mainMenus, nil
}
```

### 性能考虑

1. **批量查询**: 在GetList方法中为每个角色调用统计方法
2. **错误处理**: 统计信息获取失败时使用默认值，不影响主要数据
3. **限制数量**: 主要权限列表限制为5个，避免数据过多
4. **索引优化**: 确保相关查询字段有适当的数据库索引

## API响应示例

### 卡片式列表响应格式

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "超级管理员",
        "sort": 1,
        "remark": "超级管理员角色",
        "is_disable": 0,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z",
        "menu_count": 8,
        "user_count": 1,
        "main_menus": ["系统管理", "用户管理", "角色管理", "菜单管理", "权限管理"]
      },
      {
        "id": 2,
        "name": "系统管理员",
        "sort": 2,
        "remark": "系统管理员角色",
        "is_disable": 0,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z",
        "menu_count": 4,
        "user_count": 3,
        "main_menus": ["系统管理", "用户管理", "角色管理"]
      },
      {
        "id": 3,
        "name": "内容编辑员",
        "sort": 3,
        "remark": "内容编辑员角色",
        "is_disable": 0,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z",
        "menu_count": 3,
        "user_count": 5,
        "main_menus": ["基本信息", "content:create", "content:update"]
      },
      {
        "id": 4,
        "name": "审核员",
        "sort": 4,
        "remark": "审核员角色",
        "is_disable": 1,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z",
        "menu_count": 2,
        "user_count": 0,
        "main_menus": ["基本信息", "审核权限"]
      }
    ],
    "total": 4,
    "page": 1,
    "page_size": 10
  }
}
```

## 前端集成建议

### 卡片渲染逻辑

```javascript
function renderRoleCard(role) {
    return `
        <div class="role-card ${role.is_disable ? 'disabled' : 'enabled'}">
            <div class="card-header">
                <h3 class="role-name">${role.name}</h3>
                <span class="role-sort">排序: ${role.sort}</span>
            </div>
            
            <div class="card-stats">
                <div class="stat-item">
                    <span class="stat-label">权限数量:</span>
                    <span class="stat-value">${role.menu_count}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">用户数量:</span>
                    <span class="stat-value">${role.user_count}</span>
                </div>
            </div>
            
            <div class="main-menus">
                <span class="menus-label">主要权限:</span>
                <div class="menu-tags">
                    ${role.main_menus.map(menu => `<span class="menu-tag">${menu}</span>`).join('')}
                    ${role.menu_count > 5 ? `<span class="more-tag">+${role.menu_count - 5}</span>` : ''}
                </div>
            </div>
            
            <div class="card-footer">
                <span class="status ${role.is_disable ? 'disabled' : 'enabled'}">
                    ${role.is_disable ? '禁用' : '启用'}
                </span>
                <div class="actions">
                    <button onclick="editRole(${role.id})">编辑</button>
                    <button onclick="assignPermissions(${role.id})">分配权限</button>
                    <button onclick="deleteRole(${role.id})">删除</button>
                </div>
            </div>
        </div>
    `;
}
```

### 样式建议

```css
.role-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    margin: 8px;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.role-card.disabled {
    opacity: 0.6;
    background: #f5f5f5;
}

.card-stats {
    display: flex;
    gap: 16px;
    margin: 12px 0;
}

.stat-value {
    font-weight: bold;
    color: #1976d2;
}

.menu-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 4px;
}

.menu-tag {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.more-tag {
    background: #f5f5f5;
    color: #666;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}
```

## 优势总结

1. **丰富的展示信息**: 提供权限数量、用户数量等统计信息
2. **直观的权限预览**: 显示主要权限名称，便于快速了解角色功能
3. **性能优化**: 合理的查询策略，避免N+1查询问题
4. **向后兼容**: 新增字段不影响现有功能
5. **灵活的前端适配**: 支持多种展示方式，满足不同UI需求

通过这种设计，前端可以轻松实现美观、信息丰富的卡片式角色列表界面，提升用户体验。
