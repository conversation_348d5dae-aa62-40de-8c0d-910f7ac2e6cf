// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// WechatConfigDao is the data access object for the table wechat_config.
type WechatConfigDao struct {
	table    string                   // table is the underlying table name of the DAO.
	group    string                   // group is the database configuration group name of the current DAO.
	columns  WechatConfigColumns      // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler       // handlers for customized model modification.
}

// WechatConfigColumns defines and stores column names for the table wechat_config.
type WechatConfigColumns struct {
	Id               string //
	Appid            string // 微信公众号AppID
	AppSecret        string // 微信公众号AppSecret
	AutoReplyEnabled string // 关注自动回复开关：0=关闭，1=开启
	AutoReplyText string // 关注自动回复语
	ServerUrl        string // 服务器地址URL
	Token            string // 微信Token
	EncodingAesKey   string // 消息加解密密钥
	EncryptMode      string // 消息加解密方式：plaintext=明文模式，compatible=兼容模式，safe=安全模式
	CreatedAt        string // 创建时间
	UpdatedAt        string // 更新时间
}

// wechatConfigColumns holds the columns for the table wechat_config.
var wechatConfigColumns = WechatConfigColumns{
	Id:               "id",
	Appid:            "appid",
	AppSecret:        "app_secret",
	AutoReplyEnabled: "auto_reply_enabled",
	AutoReplyText: "auto_reply_text",
	ServerUrl:        "server_url",
	Token:            "token",
	EncodingAesKey:   "encoding_aes_key",
	EncryptMode:      "encrypt_mode",
	CreatedAt:        "created_at",
	UpdatedAt:        "updated_at",
}

// NewWechatConfigDao creates and returns a new DAO object for table data access.
func NewWechatConfigDao(handlers ...gdb.ModelHandler) *WechatConfigDao {
	return &WechatConfigDao{
		group:    "default",
		table:    "wechat_config",
		columns:  wechatConfigColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *WechatConfigDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *WechatConfigDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *WechatConfigDao) Columns() WechatConfigColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *WechatConfigDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *WechatConfigDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *WechatConfigDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}