<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>浏览记录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            -webkit-overflow-scrolling: touch;
        }
        .phone-container {
            overflow-y: auto;
            position: relative;
            background: white;
            min-height: 100vh;
            -webkit-overflow-scrolling: touch;
        }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .mini-capsule {
            position: absolute;
            top: 8px;
            right: 12px;
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .swipe-item { transition: transform 0.3s ease; }

        /* 登录弹窗样式 */
        .vip-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .vip-modal-content {
            background: white;
            border-radius: 20px;
            padding: 30px 20px;
            margin: 20px;
            text-align: center;
            max-width: 320px;
            width: 100%;
            animation: modalSlideIn 0.3s ease-out;
        }
        @keyframes modalSlideIn {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 顶部导航 -->
        <div class="gradient-bg px-4 pt-4 pb-4">
            <div class="flex items-center space-x-3">
                <h1 class="text-white text-lg font-semibold flex-1">我的浏览</h1>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="bg-white flex-1 overflow-y-auto">

            <!-- 时间分组 -->
            <div class="px-4 py-2">
                <!-- 今天 -->
                <div class="mb-4">
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <h3 class="text-sm font-semibold text-gray-800">今天</h3>
                        <span class="text-xs text-gray-500">6条记录</span>
                    </div>
                    <div class="space-y-3 mt-3">
                        <!-- 浏览记录项1 -->
                        <div class="swipe-item bg-white rounded-xl p-4 card-shadow border border-gray-100">
                            <div class="flex items-start space-x-3">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between mb-1">
                                        <h4 class="text-sm font-semibold text-gray-800 line-clamp-2">某市政府办公大楼装修改造工程招标公告</h4>
                                        <span class="text-xs text-gray-400 ml-2">14:30</span>
                                    </div>
                                    <div class="flex items-center space-x-2 mb-2">
                                        <span class="bg-red-100 text-red-600 px-2 py-1 rounded text-xs">紧急</span>
                                        <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded text-xs">工程建设</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-600">预算：500-800万元</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 浏览记录项2 -->
                        <div class="swipe-item bg-white rounded-xl p-4 card-shadow border border-gray-100">
                            <div class="flex items-start space-x-3">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between mb-1">
                                        <h4 class="text-sm font-semibold text-gray-800 line-clamp-2">智慧城市信息化系统建设项目采购公告</h4>
                                        <span class="text-xs text-gray-400 ml-2">12:15</span>
                                    </div>
                                    <div class="flex items-center space-x-2 mb-2">
                                        <span class="bg-orange-100 text-orange-600 px-2 py-1 rounded text-xs">热门</span>
                                        <span class="bg-purple-100 text-purple-600 px-2 py-1 rounded text-xs">信息化</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-600">预算：1000-1500万元</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 浏览记录项3 -->
                        <div class="swipe-item bg-white rounded-xl p-4 card-shadow border border-gray-100">
                            <div class="flex items-start space-x-3">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between mb-1">
                                        <h4 class="text-sm font-semibold text-gray-800 line-clamp-2">医疗设备采购项目招标公告</h4>
                                        <span class="text-xs text-gray-400 ml-2">09:45</span>
                                    </div>
                                    <div class="flex items-center space-x-2 mb-2">
                                        <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded text-xs">新发布</span>
                                        <span class="bg-pink-100 text-pink-600 px-2 py-1 rounded text-xs">医疗设备</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-600">预算：300-500万元</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 昨天 -->
                <div class="mb-4">
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <h3 class="text-sm font-semibold text-gray-800">昨天</h3>
                        <span class="text-xs text-gray-500">8条记录</span>
                    </div>
                    <div class="space-y-3 mt-3">
                        <!-- 浏览记录项4 -->
                        <div class="swipe-item bg-white rounded-xl p-4 card-shadow border border-gray-100">
                            <div class="flex items-start space-x-3">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between mb-1">
                                        <h4 class="text-sm font-semibold text-gray-800 line-clamp-2">城市道路改造工程施工招标</h4>
                                        <span class="text-xs text-gray-400 ml-2">16:20</span>
                                    </div>
                                    <div class="flex items-center space-x-2 mb-2">
                                        <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs">进行中</span>
                                        <span class="bg-yellow-100 text-yellow-600 px-2 py-1 rounded text-xs">道路工程</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-600">预算：2000-3000万元</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 浏览记录项5 -->
                        <div class="swipe-item bg-white rounded-xl p-4 card-shadow border border-gray-100">
                            <div class="flex items-start space-x-3">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between mb-1">
                                        <h4 class="text-sm font-semibold text-gray-800 line-clamp-2">环保设备采购及安装项目</h4>
                                        <span class="text-xs text-gray-400 ml-2">14:10</span>
                                    </div>
                                    <div class="flex items-center space-x-2 mb-2">
                                        <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs">环保</span>
                                        <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded text-xs">设备采购</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-600">预算：800-1200万元</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 更早 -->
                <div class="mb-4">
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <h3 class="text-sm font-semibold text-gray-800">更早</h3>
                        <span class="text-xs text-gray-500">142条记录</span>
                    </div>
                    <div class="text-center py-6">
                        <button class="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-2 rounded-full text-sm font-medium">
                            查看更多历史记录
                        </button>
                    </div>
                </div>
            </div>
        </div>


    </div>

    {{if not .is_logged_in}}
    <!-- 登录弹窗 -->
    <div id="loginModal" class="vip-modal">
        <div class="vip-modal-content">
            <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-user text-white text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-3">请先登录</h3>
            <p class="text-gray-600 text-sm mb-6 leading-relaxed">
                登录后即可开通会员套餐<br>
                享受更多专业服务
            </p>

            <!-- 登录优势 -->
            <div class="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6">
                <div class="space-y-2">
                    <div class="flex items-center text-sm text-gray-700">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>专享会员特权</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-700">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>多城市招标信息</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-700">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>个性化推荐服务</span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <button class="w-full bg-gradient-to-r from-purple-500 to-blue-600 text-white py-3 rounded-lg text-sm font-medium mb-3" onclick="goToLogin()">
                立即登录
            </button>
            <p class="text-xs text-gray-500">登录即可享受更多服务</p>
        </div>
    </div>
    {{end}}
    <script>
        // 微信登录
        function goToLogin() {
            // 获取当前页面URL作为回调地址
            const currentURL = window.location.pathname + window.location.search;
            // 跳转到微信授权登录
            window.location.href = '/m/auth/login?callback=' + encodeURIComponent(currentURL);

            console.log('跳转到微信登录，回调URL:', currentURL);
        }

        // 用户信息
        {{if not .is_logged_in}}
        var userInfo = undefined
        {{else}}
        var userInfo = {{json .user.openid}}
        {{end}}
    </script>
</body>
</html>
