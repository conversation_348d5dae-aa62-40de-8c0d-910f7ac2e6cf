<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>浏览记录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            -webkit-overflow-scrolling: touch;
        }
        .phone-container {
            overflow-y: auto;
            position: relative;
            background: white;
            min-height: 100vh;
            -webkit-overflow-scrolling: touch;
        }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .mini-capsule {
            position: absolute;
            top: 8px;
            right: 12px;
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .swipe-item { transition: transform 0.3s ease; }

        /* 空状态样式 */
        .empty-state {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            text-align: center;
            padding: 0 20px;
        }

        /* 登录弹窗样式 */
        .vip-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .vip-modal-content {
            background: white;
            border-radius: 20px;
            padding: 30px 20px;
            margin: 20px;
            text-align: center;
            max-width: 320px;
            width: 100%;
            animation: modalSlideIn 0.3s ease-out;
        }
        @keyframes modalSlideIn {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 顶部导航 -->
        <div class="gradient-bg px-4 pt-4 pb-4">
            <div class="flex items-center space-x-3">
                <h1 class="text-white text-lg font-semibold flex-1">我的浏览</h1>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="bg-white flex-1 overflow-y-auto relative">
            <!-- 加载状态 -->
            <div id="loadingState" class="empty-state">
                <i class="fas fa-spinner fa-spin text-gray-400 text-3xl mb-4"></i>
                <p class="text-gray-500 text-base">加载中...</p>
            </div>

            <!-- 空状态 -->
            <div id="emptyState" class="empty-state" style="display: none;">
                <i class="fas fa-history text-gray-300 text-6xl mb-6"></i>
                <p class="text-gray-600 text-lg font-medium mb-3">暂无浏览记录</p>
                <p class="text-gray-400 text-sm leading-relaxed max-w-xs mx-auto">浏览文章后会在这里显示记录</p>
            </div>

            <!-- 浏览记录列表 -->
            <div id="historyList" class="px-4 py-2" style="display: none;">
                <!-- 动态渲染的内容将在这里显示 -->
            </div>

            <!-- 加载更多 -->
            <div id="loadMoreContainer" class="px-4 py-6 text-center" style="display: none;">
                <button id="loadMoreBtn" onclick="loadMoreHistory()" class="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-2 rounded-full text-sm font-medium">
                    查看更多历史记录
                </button>
            </div>
        </div>


    </div>

    {{if not .is_logged_in}}
    <!-- 登录弹窗 -->
    <div id="loginModal" class="vip-modal">
        <div class="vip-modal-content">
            <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-user text-white text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-3">请先登录</h3>
            <p class="text-gray-600 text-sm mb-6 leading-relaxed">
                登录后即可开通会员套餐<br>
                享受更多专业服务
            </p>

            <!-- 登录优势 -->
            <div class="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6">
                <div class="space-y-2">
                    <div class="flex items-center text-sm text-gray-700">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>专享会员特权</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-700">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>多城市招标信息</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-700">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>个性化推荐服务</span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <button class="w-full bg-gradient-to-r from-purple-500 to-blue-600 text-white py-3 rounded-lg text-sm font-medium mb-3" onclick="goToLogin()">
                立即登录
            </button>
            <p class="text-xs text-gray-500">登录即可享受更多服务</p>
        </div>
    </div>
    {{end}}
    <script>
        // 微信登录
        function goToLogin() {
            // 获取当前页面URL作为回调地址
            const currentURL = window.location.pathname + window.location.search;
            // 跳转到微信授权登录
            window.location.href = '/m/auth/login?callback=' + encodeURIComponent(currentURL);

            console.log('跳转到微信登录，回调URL:', currentURL);
        }

        // 用户信息
        {{if not .is_logged_in}}
        var userInfo = undefined
        {{else}}
        var userInfo = {{json .user.openid}}
        {{end}}

        // 全局变量
        let currentPage = 1;
        const pageSize = 10;
        let isLoading = false;
        let hasMoreData = true;
        let allHistoryData = [];

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，用户信息:', userInfo);

            // 验证用户信息
            if (!userInfo || userInfo === 'undefined' || userInfo === '') {
                console.log('用户未登录或用户信息为空');
                showEmptyState();
                return;
            }

            // 加载浏览记录
            loadHistoryData(1);
        });

        // 加载浏览记录数据
        async function loadHistoryData(page = 1) {
            if (isLoading) return;

            try {
                isLoading = true;

                // 显示加载状态
                if (page === 1) {
                    document.getElementById('loadingState').style.display = 'block';
                    document.getElementById('emptyState').style.display = 'none';
                    document.getElementById('historyList').style.display = 'none';
                }

                // 调用API接口
                const response = await fetch(`/m/api/zb_user_browser/my-list?openid=${encodeURIComponent(userInfo)}&page=${page}&page_size=${pageSize}`);
                const data = await response.json();

                console.log('API响应数据:', data);

                if (data.code === 0 && data.data) {
                    const historyData = data.data;

                    if (page === 1) {
                        allHistoryData = historyData.list || [];
                    } else {
                        allHistoryData = allHistoryData.concat(historyData.list || []);
                    }

                    // 检查是否还有更多数据
                    hasMoreData = (historyData.list || []).length === pageSize;

                    // 渲染数据
                    if (allHistoryData.length > 0) {
                        renderHistoryList(allHistoryData);
                        showHistoryList();
                    } else {
                        showEmptyState();
                    }
                } else {
                    console.error('API返回错误:', data.message || '未知错误');
                    showEmptyState();
                }
            } catch (error) {
                console.error('加载浏览记录失败:', error);
                showEmptyState();
            } finally {
                isLoading = false;
                // 隐藏加载状态
                document.getElementById('loadingState').style.display = 'none';
            }
        }

        // 加载更多数据
        async function loadMoreHistory() {
            if (isLoading || !hasMoreData) return;

            currentPage++;

            // 更新按钮状态
            const loadMoreBtn = document.getElementById('loadMoreBtn');
            if (loadMoreBtn) {
                loadMoreBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>加载中...';
                loadMoreBtn.disabled = true;
            }

            try {
                await loadHistoryData(currentPage);
            } catch (error) {
                console.error('加载更多数据失败:', error);
                // 恢复按钮状态
                if (loadMoreBtn) {
                    loadMoreBtn.innerHTML = '查看更多历史记录';
                    loadMoreBtn.disabled = false;
                }
                // 回退页码
                currentPage--;
            }
        }

        // 渲染浏览记录列表
        function renderHistoryList(historyData) {
            const historyList = document.getElementById('historyList');
            if (!historyList) return;

            // 按日期分组
            const groupedData = groupByDate(historyData);

            let html = '';
            for (const [dateLabel, records] of Object.entries(groupedData)) {
                html += `
                    <div class="mb-4">
                        <div class="flex items-center justify-between py-2 border-b border-gray-100">
                            <h3 class="text-sm font-semibold text-gray-800">${dateLabel}</h3>
                            <span class="text-xs text-gray-500">${records.length}条记录</span>
                        </div>
                        <div class="space-y-3 mt-3">
                            ${records.map(record => renderHistoryItem(record)).join('')}
                        </div>
                    </div>
                `;
            }

            historyList.innerHTML = html;

            // 显示/隐藏加载更多按钮
            const loadMoreContainer = document.getElementById('loadMoreContainer');
            if (loadMoreContainer) {
                if (hasMoreData) {
                    loadMoreContainer.style.display = 'block';
                    // 恢复按钮状态
                    const loadMoreBtn = document.getElementById('loadMoreBtn');
                    if (loadMoreBtn) {
                        loadMoreBtn.innerHTML = '查看更多历史记录';
                        loadMoreBtn.disabled = false;
                    }
                } else {
                    loadMoreContainer.style.display = 'none';
                }
            }
        }

        // 渲染单个浏览记录项
        function renderHistoryItem(record) {
            const browseTime = formatTime(record.browse_time);
            const categoryColor = getCategoryColor(record.category_name);
            const cityColor = getCityColor(record.city_name);

            return `
                <div class="swipe-item bg-white rounded-xl p-4 card-shadow border border-gray-100" onclick="goToDetail(${record.article_id})">
                    <div class="flex items-start space-x-3">
                        <div class="flex-1 min-w-0">
                            <div class="flex items-start justify-between mb-1">
                                <h4 class="text-sm font-semibold text-gray-800 line-clamp-2">${record.article_title || '标题未知'}</h4>
                                <span class="text-xs text-gray-400 ml-2">${browseTime}</span>
                            </div>
                            <div class="flex items-center space-x-2 mb-2">
                                <span class="bg-${categoryColor}-100 text-${categoryColor}-600 px-2 py-1 rounded text-xs">${record.category_name || '未分类'}</span>
                                <span class="bg-${cityColor}-100 text-${cityColor}-600 px-2 py-1 rounded text-xs">${record.city_name || '未知城市'}</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-xs text-gray-600">浏览时间：${formatDateTime(record.browse_time)}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 按日期分组
        function groupByDate(historyData) {
            const groups = {};
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);

            historyData.forEach(record => {
                const recordDate = new Date(record.browse_time);
                let dateLabel;

                if (isSameDay(recordDate, today)) {
                    dateLabel = '今天';
                } else if (isSameDay(recordDate, yesterday)) {
                    dateLabel = '昨天';
                } else {
                    dateLabel = formatDate(recordDate);
                }

                if (!groups[dateLabel]) {
                    groups[dateLabel] = [];
                }
                groups[dateLabel].push(record);
            });

            return groups;
        }

        // 判断是否是同一天
        function isSameDay(date1, date2) {
            return date1.getFullYear() === date2.getFullYear() &&
                   date1.getMonth() === date2.getMonth() &&
                   date1.getDate() === date2.getDate();
        }

        // 格式化日期
        function formatDate(date) {
            const month = date.getMonth() + 1;
            const day = date.getDate();
            return `${month}月${day}日`;
        }

        // 格式化时间（显示时分）
        function formatTime(timeStr) {
            const date = new Date(timeStr);
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            return `${hours}:${minutes}`;
        }

        // 格式化完整日期时间
        function formatDateTime(timeStr) {
            const date = new Date(timeStr);
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}`;
        }

        // 获取分类颜色
        function getCategoryColor(categoryName) {
            const colors = ['blue', 'green', 'purple', 'pink', 'yellow', 'indigo'];
            const hash = categoryName ? categoryName.split('').reduce((a, b) => {
                a = ((a << 5) - a) + b.charCodeAt(0);
                return a & a;
            }, 0) : 0;
            return colors[Math.abs(hash) % colors.length];
        }

        // 获取城市颜色
        function getCityColor(cityName) {
            const colors = ['red', 'orange', 'teal', 'cyan', 'lime', 'emerald'];
            const hash = cityName ? cityName.split('').reduce((a, b) => {
                a = ((a << 5) - a) + b.charCodeAt(0);
                return a & a;
            }, 0) : 0;
            return colors[Math.abs(hash) % colors.length];
        }

        // 跳转到文章详情
        function goToDetail(articleId) {
            if (articleId) {
                window.location.href = `/m/detail?id=${articleId}`;
            }
        }

        // 显示浏览记录列表
        function showHistoryList() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('emptyState').style.display = 'none';
            document.getElementById('historyList').style.display = 'block';
        }

        // 显示空状态
        function showEmptyState() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('historyList').style.display = 'none';
            document.getElementById('emptyState').style.display = 'block';
            document.getElementById('loadMoreContainer').style.display = 'none';
        }
    </script>
</body>
</html>
