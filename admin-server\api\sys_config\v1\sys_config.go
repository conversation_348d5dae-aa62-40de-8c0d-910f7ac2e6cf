package v1

import (
	"admin-server/internal/packed"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// CreateReq 创建配置项请求体
type CreateReq struct {
	g.Meta           `path:"/sys_config/create" method:"post" tags:"SysConfig" summary:"创建配置项" permission:"system:config:add"`
	GroupId          int64          `p:"group_id" v:"required#配置分组ID不能为空" dc:"配置分组ID"`
	Key              string         `p:"key" v:"required|length:1,30#配置键名不能为空|配置键名长度为1-30位" dc:"配置键名"`
	Value            string         `p:"value" v:"length:0,255#配置键值长度不能超过255位" dc:"配置键值"`
	Name             string         `p:"name" v:"required|length:1,255#配置名称不能为空|配置名称长度为1-255位" dc:"配置名称"`
	Sort             int            `p:"sort" d:"0" v:"min:0#排序值不能小于0" dc:"排序"`
	InputType        string         `p:"input_type" v:"required|in:input,textarea,select,radio,switch,image#数据输入类型不能为空|数据输入类型必须是input,textarea,select,radio,switch,image" dc:"数据输入类型"`
	ConfigSelectData string         `p:"config_select_data" dc:"配置项数据（JSON格式）"`
	IsSystem         *packed.System `p:"is_system" v:"in:0,1" dc:"是否系统保留"`
}

type CreateRes struct {
	ID int64 `json:"id" dc:"配置项ID"`
}

// DeleteReq 删除配置项请求体
type DeleteReq struct {
	g.Meta `path:"/sys_config/{id}" method:"delete" tags:"SysConfig" summary:"删除配置项" permission:"system:config:del"`
	ID     int64 `p:"id" v:"required#请选择需要删除的配置项" dc:"配置项ID"`
}
type DeleteRes struct{}

// UpdateReq 更新配置项请求体
type UpdateReq struct {
	g.Meta           `path:"/sys_config/{id}" method:"put" tags:"SysConfig" summary:"更新配置项信息" permission:"system:config:edit"`
	ID               int64          `p:"id" v:"required#请选择需要更新的配置项" dc:"配置项ID"`
	GroupId          int64          `p:"group_id" v:"required#配置分组ID不能为空" dc:"配置分组ID"`
	Key              string         `p:"key" v:"required|length:1,30#配置键名不能为空|配置键名长度为1-30位" dc:"配置键名"`
	Value            string         `p:"value" v:"length:0,255#配置键值长度不能超过255位" dc:"配置键值"`
	Name             string         `p:"name" v:"required|length:1,255#配置名称不能为空|配置名称长度为1-255位" dc:"配置名称"`
	Sort             int            `p:"sort" d:"0" v:"min:0#排序值不能小于0" dc:"排序"`
	InputType        string         `p:"input_type" v:"required|in:input,textarea,select,radio,switch,image#数据输入类型不能为空|数据输入类型必须是input,textarea,select,radio,switch,image" dc:"数据输入类型"`
	ConfigSelectData string         `p:"config_select_data" dc:"配置项数据（JSON格式）"`
	IsSystem         *packed.System `p:"is_system" v:"in:0,1" dc:"是否系统保留"`
}
type UpdateRes struct{}

// GetListReq 获取配置项列表请求体
type GetListReq struct {
	g.Meta    `path:"/sys_config/list" tags:"SysConfig" method:"get" summary:"获取配置项列表" permission:"system:config:list"`
	GroupId   int64          `p:"group_id" dc:"配置分组ID"`
	Key       string         `p:"key" dc:"配置键名（模糊搜索）"`
	Name      string         `p:"name" dc:"配置名称（模糊搜索）"`
	InputType string         `p:"input_type" dc:"数据输入类型"`
	IsSystem  *packed.System `p:"is_system" v:"in:0,1" dc:"是否系统保留"`
}

// ConfigInfo 配置项信息
type ConfigInfo struct {
	ID               int64         `json:"id" dc:"配置项ID"`
	GroupId          int64         `json:"group_id" dc:"配置分组ID"`
	GroupName        string        `json:"group_name" dc:"配置分组名称"`
	Key              string        `json:"key" dc:"配置键名"`
	Value            string        `json:"value" dc:"配置键值"`
	Name             string        `json:"name" dc:"配置名称"`
	Sort             int           `json:"sort" dc:"排序"`
	InputType        string        `json:"input_type" dc:"数据输入类型"`
	ConfigSelectData string        `json:"config_select_data" dc:"配置项数据"`
	IsSystem         packed.System `json:"is_system" dc:"是否系统保留"`
	CreatedAt        *gtime.Time   `json:"created_at" dc:"创建时间"`
	UpdatedAt        *gtime.Time   `json:"updated_at" dc:"更新时间"`
}

type GetListRes struct {
	List  []*ConfigInfo `json:"list" dc:"配置项列表"`
	Total int           `json:"total" dc:"总数"`
}

// GetOneReq 获取单个配置项信息请求体
type GetOneReq struct {
	g.Meta `path:"/sys_config/{id}" tags:"SysConfig" method:"get" summary:"获取单个配置项信息"`
	ID     int64 `p:"id" v:"required#请选择需要查询的配置项" dc:"配置项ID"`
}
type GetOneRes struct {
	*ConfigInfo `dc:"配置项信息"`
}

// GetByGroupReq 按分组获取配置项请求体
type GetByGroupReq struct {
	g.Meta  `path:"/sys_config/group/{group_id}" tags:"SysConfig" method:"get" summary:"按分组获取配置项"`
	GroupId int64 `p:"group_id" v:"required#请选择配置分组" dc:"配置分组ID"`
}
type GetByGroupRes struct {
	List []*ConfigInfo `json:"list" dc:"配置项列表"`
}

// UpdateValueReq 更新配置项值请求体
type UpdateValueReq struct {
	g.Meta `path:"/sys_config/{id}/value" method:"put" tags:"SysConfig" summary:"更新配置项值" permission:"system:config:updateValue"`
	ID     int64  `p:"id" v:"required#请选择需要更新的配置项" dc:"配置项ID"`
	Value  string `p:"value" v:"length:0,255#配置键值长度不能超过255位" dc:"配置键值"`
}
type UpdateValueRes struct{}

// GetByKeyReq 根据键名获取配置项请求体
type GetByKeyReq struct {
	g.Meta `path:"/sys_config/key/{key}" tags:"SysConfig" method:"get" summary:"根据键名获取配置项"`
	Key    string `p:"key" v:"required#配置键名不能为空" dc:"配置键名"`
}
type GetByKeyRes struct {
	*ConfigInfo `dc:"配置项信息"`
}
