// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ZbUserBrowser is the golang structure for table zb_user_browser.
type ZbUserBrowser struct {
	Id        int64       `json:"id"         orm:"id"         description:"主键ID"`
	UserId    int64       `json:"userId"     orm:"user_id"    description:"会员id"`
	ArticleId int64       `json:"articleId"  orm:"article_id" description:"招标信息id"`
	CreatedAt *gtime.Time `json:"createdAt"  orm:"created_at" description:"创建时间"`
	UpdatedAt *gtime.Time `json:"updatedAt"  orm:"updated_at" description:"更新时间"`
}
