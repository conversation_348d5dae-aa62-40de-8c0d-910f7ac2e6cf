package sys_resources_group

import (
	"context"

	v1 "admin-server/api/sys_resources_group/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetOne(ctx context.Context, req *v1.GetOneReq) (res *v1.GetOneRes, err error) {
	group, err := service.SysResourcesGroup().GetResourcesGroupDetail(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	if group == nil {
		return &v1.GetOneRes{}, nil
	}

	// 转换为API响应格式
	detail := &v1.ResourcesGroupDetail{
		ID:        group.Id,
		Name:      group.Name,
		Type:      group.Type,
		IsDelete:  int(group.IsDelete),
		CreatedAt: group.CreatedAt,
		UpdatedAt: group.UpdatedAt,
	}

	return &v1.GetOneRes{
		ResourcesGroup: detail,
	}, nil
}
