package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// WechatServeReq 微信公众号服务端
type WechatServeReq struct {
	g.Meta `path:"/auth/wechat/serve" method:"post,get" tags:"Auth" summary:"微信公众号服务端"`
}

// LoginReq 登录请求体
type LoginReq struct {
	g.Meta   `path:"/auth/login" method:"post" tags:"Auth" summary:"管理员登录"`
	Username string `p:"username" v:"required#账号不能为空" dc:"账号"`
	Password string `p:"password" v:"required#密码不能为空" dc:"密码"`
}

// LoginRes 登录响应体
type LoginRes struct {
	Token        string      `json:"token" dc:"访问令牌"`
	RefreshToken string      `json:"refresh_token" dc:"刷新令牌"`
	ExpiresAt    *gtime.Time `json:"expires_at" dc:"访问令牌过期时间"`
	AdminInfo    *AdminInfo  `json:"admin_info" dc:"管理员信息"`
}

// AdminInfo 管理员基本信息
type AdminInfo struct {
	ID       int64  `json:"id" dc:"管理员ID"`
	Username string `json:"username" dc:"账号"`
	Nickname string `json:"nickname" dc:"昵称"`
	IsSuper  int    `json:"is_super" dc:"是否超级管理员"`
}

// RefreshTokenReq 刷新令牌请求体
type RefreshTokenReq struct {
	g.Meta       `path:"/auth/refresh" method:"post" tags:"Auth" summary:"刷新访问令牌"`
	RefreshToken string `p:"refresh_token" v:"required#刷新令牌不能为空" dc:"刷新令牌"`
}

// RefreshTokenRes 刷新令牌响应体
type RefreshTokenRes struct {
	Token        string      `json:"token" dc:"新的访问令牌"`
	RefreshToken string      `json:"refresh_token" dc:"新的刷新令牌"`
	ExpiresAt    *gtime.Time `json:"expires_at" dc:"访问令牌过期时间"`
}

// GetPermissionsReq 获取权限请求体
type GetPermissionsReq struct {
	g.Meta `path:"/auth/permissions" method:"get" tags:"Auth" summary:"获取当前管理员权限"`
}

// PermissionInfo 权限信息
type PermissionInfo struct {
	ID         int64  `json:"id" dc:"权限ID"`
	Name       string `json:"name" dc:"权限名称"`
	Permission string `json:"permission" dc:"权限标识"`
	Type       string `json:"type" dc:"权限类型"`
}

// GetPermissionsRes 获取权限响应体
type GetPermissionsRes struct {
	Permissions []*PermissionInfo `json:"permissions" dc:"权限列表"`
}

// LogoutReq 退出登录请求体
type LogoutReq struct {
	g.Meta `path:"/auth/logout" method:"post" tags:"Auth" summary:"退出登录"`
}

// LogoutRes 退出登录响应体
type LogoutRes struct{}

// GetUserInfoReq 获取当前用户信息请求体
type GetUserInfoReq struct {
	g.Meta `path:"/auth/userinfo" method:"get" tags:"Auth" summary:"获取当前用户信息"`
}

// GetUserInfoRes 获取当前用户信息响应体
type GetUserInfoRes struct {
	AdminInfo   *AdminInfo        `json:"admin_info" dc:"管理员信息"`
	Roles       []*RoleInfo       `json:"roles" dc:"角色列表"`
	Permissions []*PermissionInfo `json:"permissions" dc:"权限列表"`
}

// GetMenusReq 获取用户菜单请求体
type GetMenusReq struct {
	g.Meta `path:"/auth/menus" method:"get" tags:"Auth" summary:"获取当前用户菜单"`
}

// MenuInfo 菜单信息
type MenuInfo struct {
	ID        int64       `json:"id" dc:"菜单ID"`
	Pid       int64       `json:"pid" dc:"父级菜单ID"`
	MenuType  string      `json:"menu_type" dc:"菜单类型"`
	MenuName  string      `json:"menu_name" dc:"菜单名称"`
	MenuIcon  string      `json:"menu_icon" dc:"菜单图标"`
	MenuSort  int         `json:"menu_sort" dc:"菜单排序"`
	Perms     string      `json:"perms" dc:"权限标识"`
	Paths     string      `json:"paths" dc:"路由地址"`
	Component string      `json:"component" dc:"前端组件"`
	Params    string      `json:"params" dc:"路由参数"`
	IsCache   int         `json:"is_cache" dc:"是否缓存"`
	IsShow    int         `json:"is_show" dc:"是否显示"`
	Children  []*MenuInfo `json:"children" dc:"子菜单"`
}

// GetMenusRes 获取用户菜单响应体
type GetMenusRes struct {
	Menus []*MenuInfo `json:"menus" dc:"菜单列表"`
}

// RoleInfo 角色信息
type RoleInfo struct {
	ID   int64  `json:"id" dc:"角色ID"`
	Name string `json:"name" dc:"角色名称"`
}
