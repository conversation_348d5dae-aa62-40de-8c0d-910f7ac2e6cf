// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysAdmins is the golang structure of table sys_admins for DAO operations like Where/Data.
type SysAdmins struct {
	g.Meta        `orm:"table:sys_admins, do:true"`
	Id            interface{} //
	Username      interface{} // 账号
	Password      interface{} // 密码
	Nickname      interface{} // 昵称
	IsSuper       interface{} // 是否是超级管理员1是0否
	IsDisable     interface{} // 是否禁用: 0=否, 1=是
	IsDelete      interface{} // 是否删除: 0=否, 1=是
	LastLoginIp   interface{} // 最后登录ip
	LastLoginTime *gtime.Time // 最后登录时间
	CreatedAt     *gtime.Time // 创建时间
	UpdatedAt     *gtime.Time // 更新时间
	DeletedAt     *gtime.Time // 删除时间
}
