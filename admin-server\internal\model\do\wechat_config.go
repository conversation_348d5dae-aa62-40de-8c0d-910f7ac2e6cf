// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// WechatConfig is the golang structure of table wechat_config for DAO operations like Where/Data.
type WechatConfig struct {
	g.Meta           `orm:"table:wechat_config, do:true"`
	Id               interface{} //
	Appid            interface{} // 微信公众号AppID
	AppSecret        interface{} // 微信公众号AppSecret
	AutoReplyEnabled interface{} // 关注自动回复开关：0=关闭，1=开启
	AutoReplyText    interface{} // 关注自动回复语
	ServerUrl        interface{} // 服务器地址URL
	Token            interface{} // 微信Token
	EncodingAesKey   interface{} // 消息加解密密钥
	EncryptMode      interface{} // 消息加解密方式：plaintext=明文模式，compatible=兼容模式，safe=安全模式
	CreatedAt        *gtime.Time // 创建时间
	UpdatedAt        *gtime.Time // 更新时间
}
