# 文章统计接口条件筛选功能说明

## 功能概述

在 `zb_article/stats` 接口中新增了 `cate_id`（类别ID）和 `city_id`（城市ID）两个可选的请求参数，支持按分类和城市进行条件查询统计。

## 接口信息

### 请求信息
- **接口路径**: `GET /zb_article/stats`
- **权限要求**: `system:article:stats`

### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| cate_id | int | 否 | 0 | 类别ID，0表示全部分类 |
| city_id | int | 否 | 0 | 城市ID，0表示全部城市 |

### 响应信息

#### 响应字段
- `total_articles`: 总信息数（根据筛选条件）
- `published_articles`: 已发布信息数（根据筛选条件）
- `disabled_articles`: 禁用信息数（根据筛选条件）
- `today_articles`: 今日新增信息数（根据筛选条件）

#### 响应示例

**1. 获取全部统计**
```bash
GET /zb_article/stats
```
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "total_articles": 1256,
        "published_articles": 1180,
        "disabled_articles": 76,
        "today_articles": 23
    }
}
```

**2. 按分类统计**
```bash
GET /zb_article/stats?cate_id=1
```
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "total_articles": 320,
        "published_articles": 298,
        "disabled_articles": 22,
        "today_articles": 8
    }
}
```

**3. 按城市统计**
```bash
GET /zb_article/stats?city_id=1
```
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "total_articles": 456,
        "published_articles": 420,
        "disabled_articles": 36,
        "today_articles": 12
    }
}
```

**4. 按分类和城市统计**
```bash
GET /zb_article/stats?cate_id=1&city_id=1
```
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "total_articles": 89,
        "published_articles": 82,
        "disabled_articles": 7,
        "today_articles": 3
    }
}
```

## 技术实现

### 1. API层修改
**文件**: `api/zb_article/v1/zb_article.go`

```go
type GetStatsReq struct {
    g.Meta `path:"/zb_article/stats" tags:"ZbArticle" method:"get" summary:"获取信息统计" permission:"system:article:stats"`
    CateId int `p:"cate_id" d:"0" dc:"类别ID，0表示全部"`
    CityId int `p:"city_id" d:"0" dc:"城市ID，0表示全部"`
}
```

### 2. Service层修改
**文件**: `internal/service/zb_article.go`

```go
// GetArticleStats 获取信息统计
GetArticleStats(ctx context.Context, cateId, cityId int) (totalArticles, publishedArticles, disabledArticles, todayArticles int, err error)
```

### 3. Logic层实现
**文件**: `internal/logic/zbArticle/zb_article.go`

```go
func (s *sZbArticle) GetArticleStats(ctx context.Context, cateId, cityId int) (totalArticles, publishedArticles, disabledArticles, todayArticles int, err error) {
    // 构建基础查询条件
    baseQuery := dao.ZbArticle.Ctx(ctx).Where("is_delete", int(packed.NO_DELETE))
    
    // 添加分类筛选条件
    if cateId > 0 {
        baseQuery = baseQuery.Where("cate_id", cateId)
    }
    
    // 添加城市筛选条件
    if cityId > 0 {
        baseQuery = baseQuery.Where("city_id", cityId)
    }

    // 分别统计各种状态的数据
    // ... 具体实现
}
```

### 4. Controller层修改
**文件**: `internal/controller/zb_article/zb_article_v1_get_stats.go`

```go
func (c *ControllerV1) GetStats(ctx context.Context, req *v1.GetStatsReq) (res *v1.GetStatsRes, err error) {
    totalArticles, publishedArticles, disabledArticles, todayArticles, err := service.ZbArticle().GetArticleStats(ctx, req.CateId, req.CityId)
    if err != nil {
        return nil, err
    }

    return &v1.GetStatsRes{
        TotalArticles:     totalArticles,
        PublishedArticles: publishedArticles,
        DisabledArticles:  disabledArticles,
        TodayArticles:     todayArticles,
    }, nil
}
```

## 查询逻辑说明

### 筛选条件组合
1. **无筛选条件** (`cate_id=0, city_id=0`): 统计所有文章
2. **仅分类筛选** (`cate_id>0, city_id=0`): 统计指定分类的所有文章
3. **仅城市筛选** (`cate_id=0, city_id>0`): 统计指定城市的所有文章
4. **分类+城市筛选** (`cate_id>0, city_id>0`): 统计指定分类和城市的文章

### 统计维度
每种筛选条件下都会统计以下四个维度：
- **总信息数**: 满足筛选条件且未删除的文章总数
- **已发布信息数**: 满足筛选条件、未删除且未禁用的文章数
- **禁用信息数**: 满足筛选条件、未删除但已禁用的文章数
- **今日新增信息数**: 满足筛选条件、未删除且今日创建的文章数

### 查询优化
- 使用 `baseQuery.Clone()` 避免查询条件相互影响
- 利用数据库索引提高查询性能
- 统一的错误处理和日志记录

## 使用场景

### 1. 管理后台分类统计
```javascript
// 获取指定分类的统计信息
async function getCategoryStats(cateId) {
    const response = await fetch(`/zb_article/stats?cate_id=${cateId}`, {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    });
    const result = await response.json();
    
    if (result.code === 0) {
        const stats = result.data;
        console.log(`分类${cateId}统计:`, stats);
        return stats;
    }
}
```

### 2. 城市数据分析
```javascript
// 获取指定城市的统计信息
async function getCityStats(cityId) {
    const response = await fetch(`/zb_article/stats?city_id=${cityId}`, {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    });
    const result = await response.json();
    
    if (result.code === 0) {
        return result.data;
    }
}
```

### 3. 综合数据报表
```javascript
// 获取分类和城市的组合统计
async function getCombinedStats(cateId, cityId) {
    let url = '/zb_article/stats?';
    const params = [];
    
    if (cateId > 0) params.push(`cate_id=${cateId}`);
    if (cityId > 0) params.push(`city_id=${cityId}`);
    
    url += params.join('&');
    
    const response = await fetch(url, {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    });
    
    return await response.json();
}
```

### 4. 移动端条件统计
```javascript
// 移动端根据当前筛选条件获取统计
async function loadConditionalStats(currentCateId, currentCityId) {
    let url = '/m/api/zb_article/stats?';
    const params = [];
    
    if (currentCateId > 0) params.push(`cate_id=${currentCateId}`);
    if (currentCityId > 0) params.push(`city_id=${currentCityId}`);
    
    if (params.length > 0) {
        url += params.join('&');
    }
    
    try {
        const response = await fetch(url);
        const result = await response.json();
        
        if (result.code === 0) {
            updateStatsDisplay(result.data);
        }
    } catch (error) {
        console.error('获取条件统计失败:', error);
    }
}
```

## 测试用例

### 1. 基础功能测试
```bash
# 测试全部统计
curl -X GET "http://localhost:8000/zb_article/stats" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 测试分类筛选
curl -X GET "http://localhost:8000/zb_article/stats?cate_id=1" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 测试城市筛选
curl -X GET "http://localhost:8000/zb_article/stats?city_id=1" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 测试组合筛选
curl -X GET "http://localhost:8000/zb_article/stats?cate_id=1&city_id=1" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. 边界值测试
```bash
# 测试无效分类ID
curl -X GET "http://localhost:8000/zb_article/stats?cate_id=999" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 测试无效城市ID
curl -X GET "http://localhost:8000/zb_article/stats?city_id=999" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 测试负数参数
curl -X GET "http://localhost:8000/zb_article/stats?cate_id=-1&city_id=-1" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 数据一致性验证
- 验证各统计数据之间的逻辑关系
- 检查筛选条件的准确性
- 确认今日新增统计的时间范围正确

## 性能考虑

### 1. 数据库索引优化
确保相关字段有适当的索引：
```sql
-- 分类索引
ALTER TABLE zb_article ADD INDEX idx_cate_id (cate_id);

-- 城市索引
ALTER TABLE zb_article ADD INDEX idx_city_id (city_id);

-- 组合索引
ALTER TABLE zb_article ADD INDEX idx_cate_city (cate_id, city_id);

-- 时间索引
ALTER TABLE zb_article ADD INDEX idx_created_at (created_at);
```

### 2. 查询优化
- 使用覆盖索引减少回表查询
- 合理使用查询缓存
- 避免全表扫描

### 3. 缓存策略
对于频繁查询的统计数据，可以考虑添加缓存：
```go
// 示例：缓存分类统计数据
cacheKey := fmt.Sprintf("article_stats_cate_%d_city_%d_%s", cateId, cityId, gtime.Now().Format("Y-m-d"))
```

## 注意事项

1. **参数验证**: 确保传入的ID参数有效
2. **数据一致性**: 统计结果应与实际数据保持一致
3. **性能监控**: 关注复杂查询的执行时间
4. **错误处理**: 完善异常情况的处理逻辑
5. **日志记录**: 记录重要的统计查询操作

---

**功能状态**: ✅ 已完成  
**测试状态**: 待测试  
**文档版本**: v1.0  
**最后更新**: 2025-01-23
