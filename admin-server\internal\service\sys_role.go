package service

import (
	v1 "admin-server/api/sys_role/v1"
	"context"
)

// 1.定义接口
type ISysRole interface {
	GetList(ctx context.Context, req *v1.GetListReq) (list []*v1.RoleInfo, total int, err error)
	GetOne(ctx context.Context, id int64) (role *v1.RoleInfo, err error)
	Create(ctx context.Context, req *v1.CreateReq) (insertId int64, err error)
	Update(ctx context.Context, req *v1.UpdateReq) (err error)
	Delete(ctx context.Context, id int64) (err error)
	AssignMenus(ctx context.Context, roleId int64, menuIds []int64) (err error)
	GetRoleMenus(ctx context.Context, roleId int64) (roleMenuInfo *v1.RoleMenuInfo, err error)
	CheckRoleExists(ctx context.Context, id int64) (exists bool, err error)
	CheckRoleNameExists(ctx context.Context, name string, excludeId int64) (exists bool, err error)
	GetRoleStatistics(ctx context.Context, roleId int64) (menuCount int, userCount int, mainMenus []string, err error)
	ToggleStatus(ctx context.Context, id int64) (err error)
}

// 2.定义接口变量
var localSysRole ISysRole

// 3.定义一个获取接口实例的函数
func SysRole() ISysRole {
	if localSysRole == nil {
		panic("ISysRole接口未实现或未注册")
	}
	return localSysRole
}

// 4.定义一个接口实现的注册方法
func RegisterSysRole(i ISysRole) {
	localSysRole = i
}
