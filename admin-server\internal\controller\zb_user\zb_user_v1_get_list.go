package zb_user

import (
	v1 "admin-server/api/zb_user/v1"
	"admin-server/internal/service"
	"context"
)

func (c *ControllerV1) GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error) {
	list, total, err := service.ZbUser().GetUserList(
		ctx,
		req.Page,
		req.PageSize,
		req.Nickname,
		req.Openid,
		req.IsDisable,
		req.VipStatus,
		req.HasVipPeriod,
		req.StartTime,
		req.EndTime,
	)
	if err != nil {
		return nil, err
	}

	// 转换为API响应格式
	users := make([]v1.UserInfo, len(list))
	for i, user := range list {
		// 动态计算VIP状态
		vipStatus, daysLeft := service.ZbUser().CalculateVipStatus(ctx, user.EffectiveStart, user.EffectiveEnd)

		users[i] = v1.UserInfo{
			ID:             user.Id,
			Nickname:       user.Nickname,
			Avatar:         user.Avatar,
			Openid:         user.Openid,
			IsDisable:      int(user.IsDisable),
			IsDelete:       int(user.IsDelete),
			EffectiveStart: user.EffectiveStart,
			EffectiveEnd:   user.EffectiveEnd,
			VipStatus:      vipStatus,
			VipDaysLeft:    daysLeft,
			CreatedAt:      user.CreatedAt,
			UpdatedAt:      user.UpdatedAt,
		}
	}

	return &v1.GetListRes{
		List:  users,
		Total: total,
	}, nil
}
