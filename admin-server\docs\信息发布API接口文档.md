# 信息发布API接口文档

## 接口概述

信息发布模块基于GoFrame标准架构实现，采用Service-Logic-Controller-API四层架构，提供完整的信息发布管理功能。所有接口都需要JWT认证和相应的权限验证。

### 关联查询特性

所有返回信息数据的接口都会自动关联查询城市名称和类别名称：

- **城市关联**: `zb_article.city_id = zb_city.id`，获取城市名称
- **类别关联**: `zb_article.cate_id = zb_cate.id`，获取类别名称
- **查询优化**: 使用LEFT JOIN确保即使关联数据不存在也能正常返回
- **性能优化**: 一次查询获取所有必要数据，避免N+1查询问题
- **全面覆盖**: 包括列表接口和详情接口都支持关联查询

## 架构说明

### 🏗️ 架构层次
1. **Service层** (`internal/service/zb_article.go`) - 信息发布服务接口定义
2. **Logic层** (`internal/logic/zbArticle/zb_article.go`) - 信息发布业务逻辑实现
3. **Controller层** (`internal/controller/zb_article/`) - 信息发布控制器（自动生成风格）
4. **API层** (`api/zb_article/v1/zb_article.go`) - 信息发布API结构体定义

### 📁 文件结构
```
admin-server/
├── api/
│   └── zb_article/
│       ├── zb_article.go              # API接口定义
│       └── v1/
│           └── zb_article.go          # API结构体定义
├── internal/
│   ├── service/
│   │   └── zb_article.go              # 服务接口定义
│   ├── logic/
│   │   └── zbArticle/
│   │       └── zb_article.go          # 业务逻辑实现
│   └── controller/
│       └── zb_article/
│           ├── zb_article_new.go      # 控制器构造函数
│           ├── zb_article_v1_get_list.go
│           ├── zb_article_v1_get_one.go
│           ├── zb_article_v1_create.go
│           ├── zb_article_v1_update.go
│           ├── zb_article_v1_delete.go
│           ├── zb_article_v1_set_status.go
│           ├── zb_article_v1_get_by_city.go
│           ├── zb_article_v1_get_by_category.go
│           ├── zb_article_v1_get_hot.go
│           ├── zb_article_v1_get_recent.go
│           ├── zb_article_v1_get_stats.go
│           ├── zb_article_v1_search.go
│           └── zb_article_v1_increment_view.go
└── docs/
    └── 信息发布API接口文档.md
```

## 基础信息

- **基础URL**: `http://your-domain.com`
- **认证方式**: JWT Token（在请求头中添加 `Authorization: Bearer {token}`）
- **数据格式**: JSON
- **字符编码**: UTF-8
- **权限前缀**: `system:article:`

## API接口列表

### 1. 获取信息列表

**接口地址**: `GET /zb_article/list`

**权限要求**: `system:article:list`

**接口描述**: 获取信息列表，支持分页和多条件筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码，最小为1 |
| page_size | int | 否 | 10 | 每页数量，范围1-100 |
| title | string | 否 | - | 标题（模糊搜索） |
| city_id | int | 否 | - | 城市ID |
| cate_id | int | 否 | - | 类别ID |
| is_disable | int | 否 | -1 | 是否禁用：0=否，1=是，-1=全部 |
| start_time | string | 否 | - | 开始时间（格式：2006-01-02 15:04:05） |
| end_time | string | 否 | - | 结束时间（格式：2006-01-02 15:04:05） |

**请求示例**:
```bash
GET /zb_article/list?page=1&page_size=10&title=招聘&city_id=1&is_disable=0
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "list": [
      {
        "id": 1,
        "city_id": 1,
        "city_name": "北京市",
        "cate_id": 1,
        "cate_name": "招聘信息",
        "title": "招聘信息标题",
        "intro": "信息简介内容",
        "pic": "http://example.com/pic.jpg",
        "author": "发布者",
        "view_count": 100,
        "is_disable": 0,
        "is_delete": 0,
        "created_at": "2025-07-19T10:00:00Z",
        "updated_at": "2025-07-19T10:00:00Z"
      }
    ],
    "total": 100
  }
}
```

### 2. 获取信息详情

**接口地址**: `GET /zb_article/{id}`

**权限要求**: `system:article:detail`

**接口描述**: 根据信息ID获取信息详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 信息ID |

**请求示例**:
```bash
GET /zb_article/1
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "article": {
      "id": 1,
      "city_id": 1,
      "city_name": "北京市",
      "cate_id": 1,
      "cate_name": "招聘信息",
      "title": "招聘信息标题",
      "intro": "信息简介内容",
      "full_content": {
        "type": "rich_text",
        "content": [
          {
            "type": "paragraph",
            "text": "完整的信息内容..."
          }
        ]
      },
      "shieid_content": {
        "blocked_words": ["敏感词1", "敏感词2"],
        "replacement": "***"
      },
      "view_count": 100,
      "seo_title": "SEO标题",
      "seo_keywords": "SEO关键词",
      "seo_description": "SEO描述",
      "pic": "http://example.com/pic.jpg",
      "uid": 1,
      "author": "发布者",
      "ip": "***********",
      "is_disable": 0,
      "is_delete": 0,
      "created_at": "2025-07-19T10:00:00Z",
      "updated_at": "2025-07-19T10:00:00Z",
      "deleted_at": null
    }
  }
}
```

### 3. 创建信息

**接口地址**: `POST /zb_article/create`

**权限要求**: `system:article:create`

**接口描述**: 创建新的信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| city_id | int | 是 | 城市ID |
| cate_id | int | 是 | 类别ID |
| title | string | 是 | 标题（最大200字符） |
| intro | string | 否 | 内容简介（最大500字符） |
| full_content | object/string | 是 | 完整版内容（JSON格式） |
| shieid_content | object/string | 否 | 屏蔽内容（JSON格式） |
| seo_title | string | 否 | SEO标题（最大200字符） |
| seo_keywords | string | 否 | SEO关键词（最大200字符） |
| seo_description | string | 否 | SEO描述（最大500字符） |
| pic | string | 否 | 缩略图URL（最大255字符） |
| author | string | 否 | 作者（最大50字符） |
| is_disable | int | 否 | 是否禁用：0=否，1=是 |

**请求示例**:
```json
{
  "city_id": 1,
  "cate_id": 1,
  "title": "招聘信息标题",
  "intro": "信息简介内容",
  "full_content": {
    "type": "rich_text",
    "content": [
      {
        "type": "paragraph",
        "text": "完整的信息内容..."
      },
      {
        "type": "image",
        "url": "http://example.com/image.jpg"
      }
    ]
  },
  "shieid_content": {
    "blocked_words": ["敏感词1", "敏感词2"],
    "replacement": "***"
  },
  "pic": "http://example.com/pic.jpg",
  "author": "发布者",
  "is_disable": 0
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": 1
  }
}
```

### 4. 更新信息

**接口地址**: `PUT /zb_article/update/{id}`

**权限要求**: `system:article:update`

**接口描述**: 更新信息内容

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 信息ID |

**请求参数**: 同创建信息接口

**请求示例**:
```json
{
  "city_id": 1,
  "cate_id": 1,
  "title": "更新后的标题",
  "intro": "更新后的简介",
  "full_content": "更新后的完整内容...",
  "author": "发布者",
  "is_disable": 0
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {}
}
```

### 5. 删除信息

**接口地址**: `DELETE /zb_article/delete`

**权限要求**: `system:article:delete`

**接口描述**: 批量删除信息（软删除）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | array | 是 | 信息ID列表 |

**请求示例**:
```json
{
  "ids": [1, 2, 3, 4, 5]
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {}
}
```

### 6. 设置信息状态

**接口地址**: `PUT /zb_article/status/{id}`

**权限要求**: `system:article:status`

**接口描述**: 设置信息的启用/禁用状态

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 信息ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| is_disable | int | 是 | 是否禁用：0=否，1=是 |

**请求示例**:
```json
{
  "is_disable": 1
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {}
}
```

### 7. 根据城市获取信息列表

**接口地址**: `GET /zb_article/city/{city_id}`

**权限要求**: `system:article:list`

**接口描述**: 根据城市ID获取信息列表

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| city_id | int | 是 | 城市ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码，最小为1 |
| page_size | int | 否 | 10 | 每页数量，范围1-100 |

**请求示例**:
```bash
GET /zb_article/city/1?page=1&page_size=10
```

### 8. 根据类别获取信息列表

**接口地址**: `GET /zb_article/category/{cate_id}`

**权限要求**: `system:article:list`

**接口描述**: 根据类别ID获取信息列表

### 9. 获取热门信息

**接口地址**: `GET /zb_article/hot`

**权限要求**: `system:article:list`

**接口描述**: 获取热门信息（按浏览量排序）

### 10. 获取最新信息

**接口地址**: `GET /zb_article/recent`

**权限要求**: `system:article:list`

**接口描述**: 获取最新发布的信息

### 11. 获取信息统计

**接口地址**: `GET /zb_article/stats`

**权限要求**: `system:article:stats`

**接口描述**: 获取信息统计数据

### 12. 搜索信息

**接口地址**: `GET /zb_article/search`

**权限要求**: `system:article:list`

**接口描述**: 根据关键词搜索信息

### 13. 增加浏览次数

**接口地址**: `PUT /zb_article/view/{id}`

**权限要求**: 无（公开接口）

**接口描述**: 增加信息的浏览次数

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 操作成功 |
| 50 | 参数错误 |
| 51 | 数据不存在 |
| 500 | 服务器内部错误 |
| 401 | 未授权（Token无效或过期） |
| 403 | 权限不足 |

## 数据模型

### ArticleInfo（信息列表）
```json
{
  "id": 1,
  "city_id": 1,
  "city_name": "北京市",
  "cate_id": 1,
  "cate_name": "招聘信息",
  "title": "信息标题",
  "intro": "信息简介",
  "pic": "缩略图URL",
  "author": "作者",
  "view_count": 100,
  "is_disable": 0,
  "is_delete": 0,
  "created_at": "2025-07-19T10:00:00Z",
  "updated_at": "2025-07-19T10:00:00Z"
}
```

#### 字段说明：
- `city_id`: 城市ID，关联zb_city表的id字段
- `city_name`: 城市名称，来自zb_city表的name字段
- `cate_id`: 类别ID，关联zb_cate表的id字段
- `cate_name`: 类别名称，来自zb_cate表的name字段

### ArticleDetail（信息详情）
```json
{
  "id": 1,
  "city_id": 1,
  "city_name": "北京市",
  "cate_id": 1,
  "cate_name": "招聘信息",
  "title": "信息标题",
  "intro": "信息简介",
  "full_content": {
    "type": "rich_text",
    "content": [
      {
        "type": "paragraph",
        "text": "完整的信息内容..."
      },
      {
        "type": "image",
        "url": "http://example.com/image.jpg",
        "alt": "图片描述"
      },
      {
        "type": "list",
        "items": ["项目1", "项目2", "项目3"]
      }
    ],
    "metadata": {
      "word_count": 500,
      "created_by": "editor"
    }
  },
  "shieid_content": {
    "blocked_words": ["敏感词1", "敏感词2"],
    "replacement": "***",
    "rules": [
      {
        "pattern": "电话号码",
        "action": "mask"
      }
    ]
  },
  "view_count": 100,
  "seo_title": "SEO标题",
  "seo_keywords": "SEO关键词",
  "seo_description": "SEO描述",
  "pic": "缩略图URL",
  "uid": 1,
  "author": "作者",
  "ip": "发布IP",
  "is_disable": 0,
  "is_delete": 0,
  "created_at": "2025-07-19T10:00:00Z",
  "updated_at": "2025-07-19T10:00:00Z",
  "deleted_at": null
}
```

## 使用示例

### JavaScript/Axios示例

```javascript
// 获取信息列表
const getArticles = async () => {
  try {
    const response = await axios.get('/zb_article/list', {
      params: {
        page: 1,
        page_size: 10,
        title: '招聘'
      },
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log(response.data);
  } catch (error) {
    console.error('获取信息列表失败:', error);
  }
};

// 获取信息详情（包含城市名称和类别名称）
const getArticleDetail = async (id) => {
  try {
    const response = await axios.get(`/zb_article/${id}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('信息详情:', response.data.article);
    console.log('城市名称:', response.data.article.city_name);
    console.log('类别名称:', response.data.article.cate_name);
  } catch (error) {
    console.error('获取信息详情失败:', error);
  }
};

// 创建信息
const createArticle = async (articleData) => {
  try {
    // 构建包含JSON字段的信息数据
    const data = {
      city_id: 1,
      cate_id: 1,
      title: "招聘信息标题",
      intro: "信息简介",
      full_content: {
        type: "rich_text",
        content: [
          {
            type: "paragraph",
            text: "这是一段完整的信息内容..."
          },
          {
            type: "image",
            url: "http://example.com/image.jpg",
            alt: "示例图片"
          }
        ]
      },
      shieid_content: {
        blocked_words: ["敏感词1", "敏感词2"],
        replacement: "***",
        enabled: true
      },
      author: "发布者",
      is_disable: 0
    };

    const response = await axios.post('/zb_article/create', data, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    console.log(response.data);
  } catch (error) {
    console.error('创建信息失败:', error);
  }
};
```

### cURL示例

```bash
# 获取信息列表
curl -X GET "http://your-domain.com/zb_article/list?page=1&page_size=10" \
  -H "Authorization: Bearer your-jwt-token"

# 获取信息详情（包含城市名称和类别名称）
curl -X GET "http://your-domain.com/zb_article/1" \
  -H "Authorization: Bearer your-jwt-token"

# 创建信息
curl -X POST "http://your-domain.com/zb_article/create" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "city_id": 1,
    "cate_id": 1,
    "title": "招聘信息标题",
    "intro": "信息简介",
    "full_content": {
      "type": "rich_text",
      "content": [
        {
          "type": "paragraph",
          "text": "这是完整的信息内容..."
        },
        {
          "type": "image",
          "url": "http://example.com/image.jpg"
        }
      ]
    },
    "shieid_content": {
      "blocked_words": ["敏感词"],
      "replacement": "***"
    },
    "author": "发布者"
  }'

# 更新信息
curl -X PUT "http://your-domain.com/zb_article/update/1" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "更新后的标题",
    "intro": "更新后的简介"
  }'
```

## JSON字段说明

### full_content（完整版内容）
`full_content` 字段为JSON格式，支持富文本内容结构：

```json
{
  "type": "rich_text",
  "content": [
    {
      "type": "paragraph",
      "text": "段落文本内容"
    },
    {
      "type": "heading",
      "level": 2,
      "text": "标题内容"
    },
    {
      "type": "image",
      "url": "http://example.com/image.jpg",
      "alt": "图片描述",
      "width": 800,
      "height": 600
    },
    {
      "type": "list",
      "style": "ordered",
      "items": ["项目1", "项目2", "项目3"]
    },
    {
      "type": "table",
      "headers": ["列1", "列2", "列3"],
      "rows": [
        ["数据1", "数据2", "数据3"],
        ["数据4", "数据5", "数据6"]
      ]
    }
  ],
  "metadata": {
    "word_count": 500,
    "created_by": "editor",
    "version": "1.0"
  }
}
```

### shieid_content（屏蔽内容）
`shieid_content` 字段为JSON格式，用于配置内容屏蔽规则：

```json
{
  "blocked_words": ["敏感词1", "敏感词2", "违禁词"],
  "replacement": "***",
  "rules": [
    {
      "pattern": "\\d{11}",
      "type": "regex",
      "action": "mask",
      "description": "手机号码屏蔽"
    },
    {
      "pattern": "email",
      "type": "keyword",
      "action": "remove",
      "description": "邮箱地址移除"
    }
  ],
  "whitelist": ["允许词1", "允许词2"],
  "enabled": true
}
```

### 支持的内容类型

#### full_content 支持的内容类型：
- `paragraph`: 段落文本
- `heading`: 标题（支持level 1-6）
- `image`: 图片（支持url、alt、width、height）
- `list`: 列表（支持ordered、unordered）
- `table`: 表格（支持headers、rows）
- `link`: 链接（支持url、text）
- `code`: 代码块（支持language、code）
- `quote`: 引用块（支持text、author）

#### shieid_content 支持的规则类型：
- `keyword`: 关键词匹配
- `regex`: 正则表达式匹配
- `pattern`: 模式匹配

#### 支持的屏蔽动作：
- `mask`: 用替换字符遮蔽
- `remove`: 完全移除
- `replace`: 替换为指定内容

## 注意事项

1. **认证要求**: 除浏览次数接口外，所有接口都需要JWT Token认证
2. **权限验证**: 需要相应的信息管理权限才能访问这些接口
3. **软删除**: 删除操作为软删除，数据不会真正从数据库中删除
4. **日期格式**: 日期参数统一使用 `2006-01-02 15:04:05` 格式
5. **分页限制**: 每页最大数量为100条记录
6. **内容长度**: 注意各字段的最大长度限制
7. **SEO优化**: 建议填写SEO相关字段以提高搜索引擎收录效果
8. **JSON格式**: `full_content` 和 `shieid_content` 字段必须为有效的JSON格式
9. **内容验证**: 系统会验证JSON结构的有效性，无效格式将返回错误
10. **字段兼容**: JSON字段同时支持字符串和对象格式，便于不同客户端使用

## 更新日志

### v1.1.0 (2025-07-19)
- **关联查询优化**: 所有信息接口（列表和详情）自动返回城市名称和类别名称
- **详情接口增强**: 获取信息详情接口也支持城市名称和类别名称关联查询
- **数据库优化**: 使用LEFT JOIN避免N+1查询问题
- **响应增强**: 新增city_name和cate_name字段
- **性能提升**: 一次查询获取完整关联数据
- **向后兼容**: 保持原有字段结构不变

### v1.0.0 (2025-07-19)
- 实现完整的信息发布CRUD功能
- 支持按城市、类别筛选
- 提供热门、最新信息查询
- 实现搜索功能
- 添加浏览次数统计
- 完善权限控制机制
- 支持JSON格式的富文本内容和屏蔽规则
