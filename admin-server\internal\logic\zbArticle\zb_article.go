package zbArticle

import (
	"admin-server/internal/dao"
	"admin-server/internal/model/entity"
	"admin-server/internal/packed"
	"admin-server/internal/service"
	"context"
	"encoding/json"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ArticleWithNames 带城市名称和类别名称的信息结构
type ArticleWithNames struct {
	entity.ZbArticle
	CityName string `json:"city_name" orm:"city_name"`
	CateName string `json:"cate_name" orm:"cate_name"`
}

type sZbArticle struct{}

func init() {
	service.RegisterZbArticle(&sZbArticle{})
}

// processJSONField 处理JSON字段
func (s *sZbArticle) processJSONField(data interface{}) (string, error) {
	if data == nil {
		return "", nil
	}

	// 如果已经是字符串，直接返回
	if str, ok := data.(string); ok {
		return str, nil
	}

	// 转换为JSON字符串
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return "", gerror.Wrap(err, "JSON序列化失败")
	}

	return string(jsonBytes), nil
}

// parseJSONField 解析JSON字段
func (s *sZbArticle) parseJSONField(jsonStr string) (interface{}, error) {
	if jsonStr == "" {
		return nil, nil
	}

	var result interface{}
	err := json.Unmarshal([]byte(jsonStr), &result)
	if err != nil {
		return jsonStr, nil // 如果解析失败，返回原字符串
	}

	return result, nil
}

// GetArticleList 获取信息列表
func (s *sZbArticle) GetArticleList(ctx context.Context, page, pageSize int, title string, cityId, cateId, isDisable int, startTime, endTime string) (list []entity.ZbArticle, total int, err error) {
	// 构建基础查询，使用表别名
	query := dao.ZbArticle.Ctx(ctx).As("a").
		LeftJoin("zb_city c", "a.city_id = c.id").
		LeftJoin("zb_cate cat", "a.cate_id = cat.id").
		Where("a.is_delete", int(packed.NO_DELETE))

	// 按标题筛选
	if title != "" {
		query = query.WhereLike("a.title", "%"+title+"%")
	}

	// 按城市筛选
	if cityId > 0 {
		query = query.Where("a.city_id", cityId)
	}

	// 按类别筛选
	if cateId > 0 {
		query = query.Where("a.cate_id", cateId)
	}

	// 按禁用状态筛选
	if isDisable >= 0 {
		query = query.Where("a.is_disable", isDisable)
	}

	// 按时间范围筛选
	if startTime != "" && endTime != "" {
		query = query.WhereBetween("a.created_at", startTime, endTime)
	}

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取信息总数失败")
	}

	// 获取列表数据，包含关联的城市名称和类别名称
	var listWithNames []ArticleWithNames
	err = query.Fields("a.*, c.name as city_name, cat.name as cate_name").
		Page(page, pageSize).
		OrderDesc("a.id").
		Scan(&listWithNames)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取信息列表失败")
	}

	// 转换为标准的entity.ZbArticle格式
	list = make([]entity.ZbArticle, len(listWithNames))
	for i, item := range listWithNames {
		list[i] = item.ZbArticle
	}

	return list, total, nil
}

// GetArticleListWithNames 获取带城市名称和类别名称的信息列表
func (s *sZbArticle) GetArticleListWithNames(ctx context.Context, page, pageSize int, title string, cityId, cateId, isDisable int, startTime, endTime string) (list []service.ArticleWithNames, total int, err error) {
	// 构建基础查询，使用表别名
	query := dao.ZbArticle.Ctx(ctx).As("a").
		LeftJoin("zb_city c", "a.city_id = c.id").
		LeftJoin("zb_cate cat", "a.cate_id = cat.id").
		Where("a.is_delete", int(packed.NO_DELETE))

	// 按标题筛选
	if title != "" {
		query = query.WhereLike("a.title", "%"+title+"%")
	}

	// 按城市筛选
	if cityId > 0 {
		query = query.Where("a.city_id", cityId)
	}

	// 按类别筛选
	if cateId > 0 {
		query = query.Where("a.cate_id", cateId)
	}

	// 按禁用状态筛选
	if isDisable >= 0 {
		query = query.Where("a.is_disable", isDisable)
	}

	// 按时间范围筛选
	if startTime != "" && endTime != "" {
		query = query.WhereBetween("a.created_at", startTime, endTime)
	}

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取信息总数失败")
	}

	// 获取列表数据，包含关联的城市名称和类别名称
	err = query.Fields("a.*, c.name as city_name, cat.name as cate_name").
		Page(page, pageSize).
		OrderDesc("a.id").
		Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取信息列表失败")
	}

	return list, total, nil
}

// GetArticleListWithNamesForMobile 获取带城市名称和类别名称的信息列表 移动端使用
func (s *sZbArticle) GetArticleListWithNamesForMobile(ctx context.Context, page, pageSize int, cityId, cateId int, title string) (list []service.ArticleWithNames, total int, err error) {
	// 构建基础查询，使用表别名
	query := dao.ZbArticle.Ctx(ctx).As("a").
		LeftJoin("zb_city c", "a.city_id = c.id").
		LeftJoin("zb_cate cat", "a.cate_id = cat.id").
		Where("a.is_delete", int(packed.NO_DELETE)).
		Where("a.is_disable", int(packed.ENABLE))

	// 按城市筛选
	if cityId > 0 {
		query = query.Where("a.city_id", cityId)
	}

	// 按类别筛选
	if cateId > 0 {
		query = query.Where("a.cate_id", cateId)
	}

	// 按标题筛选
	if title != "" {
		query = query.WhereLike("a.title", "%"+title+"%")
	}

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取信息总数失败")
	}

	// 获取列表数据，包含关联的城市名称和类别名称
	err = query.Fields("a.*, c.name as city_name, cat.name as cate_name").
		Page(page, pageSize).
		OrderDesc("a.id").
		Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取信息列表失败")
	}

	return list, total, nil
}

// GetArticleDetail 获取信息详情
func (s *sZbArticle) GetArticleDetail(ctx context.Context, id int64) (*entity.ZbArticle, error) {
	var article *entity.ZbArticle
	err := dao.ZbArticle.Ctx(ctx).Where("id", id).Where("is_delete", int(packed.NO_DELETE)).Scan(&article)
	if err != nil {
		return nil, gerror.Wrap(err, "获取信息详情失败")
	}
	if article == nil {
		return nil, gerror.New("信息不存在")
	}
	return article, nil
}

// GetArticleDetailWithNames 获取带城市名称和类别名称的信息详情
func (s *sZbArticle) GetArticleDetailWithNames(ctx context.Context, id int64) (*service.ArticleWithNames, error) {
	var article *service.ArticleWithNames

	err := dao.ZbArticle.Ctx(ctx).As("a").
		LeftJoin("zb_city c", "a.city_id = c.id").
		LeftJoin("zb_cate cat", "a.cate_id = cat.id").
		Where("a.id", id).
		Where("a.is_delete", int(packed.NO_DELETE)).
		Fields("a.*, c.name as city_name, cat.name as cate_name").
		Scan(&article)
	if err != nil {
		return nil, gerror.Wrap(err, "获取信息详情失败")
	}
	if article == nil {
		return nil, gerror.New("信息不存在")
	}

	return article, nil
}

// CreateArticle 创建信息
func (s *sZbArticle) CreateArticle(ctx context.Context, data *entity.ZbArticle) (int64, error) {
	// 设置创建时间
	data.CreatedAt = gtime.Now()
	data.UpdatedAt = gtime.Now()
	data.IsDelete = int(packed.NO_DELETE)
	if data.IsDisable == 0 {
		data.IsDisable = int(packed.ENABLE)
	}
	if data.ViewCount == 0 {
		data.ViewCount = 0
	}

	// 插入数据
	result, err := dao.ZbArticle.Ctx(ctx).Data(data).Insert()
	if err != nil {
		return 0, gerror.Wrap(err, "创建信息失败")
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, gerror.Wrap(err, "获取新创建信息ID失败")
	}

	return id, nil
}

// UpdateArticle 更新信息
func (s *sZbArticle) UpdateArticle(ctx context.Context, id int64, data *entity.ZbArticle) error {
	// 检查信息是否存在
	exists, err := s.CheckArticleExists(ctx, id)
	if err != nil {
		return err
	}
	if !exists {
		return gerror.New("信息不存在")
	}

	// 构建更新数据，只更新业务字段，不更新系统字段
	updateData := g.Map{
		"city_id":         data.CityId,
		"cate_id":         data.CateId,
		"title":           data.Title,
		"intro":           data.Intro,
		"full_content":    data.FullContent,
		"shieid_content":  data.ShieidContent,
		"seo_title":       data.SeoTitle,
		"seo_keywords":    data.SeoKeywords,
		"seo_description": data.SeoDescription,
		"pic":             data.Pic,
		"author":          data.Author,
		"is_disable":      data.IsDisable,
		"updated_at":      gtime.Now(),
	}

	// 执行更新
	_, err = dao.ZbArticle.Ctx(ctx).Where("id", id).Data(updateData).Update()
	if err != nil {
		return gerror.Wrap(err, "更新信息失败")
	}

	return nil
}

// UpdateArticleFields 更新信息字段（支持部分更新）
func (s *sZbArticle) UpdateArticleFields(ctx context.Context, id int64, fields map[string]interface{}) error {
	// 检查信息是否存在
	exists, err := s.CheckArticleExists(ctx, id)
	if err != nil {
		return err
	}
	if !exists {
		return gerror.New("信息不存在")
	}

	// 添加更新时间
	fields["updated_at"] = gtime.Now()

	// 执行更新
	_, err = dao.ZbArticle.Ctx(ctx).Where("id", id).Data(fields).Update()
	if err != nil {
		return gerror.Wrap(err, "更新信息失败")
	}

	return nil
}

// DeleteArticle 删除信息（软删除）
func (s *sZbArticle) DeleteArticle(ctx context.Context, id int64) error {
	// 检查信息是否存在
	exists, err := s.CheckArticleExists(ctx, id)
	if err != nil {
		return err
	}
	if !exists {
		return gerror.New("信息不存在")
	}

	// 软删除
	_, err = dao.ZbArticle.Ctx(ctx).Where("id", id).Data(g.Map{
		"is_delete":  int(packed.IS_DELETE),
		"deleted_at": gtime.Now(),
		"updated_at": gtime.Now(),
	}).Update()
	if err != nil {
		return gerror.Wrap(err, "删除信息失败")
	}

	return nil
}

// BatchDeleteArticle 批量删除信息（软删除）
func (s *sZbArticle) BatchDeleteArticle(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return gerror.New("请选择要删除的信息")
	}

	// 批量软删除
	_, err := dao.ZbArticle.Ctx(ctx).WhereIn("id", ids).Where("is_delete", int(packed.NO_DELETE)).Data(g.Map{
		"is_delete":  int(packed.IS_DELETE),
		"deleted_at": gtime.Now(),
		"updated_at": gtime.Now(),
	}).Update()
	if err != nil {
		return gerror.Wrap(err, "批量删除信息失败")
	}

	return nil
}

// SetArticleStatus 设置信息状态
func (s *sZbArticle) SetArticleStatus(ctx context.Context, id int64, isDisable int) error {
	// 检查信息是否存在
	exists, err := s.CheckArticleExists(ctx, id)
	if err != nil {
		return err
	}
	if !exists {
		return gerror.New("信息不存在")
	}

	// 更新状态
	_, err = dao.ZbArticle.Ctx(ctx).Where("id", id).Data(g.Map{
		"is_disable": isDisable,
		"updated_at": gtime.Now(),
	}).Update()
	if err != nil {
		return gerror.Wrap(err, "设置信息状态失败")
	}

	return nil
}

// CheckArticleExists 检查信息是否存在
func (s *sZbArticle) CheckArticleExists(ctx context.Context, id int64) (bool, error) {
	count, err := dao.ZbArticle.Ctx(ctx).Where("id", id).Where("is_delete", int(packed.NO_DELETE)).Count()
	if err != nil {
		return false, gerror.Wrap(err, "检查信息是否存在失败")
	}
	return count > 0, nil
}

// IncrementViewCount 增加浏览次数
func (s *sZbArticle) IncrementViewCount(ctx context.Context, id int64) error {
	_, err := dao.ZbArticle.Ctx(ctx).Where("id", id).Where("is_delete", int(packed.NO_DELETE)).Increment("view_count", 1)
	if err != nil {
		return gerror.Wrap(err, "增加浏览次数失败")
	}
	return nil
}

// GetArticlesByCity 根据城市获取信息列表
func (s *sZbArticle) GetArticlesByCity(ctx context.Context, cityId int, page, pageSize int) (list []entity.ZbArticle, total int, err error) {
	query := dao.ZbArticle.Ctx(ctx).Where("is_delete", int(packed.NO_DELETE)).Where("is_disable", int(packed.ENABLE)).Where("city_id", cityId)

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取城市信息总数失败")
	}

	// 获取列表数据
	err = query.Page(page, pageSize).OrderDesc("id").Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取城市信息列表失败")
	}

	return list, total, nil
}

// GetArticlesByCityWithNames 根据城市获取带名称的信息列表
func (s *sZbArticle) GetArticlesByCityWithNames(ctx context.Context, cityId int, page, pageSize int) (list []service.ArticleWithNames, total int, err error) {
	query := dao.ZbArticle.Ctx(ctx).As("a").
		LeftJoin("zb_city c", "a.city_id = c.id").
		LeftJoin("zb_cate cat", "a.cate_id = cat.id").
		Where("a.is_delete", int(packed.NO_DELETE)).
		Where("a.is_disable", int(packed.ENABLE)).
		Where("a.city_id", cityId)

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取城市信息总数失败")
	}

	// 获取列表数据
	err = query.Fields("a.*, c.name as city_name, cat.name as cate_name").
		Page(page, pageSize).
		OrderDesc("a.id").
		Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取城市信息列表失败")
	}

	return list, total, nil
}

// GetArticlesByCategory 根据类别获取信息列表
func (s *sZbArticle) GetArticlesByCategory(ctx context.Context, cateId int, page, pageSize int) (list []entity.ZbArticle, total int, err error) {
	query := dao.ZbArticle.Ctx(ctx).Where("is_delete", int(packed.NO_DELETE)).Where("is_disable", int(packed.ENABLE)).Where("cate_id", cateId)

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取类别信息总数失败")
	}

	// 获取列表数据
	err = query.Page(page, pageSize).OrderDesc("id").Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取类别信息列表失败")
	}

	return list, total, nil
}

// GetArticlesByCategoryWithNames 根据类别获取带名称的信息列表
func (s *sZbArticle) GetArticlesByCategoryWithNames(ctx context.Context, cateId int, page, pageSize int) (list []service.ArticleWithNames, total int, err error) {
	query := dao.ZbArticle.Ctx(ctx).As("a").
		LeftJoin("zb_city c", "a.city_id = c.id").
		LeftJoin("zb_cate cat", "a.cate_id = cat.id").
		Where("a.is_delete", int(packed.NO_DELETE)).
		Where("a.is_disable", int(packed.ENABLE)).
		Where("a.cate_id", cateId)

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取类别信息总数失败")
	}

	// 获取列表数据
	err = query.Fields("a.*, c.name as city_name, cat.name as cate_name").
		Page(page, pageSize).
		OrderDesc("a.id").
		Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取类别信息列表失败")
	}

	return list, total, nil
}

// GetHotArticles 获取热门信息（按浏览量排序）
func (s *sZbArticle) GetHotArticles(ctx context.Context, limit int) ([]entity.ZbArticle, error) {
	var list []entity.ZbArticle

	err := dao.ZbArticle.Ctx(ctx).
		Where("is_delete", int(packed.NO_DELETE)).
		Where("is_disable", int(packed.ENABLE)).
		OrderDesc("view_count").
		Limit(limit).
		Scan(&list)
	if err != nil {
		return nil, gerror.Wrap(err, "获取热门信息失败")
	}

	return list, nil
}

// GetHotArticlesWithNames 获取带名称的热门信息
func (s *sZbArticle) GetHotArticlesWithNames(ctx context.Context, limit int) ([]service.ArticleWithNames, error) {
	var list []service.ArticleWithNames

	err := dao.ZbArticle.Ctx(ctx).As("a").
		LeftJoin("zb_city c", "a.city_id = c.id").
		LeftJoin("zb_cate cat", "a.cate_id = cat.id").
		Where("a.is_delete", int(packed.NO_DELETE)).
		Where("a.is_disable", int(packed.ENABLE)).
		Fields("a.*, c.name as city_name, cat.name as cate_name").
		OrderDesc("a.view_count").
		Limit(limit).
		Scan(&list)
	if err != nil {
		return nil, gerror.Wrap(err, "获取热门信息失败")
	}

	return list, nil
}

// GetRecentArticles 获取最新信息
func (s *sZbArticle) GetRecentArticles(ctx context.Context, limit int) ([]entity.ZbArticle, error) {
	var list []entity.ZbArticle

	err := dao.ZbArticle.Ctx(ctx).
		Where("is_delete", int(packed.NO_DELETE)).
		Where("is_disable", int(packed.ENABLE)).
		OrderDesc("created_at").
		Limit(limit).
		Scan(&list)
	if err != nil {
		return nil, gerror.Wrap(err, "获取最新信息失败")
	}

	return list, nil
}

// GetRecentArticlesWithNames 获取带名称的最新信息
func (s *sZbArticle) GetRecentArticlesWithNames(ctx context.Context, limit int) ([]service.ArticleWithNames, error) {
	var list []service.ArticleWithNames

	err := dao.ZbArticle.Ctx(ctx).As("a").
		LeftJoin("zb_city c", "a.city_id = c.id").
		LeftJoin("zb_cate cat", "a.cate_id = cat.id").
		Where("a.is_delete", int(packed.NO_DELETE)).
		Where("a.is_disable", int(packed.ENABLE)).
		Fields("a.*, c.name as city_name, cat.name as cate_name").
		OrderDesc("a.created_at").
		Limit(limit).
		Scan(&list)
	if err != nil {
		return nil, gerror.Wrap(err, "获取最新信息失败")
	}

	return list, nil
}

// GetArticleStats 获取信息统计
func (s *sZbArticle) GetArticleStats(ctx context.Context, cateId, cityId int) (totalArticles, publishedArticles, disabledArticles, todayArticles int, err error) {
	// 构建基础查询条件
	baseQuery := dao.ZbArticle.Ctx(ctx).Where("is_delete", int(packed.NO_DELETE))

	// 添加分类筛选条件
	if cateId > 0 {
		baseQuery = baseQuery.Where("cate_id", cateId)
	}

	// 添加城市筛选条件
	if cityId > 0 {
		baseQuery = baseQuery.Where("city_id", cityId)
	}

	// 获取总信息数
	totalArticles, err = baseQuery.Count()
	if err != nil {
		return 0, 0, 0, 0, gerror.Wrap(err, "获取总信息数失败")
	}

	// 获取已发布信息数
	publishedQuery := baseQuery.Clone().Where("is_disable", int(packed.ENABLE))
	publishedArticles, err = publishedQuery.Count()
	if err != nil {
		return 0, 0, 0, 0, gerror.Wrap(err, "获取已发布信息数失败")
	}

	// 获取禁用信息数
	disabledQuery := baseQuery.Clone().Where("is_disable", int(packed.DISABLE))
	disabledArticles, err = disabledQuery.Count()
	if err != nil {
		return 0, 0, 0, 0, gerror.Wrap(err, "获取禁用信息数失败")
	}

	// 获取今日新增信息数
	today := gtime.Now().Format("Y-m-d")
	todayStart := today + " 00:00:00"
	todayEnd := today + " 23:59:59"

	todayQuery := baseQuery.Clone().WhereBetween("created_at", todayStart, todayEnd)
	todayArticles, err = todayQuery.Count()
	if err != nil {
		return 0, 0, 0, 0, gerror.Wrap(err, "获取今日新增信息数失败")
	}

	return totalArticles, publishedArticles, disabledArticles, todayArticles, nil
}

// SearchArticles 搜索信息
func (s *sZbArticle) SearchArticles(ctx context.Context, keyword string, page, pageSize int) (list []entity.ZbArticle, total int, err error) {
	if keyword == "" {
		return s.GetArticleList(ctx, page, pageSize, "", 0, 0, -1, "", "")
	}

	query := dao.ZbArticle.Ctx(ctx).Where("is_delete", int(packed.NO_DELETE)).Where("is_disable", int(packed.ENABLE))

	// 在标题、简介、作者中搜索关键词
	query = query.Where("(title LIKE ? OR intro LIKE ? OR author LIKE ?)",
		"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "搜索信息总数失败")
	}

	// 获取列表数据
	err = query.Page(page, pageSize).OrderDesc("id").Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "搜索信息列表失败")
	}

	return list, total, nil
}

// SearchArticlesWithNames 搜索带名称的信息
func (s *sZbArticle) SearchArticlesWithNames(ctx context.Context, keyword string, page, pageSize int) (list []service.ArticleWithNames, total int, err error) {
	if keyword == "" {
		return s.GetArticleListWithNames(ctx, page, pageSize, "", 0, 0, -1, "", "")
	}

	query := dao.ZbArticle.Ctx(ctx).As("a").
		LeftJoin("zb_city c", "a.city_id = c.id").
		LeftJoin("zb_cate cat", "a.cate_id = cat.id").
		Where("a.is_delete", int(packed.NO_DELETE)).
		Where("a.is_disable", int(packed.ENABLE))

	// 在标题、简介、作者中搜索关键词
	query = query.Where("(a.title LIKE ? OR a.intro LIKE ? OR a.author LIKE ?)",
		"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "搜索信息总数失败")
	}

	// 获取列表数据
	err = query.Fields("a.*, c.name as city_name, cat.name as cate_name").
		Page(page, pageSize).
		OrderDesc("a.id").
		Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "搜索信息列表失败")
	}

	return list, total, nil
}
