package service

import (
	"admin-server/internal/model/entity"
	"context"
)

// ISysResourcesGroup 资源分组服务接口
type ISysResourcesGroup interface {
	GetResourcesGroupList(ctx context.Context, page, pageSize int, name, resourceType string) (list []entity.SysResourcesGroup, total int, err error)
	GetResourcesGroupDetail(ctx context.Context, id int64) (*entity.SysResourcesGroup, error)
	CreateResourcesGroup(ctx context.Context, name, resourceType string) error
	UpdateResourcesGroup(ctx context.Context, id int64, name, resourceType string) error
	DeleteResourcesGroup(ctx context.Context, ids []int64) error
	GetAllResourcesGroups(ctx context.Context, resourceType string) ([]entity.SysResourcesGroup, error)
	CheckResourcesGroupNameExists(ctx context.Context, name string, excludeId int64) (bool, error)
}

var localSysResourcesGroup ISysResourcesGroup

func SysResourcesGroup() ISysResourcesGroup {
	if localSysResourcesGroup == nil {
		panic("ISysResourcesGroup接口未实现或未注册")
	}
	return localSysResourcesGroup
}

func RegisterSysResourcesGroup(i ISysResourcesGroup) {
	localSysResourcesGroup = i
}
