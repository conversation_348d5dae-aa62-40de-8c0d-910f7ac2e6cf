// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// WechatConfig is the golang structure for table wechat_config.
type WechatConfig struct {
	Id               int         `json:"id"               orm:"id"                description:""`                                                 //
	Appid            string      `json:"appid"            orm:"appid"             description:"微信公众号AppID"`                                       // 微信公众号AppID
	AppSecret        string      `json:"appSecret"        orm:"app_secret"        description:"微信公众号AppSecret"`                                   // 微信公众号AppSecret
	AutoReplyEnabled int         `json:"autoReplyEnabled" orm:"auto_reply_enabled" description:"关注自动回复开关：0=关闭，1=开启"`                              // 关注自动回复开关：0=关闭，1=开启
	AutoReplyText    string      `json:"autoReplyText" orm:"auto_reply_text" description:"关注自动回复语"`                                               // 关注自动回复语
	ServerUrl        string      `json:"serverUrl"        orm:"server_url"        description:"服务器地址URL"`                                         // 服务器地址URL
	Token            string      `json:"token"            orm:"token"             description:"微信Token"`                                          // 微信Token
	EncodingAesKey   string      `json:"encodingAesKey"   orm:"encoding_aes_key"  description:"消息加解密密钥"`                                          // 消息加解密密钥
	EncryptMode      string      `json:"encryptMode"      orm:"encrypt_mode"      description:"消息加解密方式：plaintext=明文模式，compatible=兼容模式，safe=安全模式"` // 消息加解密方式：plaintext=明文模式，compatible=兼容模式，safe=安全模式
	CreatedAt        *gtime.Time `json:"createdAt"        orm:"created_at"        description:"创建时间"`                                             // 创建时间
	UpdatedAt        *gtime.Time `json:"updatedAt"        orm:"updated_at"        description:"更新时间"`                                             // 更新时间
}
