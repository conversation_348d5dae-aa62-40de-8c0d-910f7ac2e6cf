package sys_admin

import (
	"context"

	v1 "admin-server/api/sys_admin/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error) {
	list, total, err := service.SysAdmin().GetList(ctx, req)
	if err != nil {
		return nil, err
	}

	return &v1.GetListRes{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}
