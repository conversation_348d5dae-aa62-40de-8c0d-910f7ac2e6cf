// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-server/internal/dao/internal"
)

// wechatMenuDao is the data access object for the table wechat_menu.
// You can define custom methods on it to extend its functionality as needed.
type wechatMenuDao struct {
	*internal.WechatMenuDao
}

var (
	// WechatMenu is a globally accessible object for table wechat_menu operations.
	WechatMenu = wechatMenuDao{internal.NewWechatMenuDao()}
)

// Add your custom methods and functionality below.
