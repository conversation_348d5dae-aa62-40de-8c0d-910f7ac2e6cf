package service

import (
	v1 "admin-server/api/sys_config/v1"
	"context"
)

// 1.定义接口
type ISysConfig interface {
	GetList(ctx context.Context, req *v1.GetListReq) (list []*v1.ConfigInfo, total int, err error)
	GetOne(ctx context.Context, id int64) (config *v1.ConfigInfo, err error)
	Create(ctx context.Context, req *v1.CreateReq) (insertId int64, err error)
	Update(ctx context.Context, req *v1.UpdateReq) (err error)
	Delete(ctx context.Context, id int64) (err error)
	GetByGroup(ctx context.Context, groupId int64) (list []*v1.ConfigInfo, err error)
	UpdateValue(ctx context.Context, id int64, value string) (err error)
	GetByKey(ctx context.Context, key string) (config *v1.ConfigInfo, err error)
	CheckConfigExists(ctx context.Context, id int64) (exists bool, err error)
	CheckConfigKeyExists(ctx context.Context, key string, excludeId int64) (exists bool, err error)
}

// 2.定义接口变量
var localSysConfig ISysConfig

// 3.定义一个获取接口实例的函数
func SysConfig() ISysConfig {
	if localSysConfig == nil {
		panic("ISysConfig接口未实现或未注册")
	}
	return localSysConfig
}

// 4.定义一个接口实现的注册方法
func RegisterSysConfig(i ISysConfig) {
	localSysConfig = i
}
