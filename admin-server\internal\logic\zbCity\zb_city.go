package zbCity

import (
	v1 "admin-server/api/zb_city/v1"
	"admin-server/internal/dao"
	"admin-server/internal/model/do"
	"admin-server/internal/model/entity"
	"admin-server/internal/service"
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

func init() {
	service.RegisterZbCity(&sZbCity{})
}

type sZbCity struct{}

// Create 创建开通城市
func (s *sZbCity) Create(ctx context.Context, req *v1.ZbCityCreateReq) (res *v1.ZbCityCreateRes, err error) {
	// 检查父级城市是否存在
	if req.Pid > 0 {
		var parentEntity *entity.ZbCity
		err = dao.ZbCity.Ctx(ctx).Where("id", req.Pid).Where("is_delete", 0).<PERSON>an(&parentEntity)
		if err != nil {
			g.Log().Error(ctx, "查询父级城市失败:", err)
			return nil, gerror.New("查询父级城市失败")
		}
		if parentEntity == nil {
			return nil, gerror.New("父级城市不存在")
		}
	}

	// 检查同级城市名称是否重复
	count, err := dao.ZbCity.Ctx(ctx).Where("pid", req.Pid).Where("name", req.Name).Where("is_delete", 0).Count()
	if err != nil {
		g.Log().Error(ctx, "检查城市名称重复失败:", err)
		return nil, gerror.New("检查城市名称重复失败")
	}
	if count > 0 {
		return nil, gerror.New("同级城市名称已存在")
	}

	now := gtime.Now()
	cityData := do.ZbCity{
		Pid:       req.Pid,
		Name:      req.Name,
		Sort:      req.Sort,
		IsDisable: req.IsDisable,
		IsDelete:  0,
		CreatedAt: now,
		UpdatedAt: now,
	}

	insertResult, err := dao.ZbCity.Ctx(ctx).Insert(cityData)
	if err != nil {
		g.Log().Error(ctx, "创建城市失败:", err)
		return nil, gerror.New("创建城市失败")
	}

	insertId, err := insertResult.LastInsertId()
	if err != nil {
		g.Log().Error(ctx, "获取插入ID失败:", err)
		return nil, gerror.New("获取插入ID失败")
	}

	g.Log().Info(ctx, "创建城市成功:", "id:", insertId, "name:", req.Name)
	res = &v1.ZbCityCreateRes{
		Id: int(insertId),
	}
	return res, nil
}

// Update 更新开通城市
func (s *sZbCity) Update(ctx context.Context, req *v1.ZbCityUpdateReq) (res *v1.ZbCityUpdateRes, err error) {
	// 检查城市是否存在
	var existingEntity *entity.ZbCity
	err = dao.ZbCity.Ctx(ctx).Where("id", req.Id).Where("is_delete", 0).Scan(&existingEntity)
	if err != nil {
		g.Log().Error(ctx, "查询城市失败:", err)
		return nil, gerror.New("查询城市失败")
	}
	if existingEntity == nil {
		return nil, gerror.New("城市不存在")
	}

	// 检查父级城市是否存在
	if req.Pid > 0 {
		var parentEntity *entity.ZbCity
		err = dao.ZbCity.Ctx(ctx).Where("id", req.Pid).Where("is_delete", 0).Scan(&parentEntity)
		if err != nil {
			g.Log().Error(ctx, "查询父级城市失败:", err)
			return nil, gerror.New("查询父级城市失败")
		}
		if parentEntity == nil {
			return nil, gerror.New("父级城市不存在")
		}
	}

	// 检查是否设置自己为父级
	if req.Pid == req.Id {
		return nil, gerror.New("不能设置自己为父级城市")
	}

	// 检查同级城市名称是否重复（排除自己）
	count, err := dao.ZbCity.Ctx(ctx).Where("pid", req.Pid).Where("name", req.Name).Where("id !=", req.Id).Where("is_delete", 0).Count()
	if err != nil {
		g.Log().Error(ctx, "检查城市名称重复失败:", err)
		return nil, gerror.New("检查城市名称重复失败")
	}
	if count > 0 {
		return nil, gerror.New("同级城市名称已存在")
	}

	cityData := do.ZbCity{
		Pid:       req.Pid,
		Name:      req.Name,
		Sort:      req.Sort,
		IsDisable: req.IsDisable,
		UpdatedAt: gtime.Now(),
	}

	_, err = dao.ZbCity.Ctx(ctx).Where("id", req.Id).Update(cityData)
	if err != nil {
		g.Log().Error(ctx, "更新城市失败:", err)
		return nil, gerror.New("更新城市失败")
	}

	g.Log().Info(ctx, "更新城市成功:", "id:", req.Id, "name:", req.Name)
	res = &v1.ZbCityUpdateRes{}
	return res, nil
}

// Delete 删除开通城市
func (s *sZbCity) Delete(ctx context.Context, req *v1.ZbCityDeleteReq) (res *v1.ZbCityDeleteRes, err error) {
	// 检查城市是否存在
	var existingEntity *entity.ZbCity
	err = dao.ZbCity.Ctx(ctx).Where("id", req.Id).Where("is_delete", 0).Scan(&existingEntity)
	if err != nil {
		g.Log().Error(ctx, "查询城市失败:", err)
		return nil, gerror.New("查询城市失败")
	}
	if existingEntity == nil {
		return nil, gerror.New("城市不存在")
	}

	// 检查是否有子城市
	childCount, err := dao.ZbCity.Ctx(ctx).Where("pid", req.Id).Where("is_delete", 0).Count()
	if err != nil {
		g.Log().Error(ctx, "检查子城市失败:", err)
		return nil, gerror.New("检查子城市失败")
	}
	if childCount > 0 {
		return nil, gerror.New("存在子城市，无法删除")
	}

	// 软删除
	_, err = dao.ZbCity.Ctx(ctx).Where("id", req.Id).Update(do.ZbCity{
		IsDelete:  1,
		DeletedAt: gtime.Now(),
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "删除城市失败:", err)
		return nil, gerror.New("删除城市失败")
	}

	g.Log().Info(ctx, "删除城市成功:", "id:", req.Id)
	res = &v1.ZbCityDeleteRes{}
	return res, nil
}

// GetOne 获取单个开通城市
func (s *sZbCity) GetOne(ctx context.Context, req *v1.ZbCityGetOneReq) (res *v1.ZbCityGetOneRes, err error) {
	var entity *entity.ZbCity
	err = dao.ZbCity.Ctx(ctx).Where("id", req.Id).Where("is_delete", 0).Scan(&entity)
	if err != nil {
		g.Log().Error(ctx, "查询城市失败:", err)
		return nil, gerror.New("查询城市失败")
	}
	if entity == nil {
		return nil, gerror.New("城市不存在")
	}

	cityInfo := &v1.ZbCityInfo{}
	if err = gconv.Struct(entity, cityInfo); err != nil {
		g.Log().Error(ctx, "数据转换失败:", err)
		return nil, gerror.New("数据转换失败")
	}

	res = &v1.ZbCityGetOneRes{
		ZbCityInfo: cityInfo,
	}
	return res, nil
}

// GetList 获取开通城市列表
func (s *sZbCity) GetList(ctx context.Context, req *v1.ZbCityGetListReq) (res *v1.ZbCityGetListRes, err error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	query := dao.ZbCity.Ctx(ctx).Where("is_delete", 0)

	if req.Name != "" {
		query = query.WhereLike("name", "%"+req.Name+"%")
	}
	if req.IsDisable != nil {
		query = query.Where("is_disable", *req.IsDisable)
	}
	if req.Pid != nil {
		query = query.Where("pid", *req.Pid)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		g.Log().Error(ctx, "查询城市总数失败:", err)
		return nil, gerror.New("查询城市总数失败")
	}

	// 获取列表数据
	var entities []*entity.ZbCity
	err = query.OrderAsc("sort").OrderAsc("id").Page(req.Page, req.PageSize).Scan(&entities)
	if err != nil {
		g.Log().Error(ctx, "查询城市列表失败:", err)
		return nil, gerror.New("查询城市列表失败")
	}

	// 转换数据
	var list []*v1.ZbCityInfo
	if err = gconv.Structs(entities, &list); err != nil {
		g.Log().Error(ctx, "数据转换失败:", err)
		return nil, gerror.New("数据转换失败")
	}

	res = &v1.ZbCityGetListRes{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}
	return res, nil
}

// GetTree 获取开通城市树形结构
func (s *sZbCity) GetTree(ctx context.Context, req *v1.ZbCityGetTreeReq) (res *v1.ZbCityGetTreeRes, err error) {
	// 构建查询条件
	query := dao.ZbCity.Ctx(ctx).Where("is_delete", 0)
	if req.IsDisable != nil {
		query = query.Where("is_disable", *req.IsDisable)
	}

	// 获取所有城市数据
	var entities []*entity.ZbCity
	err = query.OrderAsc("sort").OrderAsc("id").Scan(&entities)
	if err != nil {
		g.Log().Error(ctx, "查询城市列表失败:", err)
		return nil, gerror.New("查询城市列表失败")
	}

	// 转换数据
	var cities []*v1.ZbCityInfo
	if err = gconv.Structs(entities, &cities); err != nil {
		g.Log().Error(ctx, "数据转换失败:", err)
		return nil, gerror.New("数据转换失败")
	}

	// 构建树形结构
	tree, err := s.BuildTree(ctx, cities, 0)
	if err != nil {
		return nil, err
	}

	res = &v1.ZbCityGetTreeRes{
		List: tree,
	}
	return res, nil
}

// UpdateSort 更新开通城市排序
func (s *sZbCity) UpdateSort(ctx context.Context, req *v1.ZbCityUpdateSortReq) (res *v1.ZbCityUpdateSortRes, err error) {
	// 检查城市是否存在
	var existingEntity *entity.ZbCity
	err = dao.ZbCity.Ctx(ctx).Where("id", req.Id).Where("is_delete", 0).Scan(&existingEntity)
	if err != nil {
		g.Log().Error(ctx, "查询城市失败:", err)
		return nil, gerror.New("查询城市失败")
	}
	if existingEntity == nil {
		return nil, gerror.New("城市不存在")
	}

	_, err = dao.ZbCity.Ctx(ctx).Where("id", req.Id).Update(do.ZbCity{
		Sort:      req.Sort,
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "更新城市排序失败:", err)
		return nil, gerror.New("更新城市排序失败")
	}

	g.Log().Info(ctx, "更新城市排序成功:", "id:", req.Id, "sort:", req.Sort)
	res = &v1.ZbCityUpdateSortRes{}
	return res, nil
}

// UpdateStatus 更新开通城市状态
func (s *sZbCity) UpdateStatus(ctx context.Context, req *v1.ZbCityUpdateStatusReq) (res *v1.ZbCityUpdateStatusRes, err error) {
	// 检查城市是否存在
	var existingEntity *entity.ZbCity
	err = dao.ZbCity.Ctx(ctx).Where("id", req.Id).Where("is_delete", 0).Scan(&existingEntity)
	if err != nil {
		g.Log().Error(ctx, "查询城市失败:", err)
		return nil, gerror.New("查询城市失败")
	}
	if existingEntity == nil {
		return nil, gerror.New("城市不存在")
	}

	_, err = dao.ZbCity.Ctx(ctx).Where("id", req.Id).Update(do.ZbCity{
		IsDisable: req.IsDisable,
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "更新城市状态失败:", err)
		return nil, gerror.New("更新城市状态失败")
	}

	g.Log().Info(ctx, "更新城市状态成功:", "id:", req.Id, "is_disable:", req.IsDisable)
	res = &v1.ZbCityUpdateStatusRes{}
	return res, nil
}

// BuildTree 构建树形结构
func (s *sZbCity) BuildTree(ctx context.Context, cities []*v1.ZbCityInfo, pid int) (tree []*v1.ZbCityTreeInfo, err error) {
	for _, city := range cities {
		if city.Pid == pid {
			treeNode := &v1.ZbCityTreeInfo{
				ZbCityInfo: city,
			}

			// 递归获取子节点
			children, err := s.BuildTree(ctx, cities, city.Id)
			if err != nil {
				return nil, err
			}
			if len(children) > 0 {
				treeNode.Children = children
			}

			tree = append(tree, treeNode)
		}
	}
	return tree, nil
}

// GetChildren 获取子城市
func (s *sZbCity) GetChildren(ctx context.Context, pid int) (children []*v1.ZbCityInfo, err error) {
	var entities []*entity.ZbCity
	err = dao.ZbCity.Ctx(ctx).Where("pid", pid).Where("is_delete", 0).OrderAsc("sort").OrderAsc("id").Scan(&entities)
	if err != nil {
		g.Log().Error(ctx, "查询子城市失败:", err)
		return nil, gerror.New("查询子城市失败")
	}

	if err = gconv.Structs(entities, &children); err != nil {
		g.Log().Error(ctx, "数据转换失败:", err)
		return nil, gerror.New("数据转换失败")
	}

	return children, nil
}
