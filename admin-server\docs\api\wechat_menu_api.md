# 微信菜单管理API - 前端对接文档

## 接口概览

| 接口名称 | 方法 | 路径 | 说明 |
|---------|------|------|------|
| 创建菜单 | POST | `/wechat/menu` | 创建新的微信菜单 |
| 更新菜单 | PUT | `/wechat/menu/{id}` | 更新指定菜单 |
| 删除菜单 | DELETE | `/wechat/menu/{id}` | 删除指定菜单 |
| 获取菜单详情 | GET | `/wechat/menu/{id}` | 获取单个菜单详情 |
| 获取菜单列表 | GET | `/wechat/menu/list` | 分页获取菜单列表 |
| 获取菜单树 | GET | `/wechat/menu/tree` | 获取树形菜单结构 |
| 更新排序 | PUT | `/wechat/menu/{id}/sort` | 更新菜单排序 |
| 更新状态 | PUT | `/wechat/menu/{id}/status` | 更新菜单状态 |
| 发布菜单 | POST | `/wechat/menu/publish` | 发布菜单到微信服务器 |

## 数据模型

### WechatMenuInfo

```typescript
interface WechatMenuInfo {
  id: number;           // 菜单ID
  pid: number;          // 父菜单ID，0表示一级菜单
  menu_name: string;    // 菜单名称
  menu_type: string;    // 菜单类型：click/view/miniprogram
  menu_key?: string;    // 菜单KEY值（click类型必填）
  menu_url?: string;    // 菜单链接（view类型必填）
  appid?: string;       // 小程序AppID（miniprogram类型必填）
  pagepath?: string;    // 小程序页面路径（miniprogram类型必填）
  sort: number;         // 排序
  level: number;        // 菜单层级：1=一级，2=二级
  is_disable: number;   // 是否禁用：0=否，1=是
  created_at: string;   // 创建时间
  updated_at: string;   // 更新时间
}
```

### WechatMenuTreeInfo

```typescript
interface WechatMenuTreeInfo extends WechatMenuInfo {
  children?: WechatMenuTreeInfo[];  // 子菜单
}
```

## 接口详情

### 1. 创建菜单

```typescript
// 请求
interface CreateRequest {
  pid?: number;         // 父菜单ID，默认0
  menu_name: string;    // 菜单名称
  menu_type: string;    // 菜单类型
  menu_key?: string;    // 菜单KEY值
  menu_url?: string;    // 菜单链接
  appid?: string;       // 小程序AppID
  pagepath?: string;    // 小程序页面路径
  sort?: number;        // 排序，默认0
}

// 响应
interface CreateResponse {
  id: number;           // 创建的菜单ID
}
```

### 2. 更新菜单

```typescript
// 请求（所有字段都需要传递）
interface UpdateRequest {
  pid: number;
  menu_name: string;
  menu_type: string;
  menu_key?: string;
  menu_url?: string;
  appid?: string;
  pagepath?: string;
  sort: number;
}

// 响应
interface UpdateResponse {}
```

### 3. 获取菜单列表

```typescript
// 请求
interface GetListRequest {
  page?: number;        // 页码，默认1
  page_size?: number;   // 每页数量，默认10
  menu_name?: string;   // 菜单名称模糊搜索
  menu_type?: string;   // 菜单类型筛选
  is_disable?: number;  // 状态筛选：0=启用，1=禁用
  pid?: number;         // 父菜单ID筛选
}

// 响应
interface GetListResponse {
  list: WechatMenuInfo[];
  total: number;
  page: number;
  page_size: number;
}
```

### 4. 获取菜单树

```typescript
// 请求
interface GetTreeRequest {
  is_disable?: number;  // 状态筛选：0=启用，1=禁用
}

// 响应
interface GetTreeResponse {
  list: WechatMenuTreeInfo[];
}
```

### 5. 更新排序

```typescript
// 请求
interface UpdateSortRequest {
  sort: number;         // 新的排序值
}

// 响应
interface UpdateSortResponse {}
```

### 6. 更新状态

```typescript
// 请求
interface UpdateStatusRequest {
  is_disable: number;   // 0=启用，1=禁用
}

// 响应
interface UpdateStatusResponse {}
```

### 7. 发布菜单

```typescript
// 请求
interface PublishRequest {}

// 响应
interface PublishResponse {
  success: boolean;     // 发布是否成功
  message: string;      // 发布结果消息
}
```

## 前端使用示例

### Vue 3 + TypeScript 示例

```typescript
import { ref, reactive } from 'vue'
import axios from 'axios'

// 菜单管理组合式函数
export function useWechatMenu() {
  const menuList = ref<WechatMenuInfo[]>([])
  const menuTree = ref<WechatMenuTreeInfo[]>([])
  const loading = ref(false)

  // 获取菜单列表
  const getMenuList = async (params: GetListRequest = {}) => {
    loading.value = true
    try {
      const response = await axios.get('/wechat/menu/list', { params })
      menuList.value = response.data.data.list
      return response.data.data
    } finally {
      loading.value = false
    }
  }

  // 获取菜单树
  const getMenuTree = async (params: GetTreeRequest = {}) => {
    loading.value = true
    try {
      const response = await axios.get('/wechat/menu/tree', { params })
      menuTree.value = response.data.data.list
      return response.data.data.list
    } finally {
      loading.value = false
    }
  }

  // 创建菜单
  const createMenu = async (data: CreateRequest) => {
    const response = await axios.post('/wechat/menu', data)
    return response.data.data
  }

  // 更新菜单
  const updateMenu = async (id: number, data: UpdateRequest) => {
    const response = await axios.put(`/wechat/menu/${id}`, data)
    return response.data.data
  }

  // 删除菜单
  const deleteMenu = async (id: number) => {
    const response = await axios.delete(`/wechat/menu/${id}`)
    return response.data.data
  }

  // 更新菜单状态
  const updateMenuStatus = async (id: number, isDisable: number) => {
    const response = await axios.put(`/wechat/menu/${id}/status`, {
      is_disable: isDisable
    })
    return response.data.data
  }

  // 更新菜单排序
  const updateMenuSort = async (id: number, sort: number) => {
    const response = await axios.put(`/wechat/menu/${id}/sort`, { sort })
    return response.data.data
  }

  // 发布菜单到微信服务器
  const publishMenu = async () => {
    const response = await axios.post('/wechat/menu/publish')
    return response.data.data
  }

  return {
    menuList,
    menuTree,
    loading,
    getMenuList,
    getMenuTree,
    createMenu,
    updateMenu,
    deleteMenu,
    updateMenuStatus,
    updateMenuSort,
    publishMenu
  }
}
```

### React + TypeScript 示例

```typescript
import { useState, useCallback } from 'react'
import axios from 'axios'

export function useWechatMenu() {
  const [menuList, setMenuList] = useState<WechatMenuInfo[]>([])
  const [menuTree, setMenuTree] = useState<WechatMenuTreeInfo[]>([])
  const [loading, setLoading] = useState(false)

  const getMenuList = useCallback(async (params: GetListRequest = {}) => {
    setLoading(true)
    try {
      const response = await axios.get('/wechat/menu/list', { params })
      setMenuList(response.data.data.list)
      return response.data.data
    } finally {
      setLoading(false)
    }
  }, [])

  const getMenuTree = useCallback(async (params: GetTreeRequest = {}) => {
    setLoading(true)
    try {
      const response = await axios.get('/wechat/menu/tree', { params })
      setMenuTree(response.data.data.list)
      return response.data.data.list
    } finally {
      setLoading(false)
    }
  }, [])

  const createMenu = useCallback(async (data: CreateRequest) => {
    const response = await axios.post('/wechat/menu', data)
    return response.data.data
  }, [])

  const updateMenu = useCallback(async (id: number, data: UpdateRequest) => {
    const response = await axios.put(`/wechat/menu/${id}`, data)
    return response.data.data
  }, [])

  const deleteMenu = useCallback(async (id: number) => {
    const response = await axios.delete(`/wechat/menu/${id}`)
    return response.data.data
  }, [])

  const publishMenu = useCallback(async () => {
    const response = await axios.post('/wechat/menu/publish')
    return response.data.data
  }, [])

  return {
    menuList,
    menuTree,
    loading,
    getMenuList,
    getMenuTree,
    createMenu,
    updateMenu,
    deleteMenu,
    publishMenu
  }
}
```

## 注意事项

1. **认证**: 所有接口都需要在请求头中携带JWT Token
2. **权限**: 需要相应的微信菜单管理权限
3. **菜单类型**: 根据不同类型填写对应的必填字段
4. **层级限制**: 最多支持2级菜单
5. **删除限制**: 有子菜单的父菜单无法直接删除
