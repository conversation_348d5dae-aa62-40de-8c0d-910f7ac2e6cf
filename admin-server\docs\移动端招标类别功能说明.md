# 移动端招标类别功能说明

## 功能概述

在移动端列表页面 (`/m/list`) 的顶部添加了动态加载的招标类别标签，用户可以点击不同的类别来筛选相关内容。

## 实现功能

### 1. 动态加载招标类别
- **接口**: `GET /m/api/zb_cate/all?is_disable=0`
- **说明**: 页面加载完成后自动请求招标类别数据
- **显示位置**: 页面顶部的分类标签区域

### 2. 类别标签展示
- **默认标签**: "全部" (data-id="0")
- **动态标签**: 根据API返回的数据动态生成
- **样式**: 圆角标签，支持横向滚动
- **交互**: 点击切换选中状态

### 3. 筛选功能
- **全部**: 显示所有内容
- **具体类别**: 根据选中的类别筛选内容
- **状态切换**: 选中的标签高亮显示

## 接口数据格式

### 请求
```
GET /m/api/zb_cate/all?is_disable=0
```

### 响应格式
```json
{
  "code": 0,
  "message": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "工程招标",
        "sort": 1,
        "is_disable": 0,
        "is_delete": 0,
        "created_at": "2025-07-15 11:32:06",
        "updated_at": "2025-07-15 11:36:47",
        "deleted_at": null
      },
      {
        "id": 2,
        "name": "货物招标",
        "sort": 1,
        "is_disable": 0,
        "is_delete": 0,
        "created_at": "2025-07-15 11:32:17",
        "updated_at": "2025-07-15 11:52:51",
        "deleted_at": null
      }
    ]
  }
}
```

## 页面结构

### HTML结构
```html
<div id="categoryTabs" class="flex space-x-2 overflow-x-auto pb-2">
    <!-- 默认的"全部"标签 -->
    <span class="category-tab active bg-white/90 text-purple-600 px-4 py-2 rounded-full text-xs font-medium whitespace-nowrap cursor-pointer" data-id="0">全部</span>
    
    <!-- 动态生成的类别标签 -->
    <span class="category-tab bg-white/20 text-white px-4 py-2 rounded-full text-xs whitespace-nowrap cursor-pointer" data-id="1">工程招标</span>
    <span class="category-tab bg-white/20 text-white px-4 py-2 rounded-full text-xs whitespace-nowrap cursor-pointer" data-id="2">货物招标</span>
    <!-- ... 更多类别 ... -->
</div>
```

### CSS样式
```css
/* 类别标签样式 */
.category-tab {
    transition: all 0.3s ease;
}
.category-tab.active {
    font-weight: 600;
}

/* 横向滚动优化 */
#categoryTabs {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
}
#categoryTabs::-webkit-scrollbar {
    display: none;
}
```

## JavaScript功能

### 1. 页面加载时执行
```javascript
document.addEventListener('DOMContentLoaded', function() {
    // 加载招标类别
    loadCategories();
    
    // 为"全部"标签添加点击事件
    const allTab = document.querySelector('.category-tab[data-id="0"]');
    if (allTab) {
        allTab.addEventListener('click', function() {
            // 切换选中状态和筛选逻辑
        });
    }
});
```

### 2. 加载招标类别
```javascript
async function loadCategories() {
    try {
        const response = await fetch('/m/api/zb_cate/all?is_disable=0');
        const result = await response.json();
        
        if (result.code === 0 && result.data && result.data.list) {
            const categoryTabs = document.getElementById('categoryTabs');
            const categories = result.data.list;
            
            // 动态创建类别标签
            categories.forEach(category => {
                const categoryTab = document.createElement('span');
                categoryTab.className = 'category-tab bg-white/20 text-white px-4 py-2 rounded-full text-xs whitespace-nowrap cursor-pointer';
                categoryTab.textContent = category.name;
                categoryTab.dataset.id = category.id;
                
                // 添加点击事件
                categoryTab.addEventListener('click', function() {
                    // 切换选中状态
                    switchActiveTab(this);
                    // 筛选数据
                    filterByCategory(category.id);
                });
                
                categoryTabs.appendChild(categoryTab);
            });
        }
    } catch (error) {
        console.error('加载招标类别失败:', error);
    }
}
```

### 3. 切换选中状态
```javascript
function switchActiveTab(activeTab) {
    // 移除所有标签的选中状态
    document.querySelectorAll('.category-tab').forEach(tab => {
        tab.classList.remove('active', 'bg-white/90', 'text-purple-600');
        tab.classList.add('bg-white/20', 'text-white');
    });
    
    // 设置当前标签为选中状态
    activeTab.classList.remove('bg-white/20', 'text-white');
    activeTab.classList.add('active', 'bg-white/90', 'text-purple-600');
}
```

### 4. 筛选功能
```javascript
function filterByCategory(categoryId) {
    console.log('筛选类别:', categoryId);
    
    if (categoryId === 0) {
        // 显示所有内容
        showAllContent();
    } else {
        // 根据类别ID筛选内容
        showCategoryContent(categoryId);
    }
}
```

## 测试方法

### 1. 基础功能测试
1. 访问 `http://localhost:8000/m/list`
2. 观察页面顶部是否显示类别标签
3. 检查是否有"全部"标签和动态加载的类别标签

### 2. 接口测试
1. 打开浏览器开发者工具
2. 查看Network标签页
3. 刷新页面，确认是否发送了 `/m/api/zb_cate/all?is_disable=0` 请求
4. 检查响应数据格式是否正确

### 3. 交互测试
1. 点击不同的类别标签
2. 观察标签的选中状态是否正确切换
3. 检查控制台是否输出相应的筛选信息

### 4. 样式测试
1. 在不同屏幕尺寸下测试横向滚动
2. 检查标签的颜色和字体是否正确
3. 测试点击动画效果

## 错误处理

### 1. 接口请求失败
- 在控制台输出错误信息
- 页面仍显示默认的"全部"标签

### 2. 数据格式错误
- 检查响应数据的code和data字段
- 输出详细的错误信息到控制台

### 3. DOM操作异常
- 确保页面元素存在后再进行操作
- 使用try-catch包装关键代码

## 扩展功能建议

### 1. 加载状态
- 添加类别加载的loading状态
- 显示加载动画或占位符

### 2. 缓存机制
- 将类别数据缓存到localStorage
- 减少重复的API请求

### 3. 错误重试
- 接口失败时提供重试按钮
- 自动重试机制

### 4. 数据筛选
- 实现真正的内容筛选功能
- 与后端API集成进行数据过滤

## 注意事项

1. **接口权限**: `/m/api/zb_cate/all` 接口无需token验证
2. **数据更新**: 类别数据变更后需要刷新页面才能看到最新数据
3. **兼容性**: 使用了现代JavaScript语法，需要现代浏览器支持
4. **性能**: 类别数量较多时考虑分页或虚拟滚动
5. **用户体验**: 保持标签的响应速度和流畅的动画效果
