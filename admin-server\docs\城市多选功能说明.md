# 城市多选功能说明

## 功能概述

在package.html页面中实现了动态加载城市列表和多选功能，用户可以选择多个城市来定制招标信息服务。

## 实现的功能

### 1. 动态城市加载
- 页面加载完成后自动请求 `/m/api/zb_city/tree` 接口
- 显示加载状态和错误处理
- 支持重新加载功能

### 2. 城市多选功能
- 支持选择多个城市
- 实时显示选中状态
- 动态更新选中城市列表

### 3. 用户界面优化
- 响应式网格布局（3列）
- 选中状态的视觉反馈
- 加载动画和错误提示

## 接口说明

### 请求接口
```
GET /m/api/zb_city/tree
```

### 返回数据格式
```json
{
  "code": 0,
  "message": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "pid": 0,
        "name": "北京",
        "sort": 1,
        "is_disable": 0,
        "is_delete": 0,
        "created_at": "2025-07-15 10:37:25",
        "updated_at": "2025-07-15 11:40:16",
        "deleted_at": null
      },
      {
        "id": 2,
        "pid": 0,
        "name": "浙江",
        "sort": 1,
        "is_disable": 0,
        "is_delete": 0,
        "created_at": "2025-07-15 10:37:35",
        "updated_at": "2025-07-15 10:50:06",
        "deleted_at": null
      }
    ]
  }
}
```

## 页面结构

### 1. HTML结构
```html
<!-- 加载状态 -->
<div id="cityLoading" class="text-center py-8">
    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
    <p class="text-sm text-gray-600 mt-2">正在加载城市列表...</p>
</div>

<!-- 城市网格 -->
<div id="cityGrid" class="grid grid-cols-3 gap-3 mb-4" style="display: none;">
    <!-- 动态生成的城市按钮 -->
</div>

<!-- 已选择城市提示 -->
<div id="selectedCitiesInfo" class="bg-blue-50 border-l-4 border-blue-400 p-3 rounded" style="display: none;">
    <p class="text-sm text-blue-700">
        <i class="fas fa-info-circle mr-2"></i>
        已选择：<span id="selectedCitiesText" class="font-semibold"></span> 地区的招标信息服务
    </p>
</div>

<!-- 错误状态 -->
<div id="cityError" class="text-center py-8" style="display: none;">
    <i class="fas fa-exclamation-triangle text-red-500 text-2xl mb-2"></i>
    <p class="text-sm text-gray-600 mb-3">城市列表加载失败</p>
    <button onclick="loadCities()" class="bg-purple-500 text-white px-4 py-2 rounded-lg text-sm">
        重新加载
    </button>
</div>
```

### 2. CSS样式
```css
/* 城市选择样式 */
.city-option { 
    transition: all 0.2s ease; 
    position: relative;
    overflow: hidden;
}

.city-option:hover { 
    transform: translateY(-1px); 
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.city-option.selected {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    border-color: #8b5cf6;
    color: white;
    font-weight: 600;
}

.city-option.selected::after {
    content: '✓';
    position: absolute;
    top: 4px;
    right: 6px;
    font-size: 12px;
    font-weight: bold;
}
```

## JavaScript功能

### 1. 页面初始化
```javascript
document.addEventListener('DOMContentLoaded', function() {
    loadCities();
});
```

### 2. 加载城市列表
```javascript
async function loadCities() {
    try {
        const response = await fetch('/m/api/zb_city/tree');
        const result = await response.json();
        
        if (result.code === 0 && result.data && result.data.list) {
            allCities = result.data.list;
            renderCities(allCities);
        } else {
            throw new Error(result.message || '数据格式错误');
        }
    } catch (error) {
        console.error('加载城市列表失败:', error);
        // 显示错误状态
    }
}
```

### 3. 渲染城市按钮
```javascript
function renderCities(cities) {
    const grid = document.getElementById('cityGrid');
    grid.innerHTML = '';
    
    cities.forEach(city => {
        const button = document.createElement('button');
        button.className = 'city-option bg-white border-2 border-gray-200 text-gray-600 py-3 rounded-lg text-sm font-medium';
        button.textContent = city.name;
        button.dataset.cityId = city.id;
        button.dataset.cityName = city.name;
        
        button.addEventListener('click', function() {
            toggleCitySelection(this);
        });
        
        grid.appendChild(button);
    });
}
```

### 4. 城市选择切换
```javascript
function toggleCitySelection(button) {
    const cityId = parseInt(button.dataset.cityId);
    const cityName = button.dataset.cityName;
    
    if (button.classList.contains('selected')) {
        // 取消选择
        button.classList.remove('selected');
        selectedCities = selectedCities.filter(city => city.id !== cityId);
    } else {
        // 选择城市
        button.classList.add('selected');
        selectedCities.push({ id: cityId, name: cityName });
    }
    
    updateSelectedCitiesDisplay();
}
```

### 5. 更新选中显示
```javascript
function updateSelectedCitiesDisplay() {
    const info = document.getElementById('selectedCitiesInfo');
    const text = document.getElementById('selectedCitiesText');
    
    if (selectedCities.length === 0) {
        info.style.display = 'none';
    } else {
        const cityNames = selectedCities.map(city => city.name).join('、');
        text.textContent = cityNames;
        info.style.display = 'block';
    }
}
```

## 提供的API函数

### 1. 获取选中城市
```javascript
// 获取完整的城市对象数组
const selectedCities = getSelectedCities();
// 返回: [{ id: 1, name: "北京" }, { id: 2, name: "浙江" }]

// 获取城市ID数组
const cityIds = getSelectedCityIds();
// 返回: [1, 2]

// 获取城市名称数组
const cityNames = getSelectedCityNames();
// 返回: ["北京", "浙江"]
```

### 2. 操作选择状态
```javascript
// 清空所有选择
clearAllSelections();

// 设置默认选中的城市
setDefaultSelectedCities([1, 2]); // 选中ID为1和2的城市
```

## 用户交互流程

### 1. 页面加载流程
1. 页面加载完成
2. 显示"正在加载城市列表..."
3. 请求 `/m/api/zb_city/tree` 接口
4. 成功：显示城市网格
5. 失败：显示错误提示和重试按钮

### 2. 城市选择流程
1. 用户点击城市按钮
2. 按钮状态切换（选中/未选中）
3. 更新内部选中列表
4. 更新页面显示的选中城市信息
5. 控制台输出当前选中状态

### 3. 视觉反馈
- **未选中状态**: 白色背景，灰色边框和文字
- **悬停状态**: 轻微上移，添加阴影
- **选中状态**: 紫色渐变背景，白色文字，右上角显示✓
- **点击状态**: 轻微缩放效果

## 错误处理

### 1. 网络错误
- 显示错误图标和提示信息
- 提供"重新加载"按钮
- 控制台输出详细错误信息

### 2. 数据格式错误
- 检查返回数据的code和data字段
- 处理空数据或格式不正确的情况

### 3. 接口异常
- 捕获fetch异常
- 显示用户友好的错误提示

## 测试方法

### 1. 基础功能测试
1. 访问package.html页面
2. 观察城市列表是否正确加载
3. 测试城市选择和取消选择
4. 检查选中城市的显示是否正确

### 2. 多选功能测试
1. 选择多个城市
2. 确认每个城市的选中状态
3. 检查选中城市列表的更新
4. 测试取消选择功能

### 3. 错误处理测试
1. 断开网络连接，测试错误提示
2. 点击"重新加载"按钮
3. 检查控制台错误日志

### 4. API函数测试
```javascript
// 在浏览器控制台中测试
console.log('选中的城市:', getSelectedCities());
console.log('城市ID:', getSelectedCityIds());
console.log('城市名称:', getSelectedCityNames());

// 测试清空功能
clearAllSelections();

// 测试默认选择
setDefaultSelectedCities([1, 2]);
```

## 扩展功能建议

### 1. 搜索功能
- 添加城市搜索框
- 支持拼音和汉字搜索
- 实时过滤城市列表

### 2. 分组显示
- 按省份分组显示城市
- 支持省份级别的全选/取消全选

### 3. 记忆功能
- 记住用户的选择偏好
- 下次访问时自动选中

### 4. 限制选择
- 设置最大选择数量
- 超出限制时的提示

### 5. 批量操作
- 全选/全不选按钮
- 反选功能
