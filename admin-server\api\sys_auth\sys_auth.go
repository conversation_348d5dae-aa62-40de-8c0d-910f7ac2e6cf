package sys_auth

import (
	v1 "admin-server/api/sys_auth/v1"
	"context"
)

type ISysAuthV1 interface {
	Login(ctx context.Context, req *v1.LoginReq) (res *v1.<PERSON>gin<PERSON><PERSON>, err error)
	RefreshToken(ctx context.Context, req *v1.RefreshTokenReq) (res *v1.RefreshTokenRes, err error)
	GetPermissions(ctx context.Context, req *v1.GetPermissionsReq) (res *v1.GetPermissionsRes, err error)
	Logout(ctx context.Context, req *v1.LogoutReq) (res *v1.<PERSON><PERSON><PERSON><PERSON>, err error)
	GetUserInfo(ctx context.Context, req *v1.GetUserInfoReq) (res *v1.GetUserInfoRes, err error)
	GetMenus(ctx context.Context, req *v1.GetMenusReq) (res *v1.GetMenusRes, err error)
	WechatServe(ctx context.Context, req *v1.WechatServeReq) (message string, err error)
}
