# 套餐选择功能说明

## 功能概述

在package.html页面中实现了动态加载套餐列表和单选功能，用户可以选择一个套餐进行购买。

## 实现的功能

### 1. 动态套餐加载
- 页面加载完成后自动请求 `/m/api/zb_good/all` 接口
- 显示加载状态和错误处理
- 支持重新加载功能

### 2. 套餐单选功能
- 支持选择一个套餐（单选模式）
- 默认选中第一个套餐
- 实时切换选中状态

### 3. 智能UI显示
- 根据折扣自动显示"推荐"标签
- 动态显示原价和折扣信息
- 选中状态的视觉反馈

## 接口说明

### 请求接口
```
GET /m/api/zb_good/all
```

### 返回数据格式
```json
{
  "code": 0,
  "message": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "特惠会员199元",
        "tag": "特惠会员",
        "original_price": 599,
        "price": 199,
        "effective": 1,
        "is_disable": 0,
        "is_delete": 0,
        "created_at": "2025-07-15 14:29:21",
        "updated_at": "2025-07-15 14:30:18",
        "deleted_at": null,
        "discount": 0.332220367278798,
        "discount_text": "3.3折"
      }
    ]
  }
}
```

### 字段说明
- `id`: 套餐ID
- `name`: 套餐名称
- `tag`: 套餐标签
- `original_price`: 原价
- `price`: 现价
- `effective`: 有效期（月数）
- `discount`: 折扣比例（0-1之间）
- `discount_text`: 折扣文本显示

## 页面结构

### 1. HTML结构
```html
<!-- 套餐选择 -->
<div class="mb-6">
    <h3 class="text-base font-bold text-gray-800 mb-3">选择套餐</h3>
    
    <!-- 套餐加载状态 -->
    <div id="packageLoading" class="text-center py-6">
        <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-purple-500"></div>
        <p class="text-xs text-gray-600 mt-1">正在加载套餐...</p>
    </div>
    
    <!-- 套餐列表 -->
    <div id="packageList" class="space-y-3" style="display: none;">
        <!-- 动态生成的套餐卡片 -->
    </div>
    
    <!-- 套餐错误状态 -->
    <div id="packageError" class="text-center py-6" style="display: none;">
        <i class="fas fa-exclamation-triangle text-red-500 text-lg mb-1"></i>
        <p class="text-xs text-gray-600 mb-2">套餐加载失败</p>
        <button onclick="loadPackages()" class="bg-purple-500 text-white px-3 py-1 rounded text-xs">
            重新加载
        </button>
    </div>
</div>
```

### 2. 套餐卡片结构
```html
<div class="price-card bg-white border-2 border-purple-500 rounded-xl p-4 cursor-pointer relative selected">
    <!-- 推荐标签（条件显示） -->
    <div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
        <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-medium">推荐</span>
    </div>
    
    <div class="flex items-center justify-between">
        <!-- 左侧：单选按钮和套餐信息 -->
        <div class="flex items-center space-x-3">
            <div class="w-6 h-6 border-2 border-purple-500 rounded-full flex items-center justify-center">
                <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
            </div>
            <div>
                <h4 class="text-sm font-semibold text-gray-800">特惠会员199元</h4>
                <p class="text-xs text-gray-600">特惠会员</p>
            </div>
        </div>
        
        <!-- 右侧：价格信息 -->
        <div class="text-right">
            <!-- 折扣信息（条件显示） -->
            <div class="flex items-center space-x-2 mb-1">
                <span class="text-xs text-gray-400 line-through">¥599</span>
                <span class="bg-red-100 text-red-600 px-2 py-1 rounded text-xs">3.3折</span>
            </div>
            <div class="text-lg font-bold text-purple-600">¥199</div>
            <div class="text-xs text-gray-500">1个月</div>
        </div>
    </div>
</div>
```

## JavaScript功能

### 1. 页面初始化
```javascript
document.addEventListener('DOMContentLoaded', function() {
    loadCities();
    loadPackages(); // 新增套餐加载
});
```

### 2. 加载套餐列表
```javascript
async function loadPackages() {
    try {
        const response = await fetch('/m/api/zb_good/all');
        const result = await response.json();
        
        if (result.code === 0 && result.data && result.data.list) {
            allPackages = result.data.list;
            renderPackages(allPackages);
        } else {
            throw new Error(result.message || '数据格式错误');
        }
    } catch (error) {
        console.error('加载套餐列表失败:', error);
        // 显示错误状态
    }
}
```

### 3. 渲染套餐列表
```javascript
function renderPackages(packages) {
    const list = document.getElementById('packageList');
    list.innerHTML = '';
    
    packages.forEach((pkg, index) => {
        const card = createPackageCard(pkg, index === 0); // 第一个默认选中
        list.appendChild(card);
    });
    
    // 默认选中第一个套餐
    if (packages.length > 0) {
        selectedPackage = packages[0];
    }
}
```

### 4. 创建套餐卡片
```javascript
function createPackageCard(pkg, isDefault = false) {
    const card = document.createElement('div');
    
    // 根据数据动态生成卡片内容
    // - 推荐标签：折扣小于5折时显示
    // - 价格显示：有原价时显示划线价格和折扣
    // - 选中状态：默认选中第一个
    
    card.addEventListener('click', function() {
        selectPackage(this, pkg);
    });
    
    return card;
}
```

### 5. 套餐选择逻辑
```javascript
function selectPackage(cardElement, pkg) {
    // 1. 移除所有套餐的选中状态
    const allCards = document.querySelectorAll('.price-card');
    allCards.forEach(card => {
        // 重置样式和状态
    });
    
    // 2. 设置当前套餐为选中状态
    cardElement.classList.add('selected', 'border-purple-500');
    
    // 3. 更新全局选中套餐
    selectedPackage = pkg;
}
```

## 提供的API函数

### 1. 获取选中套餐
```javascript
// 获取完整的套餐对象
const selectedPkg = getSelectedPackage();
// 返回: { id: 1, name: "特惠会员199元", price: 199, ... }

// 获取套餐ID
const packageId = getSelectedPackageId();
// 返回: 1

// 获取套餐价格
const price = getSelectedPackagePrice();
// 返回: 199
```

## 智能显示逻辑

### 1. 推荐标签显示
```javascript
// 折扣小于5折时显示推荐标签
if (pkg.discount && pkg.discount < 0.5) {
    recommendTag = `<div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
        <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-medium">推荐</span>
    </div>`;
}
```

### 2. 价格显示逻辑
```javascript
// 有原价且原价大于现价时显示折扣信息
if (pkg.original_price && pkg.original_price > pkg.price) {
    priceDisplay = `
        <div class="flex items-center space-x-2 mb-1">
            <span class="text-xs text-gray-400 line-through">¥${pkg.original_price}</span>
            <span class="bg-red-100 text-red-600 px-2 py-1 rounded text-xs">${pkg.discount_text || '优惠'}</span>
        </div>
    `;
}
```

### 3. 选中状态管理
- **未选中状态**: 灰色边框，灰色单选按钮，灰色价格
- **选中状态**: 紫色边框，紫色单选按钮，紫色价格
- **单选模式**: 只能选择一个套餐，选择新套餐时自动取消其他选择

## 用户交互流程

### 1. 页面加载流程
1. 页面加载完成
2. 显示"正在加载套餐..."
3. 请求 `/m/api/zb_good/all` 接口
4. 成功：渲染套餐列表，默认选中第一个
5. 失败：显示错误提示和重试按钮

### 2. 套餐选择流程
1. 用户点击套餐卡片
2. 取消所有其他套餐的选中状态
3. 设置当前套餐为选中状态
4. 更新内部选中套餐变量
5. 控制台输出当前选中套餐

### 3. 视觉反馈
- **未选中**: 灰色边框，空心单选按钮
- **选中**: 紫色边框，实心单选按钮，紫色价格
- **推荐**: 顶部显示"推荐"标签
- **折扣**: 显示划线原价和折扣标签

## 错误处理

### 1. 网络错误
- 显示错误图标和提示信息
- 提供"重新加载"按钮
- 控制台输出详细错误信息

### 2. 数据格式错误
- 检查返回数据的code和data字段
- 处理空数据或格式不正确的情况

### 3. 接口异常
- 捕获fetch异常
- 显示用户友好的错误提示

## 测试方法

### 1. 基础功能测试
1. 访问package.html页面
2. 观察套餐列表是否正确加载
3. 检查默认选中第一个套餐
4. 测试套餐选择切换

### 2. 单选功能测试
1. 点击不同的套餐
2. 确认只有一个套餐被选中
3. 检查选中状态的视觉反馈
4. 验证选中套餐数据的更新

### 3. 显示逻辑测试
1. 检查推荐标签是否正确显示
2. 验证折扣信息的显示
3. 测试价格和有效期显示

### 4. API函数测试
```javascript
// 在浏览器控制台中测试
console.log('选中的套餐:', getSelectedPackage());
console.log('套餐ID:', getSelectedPackageId());
console.log('套餐价格:', getSelectedPackagePrice());
```

## 扩展功能建议

### 1. 套餐对比
- 添加套餐对比功能
- 显示不同套餐的特性差异

### 2. 优惠券支持
- 支持优惠券输入
- 实时计算优惠后价格

### 3. 套餐详情
- 点击查看套餐详细说明
- 显示套餐包含的具体权益

### 4. 价格计算
- 根据选择的城市数量调整价格
- 支持阶梯定价

### 5. 推荐算法
- 根据用户历史行为推荐套餐
- 个性化套餐排序
