package sys_dict_group

import (
	"context"

	v1 "admin-server/api/sys_dict_group/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error) {
	list, total, err := service.SysDictGroup().GetDictGroupList(
		ctx,
		req.Page,
		req.PageSize,
		req.Name,
		req.Code,
		req.IsDisable,
	)
	if err != nil {
		return nil, err
	}

	// 转换为API响应格式
	dictGroups := make([]v1.DictGroupInfo, len(list))
	for i, group := range list {
		dictGroups[i] = v1.DictGroupInfo{
			ID:        group.Id,
			Name:      group.Name,
			Code:      group.Code,
			IsDisable: int(group.IsDisable),
			IsSystem:  int(group.IsSystem),
			Remark:    group.Remark,
			CreatedAt: group.CreatedAt,
			UpdatedAt: group.UpdatedAt,
		}
	}

	return &v1.GetListRes{
		List:  dictGroups,
		Total: total,
	}, nil
}
