package sys_resources

import (
	"context"

	v1 "admin-server/api/sys_resources/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error) {
	err = service.SysResources().UpdateResources(
		ctx,
		req.ID,
		req.GroupId,
		req.StorageMode,
		req.OriginName,
		req.ObjectName,
		req.Hash,
		req.MimeType,
		req.StoragePath,
		req.Suffix,
		req.SizeByte,
		req.SizeInfo,
		req.Url,
		req.Remark,
	)
	if err != nil {
		return nil, err
	}

	return &v1.UpdateRes{}, nil
}
