package zb_user

import (
	"context"

	v1 "admin-server/api/zb_user/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetVipList(ctx context.Context, req *v1.GetVipListReq) (res *v1.GetVipListRes, err error) {
	list, total, err := service.ZbUser().GetVipUsers(ctx, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	// 转换为API响应格式
	users := make([]v1.UserInfo, len(list))
	for i, user := range list {
		// 动态计算VIP状态
		vipStatus, daysLeft := service.ZbUser().CalculateVipStatus(ctx, user.EffectiveStart, user.EffectiveEnd)

		users[i] = v1.UserInfo{
			ID:             user.Id,
			Nickname:       user.Nickname,
			Avatar:         user.Avatar,
			Openid:         user.Openid,
			IsDisable:      int(user.IsDisable),
			IsDelete:       int(user.IsDelete),
			EffectiveStart: user.EffectiveStart,
			EffectiveEnd:   user.EffectiveEnd,
			VipStatus:      vipStatus,
			VipDaysLeft:    daysLeft,
			CreatedAt:      user.CreatedAt,
			UpdatedAt:      user.UpdatedAt,
		}
	}

	return &v1.GetVipListRes{
		List:  users,
		Total: total,
	}, nil
}
