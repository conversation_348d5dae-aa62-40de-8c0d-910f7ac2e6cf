package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ZbCityCreateReq 创建开通城市请求
type ZbCityCreateReq struct {
	g.Meta    `path:"/zb_city" method:"post" tags:"ZbCity" summary:"创建开通城市" permission:"system:zb_city:add"`
	Pid       int    `p:"pid" v:"min:0#父级ID不能小于0" dc:"父级ID，0表示顶级城市"`
	Name      string `p:"name" v:"required|length:1,255#城市名称不能为空|城市名称长度不能超过255个字符" dc:"城市名称"`
	Sort      int    `p:"sort" v:"min:0#排序值不能小于0" dc:"排序，数字越小越靠前"`
	IsDisable int    `p:"is_disable" v:"in:0,1#状态值必须是0或1" dc:"是否禁用：0=否，1=是"`
}

// ZbCityCreateRes 创建开通城市响应
type ZbCityCreateRes struct {
	Id int `json:"id" dc:"城市ID"`
}

// ZbCityUpdateReq 更新开通城市请求
type ZbCityUpdateReq struct {
	g.Meta    `path:"/zb_city/{id}" method:"put" tags:"ZbCity" summary:"更新开通城市" permission:"system:zb_city:edit"`
	Id        int    `p:"id" v:"required|min:1#城市ID不能为空|城市ID必须大于0" dc:"城市ID"`
	Pid       int    `p:"pid" v:"min:0#父级ID不能小于0" dc:"父级ID，0表示顶级城市"`
	Name      string `p:"name" v:"required|length:1,255#城市名称不能为空|城市名称长度不能超过255个字符" dc:"城市名称"`
	Sort      int    `p:"sort" v:"min:0#排序值不能小于0" dc:"排序，数字越小越靠前"`
	IsDisable int    `p:"is_disable" v:"in:0,1#状态值必须是0或1" dc:"是否禁用：0=否，1=是"`
}

// ZbCityUpdateRes 更新开通城市响应
type ZbCityUpdateRes struct{}

// ZbCityDeleteReq 删除开通城市请求
type ZbCityDeleteReq struct {
	g.Meta `path:"/zb_city/{id}" method:"delete" tags:"ZbCity" summary:"删除开通城市" permission:"system:zb_city:del"`
	Id     int `p:"id" v:"required|min:1#城市ID不能为空|城市ID必须大于0" dc:"城市ID"`
}

// ZbCityDeleteRes 删除开通城市响应
type ZbCityDeleteRes struct{}

// ZbCityGetOneReq 获取单个开通城市请求
type ZbCityGetOneReq struct {
	g.Meta `path:"/zb_city/{id}" method:"get" tags:"ZbCity" summary:"获取单个开通城市" permission:"system:zb_city:info"`
	Id     int `p:"id" v:"required|min:1#城市ID不能为空|城市ID必须大于0" dc:"城市ID"`
}

// ZbCityGetOneRes 获取单个开通城市响应
type ZbCityGetOneRes struct {
	*ZbCityInfo `json:",inline"`
}

// ZbCityGetListReq 获取开通城市列表请求
type ZbCityGetListReq struct {
	g.Meta    `path:"/zb_city/list" method:"get" tags:"ZbCity" summary:"获取开通城市列表" permission:"system:zb_city:list"`
	Page      int    `p:"page" v:"min:1#页码必须大于0" dc:"页码，默认1"`
	PageSize  int    `p:"page_size" v:"min:1|max:100#每页数量必须大于0|每页数量不能超过100" dc:"每页数量，默认10"`
	Name      string `p:"name" dc:"城市名称，模糊搜索"`
	IsDisable *int   `p:"is_disable" v:"in:0,1#状态值必须是0或1" dc:"是否禁用：0=否，1=是"`
	Pid       *int   `p:"pid" v:"min:0#父级ID不能小于0" dc:"父级ID筛选"`
}

// ZbCityGetListRes 获取开通城市列表响应
type ZbCityGetListRes struct {
	List     []*ZbCityInfo `json:"list" dc:"城市列表"`
	Total    int           `json:"total" dc:"总数"`
	Page     int           `json:"page" dc:"当前页码"`
	PageSize int           `json:"page_size" dc:"每页数量"`
}

// ZbCityGetTreeReq 获取开通城市树形结构请求
type ZbCityGetTreeReq struct {
	g.Meta    `path:"/zb_city/tree" method:"get" tags:"ZbCity" summary:"获取开通城市树形结构" permission:"system:zb_city:list"`
	IsDisable *int `p:"is_disable" v:"in:0,1#状态值必须是0或1" dc:"是否禁用：0=否，1=是"`
}

// ZbCityGetTreeRes 获取开通城市树形结构响应
type ZbCityGetTreeRes struct {
	List []*ZbCityTreeInfo `json:"list" dc:"城市树形列表"`
}

// ZbCityUpdateSortReq 更新开通城市排序请求
type ZbCityUpdateSortReq struct {
	g.Meta `path:"/zb_city/{id}/sort" method:"put" tags:"ZbCity" summary:"更新开通城市排序" permission:"system:zb_city:sort"`
	Id     int `p:"id" v:"required|min:1#城市ID不能为空|城市ID必须大于0" dc:"城市ID"`
	Sort   int `p:"sort" v:"min:0#排序值不能小于0" dc:"排序值"`
}

// ZbCityUpdateSortRes 更新开通城市排序响应
type ZbCityUpdateSortRes struct{}

// ZbCityUpdateStatusReq 更新开通城市状态请求
type ZbCityUpdateStatusReq struct {
	g.Meta    `path:"/zb_city/{id}/status" method:"put" tags:"ZbCity" summary:"更新开通城市状态" permission:"system:zb_city:status"`
	Id        int `p:"id" v:"required|min:1#城市ID不能为空|城市ID必须大于0" dc:"城市ID"`
	IsDisable int `p:"is_disable" v:"required|in:0,1#状态不能为空|状态值必须是0或1" dc:"是否禁用：0=否，1=是"`
}

// ZbCityUpdateStatusRes 更新开通城市状态响应
type ZbCityUpdateStatusRes struct{}

// ZbCityInfo 开通城市信息
type ZbCityInfo struct {
	Id        int         `json:"id" dc:"城市ID"`
	Pid       int         `json:"pid" dc:"父级ID"`
	Name      string      `json:"name" dc:"城市名称"`
	Sort      int         `json:"sort" dc:"排序"`
	IsDisable int         `json:"is_disable" dc:"是否禁用"`
	IsDelete  int         `json:"is_delete" dc:"是否删除"`
	CreatedAt *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt *gtime.Time `json:"updated_at" dc:"更新时间"`
	DeletedAt *gtime.Time `json:"deleted_at" dc:"删除时间"`
}

// ZbCityTreeInfo 开通城市树形信息
type ZbCityTreeInfo struct {
	*ZbCityInfo
	Children []*ZbCityTreeInfo `json:"children,omitempty" dc:"子城市"`
}
