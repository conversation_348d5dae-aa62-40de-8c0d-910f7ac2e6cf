package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GetListReq 获取会员列表请求体
type GetListReq struct {
	g.<PERSON>a       `path:"/zb_user/list" tags:"ZbUser" method:"get" summary:"获取会员列表" permission:"system:member:list"`
	Page         int    `p:"page" d:"1" v:"min:1#页码最小为1" dc:"页码"`
	PageSize     int    `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间" dc:"每页数量"`
	Nickname     string `p:"nickname" dc:"昵称（模糊搜索）"`
	Openid       string `p:"openid" dc:"微信OpenID（模糊搜索）"`
	IsDisable    int    `p:"is_disable" d:"-1" dc:"是否禁用：0=否，1=是，-1=全部"`
	VipStatus    int    `p:"vip_status" d:"-1" dc:"VIP状态：0=非VIP，1=VIP，-1=全部"`
	HasVipPeriod int    `p:"has_vip_period" d:"-1" dc:"是否设置VIP期限：0=否，1=是，-1=全部"`
	StartTime    string `p:"start_time" dc:"开始时间（注册时间范围查询，格式：2006-01-02 15:04:05）"`
	EndTime      string `p:"end_time" dc:"结束时间（注册时间范围查询，格式：2006-01-02 15:04:05）"`
}

type GetListRes struct {
	List  []UserInfo `json:"list" dc:"会员列表"`
	Total int        `json:"total" dc:"总数"`
}

// GetOneReq 获取会员详情请求体
type GetOneReq struct {
	g.Meta `path:"/zb_user/{id}" tags:"ZbUser" method:"get" summary:"获取会员详情" permission:"system:member:detail"`
	ID     int64 `p:"id" v:"required#请选择需要查询的会员" dc:"会员ID"`
}

type GetOneRes struct {
	User *UserDetail `json:"user" dc:"会员详情"`
}

// UpdateReq 更新会员信息请求体
type UpdateReq struct {
	g.Meta         `path:"/zb_user/update/{id}" method:"put" tags:"ZbUser" summary:"更新会员信息" permission:"system:member:update"`
	ID             int64  `p:"id" v:"required#请选择需要更新的会员" dc:"会员ID"`
	Nickname       string `p:"nickname" v:"max-length:50#昵称长度不能超过50个字符" dc:"昵称"`
	Avatar         string `p:"avatar" v:"max-length:255#头像URL长度不能超过255个字符" dc:"头像URL"`
	IsDisable      *int   `p:"is_disable" v:"in:0,1#状态值错误" dc:"是否禁用：0=否，1=是"`
	EffectiveStart string `p:"effective_start" v:"date#开始日期格式错误" dc:"有效开始日期（格式：2006-01-02）"`
	EffectiveEnd   string `p:"effective_end" v:"date#结束日期格式错误" dc:"有效结束日期（格式：2006-01-02）"`
}

type UpdateRes struct{}

// DeleteReq 删除会员请求体
type DeleteReq struct {
	g.Meta `path:"/zb_user/delete" method:"delete" tags:"ZbUser" summary:"删除会员" permission:"system:member:delete"`
	IDs    []int64 `p:"ids" v:"required#请选择需要删除的会员" dc:"会员ID列表"`
}

type DeleteRes struct{}

// SetStatusReq 设置会员状态请求体
type SetStatusReq struct {
	g.Meta    `path:"/zb_user/status/{id}" method:"put" tags:"ZbUser" summary:"设置会员状态" permission:"system:member:status"`
	ID        int64 `p:"id" v:"required#请选择需要设置的会员" dc:"会员ID"`
	IsDisable int   `p:"is_disable" v:"required|in:0,1#请选择状态|状态值错误" dc:"是否禁用：0=否，1=是"`
}

type SetStatusRes struct{}

// UpdateVipPeriodReq 更新会员VIP有效期请求体
type UpdateVipPeriodReq struct {
	g.Meta         `path:"/zb_user/vip_period/{id}" method:"put" tags:"ZbUser" summary:"更新会员VIP有效期" permission:"system:member:vipEdit"`
	ID             int64  `p:"id" v:"required#请选择需要更新的会员" dc:"会员ID"`
	EffectiveStart string `p:"effective_start" v:"required|date#请输入开始日期|开始日期格式错误" dc:"有效开始日期（格式：2006-01-02）"`
	EffectiveEnd   string `p:"effective_end" v:"required|date#请输入结束日期|结束日期格式错误" dc:"有效结束日期（格式：2006-01-02）"`
}

type UpdateVipPeriodRes struct{}

// GetVipListReq 获取VIP会员列表请求体
type GetVipListReq struct {
	g.Meta   `path:"/zb_user/vip/list" tags:"ZbUser" method:"get" summary:"获取VIP会员列表" permission:"system:member:vip"`
	Page     int `p:"page" d:"1" v:"min:1#页码最小为1" dc:"页码"`
	PageSize int `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间" dc:"每页数量"`
}

type GetVipListRes struct {
	List  []UserInfo `json:"list" dc:"VIP会员列表"`
	Total int        `json:"total" dc:"总数"`
}

// GetExpiredVipReq 获取即将过期的VIP会员请求体
type GetExpiredVipReq struct {
	g.Meta `path:"/zb_user/vip/expired" tags:"ZbUser" method:"get" summary:"获取即将过期的VIP会员" permission:"system:member:vip"`
	Days   int `p:"days" d:"7" v:"between:1,365#天数必须在1-365之间" dc:"过期天数"`
}

type GetExpiredVipRes struct {
	List []UserInfo `json:"list" dc:"即将过期的VIP会员列表"`
}

// GetByOpenidReq 根据OpenID获取会员请求体
type GetByOpenidReq struct {
	g.Meta `path:"/zb_user/openid/{openid}" tags:"ZbUser" method:"get" summary:"根据OpenID获取会员" permission:"system:member:detail"`
	Openid string `p:"openid" v:"required#请输入OpenID" dc:"微信OpenID"`
}

type GetByOpenidRes struct {
	User *UserDetail `json:"user" dc:"会员详情"`
}

// GetStatsReq 获取会员统计信息请求体
type GetStatsReq struct {
	g.Meta `path:"/zb_user/stats" tags:"ZbUser" method:"get" summary:"获取会员统计信息"`
}

type GetStatsRes struct {
	TotalUsers    int `json:"total_users" dc:"总用户数"`
	VipUsers      int `json:"vip_users" dc:"VIP用户数"`
	DisabledUsers int `json:"disabled_users" dc:"禁用用户数"`
	ActiveUsers   int `json:"active_users" dc:"活跃用户数"`
}

// UserInfo 会员列表信息
type UserInfo struct {
	ID             int64       `json:"id" dc:"主键ID"`
	Nickname       string      `json:"nickname" dc:"昵称"`
	Avatar         string      `json:"avatar" dc:"头像"`
	Openid         string      `json:"openid" dc:"微信OpenID"`
	IsDisable      int         `json:"is_disable" dc:"是否禁用：0=否，1=是"`
	IsDelete       int         `json:"is_delete" dc:"是否删除：0=否，1=是"`
	EffectiveStart *gtime.Time `json:"effective_start" dc:"有效开始日期"`
	EffectiveEnd   *gtime.Time `json:"effective_end" dc:"有效结束日期"`
	VipStatus      int         `json:"vip_status" dc:"VIP状态：0=非VIP，1=VIP（动态计算）"`
	VipDaysLeft    int         `json:"vip_days_left" dc:"VIP剩余天数（-1表示无限期，0表示已过期）"`
	CreatedAt      *gtime.Time `json:"created_at" dc:"注册时间"`
	UpdatedAt      *gtime.Time `json:"updated_at" dc:"更新时间"`
}

// UserDetail 会员详情信息
type UserDetail struct {
	ID             int64       `json:"id" dc:"主键ID"`
	Nickname       string      `json:"nickname" dc:"昵称"`
	Avatar         string      `json:"avatar" dc:"头像"`
	Openid         string      `json:"openid" dc:"微信OpenID"`
	IsDisable      int         `json:"is_disable" dc:"是否禁用：0=否，1=是"`
	IsDelete       int         `json:"is_delete" dc:"是否删除：0=否，1=是"`
	EffectiveStart *gtime.Time `json:"effective_start" dc:"有效开始日期"`
	EffectiveEnd   *gtime.Time `json:"effective_end" dc:"有效结束日期"`
	VipStatus      int         `json:"vip_status" dc:"VIP状态：0=非VIP，1=VIP（动态计算）"`
	VipDaysLeft    int         `json:"vip_days_left" dc:"VIP剩余天数（-1表示无限期，0表示已过期）"`
	CreatedAt      *gtime.Time `json:"created_at" dc:"注册时间"`
	UpdatedAt      *gtime.Time `json:"updated_at" dc:"更新时间"`
	DeletedAt      *gtime.Time `json:"deleted_at" dc:"删除时间"`
}

// UserOption 会员选项
type UserOption struct {
	ID       int64  `json:"id" dc:"主键ID"`
	Nickname string `json:"nickname" dc:"昵称"`
	Openid   string `json:"openid" dc:"微信OpenID"`
}
