// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SysResourcesGroup is the golang structure for table sys_resources_group.
type SysResourcesGroup struct {
	Id        int64       `json:"id"        orm:"id"         description:""`                     //
	Name      string      `json:"name"      orm:"name"       description:"资源名称"`                 // 资源名称
	Type      string      `json:"type"      orm:"type"       description:"资源类型，图片:PIC；视频:VIDEO"` // 资源类型，图片:PIC；视频:VIDEO
	IsDelete  int         `json:"isDelete"  orm:"is_delete"  description:"是否删除: 0=否, 1=是"`       // 是否删除: 0=否, 1=是
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`                 // 创建时间
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:"更新时间"`                 // 更新时间
	DeletedAt *gtime.Time `json:"deletedAt" orm:"deleted_at" description:"删除时间"`                 // 删除时间
}
