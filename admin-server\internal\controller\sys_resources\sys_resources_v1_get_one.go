package sys_resources

import (
	"context"

	v1 "admin-server/api/sys_resources/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetOne(ctx context.Context, req *v1.GetOneReq) (res *v1.GetOneRes, err error) {
	resource, err := service.SysResources().GetResourcesDetail(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	if resource == nil {
		return &v1.GetOneRes{}, nil
	}

	// 转换为API响应格式
	detail := &v1.ResourcesDetail{
		ID:          resource.Id,
		GroupId:     resource.GroupId,
		StorageMode: int(resource.StorageMode),
		OriginName:  resource.OriginName,
		ObjectName:  resource.ObjectName,
		Hash:        resource.Hash,
		MimeType:    resource.MimeType,
		StoragePath: resource.StoragePath,
		Suffix:      resource.Suffix,
		SizeByte:    resource.SizeByte,
		SizeInfo:    resource.SizeInfo,
		Url:         resource.Url,
		Remark:      resource.Remark,
		IsDelete:    int(resource.IsDelete),
		CreatedAt:   resource.CreatedAt,
		UpdatedAt:   resource.UpdatedAt,
	}

	return &v1.GetOneRes{
		Resources: detail,
	}, nil
}
