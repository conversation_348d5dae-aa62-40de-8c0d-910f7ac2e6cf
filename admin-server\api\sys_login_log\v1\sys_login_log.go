package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GetListReq 获取登录日志列表请求体
type GetListReq struct {
	g.Meta   `path:"/sys_login_log/list" tags:"SysLoginLog" method:"get" summary:"获取登录日志列表" permission:"system:loginlog:list"`
	Page     int    `p:"page" d:"1" v:"min:1#页码最小为1" dc:"页码"`
	PageSize int    `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间" dc:"每页数量"`
	Username string `p:"username" dc:"登录账号（模糊搜索）"`
}

type GetListRes struct {
	List  []LoginLogInfo `json:"list" dc:"登录日志列表"`
	Total int            `json:"total" dc:"总数"`
}

// DeleteReq 删除登录日志请求体
type DeleteReq struct {
	g.Meta `path:"/sys_login_log/delete" method:"delete" tags:"SysLoginLog" summary:"删除登录日志" permission:"system:loginlog:delete"`
	IDs    []int64 `p:"ids" v:"required#请选择需要删除的记录" dc:"登录日志ID列表"`
}

type DeleteRes struct{}

// ClearReq 清空登录日志请求体
type ClearReq struct {
	g.Meta `path:"/sys_login_log/clear" method:"delete" tags:"SysLoginLog" summary:"清空登录日志" permission:"system:loginlog:clear"`
}

type ClearRes struct{}

// LoginLogInfo 登录日志信息
type LoginLogInfo struct {
	ID        int64       `json:"id" dc:"主键ID"`
	AdminId   int64       `json:"admin_id" dc:"管理员ID"`
	Username  string      `json:"username" dc:"登录账号"`
	Ip        string      `json:"ip" dc:"登录IP"`
	Os        string      `json:"os" dc:"操作系统"`
	Browser   string      `json:"browser" dc:"浏览器"`
	CreatedAt *gtime.Time `json:"created_at" dc:"登录时间"`
}
