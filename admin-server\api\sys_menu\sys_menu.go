// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package sys_menu

import (
	"context"

	"admin-server/api/sys_menu/v1"
)

type ISysMenuV1 interface {
	Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error)
	Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error)
	Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error)
	GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error)
	GetTree(ctx context.Context, req *v1.GetTreeReq) (res *v1.GetTreeRes, err error)
	GetOne(ctx context.Context, req *v1.GetOneReq) (res *v1.GetOneRes, err error)
	UpdateSort(ctx context.Context, req *v1.UpdateSortReq) (res *v1.UpdateSortRes, err error)
}
