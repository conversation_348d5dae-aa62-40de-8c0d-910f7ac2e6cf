package search

import (
	v1 "admin-server/api/search/v1"
	"admin-server/internal/service"
	"context"
)

func (c *ControllerV1) GetHotKeywords(ctx context.Context, req *v1.GetHotKeywordsReq) (res *v1.GetHotKeywordsRes, err error) {
	// 获取热门搜索关键词
	keywords, err := service.Search().GetHotKeywords(ctx, req.Limit)
	if err != nil {
		return nil, err
	}

	// 转换为API响应格式
	var hotKeywords []v1.HotKeyword
	for _, keyword := range keywords {
		hotKeyword := v1.HotKeyword{
			Id:          keyword.Id,
			Keyword:     keyword.Keyword,
			SearchCount: keyword.SearchCount,
			ClickCount:  keyword.ClickCount,
			Trend:       keyword.Trend,
			IsHot:       keyword.IsHot == 1,
			IsNew:       keyword.IsNew == 1,
			Category:    keyword.Category,
			CreatedAt:   keyword.CreatedAt,
			UpdatedAt:   keyword.UpdatedAt,
		}
		hotKeywords = append(hotKeywords, hotKeyword)
	}

	return &v1.GetHotKeywordsRes{
		Keywords: hotKeywords,
	}, nil
}
