# 定时任务调试排查说明

## 问题描述

定时任务设置为 `"0 2 * * * *"`（每小时第2分钟执行），但过了2分钟没有执行。

## 可能原因分析

### 1. Cron表达式理解
- `"0 2 * * * *"` 表示：每小时的第2分钟的第0秒执行
- 例如：14:02:00, 15:02:00, 16:02:00 等
- **不是**：从当前时间开始2分钟后执行

### 2. 时间同步问题
- 服务器时间可能与本地时间不同步
- 需要确认服务器当前时间

### 3. 定时任务启动问题
- 定时任务可能没有正确启动
- 需要检查启动日志

## 排查步骤

### 1. 检查启动日志
重启应用后，查看是否有以下日志：
```
[INFO] 搜索趋势定时任务启动成功, CronID: xxx
[INFO] 当前定时任务数量: x
[INFO] 测试定时任务启动成功, CronID: xxx
```

### 2. 观察测试定时任务
我已经添加了一个每分钟执行的测试定时任务，应该看到：
```
[INFO] 测试定时任务执行 - 当前时间: 每分钟执行一次
```

### 3. 使用API接口测试

#### A. 检查定时任务状态
```bash
curl "http://localhost:8000/m/api/search/cron-status"
```

#### B. 手动触发趋势更新
```bash
curl -X POST "http://localhost:8000/m/api/search/trigger-trend-update"
```

### 4. 修改为更频繁的测试
如果需要立即测试，可以临时修改为：
```go
// 每30秒执行一次（仅用于测试）
"*/30 * * * * *"

// 每分钟执行一次
"0 * * * * *"

// 每5分钟执行一次
"0 */5 * * * *"
```

## 调试工具

### 1. 新增的调试功能
- **启动日志增强**：显示CronID和任务数量
- **测试定时任务**：每分钟执行，便于观察
- **手动触发接口**：可以手动测试功能
- **状态查询接口**：查看当前定时任务状态

### 2. API接口
```
GET  /m/api/search/cron-status        # 查看定时任务状态
POST /m/api/search/trigger-trend-update # 手动触发更新
```

### 3. 日志级别
确保日志级别设置为INFO或DEBUG，以便看到执行日志。

## 常见问题解决

### 1. 定时任务不执行
**检查项**：
- [ ] 启动日志是否显示任务创建成功
- [ ] 服务器时间是否正确
- [ ] Cron表达式是否正确
- [ ] 是否有错误日志

**解决方案**：
```go
// 临时改为每分钟执行测试
"0 * * * * *"
```

### 2. 时间不匹配
**检查服务器时间**：
```bash
date
```

**调整Cron表达式**：
```go
// 如果当前是14:05，想要14:07执行
"0 7 * * * *"

// 如果想要每5分钟执行一次
"0 */5 * * * *"
```

### 3. 功能测试
使用手动触发接口测试业务逻辑是否正常：
```bash
curl -X POST "http://localhost:8000/m/api/search/trigger-trend-update"
```

## Cron表达式参考

### 格式
```
秒 分 时 日 月 周
*  *  *  *  *  *
```

### 常用示例
```go
"0 * * * * *"     // 每分钟执行
"0 */5 * * * *"   // 每5分钟执行
"0 0 * * * *"     // 每小时执行
"0 0 0 * * *"     // 每天0点执行
"0 0 */6 * * *"   // 每6小时执行
"0 30 14 * * *"   // 每天14:30执行
"0 0 0 * * 1"     // 每周一0点执行
```

### 特殊字符
- `*`：任意值
- `?`：不指定值（日和周字段）
- `-`：范围，如 1-5
- `,`：列举，如 1,3,5
- `/`：步长，如 */5（每5个单位）

## 测试建议

### 1. 短期测试
```go
// 修改为每30秒执行，便于快速测试
"*/30 * * * * *"
```

### 2. 观察日志
```bash
# 实时查看日志
tail -f logs/app.log | grep "搜索趋势\|测试定时任务"
```

### 3. 验证功能
1. 手动触发更新
2. 检查数据库变化
3. 确认业务逻辑正确

### 4. 恢复正常
测试完成后，改回正常的执行频率：
```go
"0 0 * * * *"  // 每小时执行
```

## 监控建议

### 1. 添加执行统计
```go
var (
    lastExecuteTime time.Time
    executeCount    int64
)

func updateSearchTrend(ctx context.Context) {
    executeCount++
    lastExecuteTime = time.Now()
    g.Log().Info(ctx, "开始更新搜索趋势...", "执行次数:", executeCount)
    // ... 业务逻辑
}
```

### 2. 健康检查接口
可以添加一个健康检查接口，返回最后执行时间和执行次数。

### 3. 错误告警
对于重要的定时任务，可以添加错误告警机制。

---

**调试状态**: 🔧 调试中  
**测试工具**: 已添加测试定时任务和API接口  
**建议**: 先观察测试定时任务是否每分钟执行  
**文档版本**: v1.0  
**创建时间**: 2025-01-23
