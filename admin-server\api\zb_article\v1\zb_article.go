package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GetListReq 获取信息列表请求体
type GetListReq struct {
	g.Meta    `path:"/zb_article/list" tags:"ZbArticle" method:"get" summary:"获取信息列表" permission:"system:article:list"`
	Page      int    `p:"page" d:"1" v:"min:1#页码最小为1" dc:"页码"`
	PageSize  int    `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间" dc:"每页数量"`
	Title     string `p:"title" dc:"标题（模糊搜索）"`
	CityId    int    `p:"city_id" dc:"城市ID"`
	CateId    int    `p:"cate_id" dc:"类别ID"`
	IsDisable int    `p:"is_disable" d:"-1" dc:"是否禁用：0=否，1=是，-1=全部"`
	StartTime string `p:"start_time" dc:"开始时间（格式：2006-01-02 15:04:05）"`
	EndTime   string `p:"end_time" dc:"结束时间（格式：2006-01-02 15:04:05）"`
}

// GetListReq 获取信息列表请求体
type GetListMobileReq struct {
	g.Meta    `path:"/zb_article/mobileList" tags:"ZbArticle" method:"get" summary:"移动端获取信息列表"`
	Page      int    `p:"page" d:"1" v:"min:1#页码最小为1" dc:"页码"`
	PageSize  int    `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间" dc:"每页数量"`
	CityId    int    `p:"city_id" dc:"城市ID"`
	CateId    int    `p:"cate_id" dc:"类别ID"`
	Title     string `p:"title" dc:"标题（模糊搜索）"`
	IsDisable int    `p:"is_disable" d:"-1" dc:"是否禁用：0=否，1=是，-1=全部"`
}

type GetListRes struct {
	List  []ArticleInfo `json:"list" dc:"信息列表"`
	Total int           `json:"total" dc:"总数"`
}

// GetOneReq 获取信息详情请求体
type GetOneReq struct {
	g.Meta `path:"/zb_article/{id}" tags:"ZbArticle" method:"get" summary:"获取信息详情" permission:"system:article:detail"`
	ID     int64 `p:"id" v:"required#请选择需要查询的信息" dc:"信息ID"`
}

type GetOneRes struct {
	Article *ArticleDetail `json:"article" dc:"信息详情"`
}

// CreateReq 创建信息请求体
type CreateReq struct {
	g.Meta         `path:"/zb_article/create" method:"post" tags:"ZbArticle" summary:"创建信息" permission:"system:article:create"`
	CityId         int         `p:"city_id" v:"required#请选择城市" dc:"城市ID"`
	CateId         int         `p:"cate_id" v:"required#请选择类别" dc:"类别ID"`
	Title          string      `p:"title" v:"required|max-length:200#请输入标题|标题长度不能超过200个字符" dc:"标题"`
	Intro          string      `p:"intro" v:"max-length:500#简介长度不能超过500个字符" dc:"内容简介"`
	FullContent    interface{} `p:"full_content" v:"required#请输入完整内容" dc:"完整版内容（JSON格式）"`
	ShieidContent  interface{} `p:"shieid_content" dc:"屏蔽内容（JSON格式）"`
	SeoTitle       string      `p:"seo_title" v:"max-length:200#SEO标题长度不能超过200个字符" dc:"SEO标题"`
	SeoKeywords    string      `p:"seo_keywords" v:"max-length:200#SEO关键词长度不能超过200个字符" dc:"SEO关键词"`
	SeoDescription string      `p:"seo_description" v:"max-length:500#SEO描述长度不能超过500个字符" dc:"SEO描述"`
	Pic            string      `p:"pic" v:"max-length:255#缩略图URL长度不能超过255个字符" dc:"缩略图"`
	Author         string      `p:"author" v:"max-length:50#作者长度不能超过50个字符" dc:"作者"`
	IsDisable      int         `p:"is_disable" d:"0" v:"in:0,1#状态值错误" dc:"是否禁用：0=否，1=是"`
}

type CreateRes struct {
	ID int64 `json:"id" dc:"新创建的信息ID"`
}

// UpdateReq 更新信息请求体
type UpdateReq struct {
	g.Meta         `path:"/zb_article/update/{id}" method:"put" tags:"ZbArticle" summary:"更新信息" permission:"system:article:update"`
	ID             int64       `p:"id" v:"required#请选择需要更新的信息" dc:"信息ID"`
	CityId         int         `p:"city_id" v:"required#请选择城市" dc:"城市ID"`
	CateId         int         `p:"cate_id" v:"required#请选择类别" dc:"类别ID"`
	Title          string      `p:"title" v:"required|max-length:200#请输入标题|标题长度不能超过200个字符" dc:"标题"`
	Intro          string      `p:"intro" v:"max-length:500#简介长度不能超过500个字符" dc:"内容简介"`
	FullContent    interface{} `p:"full_content" v:"required#请输入完整内容" dc:"完整版内容（JSON格式）"`
	ShieidContent  interface{} `p:"shieid_content" dc:"屏蔽内容（JSON格式）"`
	SeoTitle       string      `p:"seo_title" v:"max-length:200#SEO标题长度不能超过200个字符" dc:"SEO标题"`
	SeoKeywords    string      `p:"seo_keywords" v:"max-length:200#SEO关键词长度不能超过200个字符" dc:"SEO关键词"`
	SeoDescription string      `p:"seo_description" v:"max-length:500#SEO描述长度不能超过500个字符" dc:"SEO描述"`
	Pic            string      `p:"pic" v:"max-length:255#缩略图URL长度不能超过255个字符" dc:"缩略图"`
	Author         string      `p:"author" v:"max-length:50#作者长度不能超过50个字符" dc:"作者"`
	IsDisable      int         `p:"is_disable" d:"0" v:"in:0,1#状态值错误" dc:"是否禁用：0=否，1=是"`
}

type UpdateRes struct{}

// DeleteReq 删除信息请求体
type DeleteReq struct {
	g.Meta `path:"/zb_article/delete" method:"delete" tags:"ZbArticle" summary:"删除信息" permission:"system:article:delete"`
	IDs    []int64 `p:"ids" v:"required#请选择需要删除的信息" dc:"信息ID列表"`
}

type DeleteRes struct{}

// SetStatusReq 设置信息状态请求体
type SetStatusReq struct {
	g.Meta    `path:"/zb_article/status/{id}" method:"put" tags:"ZbArticle" summary:"设置信息状态" permission:"system:article:status"`
	ID        int64 `p:"id" v:"required#请选择需要设置的信息" dc:"信息ID"`
	IsDisable int   `p:"is_disable" v:"required|in:0,1#请选择状态|状态值错误" dc:"是否禁用：0=否，1=是"`
}

type SetStatusRes struct{}

// GetByCityReq 根据城市获取信息列表请求体
type GetByCityReq struct {
	g.Meta   `path:"/zb_article/city/{city_id}" tags:"ZbArticle" method:"get" summary:"根据城市获取信息列表" permission:"system:article:list"`
	CityId   int `p:"city_id" v:"required#请选择城市" dc:"城市ID"`
	Page     int `p:"page" d:"1" v:"min:1#页码最小为1" dc:"页码"`
	PageSize int `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间" dc:"每页数量"`
}

type GetByCityRes struct {
	List  []ArticleInfo `json:"list" dc:"信息列表"`
	Total int           `json:"total" dc:"总数"`
}

// GetByCategoryReq 根据类别获取信息列表请求体
type GetByCategoryReq struct {
	g.Meta   `path:"/zb_article/category/{cate_id}" tags:"ZbArticle" method:"get" summary:"根据类别获取信息列表" permission:"system:article:list"`
	CateId   int `p:"cate_id" v:"required#请选择类别" dc:"类别ID"`
	Page     int `p:"page" d:"1" v:"min:1#页码最小为1" dc:"页码"`
	PageSize int `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间" dc:"每页数量"`
}

type GetByCategoryRes struct {
	List  []ArticleInfo `json:"list" dc:"信息列表"`
	Total int           `json:"total" dc:"总数"`
}

// GetHotReq 获取热门信息请求体
type GetHotReq struct {
	g.Meta `path:"/zb_article/hot" tags:"ZbArticle" method:"get" summary:"获取热门信息" permission:"system:article:list"`
	Limit  int `p:"limit" d:"10" v:"between:1,50#数量必须在1-50之间" dc:"获取数量"`
}

type GetHotRes struct {
	List []ArticleInfo `json:"list" dc:"热门信息列表"`
}

// GetRecentReq 获取最新信息请求体
type GetRecentReq struct {
	g.Meta `path:"/zb_article/recent" tags:"ZbArticle" method:"get" summary:"获取最新信息" permission:"system:article:list"`
	Limit  int `p:"limit" d:"10" v:"between:1,50#数量必须在1-50之间" dc:"获取数量"`
}

type GetRecentRes struct {
	List []ArticleInfo `json:"list" dc:"最新信息列表"`
}

// GetStatsReq 获取信息统计请求体
type GetStatsReq struct {
	g.Meta `path:"/zb_article/stats" tags:"ZbArticle" method:"get" summary:"获取信息统计" permission:"system:article:stats"`
	CateId int `p:"cate_id" d:"0" dc:"类别ID，0表示全部"`
	CityId int `p:"city_id" d:"0" dc:"城市ID，0表示全部"`
}

type GetStatsRes struct {
	TotalArticles     int `json:"total_articles" dc:"总信息数"`
	PublishedArticles int `json:"published_articles" dc:"已发布信息数"`
	DisabledArticles  int `json:"disabled_articles" dc:"禁用信息数"`
	TodayArticles     int `json:"today_articles" dc:"今日新增信息数"`
}

// SearchReq 搜索信息请求体
type SearchReq struct {
	g.Meta   `path:"/zb_article/search" tags:"ZbArticle" method:"get" summary:"搜索信息" permission:"system:article:list"`
	Keyword  string `p:"keyword" v:"required#请输入搜索关键词" dc:"搜索关键词"`
	Page     int    `p:"page" d:"1" v:"min:1#页码最小为1" dc:"页码"`
	PageSize int    `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间" dc:"每页数量"`
}

type SearchRes struct {
	List  []ArticleInfo `json:"list" dc:"搜索结果列表"`
	Total int           `json:"total" dc:"总数"`
}

// IncrementViewReq 增加浏览次数请求体
type IncrementViewReq struct {
	g.Meta `path:"/zb_article/view/{id}" method:"put" tags:"ZbArticle" summary:"增加浏览次数" permission:""`
	ID     int64 `p:"id" v:"required#请选择信息" dc:"信息ID"`
}

type IncrementViewRes struct{}

// ArticleInfo 信息列表信息
type ArticleInfo struct {
	ID        int         `json:"id" dc:"主键ID"`
	CityId    int         `json:"city_id" dc:"城市ID"`
	CityName  string      `json:"city_name" dc:"城市名称"`
	CateId    int         `json:"cate_id" dc:"类别ID"`
	CateName  string      `json:"cate_name" dc:"类别名称"`
	Title     string      `json:"title" dc:"标题"`
	Intro     string      `json:"intro" dc:"内容简介"`
	Pic       string      `json:"pic" dc:"缩略图"`
	Author    string      `json:"author" dc:"作者"`
	ViewCount int         `json:"view_count" dc:"浏览次数"`
	IsDisable int         `json:"is_disable" dc:"是否禁用：0=否，1=是"`
	IsDelete  int         `json:"is_delete" dc:"是否删除：0=否，1=是"`
	CreatedAt *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt *gtime.Time `json:"updated_at" dc:"更新时间"`
}

// ArticleDetail 信息详情信息
type ArticleDetail struct {
	ID             int         `json:"id" dc:"主键ID"`
	CityId         int         `json:"city_id" dc:"城市ID"`
	CityName       string      `json:"city_name" dc:"城市名称"`
	CateId         int         `json:"cate_id" dc:"类别ID"`
	CateName       string      `json:"cate_name" dc:"类别名称"`
	Title          string      `json:"title" dc:"标题"`
	Intro          string      `json:"intro" dc:"内容简介"`
	FullContent    interface{} `json:"full_content" dc:"完整版内容（JSON格式）"`
	ShieidContent  interface{} `json:"shieid_content" dc:"屏蔽内容（JSON格式）"`
	ViewCount      int         `json:"view_count" dc:"浏览次数"`
	SeoTitle       string      `json:"seo_title" dc:"SEO标题"`
	SeoKeywords    string      `json:"seo_keywords" dc:"SEO关键词"`
	SeoDescription string      `json:"seo_description" dc:"SEO描述"`
	Pic            string      `json:"pic" dc:"缩略图"`
	Uid            int         `json:"uid" dc:"发布者ID"`
	Author         string      `json:"author" dc:"作者"`
	Ip             string      `json:"ip" dc:"发布IP"`
	IsDisable      int         `json:"is_disable" dc:"是否禁用：0=否，1=是"`
	IsDelete       int         `json:"is_delete" dc:"是否删除：0=否，1=是"`
	CreatedAt      *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt      *gtime.Time `json:"updated_at" dc:"更新时间"`
	DeletedAt      *gtime.Time `json:"deleted_at" dc:"删除时间"`
}
