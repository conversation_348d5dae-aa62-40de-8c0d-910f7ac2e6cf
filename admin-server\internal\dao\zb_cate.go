// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-server/internal/dao/internal"
)

// zbCateDao is the data access object for the table zb_cate.
// You can define custom methods on it to extend its functionality as needed.
type zbCateDao struct {
	*internal.ZbCateDao
}

var (
	// ZbCate is a globally accessible object for table zb_cate operations.
	ZbCate = zbCateDao{internal.NewZbCateDao()}
)

// Add your custom methods and functionality below.
