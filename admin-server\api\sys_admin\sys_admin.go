// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package sys_admin

import (
	"context"

	"admin-server/api/sys_admin/v1"
)

type ISysAdminV1 interface {
	Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error)
	Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error)
	Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error)
	ChangePassword(ctx context.Context, req *v1.ChangePasswordReq) (res *v1.ChangePasswordRes, err error)
	GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error)
	GetOne(ctx context.Context, req *v1.GetOneReq) (res *v1.GetOneRes, err error)
	Disable(ctx context.Context, req *v1.DisableReq) (res *v1.DisableRes, err error)
	Enable(ctx context.Context, req *v1.EnableReq) (res *v1.EnableRes, err error)
	AssignRoles(ctx context.Context, req *v1.AssignRolesReq) (res *v1.AssignRolesRes, err error)
	GetRoles(ctx context.Context, req *v1.GetRolesReq) (res *v1.GetRolesRes, err error)
}
