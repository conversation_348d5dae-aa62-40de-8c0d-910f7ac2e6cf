# 项目开发规范文档

## 📋 项目概述

本项目是基于 GoFrame v2.9.0 框架开发的后台管理系统，采用标准的四层架构模式，集成了微信公众号功能、RBAC权限管理、会员管理等业务模块。

### 技术栈
- **框架**: GoFrame v2.9.0
- **数据库**: MySQL 8.0+
- **缓存**: Redis
- **认证**: JWT Token
- **微信集成**: PowerWeChat SDK v3.4.20
- **Go版本**: 1.23.0+

## 🏗️ 项目架构

### 四层架构模式
```
API层 (api/) → Controller层 (internal/controller/) → Logic层 (internal/logic/) → Service层 (internal/service/)
                                                    ↓
                                               DAO层 (internal/dao/) → Model层 (internal/model/)
```

### 目录结构规范
```
admin-server/
├── api/                    # API接口定义层
│   └── {module}/
│       ├── {module}.go     # 接口定义
│       └── v1/
│           └── {module}.go # 结构体定义
├── internal/
│   ├── cmd/                # 命令行入口
│   ├── controller/         # 控制器层
│   │   └── {module}/
│   │       ├── {module}_new.go
│   │       └── {module}_v1_{action}.go
│   ├── logic/              # 业务逻辑层
│   │   └── {module}/
│   │       └── {module}.go
│   ├── service/            # 服务接口层
│   │   └── {module}.go
│   ├── dao/                # 数据访问层
│   ├── model/              # 数据模型层
│   │   ├── entity/         # 实体结构体
│   │   └── do/             # DAO操作结构体
│   ├── middleware/         # 中间件
│   └── packed/             # 常量定义
├── manifest/               # 配置文件
├── docs/                   # 文档目录
└── main.go                 # 程序入口
```

## 📝 命名规范

### 1. 文件命名
- **模块名**: 使用下划线分隔，如 `sys_admin`, `zb_user`
- **控制器文件**: `{module}_v1_{action}.go`
- **逻辑文件**: `{module}.go`
- **服务文件**: `{module}.go`

### 2. 包命名
- **API包**: `{module}` (如 `sys_admin`)
- **控制器包**: `{module}` (如 `sys_admin`)
- **逻辑包**: `{module}` (如 `sysAdmin`)
- **服务包**: 统一使用 `service`

### 3. 结构体命名
- **API结构体**: `{Action}Req`, `{Action}Res`
- **实体结构体**: `{Module}` (如 `SysAdmin`)
- **DO结构体**: `{Module}` (如 `SysAdmin`)

### 4. 方法命名
- **控制器方法**: 与API路径对应 (如 `GetList`, `Create`)
- **逻辑方法**: 业务含义明确 (如 `GetAdminList`, `CreateAdmin`)
- **服务方法**: 接口定义 (如 `GetList`, `Create`)

## 🔧 代码规范

### 1. API层规范

#### 接口定义 (`api/{module}/{module}.go`)
```go
package sys_admin

import (
    "github.com/gogf/gf/v2/frame/g"
)

type ISysAdminV1 interface {
    GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error)
    Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error)
    // ... 其他方法
}
```

#### 结构体定义 (`api/{module}/v1/{module}.go`)
```go
type GetListReq struct {
    g.Meta   `path:"/sys_admin" method:"get" tags:"管理员管理" summary:"获取管理员列表" dc:"获取管理员列表"`
    Page     int    `p:"page" d:"1" v:"min:1#页码最小为1"`
    PageSize int    `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间"`
    Username string `p:"username" d:"" dc:"用户名"`
}

type GetListRes struct {
    List  []AdminInfo `json:"list" dc:"管理员列表"`
    Total int         `json:"total" dc:"总数"`
}
```

### 2. Controller层规范

#### 构造函数 (`{module}_new.go`)
```go
package sys_admin

import (
    "admin-server/api/sys_admin"
)

type ControllerV1 struct{}

func NewV1() sys_admin.ISysAdminV1 {
    return &ControllerV1{}
}
```

#### 控制器方法 (`{module}_v1_{action}.go`)
```go
package sys_admin

import (
    "context"
    v1 "admin-server/api/sys_admin/v1"
    "admin-server/internal/service"
)

func (c *ControllerV1) GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error) {
    return service.SysAdmin().GetList(ctx, req)
}
```

### 3. Logic层规范

```go
package sysAdmin

import (
    "context"
    "admin-server/internal/service"
    v1 "admin-server/api/sys_admin/v1"
)

type sSysAdmin struct{}

func New() *sSysAdmin {
    return &sSysAdmin{}
}

func init() {
    service.RegisterSysAdmin(New())
}

func (s *sSysAdmin) GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error) {
    // 业务逻辑实现
    return
}
```

### 4. Service层规范

```go
package service

import (
    "context"
    v1 "admin-server/api/sys_admin/v1"
)

type ISysAdmin interface {
    GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error)
    Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error)
    // ... 其他方法
}

var localSysAdmin ISysAdmin

func SysAdmin() ISysAdmin {
    if localSysAdmin == nil {
        panic("implement not found for interface ISysAdmin, forgot register?")
    }
    return localSysAdmin
}

func RegisterSysAdmin(i ISysAdmin) {
    localSysAdmin = i
}
```

## 🔐 权限管理规范

### 1. 权限标识命名
- **格式**: `{module}:{resource}:{action}`
- **示例**: `system:admin:list`, `system:admin:create`

### 2. 权限配置
在 `g.Meta` 标签中配置权限：
```go
g.Meta `path:"/sys_admin" method:"post" tags:"管理员管理" summary:"创建管理员" dc:"创建管理员" perms:"system:admin:create"`
```

### 3. 中间件应用
所有需要认证的接口自动应用以下中间件：
- JWT认证中间件
- 权限验证中间件
- 操作日志中间件

## 📊 数据库规范

### 1. 表命名
- **系统表**: `sys_` 前缀 (如 `sys_admin`, `sys_role`)
- **业务表**: `zb_` 前缀 (如 `zb_user`, `zb_article`)
- **微信表**: `wechat_` 前缀 (如 `wechat_config`, `wechat_menu`)

### 2. 字段规范
- **主键**: `id` (bigint, auto_increment)
- **软删除**: `is_delete` (tinyint, 0=否, 1=是)
- **状态字段**: `is_disable` (tinyint, 0=否, 1=是)
- **时间字段**: `created_at`, `updated_at`, `deleted_at` (datetime)

### 3. 模型定义
- **Entity**: 完整的数据实体，用于数据传输
- **DO**: DAO操作结构体，用于数据库操作
- **自动生成**: 使用 `gf gen dao` 命令生成

## 🚀 API接口规范

### 1. RESTful设计
- **GET**: 查询操作
- **POST**: 创建操作
- **PUT**: 更新操作
- **DELETE**: 删除操作

### 2. 路由规范
- **列表**: `GET /{module}`
- **详情**: `GET /{module}/{id}`
- **创建**: `POST /{module}`
- **更新**: `PUT /{module}/{id}`
- **删除**: `DELETE /{module}/{id}`

### 3. 响应格式
```json
{
    "code": 0,
    "message": "success",
    "data": {
        // 响应数据
    }
}
```

## 🧪 测试规范

### 1. 单元测试
- **文件命名**: `{module}_test.go`
- **测试函数**: `Test{Function}(t *testing.T)`
- **覆盖率**: 核心业务逻辑需达到80%以上

### 2. API测试
- **工具**: 使用 cURL 或 Postman
- **文档**: 在 `docs/api/` 目录下维护测试示例

## 📚 文档规范

### 1. API文档
- **位置**: `docs/api/{module}_api.md`
- **内容**: 接口说明、参数定义、响应示例、错误码

### 2. 实现总结
- **位置**: `docs/{module}_implementation_summary.md`
- **内容**: 架构说明、功能清单、技术特性

### 3. 代码注释
- **结构体**: 必须有中文注释说明用途
- **方法**: 复杂业务逻辑必须有注释
- **常量**: 必须有注释说明含义

## 🔄 开发流程

### 1. 新功能开发
1. 设计数据库表结构
2. 生成 DAO 和 Model
3. 定义 API 接口
4. 实现 Service 接口
5. 实现 Logic 业务逻辑
6. 实现 Controller 控制器
7. 配置路由和权限
8. 编写测试用例
9. 更新文档

### 2. 代码提交
- **提交信息**: 使用中文，格式为 `[模块] 功能描述`
- **代码审查**: 必须经过代码审查才能合并
- **测试验证**: 提交前必须通过所有测试

## ⚠️ 注意事项

1. **严格遵循四层架构**，不允许跨层调用
2. **统一错误处理**，使用 GoFrame 的错误处理机制
3. **数据验证**，所有输入参数必须进行验证
4. **安全考虑**，敏感操作必须有权限控制
5. **性能优化**，避免 N+1 查询问题
6. **日志记录**，关键操作必须记录日志
7. **配置管理**，使用配置文件管理环境相关配置
8. **依赖管理**，使用包管理器管理依赖，不手动编辑配置文件

## 🛠️ 工具和命令

### 1. GoFrame CLI 工具
```bash
# 生成 DAO 和 Model
gf gen dao

# 生成 Controller
gf gen ctrl

# 生成 Service
gf gen service

# 启动开发服务器
gf run main.go
```

### 2. 数据库迁移
```bash
# 执行 SQL 文件
mysql -u root -p database_name < file.sql

# 权限初始化
mysql -u root -p diao_tea < tools/resources_permission_init.sql
```

### 3. 代码格式化
```bash
# 格式化代码
gofmt -w .

# 代码检查
golint ./...

# 静态分析
go vet ./...
```

## 🔍 错误处理规范

### 1. 错误类型
- **参数错误**: 400 Bad Request
- **认证错误**: 401 Unauthorized
- **权限错误**: 403 Forbidden
- **资源不存在**: 404 Not Found
- **服务器错误**: 500 Internal Server Error

### 2. 错误处理模式
```go
// Logic层错误处理
func (s *sSysAdmin) GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error) {
    // 参数验证
    if req.Page < 1 {
        return nil, gerror.New("页码不能小于1")
    }

    // 数据库操作
    list, err := dao.SysAdmins.Ctx(ctx).Where("is_delete", 0).All()
    if err != nil {
        g.Log().Error(ctx, "查询管理员列表失败:", err)
        return nil, gerror.New("查询失败")
    }

    return &v1.GetListRes{List: list}, nil
}
```

### 3. 日志记录规范
```go
// 信息日志
g.Log().Info(ctx, "用户登录成功:", username)

// 警告日志
g.Log().Warning(ctx, "权限验证失败:", path)

// 错误日志
g.Log().Error(ctx, "数据库操作失败:", err)

// 调试日志
g.Log().Debug(ctx, "请求参数:", req)
```

## 🔒 安全规范

### 1. 输入验证
- **必须验证**: 所有用户输入都必须验证
- **验证规则**: 使用 GoFrame 的验证标签
- **SQL注入**: 使用 ORM 防止 SQL 注入
- **XSS防护**: 对输出内容进行转义

### 2. 认证授权
- **JWT Token**: 使用 JWT 进行身份认证
- **Token过期**: 设置合理的过期时间
- **权限验证**: 每个接口都要验证权限
- **超级管理员**: 拥有所有权限

### 3. 数据安全
- **敏感信息**: 密码等敏感信息必须加密存储
- **软删除**: 重要数据使用软删除
- **数据备份**: 定期备份重要数据
- **访问日志**: 记录所有操作日志

## 📱 移动端规范

### 1. 移动端路由
- **路径前缀**: `/m/` 开头
- **HTML页面**: `/m/list`, `/m/detail`
- **API接口**: `/m/api/` 开头

### 2. 微信集成
- **授权中间件**: 使用 `WechatAuth` 中间件
- **JSSDK配置**: 提供微信 JSSDK 配置接口
- **用户信息**: 自动获取微信用户信息

### 3. 响应式设计
- **移动优先**: 优先考虑移动端体验
- **触摸友好**: 按钮大小适合触摸操作
- **加载优化**: 优化移动端加载速度

## 🎯 性能优化规范

### 1. 数据库优化
- **索引设计**: 为常用查询字段添加索引
- **分页查询**: 避免深度分页，使用游标分页
- **连接查询**: 使用 LEFT JOIN 避免 N+1 查询
- **查询缓存**: 对热点数据进行缓存

### 2. 代码优化
- **避免循环查询**: 批量操作代替循环查询
- **内存管理**: 及时释放不用的资源
- **并发控制**: 使用适当的并发控制机制
- **缓存策略**: 合理使用 Redis 缓存

### 3. 接口优化
- **响应压缩**: 启用 GZIP 压缩
- **静态资源**: 使用 CDN 加速静态资源
- **接口合并**: 减少不必要的接口调用
- **异步处理**: 耗时操作使用异步处理

## 🧩 模块开发模板

### 1. 标准CRUD模块
创建新的CRUD模块时，请参考以下模板：

#### API定义模板
```go
// api/{module}/{module}.go
package {module}

import (
    "github.com/gogf/gf/v2/frame/g"
)

type I{Module}V1 interface {
    GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error)
    GetOne(ctx context.Context, req *v1.GetOneReq) (res *v1.GetOneRes, err error)
    Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error)
    Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error)
    Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error)
}
```

#### 请求响应结构体模板
```go
// api/{module}/v1/{module}.go
type GetListReq struct {
    g.Meta   `path:"/{module}" method:"get" tags:"{模块名}管理" summary:"获取{模块名}列表" dc:"获取{模块名}列表" perms:"{module}:list"`
    Page     int    `p:"page" d:"1" v:"min:1#页码最小为1"`
    PageSize int    `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间"`
    Keyword  string `p:"keyword" d:"" dc:"搜索关键词"`
}

type GetListRes struct {
    List  []{Module}Info `json:"list" dc:"{模块名}列表"`
    Total int            `json:"total" dc:"总数"`
}
```

### 2. 微信相关模块
微信相关功能开发时需要注意：
- 使用 PowerWeChat SDK
- 配置微信公众号信息
- 处理微信回调验证
- 实现用户授权流程

## 📋 检查清单

### 开发完成检查
- [ ] 数据库表结构设计合理
- [ ] API接口定义完整
- [ ] 权限配置正确
- [ ] 参数验证完善
- [ ] 错误处理到位
- [ ] 日志记录充分
- [ ] 单元测试通过
- [ ] 文档更新完整
- [ ] 代码审查通过
- [ ] 性能测试达标

### 部署前检查
- [ ] 配置文件正确
- [ ] 数据库迁移完成
- [ ] 权限初始化完成
- [ ] 静态资源部署
- [ ] 日志目录创建
- [ ] 监控配置完成
- [ ] 备份策略制定
- [ ] 回滚方案准备

---

## 📞 技术支持

如有任何技术问题或规范疑问，请联系技术负责人或在项目群中讨论。

本规范文档将随着项目发展持续更新，所有开发人员必须严格遵循此规范进行开发。

**最后更新时间**: 2025-01-23
