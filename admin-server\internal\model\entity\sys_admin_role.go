// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// SysAdminRole is the golang structure for table sys_admin_role.
type SysAdminRole struct {
	AdminId int64 `json:"adminId" orm:"admin_id" description:"管理员id"` // 管理员id
	RoleId  int64 `json:"roleId"  orm:"role_id"  description:"角色id"`  // 角色id
}
