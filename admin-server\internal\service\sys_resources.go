package service

import (
	"admin-server/internal/model/entity"
	"context"
)

// ISysResources 资源服务接口
type ISysResources interface {
	GetResourcesList(ctx context.Context, page, pageSize int, groupId int64, originName, mimeType string, storageMode int) (list []entity.SysResources, total int, err error)
	GetResourcesDetail(ctx context.Context, id int64) (*entity.SysResources, error)
	CreateResources(ctx context.Context, groupId int64, storageMode int, originName, objectName, hash, mimeType, storagePath, suffix, sizeByte, sizeInfo, url, remark string) error
	UpdateResources(ctx context.Context, id int64, groupId int64, storageMode int, originName, objectName, hash, mimeType, storagePath, suffix, sizeByte, sizeInfo, url, remark string) error
	DeleteResources(ctx context.Context, ids []int64) error
	GetResourcesByGroup(ctx context.Context, groupId int64, page, pageSize int) (list []entity.SysResources, total int, err error)
	CheckResourcesHashExists(ctx context.Context, hash string, excludeId int64) (bool, error)
	UploadFile(ctx context.Context, groupId int64, file interface{}) (*entity.SysResources, error)
	FileUpload(ctx context.Context, groupId int64, file interface{}) (*entity.SysResources, error)
}

var localSysResources ISysResources

func SysResources() ISysResources {
	if localSysResources == nil {
		panic("ISysResources接口未实现或未注册")
	}
	return localSysResources
}

func RegisterSysResources(i ISysResources) {
	localSysResources = i
}
