package sys_config_group

import (
	"context"

	"admin-server/api/sys_config_group/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error) {
	list, total, err := service.SysConfigGroup().GetList(ctx, req)
	if err != nil {
		return nil, err
	}
	return &v1.GetListRes{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}
