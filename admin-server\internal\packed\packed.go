package packed

// 是否是超级管理员1是0否
type Super int

const (
	NO_SUPER Super = 0
	IS_SUPER Super = 1
)

// 是否禁用: 0=否, 1=是
type Disable int

const (
	ENABLE  Disable = 0
	DISABLE Disable = 1
)

// 是否删除: 0=否, 1=是
type IsDelete int

const (
	NO_DELETE IsDelete = 0
	IS_DELETE IsDelete = 1
)

// 菜单类型: M=目录，C=菜单，A=按钮
type MenuType string

const (
	MENU_TYPE_DIR    MenuType = "M" // 目录
	MENU_TYPE_MENU   MenuType = "C" // 菜单
	MENU_TYPE_BUTTON MenuType = "A" // 按钮
)

// 是否缓存: 0=否, 1=是
type IsCache int

const (
	NO_CACHE IsCache = 0
	IS_CACHE IsCache = 1
)

// 是否显示: 0=否, 1=是
type IsShow int

const (
	NO_SHOW IsShow = 0
	IS_SHOW IsShow = 1
)

// 是否系统保留 1是 0否
type System int

const (
	NO_SYSTEM  System = 0
	YES_SYSTEM System = 1
)
