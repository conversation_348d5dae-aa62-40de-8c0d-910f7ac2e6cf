# 开发规范文档总览

## 📚 文档结构

本项目的开发规范文档包含以下几个部分：

### 1. 核心文档
- **[开发规范文档.md](./开发规范文档.md)** - 完整的开发规范，包含所有技术细节
- **[开发规范快速参考.md](./开发规范快速参考.md)** - 快速查阅的规范摘要
- **[新模块开发模板.md](./新模块开发模板.md)** - 新功能模块开发的完整模板

### 2. 架构图表
- **项目架构图** - 展示整体系统架构和各层关系
- **RBAC权限系统流程图** - 权限验证的完整流程

### 3. API文档
- **[api/](./api/)** - 各模块的API接口文档
- **测试示例** - 每个模块的API测试用例

## 🎯 规范要点

### 技术栈
- **框架**: GoFrame v2.9.0
- **数据库**: MySQL 8.0+
- **缓存**: Redis
- **认证**: JWT Token
- **微信**: PowerWeChat SDK

### 架构模式
采用GoFrame标准的四层架构：
```
API层 → Controller层 → Logic层 → Service层
                    ↓
               DAO层 → Model层
```

### 命名规范
- **模块名**: 下划线分隔 (`sys_admin`, `zb_user`)
- **文件名**: `{module}_v1_{action}.go`
- **权限标识**: `{module}:{resource}:{action}`

### 开发流程
1. 设计数据库表结构
2. 生成DAO和Model (`gf gen dao`)
3. 定义API接口
4. 实现Service接口
5. 实现Logic业务逻辑
6. 生成Controller (`gf gen ctrl`)
7. 配置路由和权限
8. 编写测试用例
9. 更新文档

## 🚀 快速开始

### 环境准备
```bash
# 安装GoFrame CLI
go install github.com/gogf/gf/cmd/gf/v2@latest

# 启动项目
cd admin-server
go mod tidy
gf run main.go
```

### 新模块开发
1. 参考 [新模块开发模板.md](./新模块开发模板.md)
2. 按照模板创建相关文件
3. 执行代码生成命令
4. 测试验证功能

### 常用命令
```bash
# 生成代码
gf gen dao      # 生成DAO和Model
gf gen ctrl     # 生成Controller

# 代码检查
gofmt -w .      # 格式化代码
go vet ./...    # 静态分析
```

## 📋 开发检查清单

### 功能开发完成
- [ ] 数据库表设计合理
- [ ] API接口定义完整
- [ ] 权限配置正确
- [ ] 参数验证完善
- [ ] 错误处理到位
- [ ] 日志记录充分
- [ ] 单元测试通过
- [ ] 文档更新完整

### 代码提交前
- [ ] 代码格式化完成
- [ ] 静态分析通过
- [ ] 所有测试通过
- [ ] 权限验证正确
- [ ] 文档同步更新

## 🔧 工具推荐

### 开发工具
- **IDE**: GoLand / VS Code
- **数据库**: Navicat / DBeaver
- **API测试**: Postman / Insomnia
- **版本控制**: Git

### GoFrame工具
- **gf gen dao** - 生成数据访问层代码
- **gf gen ctrl** - 生成控制器代码
- **gf gen service** - 生成服务接口代码
- **gf run** - 热重载运行项目

## 📞 技术支持

### 问题反馈
- 技术问题请在项目群中讨论
- Bug报告请提交到项目Issue
- 规范建议请联系技术负责人

### 学习资源
- [GoFrame官方文档](https://goframe.org/)
- [项目API文档](./api/)
- [数据库设计文档](./database_design.md)

## 🔄 规范更新

### 版本历史
- **v1.0** (2025-01-23) - 初始版本，包含完整开发规范

### 更新流程
1. 技术负责人审核规范变更
2. 团队讨论确认变更内容
3. 更新相关文档
4. 通知所有开发人员

## ⚠️ 重要提醒

1. **严格遵循规范** - 所有开发必须按照规范执行
2. **代码审查** - 提交前必须经过代码审查
3. **测试验证** - 功能完成后必须进行充分测试
4. **文档同步** - 代码变更时必须同步更新文档
5. **安全考虑** - 涉及权限和数据安全的操作需特别注意

## 📈 持续改进

### 规范优化
- 根据项目发展持续优化规范
- 收集团队反馈改进开发体验
- 引入新的最佳实践

### 工具升级
- 跟进GoFrame框架更新
- 升级相关依赖包版本
- 优化开发工具链

---

**开发规范文档** 是项目开发的重要指导文件，请所有开发人员认真阅读并严格执行。

如有疑问，请及时沟通交流，确保项目开发的规范性和一致性。

**文档维护**: 技术团队  
**最后更新**: 2025-01-23
