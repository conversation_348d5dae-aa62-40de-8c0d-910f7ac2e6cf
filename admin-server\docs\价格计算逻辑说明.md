# 价格计算逻辑说明

## 计算规则

在package.html页面中，应付金额的计算逻辑为：

**总价格 = 套餐单价 × 选择的城市数量**

## 实现逻辑

### 1. 基础计算公式
```javascript
const cityCount = selectedCities.length || 1; // 至少选择1个城市
const totalPrice = selectedPackage.price * cityCount;
```

### 2. 最小城市数量限制
- 当用户未选择任何城市时，默认按1个城市计算
- 确保价格不会为0，避免用户困惑

### 3. 实时价格更新
价格会在以下情况下自动更新：
- 页面加载完成时
- 选择或取消选择城市时
- 切换套餐时
- 全选城市时
- 清空城市选择时

## 代码实现

### 1. 核心价格更新函数
```javascript
function updateTotalPrice() {
    const totalPriceElement = document.getElementById('totalPrice');
    if (selectedPackage && totalPriceElement) {
        const cityCount = selectedCities.length || 1; // 至少选择1个城市
        const totalPrice = selectedPackage.price * cityCount;
        totalPriceElement.textContent = `¥${totalPrice}`;
        
        console.log('价格计算:', {
            packagePrice: selectedPackage.price,
            cityCount: cityCount,
            totalPrice: totalPrice
        });
    }
}
```

### 2. 触发价格更新的场景

#### 城市选择变化
```javascript
function toggleCitySelection(button) {
    // ... 城市选择逻辑
    updateTotalPrice(); // 更新总价格
}
```

#### 全选城市
```javascript
function selectAllCities() {
    // ... 全选逻辑
    updateTotalPrice(); // 更新总价格
}
```

#### 清空城市选择
```javascript
function clearAllSelections() {
    // ... 清空逻辑
    updateTotalPrice(); // 更新总价格
}
```

#### 套餐选择变化
```javascript
function selectPackage(cardElement, pkg) {
    // ... 套餐选择逻辑
    updateTotalPrice(); // 更新总价格
}
```

#### 页面初始化
```javascript
// 城市加载完成后
function loadCities() {
    // ... 加载逻辑
    updateTotalPrice(); // 初始化价格显示
}

// 套餐加载完成后
function renderPackages(packages) {
    // ... 渲染逻辑
    updateTotalPrice(); // 更新价格显示
}
```

## 计算示例

### 示例1：单个城市
```
选择套餐：特惠会员199元 (¥199)
选择城市：北京 (1个城市)
应付金额：¥199 × 1 = ¥199
```

### 示例2：多个城市
```
选择套餐：特惠会员199元 (¥199)
选择城市：北京、上海、广州 (3个城市)
应付金额：¥199 × 3 = ¥597
```

### 示例3：全选城市
```
选择套餐：特惠会员199元 (¥199)
选择城市：全选 (假设有8个城市)
应付金额：¥199 × 8 = ¥1592
```

### 示例4：未选择城市
```
选择套餐：特惠会员199元 (¥199)
选择城市：无 (默认按1个城市计算)
应付金额：¥199 × 1 = ¥199
```

## 提供的API函数

### 1. 获取总价格
```javascript
const totalPrice = getTotalPrice();
// 返回: 597 (数字类型)
```

### 2. 获取价格计算详情
```javascript
const details = getPriceDetails();
// 返回: {
//   packagePrice: 199,
//   cityCount: 3,
//   totalPrice: 597,
//   selectedCities: ["北京", "上海", "广州"],
//   selectedPackage: "特惠会员199元"
// }
```

### 3. 获取选中套餐价格
```javascript
const packagePrice = getSelectedPackagePrice();
// 返回: 199
```

### 4. 获取选中城市数量
```javascript
const cityCount = selectedCities.length;
// 返回: 3
```

## 用户界面显示

### 1. 底部价格显示
```html
<div class="flex items-center justify-between mb-3">
    <span class="text-sm text-gray-600">应付金额</span>
    <span id="totalPrice" class="text-lg font-bold text-purple-600">¥597</span>
</div>
```

### 2. 价格更新动画
- 价格变化时平滑过渡
- 数字更新有视觉反馈
- 确保用户能感知到价格变化

### 3. 价格计算提示
在控制台中输出详细的计算过程：
```
价格计算: {
  packagePrice: 199,
  cityCount: 3,
  totalPrice: 597
}
```

## 边界情况处理

### 1. 未选择套餐
```javascript
if (!selectedPackage) {
    return 0; // 返回0或不更新价格
}
```

### 2. 未选择城市
```javascript
const cityCount = selectedCities.length || 1; // 默认1个城市
```

### 3. 套餐价格为0
```javascript
if (selectedPackage.price <= 0) {
    // 处理免费套餐或价格异常情况
}
```

### 4. 城市数量异常
```javascript
const cityCount = Math.max(selectedCities.length, 1); // 确保至少为1
```

## 测试用例

### 1. 基础功能测试
```javascript
// 测试价格计算
console.log('测试1 - 单城市:', getTotalPrice()); // 应该等于套餐价格
selectAllCities();
console.log('测试2 - 全选城市:', getTotalPrice()); // 应该等于套餐价格×城市数
clearAllSelections();
console.log('测试3 - 清空城市:', getTotalPrice()); // 应该等于套餐价格×1
```

### 2. 边界情况测试
```javascript
// 测试边界情况
console.log('当前价格详情:', getPriceDetails());
console.log('总价格:', getTotalPrice());
console.log('套餐价格:', getSelectedPackagePrice());
console.log('城市数量:', selectedCities.length);
```

### 3. 用户操作测试
1. 选择不同套餐，观察价格变化
2. 选择不同数量的城市，观察价格变化
3. 使用全选/清空功能，观察价格变化
4. 检查价格显示是否实时更新

## 性能优化

### 1. 避免重复计算
```javascript
// 缓存计算结果，避免频繁计算
let cachedTotalPrice = null;
let lastCalculationState = null;

function updateTotalPrice() {
    const currentState = {
        packageId: selectedPackage?.id,
        cityCount: selectedCities.length
    };
    
    // 如果状态未变化，使用缓存结果
    if (JSON.stringify(currentState) === JSON.stringify(lastCalculationState)) {
        return;
    }
    
    // 重新计算价格
    // ...
}
```

### 2. 防抖处理
```javascript
// 对于频繁的价格更新，可以使用防抖
let priceUpdateTimer = null;

function debouncedUpdateTotalPrice() {
    clearTimeout(priceUpdateTimer);
    priceUpdateTimer = setTimeout(updateTotalPrice, 100);
}
```

## 扩展功能建议

### 1. 优惠券支持
```javascript
function calculateTotalPrice() {
    let basePrice = selectedPackage.price * cityCount;
    
    // 应用优惠券
    if (selectedCoupon) {
        basePrice = applyCoupon(basePrice, selectedCoupon);
    }
    
    return basePrice;
}
```

### 2. 阶梯定价
```javascript
function getPackagePriceByCity(packageId, cityCount) {
    // 根据城市数量返回不同的单价
    if (cityCount >= 10) return package.price * 0.8; // 8折
    if (cityCount >= 5) return package.price * 0.9;  // 9折
    return package.price; // 原价
}
```

### 3. 税费计算
```javascript
function calculateTotalWithTax(basePrice) {
    const taxRate = 0.06; // 6%税率
    const tax = basePrice * taxRate;
    return {
        basePrice: basePrice,
        tax: tax,
        total: basePrice + tax
    };
}
```

### 4. 价格历史记录
```javascript
const priceHistory = [];

function recordPriceChange(oldPrice, newPrice, reason) {
    priceHistory.push({
        timestamp: new Date(),
        oldPrice: oldPrice,
        newPrice: newPrice,
        reason: reason
    });
}
```
