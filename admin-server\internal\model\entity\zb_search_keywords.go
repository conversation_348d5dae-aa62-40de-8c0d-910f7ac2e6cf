// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ZbSearchKeywords is the golang structure for table zb_search_keywords.
type ZbSearchKeywords struct {
	Id          int64       `json:"id"          orm:"id"          description:"主键ID"`
	Keyword     string      `json:"keyword"     orm:"keyword"     description:"搜索关键词"`
	SearchCount int         `json:"searchCount" orm:"search_count" description:"搜索次数"`
	ClickCount  int         `json:"clickCount"  orm:"click_count"  description:"点击次数"`
	Trend       string      `json:"trend"       orm:"trend"       description:"趋势：up上升，down下降，stable稳定"`
	IsHot       int         `json:"isHot"       orm:"is_hot"      description:"是否热门：0否，1是"`
	IsNew       int         `json:"isNew"       orm:"is_new"      description:"是否新增：0否，1是"`
	Category    string      `json:"category"    orm:"category"    description:"分类"`
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"  description:"创建时间"`
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"  description:"更新时间"`
}
