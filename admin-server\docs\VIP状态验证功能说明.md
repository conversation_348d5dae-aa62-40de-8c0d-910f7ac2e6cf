# VIP状态验证功能说明

## 功能概述

在Detail页面中添加了VIP状态验证功能，根据用户的套餐有效期判断用户是否为有效的VIP用户。

## 实现的功能

### 1. VIP状态验证逻辑
- 检查用户是否被禁用或删除
- 验证套餐有效期是否设置
- 判断当前日期是否在有效期内
- 返回VIP状态（1=有效，0=无效）

### 2. 模板数据传递
- `is_vip`: VIP状态（1=有效，0=无效）
- `vip_start`: VIP开始日期（格式：2025-07-16）
- `vip_end`: VIP结束日期（格式：2025-07-26）

## 代码实现

### 1. Detail方法中的VIP验证
```go
// 根据openid查询用户信息
userInfo, err := service.ZbUser().GetByOpenid(r.GetCtx(), wechatUser["openid"].(string))
if err != nil {
    templateData["is_vip"] = 0 // 查询失败，设为无效
    g.Log().Error(r.GetCtx(), "查询用户信息失败:", err)
} else {
    // 验证VIP有效期
    isVip := c.validateVipStatus(userInfo)
    templateData["is_vip"] = isVip
    
    // 添加VIP相关信息到模板数据
    if userInfo.EffectiveStart != nil {
        templateData["vip_start"] = userInfo.EffectiveStart.Format("2006-01-02")
    }
    if userInfo.EffectiveEnd != nil {
        templateData["vip_end"] = userInfo.EffectiveEnd.Format("2006-01-02")
    }
    
    g.Log().Info(r.GetCtx(), "用户VIP状态验证完成:", g.Map{
        "user_id":         userInfo.Id,
        "is_vip":          isVip,
        "effective_start": userInfo.EffectiveStart,
        "effective_end":   userInfo.EffectiveEnd,
    })
}
```

### 2. validateVipStatus方法
```go
func (c *ControllerMobile) validateVipStatus(userInfo *entity.ZbUser) int {
    // 如果用户被禁用或删除，直接返回无效
    if userInfo.IsDisable == 1 || userInfo.IsDelete == 1 {
        return 0
    }
    
    // 如果没有设置有效期，返回无效
    if userInfo.EffectiveStart == nil || userInfo.EffectiveEnd == nil {
        return 0
    }
    
    // 获取当前时间（只比较日期部分）
    now := time.Now()
    currentDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
    
    // 获取有效期开始时间（日期开始）
    startDate := time.Date(
        userInfo.EffectiveStart.Year(), 
        userInfo.EffectiveStart.Month(), 
        userInfo.EffectiveStart.Day(), 
        0, 0, 0, 0, 
        userInfo.EffectiveStart.Location(),
    )
    
    // 获取有效期结束时间（日期结束）
    endDate := time.Date(
        userInfo.EffectiveEnd.Year(), 
        userInfo.EffectiveEnd.Month(), 
        userInfo.EffectiveEnd.Day(), 
        23, 59, 59, 0, // 结束日期包含当天全天
        userInfo.EffectiveEnd.Location(),
    )
    
    // 验证当前日期是否在有效期内
    if (currentDate.Equal(startDate) || currentDate.After(startDate)) && 
       (currentDate.Equal(endDate) || currentDate.Before(endDate)) {
        return 1 // VIP有效
    }
    
    return 0 // VIP无效
}
```

## 验证逻辑详解

### 1. 用户状态检查
```go
if userInfo.IsDisable == 1 || userInfo.IsDelete == 1 {
    return 0 // 用户被禁用或删除，VIP无效
}
```

### 2. 有效期设置检查
```go
if userInfo.EffectiveStart == nil || userInfo.EffectiveEnd == nil {
    return 0 // 没有设置有效期，VIP无效
}
```

### 3. 日期范围验证
- **开始日期**: 从当天00:00:00开始
- **结束日期**: 到当天23:59:59结束
- **当前日期**: 只比较日期部分，不考虑具体时间

### 4. 边界情况处理
- **开始日期当天**: 有效（包含开始日期）
- **结束日期当天**: 有效（包含结束日期）
- **开始日期之前**: 无效
- **结束日期之后**: 无效

## 测试用例

### 1. 有效VIP用户
```
当前日期: 2025-07-20
开始日期: 2025-07-16
结束日期: 2025-07-26
结果: is_vip = 1 (有效)
```

### 2. 开始日期当天
```
当前日期: 2025-07-16
开始日期: 2025-07-16
结束日期: 2025-07-26
结果: is_vip = 1 (有效)
```

### 3. 结束日期当天
```
当前日期: 2025-07-26
开始日期: 2025-07-16
结束日期: 2025-07-26
结果: is_vip = 1 (有效)
```

### 4. 过期VIP用户
```
当前日期: 2025-07-27
开始日期: 2025-07-16
结束日期: 2025-07-26
结果: is_vip = 0 (无效)
```

### 5. 未开始VIP用户
```
当前日期: 2025-07-15
开始日期: 2025-07-16
结束日期: 2025-07-26
结果: is_vip = 0 (无效)
```

### 6. 被禁用用户
```
用户状态: is_disable = 1
结果: is_vip = 0 (无效)
```

### 7. 未设置有效期
```
开始日期: null
结束日期: null
结果: is_vip = 0 (无效)
```

## 模板中的使用

### 1. 检查VIP状态
```html
{{if eq .is_vip 1}}
    <span class="vip-badge">VIP会员</span>
    <p>VIP有效期：{{.vip_start}} 至 {{.vip_end}}</p>
{{else}}
    <span class="normal-badge">普通用户</span>
    <p>升级VIP享受更多特权</p>
{{end}}
```

### 2. 根据VIP状态显示不同内容
```html
{{if eq .is_vip 1}}
    <!-- VIP用户可以看到的内容 -->
    <div class="vip-content">
        <h3>VIP专享信息</h3>
        <p>联系方式：138****8888</p>
        <p>详细要求：...</p>
    </div>
{{else}}
    <!-- 普通用户看到的内容 -->
    <div class="normal-content">
        <h3>基础信息</h3>
        <p>升级VIP查看联系方式</p>
        <button onclick="showVipModal()">立即升级</button>
    </div>
{{end}}
```

### 3. VIP状态样式
```css
.vip-badge {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.normal-badge {
    background: #gray-100;
    color: #gray-600;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}
```

## 日志输出

### 1. VIP验证成功
```
[INFO] 用户VIP状态验证完成: {
    user_id: 1, 
    is_vip: 1, 
    effective_start: 2025-07-16 00:00:00, 
    effective_end: 2025-07-26 23:59:59
}
```

### 2. VIP验证失败
```
[INFO] 用户VIP状态验证完成: {
    user_id: 1, 
    is_vip: 0, 
    effective_start: 2025-07-16 00:00:00, 
    effective_end: 2025-07-15 23:59:59
}
```

### 3. 查询用户失败
```
[ERROR] 查询用户信息失败: record not found
```

## 注意事项

1. **时区处理**: 使用用户数据中的时区信息，确保时间比较的准确性
2. **边界包含**: 开始日期和结束日期都包含在有效期内
3. **日期精度**: 只比较日期部分，不考虑具体的时分秒
4. **状态优先**: 用户被禁用或删除时，即使在有效期内也返回无效
5. **空值处理**: 有效期为空时返回无效状态

## 扩展功能建议

1. **提前提醒**: VIP到期前N天提醒用户续费
2. **宽限期**: VIP到期后给予几天宽限期
3. **自动续费**: 支持自动续费功能
4. **等级区分**: 支持不同等级的VIP（如VIP1、VIP2等）
5. **使用统计**: 记录VIP功能的使用情况
