# 招标类别管理API - 前端对接文档

## 接口概览

| 接口名称 | 方法 | 路径 | 说明 |
|---------|------|------|------|
| 创建类别 | POST | `/zb_cate` | 创建新的招标类别 |
| 更新类别 | PUT | `/zb_cate/{id}` | 更新类别信息 |
| 删除类别 | DELETE | `/zb_cate/{id}` | 删除类别（软删除） |
| 获取类别详情 | GET | `/zb_cate/{id}` | 获取单个类别信息 |
| 获取类别列表 | GET | `/zb_cate/list` | 获取类别分页列表 |
| 获取所有类别 | GET | `/zb_cate/all` | 获取所有类别（不分页） |
| 更新排序 | PUT | `/zb_cate/{id}/sort` | 更新类别排序 |
| 更新状态 | PUT | `/zb_cate/{id}/status` | 更新类别状态 |
| 批量删除 | DELETE | `/zb_cate/batch` | 批量删除类别 |

## 数据模型

### ZbCateInfo

```typescript
interface ZbCateInfo {
  id: number;                    // 类别ID
  name: string;                  // 类别名称
  sort: number;                  // 排序值
  is_disable: number;            // 是否禁用：0=否，1=是
  is_delete: number;             // 是否删除：0=否，1=是
  created_at: string | null;     // 创建时间
  updated_at: string | null;     // 更新时间
  deleted_at: string | null;     // 删除时间
}
```

### 状态枚举

```typescript
enum CateStatus {
  ENABLED = 0,    // 启用
  DISABLED = 1    // 禁用
}

enum DeleteStatus {
  NORMAL = 0,     // 正常
  DELETED = 1     // 已删除
}
```

## 接口详情

### 1. 创建类别

```typescript
// 请求
interface CreateCateRequest {
  name: string;                  // 类别名称
  sort?: number;                 // 排序值，默认1
  is_disable?: number;           // 是否禁用，默认0
}

// 响应
interface CreateCateResponse {
  id: number;                    // 创建的类别ID
}
```

### 2. 更新类别

```typescript
// 请求
interface UpdateCateRequest {
  id: number;                    // 类别ID（路径参数）
  name: string;                  // 类别名称
  sort: number;                  // 排序值
  is_disable: number;            // 是否禁用
}

// 响应
interface UpdateCateResponse {}
```

### 3. 删除类别

```typescript
// 请求
interface DeleteCateRequest {
  id: number;                    // 类别ID（路径参数）
}

// 响应
interface DeleteCateResponse {}
```

### 4. 获取类别详情

```typescript
// 请求
interface GetCateRequest {
  id: number;                    // 类别ID（路径参数）
}

// 响应
interface GetCateResponse extends ZbCateInfo {}
```

### 5. 获取类别列表

```typescript
// 请求
interface GetCateListRequest {
  page?: number;                 // 页码，默认1
  page_size?: number;            // 每页数量，默认10
  name?: string;                 // 类别名称，模糊搜索
  is_disable?: number;           // 是否禁用筛选
}

// 响应
interface GetCateListResponse {
  list: ZbCateInfo[];            // 类别列表
  total: number;                 // 总数
  page: number;                  // 当前页码
  page_size: number;             // 每页数量
}
```

### 6. 获取所有类别

```typescript
// 请求
interface GetAllCateRequest {
  is_disable?: number;           // 是否禁用筛选
}

// 响应
interface GetAllCateResponse {
  list: ZbCateInfo[];            // 类别列表
}
```

### 7. 更新排序

```typescript
// 请求
interface UpdateCateSortRequest {
  id: number;                    // 类别ID（路径参数）
  sort: number;                  // 排序值
}

// 响应
interface UpdateCateSortResponse {}
```

### 8. 更新状态

```typescript
// 请求
interface UpdateCateStatusRequest {
  id: number;                    // 类别ID（路径参数）
  is_disable: number;            // 是否禁用：0=否，1=是
}

// 响应
interface UpdateCateStatusResponse {}
```

### 9. 批量删除

```typescript
// 请求
interface BatchDeleteCateRequest {
  ids: number[];                 // 类别ID列表
}

// 响应
interface BatchDeleteCateResponse {
  count: number;                 // 删除数量
}
```

## Vue 3 + TypeScript 示例

```typescript
import { ref, reactive } from 'vue'
import axios from 'axios'

// 招标类别管理组合式函数
export function useZbCate() {
  const cates = ref<ZbCateInfo[]>([])
  const allCates = ref<ZbCateInfo[]>([])
  const loading = ref(false)
  const total = ref(0)

  // 获取类别列表
  const getCateList = async (params: GetCateListRequest) => {
    loading.value = true
    try {
      const response = await axios.get('/zb_cate/list', { params })
      cates.value = response.data.data.list
      total.value = response.data.data.total
      return response.data.data
    } catch (error) {
      console.error('获取类别列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取所有类别
  const getAllCates = async (params?: GetAllCateRequest) => {
    loading.value = true
    try {
      const response = await axios.get('/zb_cate/all', { params })
      allCates.value = response.data.data.list
      return response.data.data.list
    } catch (error) {
      console.error('获取所有类别失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建类别
  const createCate = async (data: CreateCateRequest) => {
    try {
      const response = await axios.post('/zb_cate', data)
      return response.data.data
    } catch (error) {
      console.error('创建类别失败:', error)
      throw error
    }
  }

  // 更新类别
  const updateCate = async (id: number, data: Omit<UpdateCateRequest, 'id'>) => {
    try {
      const response = await axios.put(`/zb_cate/${id}`, data)
      return response.data.data
    } catch (error) {
      console.error('更新类别失败:', error)
      throw error
    }
  }

  // 删除类别
  const deleteCate = async (id: number) => {
    try {
      const response = await axios.delete(`/zb_cate/${id}`)
      return response.data.data
    } catch (error) {
      console.error('删除类别失败:', error)
      throw error
    }
  }

  // 获取类别详情
  const getCateDetail = async (id: number) => {
    try {
      const response = await axios.get(`/zb_cate/${id}`)
      return response.data.data
    } catch (error) {
      console.error('获取类别详情失败:', error)
      throw error
    }
  }

  // 更新类别排序
  const updateCateSort = async (id: number, sort: number) => {
    try {
      const response = await axios.put(`/zb_cate/${id}/sort`, { sort })
      return response.data.data
    } catch (error) {
      console.error('更新类别排序失败:', error)
      throw error
    }
  }

  // 更新类别状态
  const updateCateStatus = async (id: number, is_disable: number) => {
    try {
      const response = await axios.put(`/zb_cate/${id}/status`, { is_disable })
      return response.data.data
    } catch (error) {
      console.error('更新类别状态失败:', error)
      throw error
    }
  }

  // 批量删除类别
  const batchDeleteCates = async (ids: number[]) => {
    try {
      const response = await axios.delete('/zb_cate/batch', { data: { ids } })
      return response.data.data
    } catch (error) {
      console.error('批量删除类别失败:', error)
      throw error
    }
  }

  return {
    cates,
    allCates,
    loading,
    total,
    getCateList,
    getAllCates,
    createCate,
    updateCate,
    deleteCate,
    getCateDetail,
    updateCateSort,
    updateCateStatus,
    batchDeleteCates
  }
}
```

## 表单验证规则

```typescript
// 类别表单验证规则
export const cateFormRules = {
  name: [
    { required: true, message: '类别名称不能为空' },
    { min: 1, max: 255, message: '类别名称长度必须在1-255个字符之间' }
  ],
  sort: [
    { type: 'number', min: 0, message: '排序值不能小于0' }
  ],
  is_disable: [
    { required: true, message: '请选择状态' },
    { type: 'enum', enum: [0, 1], message: '状态值必须是0或1' }
  ]
}
```

## 页面组件示例（Vue 3）

```vue
<template>
  <div class="cate-management">
    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleCreate">新增类别</el-button>
      <el-button 
        type="danger" 
        :disabled="selectedIds.length === 0"
        @click="handleBatchDelete"
      >
        批量删除
      </el-button>
    </div>

    <!-- 类别列表 -->
    <el-table 
      :data="cates" 
      v-loading="loading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="类别名称" />
      <el-table-column prop="sort" label="排序" width="100" />
      <el-table-column prop="is_disable" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.is_disable === 0 ? 'success' : 'danger'">
            {{ row.is_disable === 0 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250">
        <template #default="{ row }">
          <el-button size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button 
            size="small" 
            :type="row.is_disable === 0 ? 'warning' : 'success'"
            @click="handleToggleStatus(row)"
          >
            {{ row.is_disable === 0 ? '禁用' : '启用' }}
          </el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.pageSize"
      :total="total"
      @current-change="handlePageChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useZbCate } from './composables/useZbCate'

const { 
  cates, 
  loading, 
  total, 
  getCateList, 
  updateCateStatus, 
  deleteCate, 
  batchDeleteCates 
} = useZbCate()

const selectedIds = ref<number[]>([])
const pagination = ref({
  page: 1,
  pageSize: 10
})

// 获取列表数据
const fetchData = async () => {
  await getCateList({
    page: pagination.value.page,
    page_size: pagination.value.pageSize
  })
}

// 选择变化
const handleSelectionChange = (selection: ZbCateInfo[]) => {
  selectedIds.value = selection.map(item => item.id)
}

// 切换状态
const handleToggleStatus = async (row: ZbCateInfo) => {
  const newStatus = row.is_disable === 0 ? 1 : 0
  await updateCateStatus(row.id, newStatus)
  await fetchData()
}

// 删除类别
const handleDelete = async (row: ZbCateInfo) => {
  await deleteCate(row.id)
  await fetchData()
}

// 批量删除
const handleBatchDelete = async () => {
  await batchDeleteCates(selectedIds.value)
  selectedIds.value = []
  await fetchData()
}

// 页码变化
const handlePageChange = (page: number) => {
  pagination.value.page = page
  fetchData()
}

// 页大小变化
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.page = 1
  fetchData()
}

onMounted(() => {
  fetchData()
})
</script>
```

## 注意事项

1. **名称唯一性**：创建或更新类别时需要检查名称重复
2. **软删除机制**：删除操作为软删除，不会真正删除数据
3. **状态管理**：支持启用/禁用状态切换
4. **排序功能**：支持自定义排序值
5. **批量操作**：支持批量删除功能
6. **权限控制**：根据用户权限显示相应操作按钮
7. **数据筛选**：支持按名称和状态筛选数据
