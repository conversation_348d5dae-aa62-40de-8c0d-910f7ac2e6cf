# CORS 跨域问题解决方案

## 问题描述

当前端应用（运行在 `http://localhost:3000`）尝试访问后端API（运行在 `http://localhost:8000`）时，浏览器会阻止跨域请求，报错：

```
Access to XMLHttpRequest at 'http://localhost:8000/sys_admin/list' from origin 'http://localhost:3000' has been blocked by CORS policy
```

## 解决方案

### 1. 服务器端配置（已完成）

#### 方案一：使用GoFrame内置CORS中间件

在 `internal/cmd/cmd.go` 中添加了CORS中间件：

```go
s.Group("/", func(group *ghttp.RouterGroup) {
    group.Middleware(func(r *ghttp.Request) {
        r.Response.CORSDefault()
        r.Middleware.Next()
    })
    group.Middleware(ghttp.MiddlewareHandlerResponse)
    group.Bind(
        sys_admin.NewV1(),
        sys_auth.NewV1(),
    )
})
```

#### 方案二：配置文件方式

在 `manifest/config/config.yaml` 中添加了CORS配置：

```yaml
server:
  address: ":8000"
  openapiPath: "/api.json"
  swaggerPath: "/swagger"
  # CORS配置
  cors:
    allowOrigin: "*"
    allowMethods: "GET,POST,PUT,DELETE,OPTIONS"
    allowHeaders: "Origin,Content-Type,Accept,Authorization,X-Requested-With"
    exposeHeaders: "Content-Length,Access-Control-Allow-Origin,Access-Control-Allow-Headers"
    allowCredentials: true
    maxAge: 3600
```

#### 方案三：自定义CORS中间件

创建了 `internal/middleware/cors.go` 文件，提供更灵活的CORS配置：

```go
func CORS(r *ghttp.Request) {
    r.Response.CORSDefault()
    r.Middleware.Next()
}

func CORSCustom(r *ghttp.Request) {
    corsOptions := r.Response.DefaultCORSOptions()
    corsOptions.AllowDomain = []string{"*"}
    corsOptions.AllowOrigin = "*"
    corsOptions.AllowCredentials = "true"
    corsOptions.AllowMethods = "GET,POST,PUT,DELETE,OPTIONS,HEAD,PATCH"
    corsOptions.AllowHeaders = "Origin,Content-Type,Accept,Authorization,X-Requested-With,X-CSRF-Token"
    corsOptions.ExposeHeaders = "Content-Length,Access-Control-Allow-Origin,Access-Control-Allow-Headers,Content-Type"
    corsOptions.MaxAge = 3600

    if r.Method == "OPTIONS" {
        r.Response.CORS(corsOptions)
        r.Response.WriteStatus(200)
        return
    }

    r.Response.CORS(corsOptions)
    r.Middleware.Next()
}
```

### 2. 前端配置（可选）

如果服务器端配置无法解决问题，可以在前端进行以下配置：

#### React 开发环境

在 `package.json` 中添加代理配置：

```json
{
  "name": "your-app",
  "version": "0.1.0",
  "proxy": "http://localhost:8000",
  "dependencies": {
    // ...
  }
}
```

或者在 `src/setupProxy.js` 中配置：

```javascript
const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'http://localhost:8000',
      changeOrigin: true,
      pathRewrite: {
        '^/api': '', // 移除 /api 前缀
      },
    })
  );
};
```

#### Vue 开发环境

在 `vue.config.js` 中配置：

```javascript
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  }
}
```

### 3. 测试验证

重启后端服务器后，可以通过以下方式测试CORS是否配置成功：

#### 浏览器控制台测试

```javascript
fetch('http://localhost:8000/sys_admin/list?page=1&page_size=10', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN'
  }
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));
```

#### curl 测试

```bash
curl -X GET "http://localhost:8000/sys_admin/list?page=1&page_size=10" \
  -H "Origin: http://localhost:3000" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -v
```

检查响应头中是否包含：
- `Access-Control-Allow-Origin: *`
- `Access-Control-Allow-Methods: GET,POST,PUT,DELETE,OPTIONS`
- `Access-Control-Allow-Headers: ...`

### 4. 生产环境注意事项

在生产环境中，建议：

1. **限制允许的域名**：将 `allowOrigin: "*"` 改为具体的前端域名
2. **限制允许的方法**：只允许实际需要的HTTP方法
3. **限制允许的头部**：只允许必要的请求头
4. **设置合适的缓存时间**：`maxAge` 设置为合适的值

生产环境配置示例：

```yaml
server:
  cors:
    allowOrigin: "https://yourdomain.com"
    allowMethods: "GET,POST,PUT,DELETE"
    allowHeaders: "Origin,Content-Type,Accept,Authorization"
    allowCredentials: true
    maxAge: 86400
```

### 5. 常见问题

1. **预检请求失败**：确保服务器正确处理 OPTIONS 请求
2. **认证头被阻止**：确保 `Authorization` 头在 `allowHeaders` 中
3. **Cookie 无法发送**：设置 `allowCredentials: true`
4. **自定义头被阻止**：将自定义头添加到 `allowHeaders` 中

通过以上配置，前端应用应该能够正常访问后端API而不会遇到CORS问题。
