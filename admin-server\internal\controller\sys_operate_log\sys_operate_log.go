package sys_operate_log

import (
	"admin-server/api/sys_operate_log"
	v1 "admin-server/api/sys_operate_log/v1"
	"admin-server/internal/service"
	"context"
)

var (
	// ControllerV1 控制器实例
	ControllerV1 = cSysOperateLogV1{}
)

type cSysOperateLogV1 struct{}

// NewV1 创建V1版本控制器
func NewV1() sys_operate_log.ISysOperateLogV1 {
	return &ControllerV1
}

// GetList 获取操作日志列表
func (c *cSysOperateLogV1) GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error) {
	list, total, err := service.SysOperateLog().GetOperateLogList(
		ctx,
		req.Page,
		req.PageSize,
		req.Username,
		req.Title,
		req.Method,
		req.Status,
		req.StartDate,
		req.EndDate,
	)
	if err != nil {
		return nil, err
	}

	// 转换为API响应格式
	operateLogs := make([]v1.OperateLogInfo, len(list))
	for i, log := range list {
		operateLogs[i] = v1.OperateLogInfo{
			ID:        int64(log.Id),
			RequestId: log.RequestId,
			AdminId:   log.AdminId,
			Username:  log.Username,
			Method:    log.Method,
			Title:     log.Title,
			Ip:        log.Ip,
			Status:    log.Status,
			TaskTime:  int64(log.TaskTime),
			CreatedAt: log.CreatedAt,
		}
	}

	return &v1.GetListRes{
		List:  operateLogs,
		Total: total,
	}, nil
}

// GetOne 获取操作日志详情
func (c *cSysOperateLogV1) GetOne(ctx context.Context, req *v1.GetOneReq) (res *v1.GetOneRes, err error) {
	log, err := service.SysOperateLog().GetOperateLogDetail(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	if log == nil {
		return &v1.GetOneRes{}, nil
	}

	// 转换为API响应格式
	detail := &v1.OperateLogDetail{
		ID:        int64(log.Id),
		RequestId: log.RequestId,
		AdminId:   log.AdminId,
		Username:  log.Username,
		Method:    log.Method,
		Title:     log.Title,
		Ip:        log.Ip,
		ReqHeader: log.ReqHeader,
		ReqBody:   log.ReqBody,
		ResHeader: log.ResHeader,
		ResBody:   log.ResBody,
		Status:    log.Status,
		StartTime: log.StartTime,
		EndTime:   log.EndTime,
		TaskTime:  log.TaskTime,
		CreatedAt: log.CreatedAt,
		UpdatedAt: log.UpdatedAt,
	}

	return &v1.GetOneRes{
		OperateLogDetail: detail,
	}, nil
}

// Delete 删除操作日志
func (c *cSysOperateLogV1) Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error) {
	err = service.SysOperateLog().DeleteOperateLog(ctx, req.IDs)
	if err != nil {
		return nil, err
	}

	return &v1.DeleteRes{}, nil
}

// Clear 清空操作日志
func (c *cSysOperateLogV1) Clear(ctx context.Context, req *v1.ClearReq) (res *v1.ClearRes, err error) {
	err = service.SysOperateLog().ClearOperateLog(ctx)
	if err != nil {
		return nil, err
	}

	return &v1.ClearRes{}, nil
}
