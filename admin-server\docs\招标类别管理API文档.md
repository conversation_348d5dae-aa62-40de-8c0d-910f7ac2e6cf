# 招标类别管理API文档

## 概述

招标类别管理模块提供了招标类别信息的完整CRUD功能，支持状态控制、排序、批量操作等功能。该模块用于管理招标项目的分类信息。

## 基础信息

- **模块名称**: 招标类别管理
- **基础路径**: `/zb_cate`
- **认证方式**: JWT Token
- **权限验证**: 需要相应的类别管理权限

## 数据字段说明

| 字段名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| id | int | 否 | 类别ID，自增主键 | `1` |
| name | string | 是 | 类别名称，1-255个字符 | `建筑工程` |
| sort | int | 否 | 排序值，数字越小越靠前 | `1` |
| is_disable | int | 否 | 是否禁用：0=否，1=是 | `0` |
| is_delete | int | 否 | 是否删除：0=否，1=是 | `0` |
| created_at | datetime | 否 | 创建时间 | `2025-01-15 10:00:00` |
| updated_at | datetime | 否 | 更新时间 | `2025-01-15 10:00:00` |
| deleted_at | datetime | 否 | 删除时间 | `null` |

## API接口列表

### 1. 创建招标类别

**接口地址**: `POST /zb_cate`

**接口描述**: 创建新的招标类别

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 类别名称，1-255个字符 |
| sort | int | 否 | 排序值，默认1 |
| is_disable | int | 否 | 是否禁用：0=否，1=是，默认0 |

**请求示例**:
```json
{
  "name": "建筑工程",
  "sort": 1,
  "is_disable": 0
}
```

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| id | int | 创建的类别ID |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1
  }
}
```

### 2. 更新招标类别

**接口地址**: `PUT /zb_cate/{id}`

**接口描述**: 更新指定的招标类别信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 类别ID（路径参数） |
| name | string | 是 | 类别名称，1-255个字符 |
| sort | int | 否 | 排序值 |
| is_disable | int | 否 | 是否禁用：0=否，1=是 |

**请求示例**:
```json
{
  "name": "建筑工程",
  "sort": 1,
  "is_disable": 0
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 3. 删除招标类别

**接口地址**: `DELETE /zb_cate/{id}`

**接口描述**: 删除指定的招标类别（软删除）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 类别ID（路径参数） |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 4. 获取单个招标类别

**接口地址**: `GET /zb_cate/{id}`

**接口描述**: 获取指定的招标类别详细信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 类别ID（路径参数） |

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| id | int | 类别ID |
| name | string | 类别名称 |
| sort | int | 排序值 |
| is_disable | int | 是否禁用 |
| is_delete | int | 是否删除 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |
| deleted_at | string | 删除时间 |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "建筑工程",
    "sort": 1,
    "is_disable": 0,
    "is_delete": 0,
    "created_at": "2025-01-15 10:00:00",
    "updated_at": "2025-01-15 10:00:00",
    "deleted_at": null
  }
}
```

### 5. 获取招标类别列表

**接口地址**: `GET /zb_cate/list`

**接口描述**: 获取招标类别分页列表

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认10，最大100 |
| name | string | 否 | 类别名称，模糊搜索 |
| is_disable | int | 否 | 是否禁用：0=否，1=是 |

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| list | array | 类别列表 |
| total | int | 总数 |
| page | int | 当前页码 |
| page_size | int | 每页数量 |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "建筑工程",
        "sort": 1,
        "is_disable": 0,
        "is_delete": 0,
        "created_at": "2025-01-15 10:00:00",
        "updated_at": "2025-01-15 10:00:00",
        "deleted_at": null
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10
  }
}
```

### 6. 获取所有招标类别

**接口地址**: `GET /zb_cate/all`

**接口描述**: 获取所有招标类别（不分页）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| is_disable | int | 否 | 是否禁用：0=否，1=是 |

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| list | array | 类别列表 |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "建筑工程",
        "sort": 1,
        "is_disable": 0,
        "is_delete": 0,
        "created_at": "2025-01-15 10:00:00",
        "updated_at": "2025-01-15 10:00:00",
        "deleted_at": null
      }
    ]
  }
}
```

### 7. 更新招标类别排序

**接口地址**: `PUT /zb_cate/{id}/sort`

**接口描述**: 更新指定类别的排序值

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 类别ID（路径参数） |
| sort | int | 是 | 排序值 |

**请求示例**:
```json
{
  "sort": 10
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 8. 更新招标类别状态

**接口地址**: `PUT /zb_cate/{id}/status`

**接口描述**: 更新指定类别的启用/禁用状态

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 类别ID（路径参数） |
| is_disable | int | 是 | 是否禁用：0=否，1=是 |

**请求示例**:
```json
{
  "is_disable": 1
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 9. 批量删除招标类别

**接口地址**: `DELETE /zb_cate/batch`

**接口描述**: 批量删除招标类别（软删除）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | array | 是 | 类别ID列表 |

**请求示例**:
```json
{
  "ids": [1, 2, 3]
}
```

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| count | int | 删除数量 |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "count": 3
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 常见错误示例

### 参数验证错误
```json
{
  "code": 400,
  "message": "类别名称不能为空",
  "data": null
}
```

### 业务逻辑错误
```json
{
  "code": 400,
  "message": "类别名称已存在",
  "data": null
}
```

### 资源不存在
```json
{
  "code": 404,
  "message": "类别不存在",
  "data": null
}
```

## 业务规则

1. **名称唯一性**：
   - 类别名称不能重复
   - 更新时排除自己进行重复检查

2. **软删除机制**：
   - 删除采用软删除方式
   - 删除后的数据不会在列表中显示

3. **状态控制**：
   - 支持启用/禁用状态切换
   - 禁用的类别可以在下拉选择中过滤

4. **排序规则**：
   - 按sort字段升序排列
   - sort相同时按id升序排列

5. **批量操作**：
   - 支持批量删除功能
   - 返回实际删除的数量

## 使用示例

### 创建类别
```bash
curl -X POST "http://localhost:8000/zb_cate" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "建筑工程",
    "sort": 1,
    "is_disable": 0
  }'
```

### 获取类别列表
```bash
curl -X GET "http://localhost:8000/zb_cate/list?page=1&page_size=10" \
  -H "Authorization: Bearer your_token"
```

### 获取所有类别
```bash
curl -X GET "http://localhost:8000/zb_cate/all?is_disable=0" \
  -H "Authorization: Bearer your_token"
```

### 更新类别状态
```bash
curl -X PUT "http://localhost:8000/zb_cate/1/status" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "is_disable": 1
  }'
```

### 批量删除类别
```bash
curl -X DELETE "http://localhost:8000/zb_cate/batch" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [1, 2, 3]
  }'
```
