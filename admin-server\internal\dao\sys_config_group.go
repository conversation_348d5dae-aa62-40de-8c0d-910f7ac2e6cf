// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-server/internal/dao/internal"
)

// sysConfigGroupDao is the data access object for the table sys_config_group.
// You can define custom methods on it to extend its functionality as needed.
type sysConfigGroupDao struct {
	*internal.SysConfigGroupDao
}

var (
	// SysConfigGroup is a globally accessible object for table sys_config_group operations.
	SysConfigGroup = sysConfigGroupDao{internal.NewSysConfigGroupDao()}
)

// Add your custom methods and functionality below.
