// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// ZbOrderCity is the golang structure for table zb_order_city.
type ZbOrderCity struct {
	Id       int64  `json:"id"       orm:"id"        description:"主键ID"`
	OrderId  int64  `json:"orderId"  orm:"order_id"  description:"订单id"`
	CityId   int64  `json:"cityId"   orm:"city_id"   description:"城市id"`
	CityName string `json:"cityName" orm:"city_name" description:"城市名称"`
}
