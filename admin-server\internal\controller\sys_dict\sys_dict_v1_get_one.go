package sys_dict

import (
	"context"

	v1 "admin-server/api/sys_dict/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetOne(ctx context.Context, req *v1.GetOneReq) (res *v1.GetOneRes, err error) {
	dict, err := service.SysDict().GetDictDetail(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	if dict == nil {
		return &v1.GetOneRes{}, nil
	}

	// 转换为API响应格式
	detail := &v1.DictDetail{
		ID:        dict.Id,
		GroupId:   dict.GroupId,
		Name:      dict.Name,
		Value:     dict.Value,
		Code:      dict.Code,
		Sort:      int(dict.Sort),
		IsDisable: int(dict.IsDisable),
		IsSystem:  int(dict.IsSystem),
		Remark:    dict.Remark,
		CreatedAt: dict.CreatedAt,
		UpdatedAt: dict.UpdatedAt,
	}

	return &v1.GetOneRes{
		Dict: detail,
	}, nil
}
