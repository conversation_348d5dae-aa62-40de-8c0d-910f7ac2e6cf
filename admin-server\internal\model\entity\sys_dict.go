// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"admin-server/internal/packed"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysDict is the golang structure for table sys_dict.
type SysDict struct {
	Id        int64       `json:"id"        orm:"id"         description:""`               //
	GroupId   int64       `json:"groupId"   orm:"group_id"   description:"字典分组id"`         // 字典分组id
	Name      string      `json:"name"      orm:"name"       description:"字典名称"`           // 字典名称
	Value     string      `json:"value"     orm:"value"      description:"字典值"`            // 字典值
	Code      string      `json:"code"      orm:"code"       description:"字典标识"`           // 字典标识
	Sort      int         `json:"sort"      orm:"sort"       description:"排序"`             // 排序
	IsDisable packed.Disable         `json:"isDisable" orm:"is_disable" description:"是否禁用: 0=否, 1=是"` // 是否禁用: 0=否, 1=是
	IsDelete  packed.IsDelete         `json:"isDelete"  orm:"is_delete"  description:"是否删除: 0=否, 1=是"` // 是否删除: 0=否, 1=是
	IsSystem  packed.System         `json:"isSystem"  orm:"is_system"  description:"是否系统保留 1是 0否"`   // 是否系统保留 1是 0否
	Remark    string      `json:"remark"    orm:"remark"     description:"备注"`             // 备注
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`           // 创建时间
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:"更新时间"`           // 更新时间
	DeletedAt *gtime.Time `json:"deletedAt" orm:"deleted_at" description:"删除时间"`           // 删除时间
}
