<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>搜索页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            -webkit-overflow-scrolling: touch;
        }
        .phone-container {
            overflow-y: auto;
            position: relative;
            background: white;
            min-height: 100vh;
            -webkit-overflow-scrolling: touch;
        }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .mini-capsule {
            position: absolute;
            top: 8px;
            right: 12px;
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .search-tag { transition: all 0.2s ease; }
        .search-tag:hover { transform: translateY(-1px); }

        /* 城市选择框样式 */
        #citySelect {
            background-image: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        #citySelect:focus {
            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
        }

        /* 统一搜索元素高度 */
        .search-element {
            height: 42px;
            line-height: 42px;
        }

        /* 城市选择按钮样式 */
        #citySelectBtn {
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #citySelectBtn:hover {
            background: rgba(255, 255, 255, 0.95);
        }

        #citySelectBtn:active {
            transform: scale(0.98);
        }

        /* 城市弹出对话框样式 */
        #cityModal {
            backdrop-filter: blur(4px);
        }

        #cityModal .bg-white {
            animation: slideUp 0.3s ease-out;
        }

        @keyframes slideUp {
            from {
                transform: translateY(100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* 城市选项样式 */
        .city-option {
            transition: all 0.2s ease;
        }

        .city-option:hover {
            background-color: #f3f4f6;
        }

        .city-option.selected {
            background-color: #f3e8ff;
            color: #7c3aed;
        }

        /* 加载动画 */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .animate-spin {
            animation: spin 1s linear infinite;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        
        <!-- 顶部搜索区域 -->
        <div class="gradient-bg px-4 pt-12 pb-6">
            <div class="flex items-center space-x-2">
                <!-- 城市选择按钮 -->
                <button id="citySelectBtn" onclick="showCityModal()" class="search-element bg-white/90 backdrop-blur-sm rounded-full px-3 text-sm border-0 focus:outline-none focus:ring-2 focus:ring-white/50 flex items-center space-x-1 min-w-0 flex-shrink-0">
                    <i class="fas fa-map-marker-alt text-gray-400 text-xs"></i>
                    <span id="citySelectText" class="text-gray-600 text-xs truncate max-w-12">全部</span>
                    <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                </button>

                <!-- 搜索框 -->
                <div class="flex-1 relative min-w-0">
                    <input type="text" id="searchInput" placeholder="搜索招标信息..."
                           class="search-element w-full bg-white/90 backdrop-blur-sm rounded-full px-4 pl-10 pr-10 text-sm placeholder-gray-500 border-0 focus:outline-none focus:ring-2 focus:ring-white/50"
                           value="">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm"></i>
                    <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" onclick="clearSearch()">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>

                <!-- 搜索按钮 -->
                <button class="search-element bg-white/20 backdrop-blur-sm rounded-full px-4 text-white text-sm flex-shrink-0" onclick="performSearch()">搜索</button>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="bg-white flex-1 overflow-y-auto">
            <!-- 搜索历史 -->
            <div class="px-4 py-4 border-b border-gray-100">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-sm font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-history text-gray-500 mr-2"></i>
                        搜索历史
                    </h3>
                    <button class="text-xs text-gray-500">清空</button>
                </div>
                <div class="flex flex-wrap gap-2">

                </div>
            </div>

            <!-- 热门搜索 -->
            <div class="px-4 py-4 border-b border-gray-100">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-sm font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-fire text-red-500 mr-2"></i>
                        热门搜索
                    </h3>
                    <button onclick="refreshHotKeywords()" class="text-xs text-gray-500 hover:text-gray-700">
                        <i class="fas fa-sync-alt mr-1"></i>刷新
                    </button>
                </div>

                <!-- 热门搜索加载状态 -->
                <div id="hotKeywordsLoading" class="text-center py-4">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-red-500"></div>
                    <p class="text-xs text-gray-600 mt-1">加载中...</p>
                </div>

                <!-- 热门搜索列表 -->
                <div id="hotKeywordsList" class="space-y-2 hidden">
                    <!-- 动态生成的热门搜索项 -->
                </div>

                <!-- 热门搜索加载失败 -->
                <div id="hotKeywordsError" class="text-center py-4 hidden">
                    <i class="fas fa-exclamation-triangle text-red-400 text-lg mb-2"></i>
                    <p class="text-xs text-gray-600 mb-2">热门搜索加载失败</p>
                    <button onclick="loadHotKeywords()" class="bg-red-500 text-white px-3 py-1 rounded text-xs">
                        重新加载
                    </button>
                </div>
            </div>

            <!-- 搜索结果 -->
            <div class="px-4 py-4" id="searchResultsSection">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-sm font-semibold text-gray-800" id="searchResultsTitle">搜索结果</h3>
                    <span class="text-xs text-gray-500" id="searchResultsCount">加载中...</span>
                </div>

                <!-- 加载状态 -->
                <div id="searchLoading" class="text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
                    <p class="text-sm text-gray-600 mt-2">正在加载...</p>
                </div>

                <!-- 搜索结果列表 -->
                <div id="searchResultsList" class="space-y-3 hidden">
                    <!-- 动态生成的搜索结果 -->
                </div>

                <!-- 空结果提示 -->
                <div id="searchEmpty" class="text-center py-8 hidden">
                    <i class="fas fa-search text-gray-300 text-4xl mb-4"></i>
                    <p class="text-gray-500 text-sm mb-2">没有找到相关信息</p>
                    <p class="text-gray-400 text-xs">请尝试其他关键词或调整搜索条件</p>
                </div>

                <!-- 错误提示 -->
                <div id="searchError" class="text-center py-8 hidden">
                    <i class="fas fa-exclamation-triangle text-red-400 text-4xl mb-4"></i>
                    <p class="text-gray-500 text-sm mb-2">加载失败</p>
                    <button onclick="retrySearch()" class="bg-purple-500 text-white px-4 py-2 rounded-lg text-sm">
                        重新加载
                    </button>
                </div>

                <!-- 加载更多 -->
                <div id="loadMoreSection" class="text-center py-6 hidden">
                    <button id="loadMoreBtn" onclick="loadMoreResults()" class="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-2 rounded-full text-sm font-medium">
                        查看更多结果
                    </button>
                </div>
            </div>
        </div>

        <!-- 城市选择弹出对话框 -->
        <div id="cityModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
            <div class="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl max-h-96 overflow-hidden">
                <!-- 对话框头部 -->
                <div class="flex items-center justify-between p-4 border-b border-gray-100">
                    <h3 class="text-lg font-semibold text-gray-800">选择城市</h3>
                    <button onclick="hideCityModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>

                <!-- 城市列表 -->
                <div class="overflow-y-auto max-h-80">
                    <!-- 全部城市选项 -->
                    <div class="p-4 border-b border-gray-50">
                        <button onclick="selectCity(null, '全部')" class="w-full text-left py-2 px-3 rounded-lg hover:bg-gray-50 flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-globe text-gray-400"></i>
                                <span class="text-gray-800">全部城市</span>
                            </div>
                            <i id="allCityCheck" class="fas fa-check text-purple-600"></i>
                        </button>
                    </div>

                    <!-- 城市加载状态 -->
                    <div id="cityModalLoading" class="p-8 text-center">
                        <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-purple-500"></div>
                        <p class="text-sm text-gray-600 mt-2">加载中...</p>
                    </div>

                    <!-- 城市列表容器 -->
                    <div id="cityModalList" class="hidden">
                        <!-- 城市选项将通过JavaScript动态生成 -->
                    </div>

                    <!-- 加载失败提示 -->
                    <div id="cityModalError" class="p-8 text-center hidden">
                        <i class="fas fa-exclamation-triangle text-red-500 text-2xl mb-2"></i>
                        <p class="text-sm text-gray-600 mb-3">城市列表加载失败</p>
                        <button onclick="reloadCitiesInModal()" class="bg-purple-500 text-white px-4 py-2 rounded-lg text-sm">
                            重新加载
                        </button>
                    </div>
                </div>
            </div>
        </div>

    </div>

<script>
// 全局变量
let allCities = []; // 存储所有城市数据
let selectedCity = null; // 存储选中的城市
let currentPage = 1; // 当前页码
let totalPages = 1; // 总页数
let isLoading = false; // 是否正在加载
let searchResults = []; // 搜索结果数据
let hotKeywords = []; // 热门搜索关键词
const PAGE_SIZE = 20; // 每页显示数量

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    loadCities();
    initSearchEvents();
    loadHotKeywords(); // 加载热门搜索
    loadInitialData(); // 加载初始数据
});

// 加载城市列表
async function loadCities() {
    try {
        console.log('正在加载城市列表...');

        const response = await fetch('/m/api/zb_city/tree');
        const result = await response.json();

        if (result.code === 0 && result.data && result.data.list) {
            allCities = result.data.list;
            renderCityModalList(allCities);
            console.log('城市列表加载成功:', allCities);
        } else {
            console.error('城市数据格式错误:', result);
            throw new Error(result.message || '城市数据加载失败');
        }
    } catch (error) {
        console.error('加载城市列表失败:', error);
        showCityModalError();
    }
}

// 渲染城市弹出对话框列表
function renderCityModalList(cities) {
    const loading = document.getElementById('cityModalLoading');
    const list = document.getElementById('cityModalList');
    const error = document.getElementById('cityModalError');

    // 隐藏加载状态和错误提示
    loading.style.display = 'none';
    error.style.display = 'none';

    // 清空现有列表
    list.innerHTML = '';

    // 添加城市选项
    cities.forEach(city => {
        const cityItem = document.createElement('div');
        cityItem.className = 'p-4 border-b border-gray-50';
        cityItem.innerHTML = `
            <button onclick="selectCity(${city.id}, '${city.name}')" class="city-option w-full text-left py-2 px-3 rounded-lg hover:bg-gray-50 flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-map-marker-alt text-gray-400"></i>
                    <span class="text-gray-800">${city.name}</span>
                </div>
                <i id="cityCheck_${city.id}" class="fas fa-check text-purple-600 hidden"></i>
            </button>
        `;
        list.appendChild(cityItem);
    });

    // 显示城市列表
    list.style.display = 'block';

    console.log('城市弹出列表渲染完成，共', cities.length, '个城市');
}

// 显示城市加载错误
function showCityModalError() {
    const loading = document.getElementById('cityModalLoading');
    const list = document.getElementById('cityModalList');
    const error = document.getElementById('cityModalError');

    loading.style.display = 'none';
    list.style.display = 'none';
    error.style.display = 'block';
}

// 显示城市选择弹出对话框
function showCityModal() {
    const modal = document.getElementById('cityModal');
    modal.classList.remove('hidden');

    // 如果城市列表还没有加载，显示加载状态
    if (allCities.length === 0) {
        document.getElementById('cityModalLoading').style.display = 'block';
        document.getElementById('cityModalList').style.display = 'none';
        document.getElementById('cityModalError').style.display = 'none';
        loadCities();
    }
}

// 隐藏城市选择弹出对话框
function hideCityModal() {
    const modal = document.getElementById('cityModal');
    modal.classList.add('hidden');
}

// 选择城市
function selectCity(cityId, cityName) {
    if (cityId) {
        selectedCity = { id: cityId, name: cityName };
        document.getElementById('citySelectText').textContent = cityName;

        // 更新选中状态
        updateCityCheckStatus(cityId);

        console.log('选中城市:', selectedCity);
    } else {
        selectedCity = null;
        document.getElementById('citySelectText').textContent = '全部';

        // 更新选中状态
        updateCityCheckStatus(null);

        console.log('选择全部城市');
    }

    // 隐藏弹出对话框
    hideCityModal();

    // 如果有搜索关键字，自动执行搜索
    const keyword = document.getElementById('searchInput').value.trim();
    if (keyword) {
        performSearch();
    }
}

// 更新城市选中状态显示
function updateCityCheckStatus(selectedCityId) {
    // 隐藏所有选中标记
    const allChecks = document.querySelectorAll('[id^="cityCheck_"], #allCityCheck');
    allChecks.forEach(check => {
        check.classList.add('hidden');
    });

    // 显示当前选中的标记
    if (selectedCityId) {
        const selectedCheck = document.getElementById(`cityCheck_${selectedCityId}`);
        if (selectedCheck) {
            selectedCheck.classList.remove('hidden');
        }
    } else {
        document.getElementById('allCityCheck').classList.remove('hidden');
    }
}

// 重新加载城市列表（在弹出对话框中）
function reloadCitiesInModal() {
    document.getElementById('cityModalLoading').style.display = 'block';
    document.getElementById('cityModalList').style.display = 'none';
    document.getElementById('cityModalError').style.display = 'none';
    loadCities();
}

// 初始化搜索相关事件
function initSearchEvents() {
    const searchInput = document.getElementById('searchInput');

    // 搜索框回车事件
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });

    // 搜索历史标签点击事件
    const searchTags = document.querySelectorAll('.search-tag');
    searchTags.forEach(tag => {
        tag.addEventListener('click', function() {
            const keyword = this.textContent.trim();
            searchInput.value = keyword;
            performSearch();
        });
    });

    // 点击弹出对话框背景关闭
    document.getElementById('cityModal').addEventListener('click', function(e) {
        if (e.target === this) {
            hideCityModal();
        }
    });
}

// 加载初始数据
async function loadInitialData() {
    console.log('加载初始数据...');
    currentPage = 1;
    await fetchSearchResults();
}

// 执行搜索
function performSearch() {
    const keyword = document.getElementById('searchInput').value.trim();
    const cityId = selectedCity ? selectedCity.id : '';
    const cityName = selectedCity ? selectedCity.name : null;

    console.log('执行搜索:', {
        keyword: keyword,
        cityId: cityId,
        cityName: cityName
    });

    // 重置页码
    currentPage = 1;
    searchResults = [];

    // 执行搜索
    fetchSearchResults(keyword, cityId);

    // 添加到搜索历史
    if (keyword) {
        addToSearchHistory(keyword);
        // 记录搜索统计
        trackSearchKeyword(keyword, cityId);
    }
}

// 获取搜索结果数据
async function fetchSearchResults(title = '', cityId = '') {
    if (isLoading) return;

    isLoading = true;
    showLoading();

    try {
        // 构建API URL
        const params = new URLSearchParams({
            page: currentPage,
            page_size: PAGE_SIZE
        });

        if (title) {
            params.append('title', title);
        }
        if (cityId) {
            params.append('city_id', cityId);
        }

        const apiUrl = `/m/api/zb_article/mobileList?${params.toString()}`;
        console.log('调用API:', apiUrl);

        const response = await fetch(apiUrl);
        const result = await response.json();

        if (result.code === 0 && result.data) {
            const data = result.data;

            // 更新分页信息
            totalPages = Math.ceil(data.total / PAGE_SIZE);

            if (currentPage === 1) {
                // 第一页，替换结果
                searchResults = data.list || [];
            } else {
                // 后续页，追加结果
                const newResults = data.list || [];
                searchResults = searchResults.concat(newResults);

                console.log('追加数据:', {
                    newResultsCount: newResults.length,
                    totalResultsCount: searchResults.length,
                    currentPage: currentPage,
                    totalPages: totalPages
                });
            }

            // 渲染搜索结果
            renderSearchResults(title, cityId);

            // 更新结果统计
            updateResultsCount(data.total, title, cityId);

            console.log('搜索结果加载成功:', {
                total: data.total,
                currentPage: currentPage,
                totalPages: totalPages,
                results: searchResults.length,
                hasMore: currentPage < totalPages
            });

        } else {
            console.error('API返回错误:', result);
            showError();
        }

    } catch (error) {
        console.error('搜索请求失败:', error);
        showError();
    } finally {
        isLoading = false;
        hideLoading();
    }
}

// 渲染搜索结果
function renderSearchResults(title = '', cityId = '') {
    const listContainer = document.getElementById('searchResultsList');
    const emptyContainer = document.getElementById('searchEmpty');
    const loadMoreSection = document.getElementById('loadMoreSection');

    if (searchResults.length === 0) {
        // 显示空结果
        listContainer.classList.add('hidden');
        emptyContainer.classList.remove('hidden');
        loadMoreSection.classList.add('hidden');
        return;
    }

    // 隐藏空结果提示
    emptyContainer.classList.add('hidden');
    listContainer.classList.remove('hidden');

    // 清空现有内容（仅第一页）
    if (currentPage === 1) {
        listContainer.innerHTML = '';
        // 渲染所有结果
        searchResults.forEach(article => {
            const articleElement = createArticleElement(article, title);
            listContainer.appendChild(articleElement);
        });
    } else {
        // 追加新数据（只渲染新获取的数据）
        const startIndex = (currentPage - 1) * PAGE_SIZE;
        const newResults = searchResults.slice(startIndex);

        newResults.forEach(article => {
            const articleElement = createArticleElement(article, title);
            listContainer.appendChild(articleElement);
        });
    }

    // 显示/隐藏加载更多按钮
    console.log('加载更多按钮状态检查:', {
        currentPage: currentPage,
        totalPages: totalPages,
        hasMore: currentPage < totalPages,
        searchResultsLength: searchResults.length
    });

    if (currentPage < totalPages) {
        loadMoreSection.classList.remove('hidden');
        console.log('显示加载更多按钮');
    } else {
        loadMoreSection.classList.add('hidden');
        console.log('隐藏加载更多按钮');
    }
}

// 创建文章元素
function createArticleElement(article, searchKeyword = '') {
    const articleDiv = document.createElement('div');
    articleDiv.className = 'bg-white rounded-xl p-4 border border-gray-100 shadow-sm cursor-pointer hover:shadow-md transition-shadow';
    articleDiv.onclick = () => goToDetail(article.id);

    // 处理标题高亮
    let highlightedTitle = article.title || '无标题';
    if (searchKeyword && searchKeyword.trim()) {
        const keyword = searchKeyword.trim();
        const regex = new RegExp(`(${keyword})`, 'gi');
        highlightedTitle = highlightedTitle.replace(regex, '<span class="bg-yellow-200">$1</span>');
    }

    // 格式化时间
    const timeAgo = formatTimeAgo(article.created_at);

    // 获取分类颜色
    const categoryColor = getCategoryColor(article.cate_name);

    articleDiv.innerHTML = `
        <div class="flex items-start justify-between mb-2">
            <div class="flex items-center space-x-2">
                <span class="bg-${categoryColor}-100 text-${categoryColor}-600 px-2 py-1 rounded-md text-xs font-medium">
                    ${article.cate_name || '未分类'}
                </span>
                <div class="flex items-center">
                    <i class="fas fa-map-marker-alt text-red-500 text-xs mr-1"></i>
                    <span class="bg-red-50 text-red-700 px-2 py-1 rounded-md text-xs font-medium">${article.city_name || '未知地区'}</span>
                </div>
            </div>
            <span class="text-xs text-gray-400">${timeAgo}</span>
        </div>
        <h4 class="text-sm font-semibold text-gray-800 mb-2 line-clamp-2">
            ${highlightedTitle}
        </h4>
        <div class="space-y-1 mb-3">
            <div class="flex items-center text-xs text-gray-600">
                <i class="fas fa-eye w-4"></i>
                <span>浏览 ${article.viewCount || 0} 次</span>
            </div>
        </div>
        <div class="flex items-center justify-between">
            <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs">${article.author || '招标平台'}</span>
            <button class="text-purple-600 text-xs font-medium">查看详情</button>
        </div>
    `;

    return articleDiv;
}

// 获取分类颜色
function getCategoryColor(cateName) {
    const colorMap = {
        '招标公告': 'blue',
        '中标公告': 'green',
        '政府采购': 'purple',
        '工程建设': 'orange',
        '其他': 'gray'
    };

    for (const [key, color] of Object.entries(colorMap)) {
        if (cateName && cateName.includes(key)) {
            return color;
        }
    }

    return 'blue'; // 默认颜色
}

// 格式化时间
function formatTimeAgo(dateString) {
    if (!dateString) return '未知时间';

    const now = new Date();
    const date = new Date(dateString);
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return '刚刚发布';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;

    return date.toLocaleDateString('zh-CN');
}

// 跳转到详情页
function goToDetail(articleId) {
    window.location.href = `/m/detail/${articleId}`;
}

// 显示加载状态
function showLoading() {
    document.getElementById('searchLoading').classList.remove('hidden');
    document.getElementById('searchResultsList').classList.add('hidden');
    document.getElementById('searchEmpty').classList.add('hidden');
    document.getElementById('searchError').classList.add('hidden');
}

// 隐藏加载状态
function hideLoading() {
    document.getElementById('searchLoading').classList.add('hidden');
}

// 显示错误状态
function showError() {
    document.getElementById('searchLoading').classList.add('hidden');
    document.getElementById('searchResultsList').classList.add('hidden');
    document.getElementById('searchEmpty').classList.add('hidden');
    document.getElementById('searchError').classList.remove('hidden');
}

// 更新结果统计
function updateResultsCount(total, title = '', cityId = '') {
    const countElement = document.getElementById('searchResultsCount');
    const titleElement = document.getElementById('searchResultsTitle');

    if (countElement) {
        countElement.textContent = `找到 ${total} 条相关信息`;
    }

    if (titleElement) {
        let searchInfo = '搜索结果';
        const cityName = selectedCity ? selectedCity.name : null;

        if (title && cityName) {
            searchInfo = `"${title}" 在 ${cityName} 的搜索结果`;
        } else if (title) {
            searchInfo = `"${title}" 的搜索结果`;
        } else if (cityName) {
            searchInfo = `${cityName} 的招标信息`;
        }

        titleElement.textContent = searchInfo;
    }
}

// 加载更多结果
function loadMoreResults() {
    if (currentPage < totalPages && !isLoading) {
        currentPage++;
        const keyword = document.getElementById('searchInput').value.trim();
        const cityId = selectedCity ? selectedCity.id : '';
        fetchSearchResults(keyword, cityId);
    }
}

// 重试搜索
function retrySearch() {
    const keyword = document.getElementById('searchInput').value.trim();
    const cityId = selectedCity ? selectedCity.id : '';
    fetchSearchResults(keyword, cityId);
}

// 加载热门搜索关键词
async function loadHotKeywords() {
    try {
        showHotKeywordsLoading();
        console.log('正在加载热门搜索...');

        // 尝试从API获取热门搜索（限制5个）
        const response = await fetch('/m/api/search/hot-keywords?limit=5');
        const result = await response.json();

        if (result.code === 0 && result.data && result.data.keywords) {
            hotKeywords = result.data.keywords;
            renderHotKeywords(hotKeywords);
            console.log('热门搜索加载成功:', hotKeywords);
        } else {
            throw new Error('API返回数据格式错误');
        }

    } catch (error) {
        console.warn('API加载失败，使用默认热门搜索:', error);
        // 使用默认的热门搜索数据
        hotKeywords = getDefaultHotKeywords();
        renderHotKeywords(hotKeywords);
    }
}

// 获取默认热门搜索数据（API失败时的备用数据）
function getDefaultHotKeywords() {
    return [
        { keyword: "智慧城市建设", search_count: 1250, trend: "up", is_hot: true },
        { keyword: "医院设备采购", search_count: 980, trend: "up", is_new: true },
        { keyword: "学校装修工程", search_count: 856, trend: "stable" },
        { keyword: "道路建设项目", search_count: 742, trend: "down" },
        { keyword: "环保设备招标", search_count: 698, trend: "up" }
    ];
}

// 渲染热门搜索列表
function renderHotKeywords(keywords) {
    const loading = document.getElementById('hotKeywordsLoading');
    const list = document.getElementById('hotKeywordsList');
    const error = document.getElementById('hotKeywordsError');

    // 隐藏加载状态和错误提示
    loading.classList.add('hidden');
    error.classList.add('hidden');

    // 清空现有列表
    list.innerHTML = '';

    // 渲染热门搜索项（只显示前5个）
    keywords.slice(0, 5).forEach((item, index) => {
        const keywordDiv = document.createElement('div');
        keywordDiv.className = 'flex items-center justify-between cursor-pointer hover:bg-gray-50 rounded-lg p-2 -mx-2 transition-colors';
        keywordDiv.onclick = () => searchByKeyword(item.keyword);

        // 获取排名颜色
        const rankColor = getRankColor(index + 1);

        // 获取趋势图标
        const trendIcon = getTrendIcon(item.trend);

        // 获取标签
        const tags = getKeywordTags(item);

        keywordDiv.innerHTML = `
            <div class="flex items-center space-x-3">
                <span class="bg-${rankColor}-500 text-white text-xs px-2 py-1 rounded font-bold min-w-[20px] text-center">
                    ${index + 1}
                </span>
                <span class="text-sm text-gray-800 font-medium">${item.keyword}</span>
                ${trendIcon}
            </div>
            <div class="flex items-center space-x-1">
                ${tags}
                <span class="text-xs text-gray-400">${formatSearchCount(item.search_count)}</span>
            </div>
        `;

        list.appendChild(keywordDiv);
    });

    // 显示列表
    list.classList.remove('hidden');

    console.log('热门搜索渲染完成，共', keywords.length, '个关键词');
}

// 获取排名颜色
function getRankColor(rank) {
    if (rank === 1) return 'red';
    if (rank === 2) return 'orange';
    if (rank === 3) return 'yellow';
    return 'gray';
}

// 获取趋势图标
function getTrendIcon(trend) {
    switch (trend) {
        case 'up':
            return '<i class="fas fa-arrow-up text-red-500 text-xs"></i>';
        case 'down':
            return '<i class="fas fa-arrow-down text-green-500 text-xs"></i>';
        default:
            return '<i class="fas fa-minus text-gray-400 text-xs"></i>';
    }
}

// 获取关键词标签
function getKeywordTags(item) {
    let tags = '';
    if (item.is_hot) {
        tags += '<span class="text-xs text-red-500 bg-red-50 px-1 rounded">热</span>';
    }
    if (item.is_new) {
        tags += '<span class="text-xs text-orange-500 bg-orange-50 px-1 rounded">新</span>';
    }
    return tags;
}

// 格式化搜索次数
function formatSearchCount(count) {
    if (!count) return '';
    if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'k';
    }
    return count.toString();
}

// 通过关键词搜索
function searchByKeyword(keyword) {
    document.getElementById('searchInput').value = keyword;
    performSearch();

    // 记录点击统计
    trackHotKeywordClick(keyword);
}

// 记录热门搜索点击
function trackHotKeywordClick(keyword) {
    console.log('热门搜索点击:', keyword);

    // 发送点击统计到后端
    fetch('/m/api/search/track-click', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ keyword: keyword })
    }).catch(error => {
        console.error('点击统计发送失败:', error);
    });
}

// 记录搜索统计
function trackSearchKeyword(keyword, cityId = '') {
    console.log('搜索统计:', keyword, cityId);

    // 发送搜索统计到后端
    const data = { keyword: keyword };
    if (cityId) {
        data.city_id = parseInt(cityId);
    }

    fetch('/m/api/search/track-search', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    }).catch(error => {
        console.error('搜索统计发送失败:', error);
    });
}

// 显示热门搜索加载状态
function showHotKeywordsLoading() {
    document.getElementById('hotKeywordsLoading').classList.remove('hidden');
    document.getElementById('hotKeywordsList').classList.add('hidden');
    document.getElementById('hotKeywordsError').classList.add('hidden');
}

// 显示热门搜索错误状态
function showHotKeywordsError() {
    document.getElementById('hotKeywordsLoading').classList.add('hidden');
    document.getElementById('hotKeywordsList').classList.add('hidden');
    document.getElementById('hotKeywordsError').classList.remove('hidden');
}

// 刷新热门搜索
function refreshHotKeywords() {
    console.log('刷新热门搜索');
    loadHotKeywords();
}

// 添加到搜索历史
function addToSearchHistory(keyword) {
    const historyContainer = document.querySelector('.flex.flex-wrap.gap-2');
    if (historyContainer && keyword) {
        // 检查是否已存在
        const existingTags = Array.from(historyContainer.children);
        const exists = existingTags.some(tag => tag.textContent.trim() === keyword);

        if (!exists) {
            const newTag = document.createElement('span');
            newTag.className = 'search-tag bg-gray-100 text-gray-700 px-3 py-2 rounded-full text-xs cursor-pointer';
            newTag.textContent = keyword;
            newTag.addEventListener('click', function() {
                document.getElementById('searchInput').value = keyword;
                performSearch();
            });

            // 插入到第一个位置
            historyContainer.insertBefore(newTag, historyContainer.firstChild);

            // 限制历史记录数量
            if (historyContainer.children.length > 8) {
                historyContainer.removeChild(historyContainer.lastChild);
            }
        }
    }
}

// 清空搜索框
function clearSearch() {
    document.getElementById('searchInput').value = '';
    selectedCity = null;
    document.getElementById('citySelectText').textContent = '全部';
    updateCityCheckStatus(null);
    console.log('搜索框已清空');
}

// 获取当前搜索条件
function getCurrentSearchConditions() {
    const keyword = document.getElementById('searchInput').value.trim();
    const cityId = selectedCity ? selectedCity.id : null;

    return {
        keyword: keyword,
        cityId: cityId,
        cityName: selectedCity ? selectedCity.name : null
    };
}

// 设置搜索条件
function setSearchConditions(keyword, cityId) {
    if (keyword) {
        document.getElementById('searchInput').value = keyword;
    }

    if (cityId) {
        selectedCity = allCities.find(city => city.id == cityId);
        if (selectedCity) {
            document.getElementById('citySelectText').textContent = selectedCity.name;
            updateCityCheckStatus(cityId);
        }
    } else {
        selectedCity = null;
        document.getElementById('citySelectText').textContent = '全部';
        updateCityCheckStatus(null);
    }
}
</script>
</body>
</html>
