// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ZbUser is the golang structure of table zb_user for DAO operations like Where/Data.
type ZbUser struct {
	g.Meta          `orm:"table:zb_user, do:true"`
	Id              interface{} //
	Nickname        interface{} // 昵称
	Avatar          interface{} // 头像
	Openid          interface{} // 会员微信openid
	IsDisable       interface{} // 是否禁用: 0=否, 1=是
	IsDelete        interface{} // 是否删除: 0=否, 1=是
	EffectiveStart  *gtime.Time // 有效开始日期
	EffectiveEnd    *gtime.Time // 有效结束日期
	CreatedAt       *gtime.Time // 注册时间
	UpdatedAt       *gtime.Time // 更新时间
	DeletedAt       *gtime.Time // 删除时间
	EffectiveStatus interface{} // 是否有效 1有效 0无效  根据会员购买的套餐先计算有效开始日期和结束日期，然后在计算有效状态
}
