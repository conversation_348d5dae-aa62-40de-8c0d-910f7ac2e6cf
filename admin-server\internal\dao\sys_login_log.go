// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-server/internal/dao/internal"
)

// sysLoginLogDao is the data access object for the table sys_login_log.
// You can define custom methods on it to extend its functionality as needed.
type sysLoginLogDao struct {
	*internal.SysLoginLogDao
}

var (
	// SysLoginLog is a globally accessible object for table sys_login_log operations.
	SysLoginLog = sysLoginLogDao{internal.NewSysLoginLogDao()}
)

// Add your custom methods and functionality below.
