package v1

import (
	"admin-server/internal/packed"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// CreateReq 创建管理员请求体
type CreateReq struct {
	g.Meta    `path:"/sys_admin/create" method:"post" tags:"SysAdmin" summary:"创建管理员" permission:"system:user:add"`
	Username  string          `p:"username" v:"required|length:3,30#用户名不能为空|用户名长度为3-30位" dc:"账号"`
	Password  string          `p:"password" v:"required|length:6,30#密码不能为空|密码长度为6-30位" dc:"密码"`
	Nickname  string          `p:"nickname" v:"required|length:2,20#昵称不能为空|昵称长度为2-20位" dc:"昵称"`
	IsSuper   *packed.Super   `p:"is_super" v:"in:0,1" dc:"是否是超级管理员"`
	IsDisable *packed.Disable `p:"is_disable" v:"in:0,1" dc:"是否禁用"`
	RoleIds   []int64         `p:"role_ids" dc:"角色ID列表"`
}

type CreateRes struct {
	ID int64 `json:"id" dc:"管理员ID"`
}

// DeleteReq 删除管理员请求体
type DeleteReq struct {
	g.Meta `path:"/sys_admin/{id}" method:"delete" tags:"SysAdmin" summary:"删除管理员" permission:"system:user:delete"`
	ID     int64 `p:"id" v:"required#请选择需要删除的记录" dc:"管理员ID"`
}
type DeleteRes struct{}

// UpdateReq 更新管理员请求体
type UpdateReq struct {
	g.Meta    `path:"/sys_admin/{id}" method:"put" tags:"SysAdmin" summary:"更新管理员信息" permission:"system:user:edit"`
	ID        int64           `p:"id" v:"required#请选择需要更新的记录" dc:"管理员ID"`
	Username  string          `p:"username" v:"required|length:3,30#用户名不能为空|用户名长度为3-30位" dc:"账号"`
	Password  string          `p:"password" v:"length:6,30#密码长度为6-30位" dc:"密码（可选，传入则修改密码）"`
	Nickname  string          `p:"nickname" v:"required|length:2,20#昵称不能为空|昵称长度为2-20位" dc:"昵称"`
	IsSuper   *packed.Super   `p:"is_super" v:"in:0,1" dc:"是否是超级管理员"`
	IsDisable *packed.Disable `p:"is_disable" v:"in:0,1" dc:"是否禁用"`
	RoleIds   []int64         `p:"role_ids" dc:"角色ID列表"`
}
type UpdateRes struct{}

// ChangePasswordReq 修改密码请求体
type ChangePasswordReq struct {
	g.Meta      `path:"/sys_admin/{id}/password" method:"put" tags:"SysAdmin" summary:"修改管理员密码"`
	ID          int64  `p:"id" v:"required#请选择需要修改密码的管理员" dc:"管理员ID"`
	OldPassword string `p:"old_password" v:"required#原密码不能为空" dc:"原密码"`
	NewPassword string `p:"new_password" v:"required|length:6,30#新密码不能为空|新密码长度为6-30位" dc:"新密码"`
}
type ChangePasswordRes struct{}

// GetListReq 获取管理员列表请求体
type GetListReq struct {
	g.Meta    `path:"/sys_admin/list" tags:"SysAdmin" method:"get" summary:"获取管理员列表" permission:"system:user:list"`
	Page      int             `p:"page" d:"1" v:"min:1#页码最小为1" dc:"页码"`
	PageSize  int             `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间" dc:"每页数量"`
	Username  string          `p:"username" dc:"账号（模糊搜索）"`
	Nickname  string          `p:"nickname" dc:"昵称（模糊搜索）"`
	IsSuper   *packed.Super   `p:"is_super" v:"in:0,1" dc:"是否是超级管理员"`
	IsDisable *packed.Disable `p:"is_disable" v:"in:0,1" dc:"是否禁用"`
}

// RoleInfo 角色信息
type RoleInfo struct {
	ID   int64  `json:"id" dc:"角色ID"`
	Name string `json:"name" dc:"角色名称"`
}

// AdminInfo 管理员信息（不包含密码）
type AdminInfo struct {
	ID            int64          `json:"id" dc:"管理员ID"`
	Username      string         `json:"username" dc:"账号"`
	Nickname      string         `json:"nickname" dc:"昵称"`
	IsSuper       packed.Super   `json:"is_super" dc:"是否是超级管理员"`
	IsDisable     packed.Disable `json:"is_disable" dc:"是否禁用"`
	LastLoginIp   string         `json:"last_login_ip" dc:"最后登录IP"`
	LastLoginTime *gtime.Time    `json:"last_login_time" dc:"最后登录时间"`
	CreatedAt     *gtime.Time    `json:"created_at" dc:"创建时间"`
	UpdatedAt     *gtime.Time    `json:"updated_at" dc:"更新时间"`
	Roles         []*RoleInfo    `json:"roles" dc:"角色列表"`
}

type GetListRes struct {
	List     []*AdminInfo `json:"list" dc:"管理员列表"`
	Total    int          `json:"total" dc:"总数"`
	Page     int          `json:"page" dc:"当前页码"`
	PageSize int          `json:"page_size" dc:"每页数量"`
}

// GetOneReq 获取单个管理员信息请求体
type GetOneReq struct {
	g.Meta `path:"/sys_admin/{id}" tags:"SysAdmin" method:"get" summary:"获取单个管理员信息"`
	ID     int64 `p:"id" v:"required#请选择需要查询的记录" dc:"管理员ID"`
}
type GetOneRes struct {
	*AdminInfo `dc:"管理员信息"`
}

// DisableReq 禁用管理员请求体
type DisableReq struct {
	g.Meta `path:"/sys_admin/{id}/disable" method:"put" tags:"SysAdmin" summary:"禁用管理员"`
	ID     int64 `p:"id" v:"required#请选择需要禁用的管理员" dc:"管理员ID"`
}
type DisableRes struct{}

// EnableReq 启用管理员请求体
type EnableReq struct {
	g.Meta `path:"/sys_admin/{id}/enable" method:"put" tags:"SysAdmin" summary:"启用管理员"`
	ID     int64 `p:"id" v:"required#请选择需要启用的管理员" dc:"管理员ID"`
}
type EnableRes struct{}

// AssignRolesReq 分配角色请求体
type AssignRolesReq struct {
	g.Meta  `path:"/sys_admin/{id}/roles" method:"put" tags:"SysAdmin" summary:"分配管理员角色" permission:"system:user:setRoles"`
	ID      int64   `p:"id" v:"required#请选择需要分配角色的管理员" dc:"管理员ID"`
	RoleIds []int64 `p:"role_ids" dc:"角色ID列表"`
}
type AssignRolesRes struct{}

// GetRolesReq 获取管理员角色请求体
type GetRolesReq struct {
	g.Meta `path:"/sys_admin/{id}/roles" method:"get" tags:"SysAdmin" summary:"获取管理员角色列表"`
	ID     int64 `p:"id" v:"required#请选择需要查询角色的管理员" dc:"管理员ID"`
}
type GetRolesRes struct {
	Roles []*RoleInfo `json:"roles" dc:"角色列表"`
}
