// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ZbArticle is the golang structure for table zb_article.
type ZbArticle struct {
	Id             int         `json:"id"             orm:"id"              description:""`               //
	CityId         int         `json:"cityId"         orm:"city_id"         description:"开通城市id"`         // 开通城市id
	CateId         int         `json:"cateId"         orm:"cate_id"         description:"招标类别id"`         // 招标类别id
	Title          string      `json:"title"          orm:"title"           description:"标题"`             // 标题
	Intro          string      `json:"intro"          orm:"intro"           description:"内容简介"`           // 内容简介
	FullContent    string      `json:"fullContent"    orm:"full_content"    description:"完整版内容"`          // 完整版内容
	ShieidContent  string      `json:"shieidContent"  orm:"shieid_content"  description:"屏蔽内容"`           // 屏蔽内容
	ViewCount      int         `json:"viewCount"      orm:"view_count"      description:"浏览次数"`           // 浏览次数
	SeoTitle       string      `json:"seoTitle"       orm:"seo_title"       description:"SEO标题"`          // SEO标题
	SeoKeywords    string      `json:"seoKeywords"    orm:"seo_keywords"    description:"SEO关键词"`         // SEO关键词
	SeoDescription string      `json:"seoDescription" orm:"seo_description" description:"SEO描述"`          // SEO描述
	Pic            string      `json:"pic"            orm:"pic"             description:"缩略图"`            // 缩略图
	Uid            int         `json:"uid"            orm:"uid"             description:"发布者id"`          // 发布者id
	Author         string      `json:"author"         orm:"author"          description:"作者"`             // 作者
	Ip             string      `json:"ip"             orm:"ip"              description:"发布ip"`           // 发布ip
	IsDisable      int         `json:"isDisable"      orm:"is_disable"      description:"是否禁用: 0=否, 1=是"` // 是否禁用: 0=否, 1=是
	IsDelete       int         `json:"isDelete"       orm:"is_delete"       description:"是否删除: 0=否, 1=是"` // 是否删除: 0=否, 1=是
	CreatedAt      *gtime.Time `json:"createdAt"      orm:"created_at"      description:"创建日期"`           // 创建日期
	UpdatedAt      *gtime.Time `json:"updatedAt"      orm:"updated_at"      description:"更新日期"`           // 更新日期
	DeletedAt      *gtime.Time `json:"deletedAt"      orm:"deleted_at"      description:"删除时间"`           // 删除时间
}
