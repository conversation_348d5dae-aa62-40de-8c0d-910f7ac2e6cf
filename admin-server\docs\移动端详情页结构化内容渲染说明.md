# 移动端详情页结构化内容渲染说明

## 功能概述

移动端详情页面现在支持渲染新的结构化数据格式，其中 `content` 字段包含JSON格式的结构化内容，支持多个章节和字段的展示。

## 数据格式

### 完整数据结构
```javascript
{
    "author": "招标平台",
    "cate_name": "中标公告", 
    "city_name": "北京",
    "content": "[{\"order\": 1, \"title\": \"基础信息\", \"fields\": [...]}]", // JSON字符串
    "created_at": "2025-07-23 15:43:48",
    "id": 6,
    "pic": "",
    "seo_description": "",
    "seo_keywords": "",
    "seo_title": "",
    "title": "项目标题",
    "viewCount": 0
}
```

### Content字段结构
`content` 字段是一个JSON字符串，解析后的结构为：
```javascript
[
    {
        "order": 1,
        "title": "基础信息",
        "fields": [
            {
                "label": "项目名称：",
                "order": 1,
                "value": "项目名称内容",
                "visibility": "show"
            },
            {
                "label": "预算金额：",
                "order": 5,
                "value": "[已屏蔽]",
                "visibility": "hide"
            }
        ]
    },
    {
        "order": 2,
        "title": "要求",
        "fields": [
            {
                "label": "投标要求：",
                "order": 1,
                "value": "具体要求内容",
                "visibility": "show"
            }
        ]
    }
]
```

## 渲染逻辑

### 1. 数据解析
```javascript
function renderArticleData() {
    // 解析content字段
    let parsedContent = null;
    if (articleContent.content) {
        try {
            parsedContent = JSON.parse(articleContent.content);
        } catch (error) {
            console.error('解析content字段失败:', error);
        }
    }
    
    // 传递解析后的内容给各个渲染函数
    renderBasicInfo(parsedContent);
    renderProjectOverview(parsedContent);
    renderDetailContent(parsedContent);
}
```

### 2. 标题区域渲染
- 使用 `title` 字段显示文章标题
- 使用 `viewCount` 字段显示浏览次数（注意不是view_count）
- 使用 `author` 字段显示作者
- 使用 `cate_name` 和 `city_name` 生成标签

### 3. 基本信息渲染
```javascript
function renderBasicInfo(parsedContent) {
    // 1. 先显示文章基本信息（ID、分类、城市等）
    
    // 2. 然后显示结构化内容中的"基础信息"部分
    const basicInfoSection = parsedContent.find(section => section.title === '基础信息');
    if (basicInfoSection && basicInfoSection.fields) {
        basicInfoSection.fields.forEach(field => {
            if (field.visibility === 'show' && field.value && field.value.trim()) {
                // 渲染字段
            }
        });
    }
}
```

### 4. 项目概况渲染
从"基础信息"章节中查找"项目内容"字段作为项目概况：
```javascript
function renderProjectOverview(parsedContent) {
    const projectContentField = basicInfoSection.fields.find(field => 
        field.label.includes('项目内容') && field.visibility === 'show'
    );
    
    if (projectContentField && projectContentField.value) {
        // 渲染项目内容
    }
}
```

### 5. 详细内容渲染
按章节顺序渲染所有结构化内容：
```javascript
function renderDetailContent(parsedContent) {
    parsedContent.forEach((section, sectionIndex) => {
        // 创建章节标题
        const sectionTitle = document.createElement('h4');
        
        // 根据章节类型设置图标
        let iconClass = 'fas fa-info-circle text-blue-500';
        if (section.title.includes('要求')) {
            iconClass = 'fas fa-clipboard-check text-orange-500';
        }
        
        // 渲染章节字段
        section.fields.forEach((field, fieldIndex) => {
            if (field.visibility === 'show') {
                // 使用不同颜色的卡片样式
                const colors = ['blue', 'green', 'purple', 'orange', 'red', 'indigo'];
                const color = colors[fieldIndex % colors.length];
                
                // 处理换行和段落格式
                if (value.includes('\n\n')) {
                    // 按段落分割
                } else if (value.includes('\n')) {
                    // 按行分割
                }
            }
        });
    });
}
```

## 样式设计

### 1. 章节样式
- **基础信息**: 蓝色图标 `fas fa-info-circle text-blue-500`
- **要求**: 橙色图标 `fas fa-clipboard-check text-orange-500`
- **联系方式**: 绿色图标 `fas fa-phone text-green-500`

### 2. 字段卡片样式
使用不同颜色的卡片来区分不同字段：
```css
.bg-blue-50.border-l-4.border-blue-400    /* 蓝色卡片 */
.bg-green-50.border-l-4.border-green-400  /* 绿色卡片 */
.bg-purple-50.border-l-4.border-purple-400 /* 紫色卡片 */
.bg-orange-50.border-l-4.border-orange-400 /* 橙色卡片 */
.bg-red-50.border-l-4.border-red-400      /* 红色卡片 */
.bg-indigo-50.border-l-4.border-indigo-400 /* 靛蓝卡片 */
```

### 3. 内容格式处理
- **段落分割**: 双换行符 `\n\n` 分割段落
- **行分割**: 单换行符 `\n` 分割行
- **单行内容**: 直接显示

## 特殊处理

### 1. 隐藏字段处理
```javascript
if (field.visibility === 'hide') {
    // 不渲染隐藏字段，如预算金额等敏感信息
    return;
}
```

### 2. 空值处理
```javascript
if (field.value && field.value.trim()) {
    // 只渲染有内容的字段
}
```

### 3. 分享描述生成
```javascript
function getShareDescription() {
    // 1. 尝试从项目内容字段获取描述
    // 2. 截取前100个字符
    // 3. 如果没有，使用默认格式：分类名 - 城市名
}
```

## 兼容性

### 1. 向后兼容
如果 `content` 字段不是JSON格式，会fallback到原来的处理方式：
```javascript
} else {
    // 如果没有结构化内容，显示原始内容
    if (articleContent.content) {
        const rawContentDiv = document.createElement('div');
        rawContentDiv.textContent = articleContent.content;
        contentElement.appendChild(rawContentDiv);
    }
}
```

### 2. 错误处理
```javascript
try {
    parsedContent = JSON.parse(articleContent.content);
} catch (error) {
    console.error('解析content字段失败:', error);
    // 继续使用原始内容
}
```

## 测试用例

### 1. 完整数据测试
- 包含多个章节的完整结构化数据
- 验证所有字段正确渲染
- 检查样式和布局

### 2. 部分数据测试
- 只有基础信息章节
- 某些字段为空或隐藏
- 验证graceful degradation

### 3. 异常数据测试
- content字段不是有效JSON
- content字段为空
- 字段结构不完整

### 4. 样式测试
- 不同章节的图标和颜色
- 字段卡片的颜色循环
- 响应式布局在移动端的表现

## 注意事项

1. **数据安全**: 只渲染 `visibility: "show"` 的字段
2. **性能考虑**: 大量字段时的渲染性能
3. **用户体验**: 长内容的换行和格式化
4. **错误处理**: JSON解析失败时的fallback机制
5. **样式一致性**: 保持与原有设计的一致性

---

**功能状态**: ✅ 已完成  
**测试状态**: 待测试  
**文档版本**: v1.0  
**最后更新**: 2025-01-23
