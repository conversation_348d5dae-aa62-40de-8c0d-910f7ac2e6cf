package search

import (
	"context"
	v1 "admin-server/api/search/v1"
	"github.com/gogf/gf/v2/os/gcron"
	"github.com/gogf/gf/v2/util/gconv"
)

func (c *ControllerV1) GetCronStatus(ctx context.Context, req *v1.GetCronStatusReq) (res *v1.GetCronStatusRes, err error) {
	// 获取所有定时任务
	entries := gcron.Entries()
	
	var tasks []string
	for i, entry := range entries {
		tasks = append(tasks, gconv.String(i+1)+": "+gconv.String(entry))
	}

	return &v1.GetCronStatusRes{
		TaskCount: len(entries),
		Tasks:     tasks,
	}, nil
}
