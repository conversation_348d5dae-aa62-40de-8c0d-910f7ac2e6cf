# 会员管理API接口文档（标准架构版）

## 接口概述

会员管理模块基于GoFrame标准架构实现，采用Service-Logic-Controller-API四层架构，提供完整的会员管理功能。所有接口都需要JWT认证和相应的权限验证。

**最新优化**: 基于实际数据库表结构进行了全面优化，支持动态VIP状态计算，新增会员统计功能，提供更准确的VIP管理体验。

## 架构说明

### 🏗️ 架构层次
1. **Service层** (`internal/service/zb_user.go`) - 会员服务接口定义
2. **Logic层** (`internal/logic/zbUser/zb_user.go`) - 会员业务逻辑实现
3. **Controller层** (`internal/controller/zb_user/`) - 会员控制器（自动生成风格）
4. **API层** (`api/zb_user/v1/zb_user.go`) - 会员API结构体定义

### 📁 文件结构
```
admin-server/
├── api/
│   └── zb_user/
│       ├── zb_user.go              # API接口定义
│       └── v1/
│           └── zb_user.go          # API结构体定义
├── internal/
│   ├── service/
│   │   └── zb_user.go              # 服务接口定义
│   ├── logic/
│   │   └── zbUser/
│   │       └── zb_user.go          # 业务逻辑实现
│   └── controller/
│       └── zb_user/
│           ├── zb_user_new.go      # 控制器构造函数
│           ├── zb_user_v1_get_list.go
│           ├── zb_user_v1_get_one.go
│           ├── zb_user_v1_update.go
│           ├── zb_user_v1_delete.go
│           ├── zb_user_v1_set_status.go
│           ├── zb_user_v1_update_vip_period.go
│           ├── zb_user_v1_get_vip_list.go
│           ├── zb_user_v1_get_expired_vip.go
│           ├── zb_user_v1_get_by_openid.go
│           └── zb_user_v1_get_stats.go      # 会员统计接口
└── docs/
    ├── 会员管理API接口文档_标准架构版.md
    └── 会员API优化总结.md
```

## 基础信息

- **基础URL**: `http://your-domain.com`
- **认证方式**: JWT Token（在请求头中添加 `Authorization: Bearer {token}`）
- **数据格式**: JSON
- **字符编码**: UTF-8
- **权限前缀**: `system:user:`

## 核心特性

### 🎯 VIP状态动态计算
- **实时计算**: 基于 `effective_start` 和 `effective_end` 字段动态计算VIP状态
- **剩余天数**: 自动计算VIP剩余天数，便于管理和提醒
- **智能筛选**: 支持按VIP状态、VIP期限设置等条件筛选

### 📊 数据库表结构适配
- **完全匹配**: 代码与实际数据库表结构完全一致
- **移除冗余**: 移除了不存在的 `effective_status` 字段
- **优化查询**: 基于实际字段优化查询性能

### 📈 统计功能增强
- **会员统计**: 提供总数、VIP数、禁用数等统计信息
- **实时数据**: 统计数据实时计算，确保准确性

## API接口列表

### 1. 获取会员列表

**接口地址**: `GET /zb_user/list`

**权限要求**: `system:user:list`

**接口描述**: 获取会员列表，支持分页和多条件筛选，动态计算VIP状态

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码，最小为1 |
| page_size | int | 否 | 10 | 每页数量，范围1-100 |
| nickname | string | 否 | - | 昵称（模糊搜索） |
| openid | string | 否 | - | 微信OpenID（模糊搜索） |
| is_disable | int | 否 | -1 | 是否禁用：0=否，1=是，-1=全部 |
| vip_status | int | 否 | -1 | **新增** VIP状态：0=非VIP，1=VIP，-1=全部 |
| has_vip_period | int | 否 | -1 | **新增** 是否设置VIP期限：0=否，1=是，-1=全部 |
| start_time | string | 否 | - | 开始时间（注册时间范围查询，格式：2006-01-02 15:04:05） |
| end_time | string | 否 | - | 结束时间（注册时间范围查询，格式：2006-01-02 15:04:05） |

**请求示例**:
```bash
# 基础查询
GET /zb_user/list?page=1&page_size=10&nickname=张&is_disable=0

# VIP状态筛选
GET /zb_user/list?vip_status=1&page=1&page_size=10

# 查询设置了VIP期限的用户
GET /zb_user/list?has_vip_period=1&page=1&page_size=10
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "list": [
      {
        "id": 1,
        "nickname": "张三",
        "avatar": "http://example.com/avatar.jpg",
        "openid": "wx_openid_123456",
        "is_disable": 0,
        "is_delete": 0,
        "effective_start": "2025-01-01T00:00:00Z",
        "effective_end": "2025-12-31T00:00:00Z",
        "vip_status": 1,
        "vip_days_left": 165,
        "created_at": "2025-01-01T10:00:00Z",
        "updated_at": "2025-01-01T10:00:00Z"
      }
    ],
    "total": 100
  }
}
```

### 2. 获取会员详情

**接口地址**: `GET /zb_user/{id}`

**权限要求**: `system:user:detail`

**接口描述**: 根据会员ID获取会员详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 会员ID |

**请求示例**:
```bash
GET /zb_user/1
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "user": {
      "id": 1,
      "nickname": "张三",
      "avatar": "http://example.com/avatar.jpg",
      "openid": "wx_openid_123456",
      "is_disable": 0,
      "is_delete": 0,
      "effective_start": "2025-01-01T00:00:00Z",
      "effective_end": "2025-12-31T00:00:00Z",
      "vip_status": 1,
      "vip_days_left": 165,
      "created_at": "2025-01-01T10:00:00Z",
      "updated_at": "2025-01-01T10:00:00Z",
      "deleted_at": null
    }
  }
}
```

### 3. 更新会员信息

**接口地址**: `PUT /zb_user/update/{id}`

**权限要求**: `system:user:update`

**接口描述**: 更新会员基本信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 会员ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| nickname | string | 否 | 昵称（最大50字符） |
| avatar | string | 否 | 头像URL（最大255字符） |
| is_disable | int | 否 | 是否禁用：0=否，1=是 |
| effective_start | string | 否 | 有效开始日期（格式：2006-01-02） |
| effective_end | string | 否 | 有效结束日期（格式：2006-01-02） |

**请求示例**:
```json
{
  "nickname": "李四",
  "avatar": "http://example.com/new_avatar.jpg",
  "is_disable": 0
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {}
}
```

### 4. 删除会员

**接口地址**: `DELETE /zb_user/delete`

**权限要求**: `system:user:delete`

**接口描述**: 批量删除会员（软删除）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | array | 是 | 会员ID列表 |

**请求示例**:
```json
{
  "ids": [1, 2, 3, 4, 5]
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {}
}
```

### 5. 设置会员状态

**接口地址**: `PUT /zb_user/status/{id}`

**权限要求**: `system:user:status`

**接口描述**: 设置会员的启用/禁用状态

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 会员ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| is_disable | int | 是 | 是否禁用：0=否，1=是 |

**请求示例**:
```json
{
  "is_disable": 1
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {}
}
```

### 6. 更新会员VIP有效期

**接口地址**: `PUT /zb_user/vip_period/{id}`

**权限要求**: `system:user:vip`

**接口描述**: 更新会员的VIP有效期，系统会自动计算有效状态

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 会员ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| effective_start | string | 是 | 有效开始日期（格式：2006-01-02） |
| effective_end | string | 是 | 有效结束日期（格式：2006-01-02） |

**请求示例**:
```json
{
  "effective_start": "2025-01-01",
  "effective_end": "2025-12-31"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {}
}
```

### 7. 获取VIP会员列表

**接口地址**: `GET /zb_user/vip/list`

**权限要求**: `system:user:vip`

**接口描述**: 获取有效的VIP会员列表

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码，最小为1 |
| page_size | int | 否 | 10 | 每页数量，范围1-100 |

**请求示例**:
```bash
GET /zb_user/vip/list?page=1&page_size=10
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "list": [
      {
        "id": 1,
        "nickname": "张三",
        "avatar": "http://example.com/avatar.jpg",
        "openid": "wx_openid_123456",
        "is_disable": 0,
        "is_delete": 0,
        "effective_start": "2025-01-01T00:00:00Z",
        "effective_end": "2025-12-31T00:00:00Z",
        "vip_status": 1,
        "vip_days_left": 165,
        "created_at": "2025-01-01T10:00:00Z",
        "updated_at": "2025-01-01T10:00:00Z"
      }
    ],
    "total": 50
  }
}
```

### 8. 获取即将过期的VIP会员

**接口地址**: `GET /zb_user/vip/expired`

**权限要求**: `system:user:vip`

**接口描述**: 获取指定天数内即将过期的VIP会员列表

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| days | int | 否 | 7 | 过期天数，范围1-365 |

**请求示例**:
```bash
GET /zb_user/vip/expired?days=30
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "list": [
      {
        "id": 1,
        "nickname": "张三",
        "avatar": "http://example.com/avatar.jpg",
        "openid": "wx_openid_123456",
        "is_disable": 0,
        "is_delete": 0,
        "effective_start": "2025-01-01T00:00:00Z",
        "effective_end": "2025-01-31T00:00:00Z",
        "vip_status": 1,
        "vip_days_left": 15,
        "created_at": "2025-01-01T10:00:00Z",
        "updated_at": "2025-01-01T10:00:00Z"
      }
    ]
  }
}
```

### 9. 根据OpenID获取会员

**接口地址**: `GET /zb_user/openid/{openid}`

**权限要求**: `system:user:detail`

**接口描述**: 根据微信OpenID获取会员信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| openid | string | 是 | 微信OpenID |

**请求示例**:
```bash
GET /zb_user/openid/wx_openid_123456
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "user": {
      "id": 1,
      "nickname": "张三",
      "avatar": "http://example.com/avatar.jpg",
      "openid": "wx_openid_123456",
      "is_disable": 0,
      "is_delete": 0,
      "effective_start": "2025-01-01T00:00:00Z",
      "effective_end": "2025-12-31T00:00:00Z",
      "vip_status": 1,
      "vip_days_left": 165,
      "created_at": "2025-01-01T10:00:00Z",
      "updated_at": "2025-01-01T10:00:00Z",
      "deleted_at": null
    }
  }
}
```

### 10. 获取会员统计信息

**接口地址**: `GET /zb_user/stats`

**权限要求**: `system:user:stats`

**接口描述**: 获取会员统计信息，包括总数、VIP数、禁用数等

**请求参数**: 无

**请求示例**:
```bash
GET /zb_user/stats
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "total_users": 1000,
    "vip_users": 250,
    "disabled_users": 50,
    "active_users": 950
  }
}
```

**响应字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| total_users | int | 总用户数（未删除的所有用户） |
| vip_users | int | VIP用户数（当前有效的VIP用户） |
| disabled_users | int | 禁用用户数 |
| active_users | int | 活跃用户数（总用户数 - 禁用用户数） |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 操作成功 |
| 50 | 参数错误 |
| 51 | 数据不存在 |
| 500 | 服务器内部错误 |
| 401 | 未授权（Token无效或过期） |
| 403 | 权限不足 |

## 数据模型

### UserInfo（会员列表信息）
```json
{
  "id": 1,                                    // 主键ID
  "nickname": "张三",                         // 昵称
  "avatar": "http://example.com/avatar.jpg",  // 头像URL
  "openid": "wx_openid_123456",              // 微信OpenID
  "is_disable": 0,                           // 是否禁用：0=否，1=是
  "is_delete": 0,                            // 是否删除：0=否，1=是
  "effective_start": "2025-01-01T00:00:00Z", // 有效开始日期
  "effective_end": "2025-12-31T00:00:00Z",   // 有效结束日期
  "vip_status": 1,                           // VIP状态：0=非VIP，1=VIP（动态计算）
  "vip_days_left": 165,                      // VIP剩余天数（-1表示无限期，0表示已过期）
  "created_at": "2025-01-01T10:00:00Z",      // 注册时间
  "updated_at": "2025-01-01T10:00:00Z"       // 更新时间
}
```

### UserDetail（会员详情信息）
```json
{
  "id": 1,                                    // 主键ID
  "nickname": "张三",                         // 昵称
  "avatar": "http://example.com/avatar.jpg",  // 头像URL
  "openid": "wx_openid_123456",              // 微信OpenID
  "is_disable": 0,                           // 是否禁用：0=否，1=是
  "is_delete": 0,                            // 是否删除：0=否，1=是
  "effective_start": "2025-01-01T00:00:00Z", // 有效开始日期
  "effective_end": "2025-12-31T00:00:00Z",   // 有效结束日期
  "vip_status": 1,                           // VIP状态：0=非VIP，1=VIP（动态计算）
  "vip_days_left": 165,                      // VIP剩余天数（-1表示无限期，0表示已过期）
  "created_at": "2025-01-01T10:00:00Z",      // 注册时间
  "updated_at": "2025-01-01T10:00:00Z",      // 更新时间
  "deleted_at": null                         // 删除时间
}
```

### UserStats（会员统计信息）
```json
{
  "total_users": 1000,    // 总用户数
  "vip_users": 250,       // VIP用户数
  "disabled_users": 50,   // 禁用用户数
  "active_users": 950     // 活跃用户数
}
```

## 使用示例

### JavaScript/Axios示例

```javascript
// 获取会员列表（基础查询）
const getUsers = async () => {
  try {
    const response = await axios.get('/zb_user/list', {
      params: {
        page: 1,
        page_size: 10,
        nickname: '张'
      },
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log(response.data);
  } catch (error) {
    console.error('获取会员列表失败:', error);
  }
};

// 获取VIP会员列表
const getVipUsers = async () => {
  try {
    const response = await axios.get('/zb_user/list', {
      params: {
        page: 1,
        page_size: 10,
        vip_status: 1  // 筛选VIP用户
      },
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('VIP用户列表:', response.data);
  } catch (error) {
    console.error('获取VIP会员列表失败:', error);
  }
};

// 获取会员统计信息
const getUserStats = async () => {
  try {
    const response = await axios.get('/zb_user/stats', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('会员统计:', response.data);
  } catch (error) {
    console.error('获取会员统计失败:', error);
  }
};

// 更新会员信息
const updateUser = async (id, userData) => {
  try {
    const response = await axios.put(`/zb_user/update/${id}`, userData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    console.log(response.data);
  } catch (error) {
    console.error('更新会员信息失败:', error);
  }
};
```

### cURL示例

```bash
# 获取会员列表（基础查询）
curl -X GET "http://your-domain.com/zb_user/list?page=1&page_size=10" \
  -H "Authorization: Bearer your-jwt-token"

# 获取VIP会员列表
curl -X GET "http://your-domain.com/zb_user/list?vip_status=1&page=1&page_size=10" \
  -H "Authorization: Bearer your-jwt-token"

# 获取设置了VIP期限的用户
curl -X GET "http://your-domain.com/zb_user/list?has_vip_period=1&page=1&page_size=10" \
  -H "Authorization: Bearer your-jwt-token"

# 获取会员统计信息
curl -X GET "http://your-domain.com/zb_user/stats" \
  -H "Authorization: Bearer your-jwt-token"

# 获取即将过期的VIP会员（30天内）
curl -X GET "http://your-domain.com/zb_user/vip/expired?days=30" \
  -H "Authorization: Bearer your-jwt-token"

# 更新会员信息
curl -X PUT "http://your-domain.com/zb_user/update/1" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{"nickname": "新昵称"}'

# 设置会员状态
curl -X PUT "http://your-domain.com/zb_user/status/1" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{"is_disable": 1}'

# 更新VIP有效期
curl -X PUT "http://your-domain.com/zb_user/vip_period/1" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{"effective_start": "2025-01-01", "effective_end": "2025-12-31"}'
```

## 注意事项

1. **认证要求**: 所有接口都需要在请求头中携带有效的JWT Token
2. **权限验证**: 需要相应的会员管理权限才能访问这些接口
3. **软删除**: 删除操作为软删除，数据不会真正从数据库中删除
4. **日期格式**: 日期参数统一使用 `2006-01-02` 格式
5. **分页限制**: 每页最大数量为100条记录
6. **VIP状态动态计算**: VIP状态和剩余天数都是实时计算的，确保数据准确性
7. **数据库表结构**: 代码完全适配实际数据库表结构，移除了不存在的字段
8. **架构规范**: 严格按照GoFrame标准四层架构实现，便于维护和扩展
9. **统计数据实时性**: 统计接口返回的数据都是实时计算的，确保准确性

## 更新日志

### v3.0.0 (2025-07-19)
- **数据库表结构适配**: 完全适配实际的zb_user表结构
- **VIP状态动态计算**: 移除effective_status字段，改为实时计算VIP状态
- **新增统计功能**: 添加会员统计接口，提供总数、VIP数、禁用数等信息
- **增强查询功能**: 新增vip_status和has_vip_period筛选参数
- **优化响应数据**: 添加vip_status和vip_days_left字段，提供更丰富的VIP信息
- **修复验证规则**: 修复API参数验证语法错误
- **完善文档**: 更新所有接口文档，添加新功能说明和使用示例

### v2.0.0 (2025-07-17)
- 重构为标准四层架构
- 采用GoFrame规范的API定义方式
- 完善权限控制机制
- 优化错误处理和参数验证
- 增加VIP管理相关接口

## 快速开始

### 1. 基础查询
```bash
# 获取所有会员
GET /zb_user/list

# 获取VIP会员
GET /zb_user/list?vip_status=1

# 获取统计信息
GET /zb_user/stats
```

### 2. VIP管理
```bash
# 设置VIP有效期
PUT /zb_user/vip_period/1
{
  "effective_start": "2025-01-01",
  "effective_end": "2025-12-31"
}

# 查看即将过期的VIP
GET /zb_user/vip/expired?days=30
```

### 3. 响应数据示例
```json
{
  "id": 1,
  "nickname": "张三",
  "vip_status": 1,        // 动态计算的VIP状态
  "vip_days_left": 165,   // VIP剩余天数
  "effective_start": "2025-01-01T00:00:00Z",
  "effective_end": "2025-12-31T00:00:00Z"
}
```

现在您可以使用完全优化的会员管理API，享受更准确的VIP状态管理和丰富的统计功能！
