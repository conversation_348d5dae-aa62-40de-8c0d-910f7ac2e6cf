package service

import (
	"admin-server/internal/model/entity"
	"context"
)

// ArticleWithNames 带城市名称和类别名称的信息结构
type ArticleWithNames struct {
	entity.ZbArticle
	CityName string `json:"city_name"`
	CateName string `json:"cate_name"`
}

// IZbArticle 信息发布服务接口
type IZbArticle interface {
	// GetArticleList 获取信息列表
	GetArticleList(ctx context.Context, page, pageSize int, title string, cityId, cateId, isDisable int, startTime, endTime string) (list []entity.ZbArticle, total int, err error)

	// GetArticleListWithNames 获取带城市名称和类别名称的信息列表
	GetArticleListWithNames(ctx context.Context, page, pageSize int, title string, cityId, cateId, isDisable int, startTime, endTime string) (list []ArticleWithNames, total int, err error)

	// GetArticleListWithNamesForMobile 获取带城市名称和类别名称的信息列表 移动端使用
	GetArticleListWithNamesForMobile(ctx context.Context, page, pageSize int, cityId, cateId int, title string) (list []ArticleWithNames, total int, err error)

	// GetArticleDetail 获取信息详情
	GetArticleDetail(ctx context.Context, id int64) (*entity.ZbArticle, error)

	// GetArticleDetailWithNames 获取带城市名称和类别名称的信息详情
	GetArticleDetailWithNames(ctx context.Context, id int64) (*ArticleWithNames, error)

	// CreateArticle 创建信息
	CreateArticle(ctx context.Context, data *entity.ZbArticle) (int64, error)

	// UpdateArticle 更新信息
	UpdateArticle(ctx context.Context, id int64, data *entity.ZbArticle) error

	// UpdateArticleFields 更新信息字段（支持部分更新）
	UpdateArticleFields(ctx context.Context, id int64, fields map[string]interface{}) error

	// DeleteArticle 删除信息（软删除）
	DeleteArticle(ctx context.Context, id int64) error

	// BatchDeleteArticle 批量删除信息（软删除）
	BatchDeleteArticle(ctx context.Context, ids []int64) error

	// SetArticleStatus 设置信息状态
	SetArticleStatus(ctx context.Context, id int64, isDisable int) error

	// CheckArticleExists 检查信息是否存在
	CheckArticleExists(ctx context.Context, id int64) (bool, error)

	// IncrementViewCount 增加浏览次数
	IncrementViewCount(ctx context.Context, id int64) error

	// GetArticlesByCity 根据城市获取信息列表
	GetArticlesByCity(ctx context.Context, cityId int, page, pageSize int) (list []entity.ZbArticle, total int, err error)

	// GetArticlesByCityWithNames 根据城市获取带名称的信息列表
	GetArticlesByCityWithNames(ctx context.Context, cityId int, page, pageSize int) (list []ArticleWithNames, total int, err error)

	// GetArticlesByCategory 根据类别获取信息列表
	GetArticlesByCategory(ctx context.Context, cateId int, page, pageSize int) (list []entity.ZbArticle, total int, err error)

	// GetArticlesByCategoryWithNames 根据类别获取带名称的信息列表
	GetArticlesByCategoryWithNames(ctx context.Context, cateId int, page, pageSize int) (list []ArticleWithNames, total int, err error)

	// GetHotArticles 获取热门信息（按浏览量排序）
	GetHotArticles(ctx context.Context, limit int) ([]entity.ZbArticle, error)

	// GetHotArticlesWithNames 获取带名称的热门信息
	GetHotArticlesWithNames(ctx context.Context, limit int) ([]ArticleWithNames, error)

	// GetRecentArticles 获取最新信息
	GetRecentArticles(ctx context.Context, limit int) ([]entity.ZbArticle, error)

	// GetRecentArticlesWithNames 获取带名称的最新信息
	GetRecentArticlesWithNames(ctx context.Context, limit int) ([]ArticleWithNames, error)

	// GetArticleStats 获取信息统计
	GetArticleStats(ctx context.Context, cateId, cityId int) (totalArticles, publishedArticles, disabledArticles, todayArticles int, err error)

	// SearchArticles 搜索信息
	SearchArticles(ctx context.Context, keyword string, page, pageSize int) (list []entity.ZbArticle, total int, err error)

	// SearchArticlesWithNames 搜索带名称的信息
	SearchArticlesWithNames(ctx context.Context, keyword string, page, pageSize int) (list []ArticleWithNames, total int, err error)
}

var localZbArticle IZbArticle

func ZbArticle() IZbArticle {
	if localZbArticle == nil {
		panic("IZbArticle接口未实现或未注册")
	}
	return localZbArticle
}

func RegisterZbArticle(i IZbArticle) {
	localZbArticle = i
}
