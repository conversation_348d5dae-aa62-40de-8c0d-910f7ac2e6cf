package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// 订单信息结构
type OrderInfo struct {
	Id            int64       `json:"id"            dc:"订单ID"`
	OrderSn       string      `json:"order_sn"      dc:"订单编号"`
	GoodId        int64       `json:"good_id"       dc:"套餐ID"`
	GoodName      string      `json:"good_name"     dc:"套餐名称"`
	GoodPrice     float64     `json:"good_price"    dc:"套餐单价"`
	Effective     int         `json:"effective"     dc:"会员有效期；单位月"`
	CityCount     int         `json:"city_count"    dc:"选择的城市数量"`
	UserId        int64       `json:"user_id"       dc:"用户ID"`
	UserNickname  string      `json:"user_nickname" dc:"用户昵称"`
	Price         float64     `json:"price"         dc:"需支付金额"`
	Amount        float64     `json:"amount"        dc:"支付金额"`
	PayStatus     int         `json:"pay_status"    dc:"支付状态 1已支付 0未支付"`
	TransactionId string      `json:"transaction_id" dc:"微信支付订单号"`
	TradeType     string      `json:"trade_type"    dc:"交易类型"`
	TradeState    string      `json:"trade_state"   dc:"交易状态"`
	Remark        string      `json:"remark"        dc:"订单备注"`
	PayAt         *gtime.Time `json:"pay_at"        dc:"支付时间"`
	CreatedAt     *gtime.Time `json:"created_at"    dc:"创建时间"`
	UpdatedAt     *gtime.Time `json:"updated_at"    dc:"更新时间"`
	Cities        []CityInfo  `json:"cities"        dc:"选择的城市列表"`
}

// 城市信息结构
type CityInfo struct {
	CityId   int64  `json:"city_id"   dc:"城市ID"`
	CityName string `json:"city_name" dc:"城市名称"`
}

// CreateReq 创建订单请求
type CreateReq struct {
	g.Meta   `path:"/zb_order/create" tags:"Order" method:"post" summary:"创建订单"`
	OpenId   string  `json:"openid"    v:"required|length:1,100" dc:"用户OpenID"`
	GoodId   int64   `json:"good_id"   v:"required|min:1" dc:"套餐ID"`
	GoodName string  `json:"good_name" v:"required|length:1,255" dc:"套餐名称"`
	Price    float64 `json:"price"     v:"required|min:0" dc:"套餐单价"`
	CityIds  []int64 `json:"city_ids"  v:"required" dc:"选择的城市ID列表"`
	Remark   string  `json:"remark"    v:"length:0,500" dc:"订单备注"`
}

// CreateRes 创建订单响应
type CreateRes struct {
	OrderId int64  `json:"order_id" dc:"订单ID"`
	OrderSn string `json:"order_sn" dc:"订单编号"`
}

// GetListReq 获取订单列表请求
type GetListReq struct {
	g.Meta    `path:"/zb_order/list" tags:"Order" method:"get" summary:"获取订单列表" permission:"system:zb_order:list"`
	Page      int    `json:"page"       d:"1"  v:"min:0"     dc:"页码"`
	PageSize  int    `json:"page_size"  d:"10" v:"min:0|max:50" dc:"每页数量"`
	OrderSn   string `json:"order_sn"   dc:"订单编号"`
	UserId    int64  `json:"user_id"    dc:"用户ID"`
	PayStatus *int   `json:"pay_status" dc:"支付状态 1已支付 0未支付"`
	StartTime string `json:"start_time" dc:"开始时间 YYYY-MM-DD"`
	EndTime   string `json:"end_time"   dc:"结束时间 YYYY-MM-DD"`
}

// GetListRes 获取订单列表响应
type GetListRes struct {
	List  []OrderInfo `json:"list"  dc:"订单列表"`
	Total int         `json:"total" dc:"总数"`
}

// GetDetailReq 获取订单详情请求
type GetDetailReq struct {
	g.Meta `path:"/zb_order/detail" tags:"Order" method:"get" summary:"获取订单详情" permission:"system:zb_order:detail"`
	Id     int64 `json:"id" v:"required|min:1" dc:"订单ID"`
}

// GetDetailRes 获取订单详情响应
type GetDetailRes struct {
	OrderInfo
}

// UpdatePayStatusReq 更新支付状态请求
type UpdatePayStatusReq struct {
	g.Meta `path:"/zb_order/pay-status" tags:"Order" method:"put" summary:"更新支付状态" permission:"system:zb_order:pay"`
	Id     int64 `json:"id" v:"required|min:1" dc:"订单ID"`
}

// UpdatePayStatusRes 更新支付状态响应
type UpdatePayStatusRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// DeleteReq 删除订单请求
type DeleteReq struct {
	g.Meta `path:"/zb_order/delete" tags:"Order" method:"delete" summary:"删除订单"`
	Id     int64 `json:"id" v:"required|min:1" dc:"订单ID"`
}

// DeleteRes 删除订单响应
type DeleteRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// GetMyListReq 获取我的订单列表请求
type GetMyListReq struct {
	g.Meta   `path:"/zb_order/my-list" tags:"Order" method:"get" summary:"获取我的订单列表"`
	OpenId   string `json:"openid"    v:"required|length:1,100" dc:"用户OpenID"`
	Page     int    `json:"page"      v:"min:1" d:"1" dc:"页码"`
	PageSize int    `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间" dc:"每页数量"`
}

// GetMyListRes 获取我的订单列表响应
type GetMyListRes struct {
	List  []OrderInfo `json:"list"  dc:"订单列表"`
	Total int         `json:"total" dc:"总数"`
}

// GetMyStatsReq 获取我的订单统计请求
type GetMyStatsReq struct {
	g.Meta `path:"/zb_order/my-stats" tags:"Order" method:"get" summary:"获取我的订单统计"`
	OpenId string `json:"openid" v:"required|length:1,100" dc:"用户OpenID"`
}

// GetMyStatsRes 获取我的订单统计响应
type GetMyStatsRes struct {
	PaidOrderCount   int     `json:"paid_order_count" dc:"已支付订单数"`
	PaidTotalAmount  float64 `json:"paid_total_amount" dc:"已支付总金额"`
	UnpaidOrderCount int     `json:"unpaid_order_count" dc:"未支付订单数"`
	TotalOrderCount  int     `json:"total_order_count" dc:"总订单数"`
}
