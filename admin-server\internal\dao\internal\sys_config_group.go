// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SysConfigGroupDao is the data access object for the table sys_config_group.
type SysConfigGroupDao struct {
	table    string                // table is the underlying table name of the DAO.
	group    string                // group is the database configuration group name of the current DAO.
	columns  SysConfigGroupColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler    // handlers for customized model modification.
}

// SysConfigGroupColumns defines and stores column names for the table sys_config_group.
type SysConfigGroupColumns struct {
	Id        string //
	Name      string // 系统配置分组名称
	Code      string // 系统配置分组编码
	IsSystem  string // 是否系统保留 1是 0否
	IsDisable string // 是否禁用: 0=否, 1=是
	IsDelete  string // 是否删除: 0=否, 1=是
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
	DeletedAt string // 删除时间
}

// sysConfigGroupColumns holds the columns for the table sys_config_group.
var sysConfigGroupColumns = SysConfigGroupColumns{
	Id:        "id",
	Name:      "name",
	Code:      "code",
	IsSystem:  "is_system",
	IsDisable: "is_disable",
	IsDelete:  "is_delete",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
	DeletedAt: "deleted_at",
}

// NewSysConfigGroupDao creates and returns a new DAO object for table data access.
func NewSysConfigGroupDao(handlers ...gdb.ModelHandler) *SysConfigGroupDao {
	return &SysConfigGroupDao{
		group:    "default",
		table:    "sys_config_group",
		columns:  sysConfigGroupColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SysConfigGroupDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SysConfigGroupDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SysConfigGroupDao) Columns() SysConfigGroupColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SysConfigGroupDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SysConfigGroupDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SysConfigGroupDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
