# 移动端搜索页面API集成说明

## 功能概述

为移动端搜索页面添加了完整的API集成功能，支持页面加载时获取所有信息，以及根据搜索条件动态获取和渲染搜索结果。

## 主要功能

### 1. 初始数据加载
- **页面加载**: 页面加载完毕后自动调用API获取所有信息
- **API接口**: `GET /m/api/zb_article/mobileList?page=1&page_size=20`
- **显示内容**: 展示最新的招标信息列表

### 2. 搜索功能
- **搜索触发**: 点击搜索按钮或按回车键
- **API接口**: `GET /m/api/zb_article/mobileList?page=1&page_size=20&city_id=&title=`
- **搜索参数**: 支持标题关键词和城市筛选

### 3. 分页加载
- **分页支持**: 支持加载更多结果
- **页码管理**: 自动管理当前页码和总页数
- **数据追加**: 后续页面数据追加到现有结果

## API接口详情

### 1. 接口地址
```
GET /m/api/zb_article/mobileList
```

### 2. 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 是 | 页码，从1开始 |
| page_size | int | 是 | 每页数量，固定20 |
| title | string | 否 | 搜索关键词 |
| city_id | int | 否 | 城市ID |

### 3. 响应格式
```javascript
{
    "code": 0,
    "message": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "title": "招标标题",
                "cate_name": "招标公告",
                "city_name": "北京",
                "author": "招标平台",
                "viewCount": 100,
                "created_at": "2025-01-23T10:00:00Z"
            }
        ],
        "total": 156
    }
}
```

## 技术实现

### 1. 全局变量
```javascript
let allCities = [];      // 存储所有城市数据
let selectedCity = null; // 存储选中的城市
let currentPage = 1;     // 当前页码
let totalPages = 1;      // 总页数
let isLoading = false;   // 是否正在加载
let searchResults = [];  // 搜索结果数据
```

### 2. 初始化流程
```javascript
document.addEventListener('DOMContentLoaded', function() {
    loadCities();        // 加载城市列表
    initSearchEvents();  // 初始化搜索事件
    loadInitialData();   // 加载初始数据
});
```

### 3. 数据获取函数
```javascript
async function fetchSearchResults(title = '', cityId = '') {
    // 构建API URL
    const params = new URLSearchParams({
        page: currentPage,
        page_size: 20
    });
    
    if (title) params.append('title', title);
    if (cityId) params.append('city_id', cityId);
    
    // 调用API
    const response = await fetch(`/m/api/zb_article/mobileList?${params}`);
    const result = await response.json();
    
    // 处理响应数据
    if (result.code === 0) {
        // 更新搜索结果和分页信息
        renderSearchResults(title, cityId);
    }
}
```

### 4. 搜索结果渲染
```javascript
function renderSearchResults(title = '', cityId = '') {
    // 清空现有内容（仅第一页）
    if (currentPage === 1) {
        listContainer.innerHTML = '';
    }
    
    // 渲染每个搜索结果
    searchResults.forEach(article => {
        const articleElement = createArticleElement(article, title);
        listContainer.appendChild(articleElement);
    });
}
```

## 页面状态管理

### 1. 加载状态
- **显示**: 加载动画和"正在加载..."文字
- **隐藏**: 搜索结果列表和其他状态

### 2. 空结果状态
- **显示**: 搜索图标和"没有找到相关信息"提示
- **建议**: 提示用户尝试其他关键词

### 3. 错误状态
- **显示**: 错误图标和"加载失败"提示
- **操作**: 提供"重新加载"按钮

### 4. 正常状态
- **显示**: 搜索结果列表
- **分页**: 显示"查看更多结果"按钮（如有更多页）

## 搜索功能特性

### 1. 关键词高亮
```javascript
function createArticleElement(article, searchKeyword = '') {
    let highlightedTitle = article.title;
    if (searchKeyword) {
        const regex = new RegExp(`(${searchKeyword})`, 'gi');
        highlightedTitle = highlightedTitle.replace(regex, '<span class="bg-yellow-200">$1</span>');
    }
}
```

### 2. 智能时间显示
```javascript
function formatTimeAgo(dateString) {
    const diffMins = Math.floor((now - date) / 60000);
    
    if (diffMins < 1) return '刚刚发布';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    // ...
}
```

### 3. 分类颜色映射
```javascript
function getCategoryColor(cateName) {
    const colorMap = {
        '招标公告': 'blue',
        '中标公告': 'green',
        '政府采购': 'purple',
        '工程建设': 'orange'
    };
    // 返回对应颜色
}
```

## 用户交互

### 1. 搜索触发
- **搜索按钮**: 点击"搜索"按钮
- **回车键**: 在搜索框中按回车键
- **城市选择**: 选择城市后自动搜索（如有关键词）

### 2. 结果交互
- **点击详情**: 点击文章卡片跳转到详情页
- **加载更多**: 点击"查看更多结果"按钮
- **重试加载**: 错误状态下点击"重新加载"

### 3. 搜索历史
- **自动添加**: 搜索后自动添加到搜索历史
- **点击搜索**: 点击历史标签自动填入并搜索
- **数量限制**: 最多保存8个历史记录

## 响应式设计

### 1. 移动端优化
- **触摸友好**: 按钮和卡片有足够的点击区域
- **滚动优化**: 平滑滚动和适当的间距
- **加载动画**: 提供视觉反馈

### 2. 网络优化
- **防重复请求**: 通过`isLoading`标志防止重复请求
- **错误处理**: 完善的网络错误处理
- **重试机制**: 提供重试功能

## 数据流程

### 1. 页面加载流程
```
页面加载 → 加载城市列表 → 初始化事件 → 加载初始数据 → 渲染结果
```

### 2. 搜索流程
```
用户输入 → 点击搜索 → 重置页码 → 调用API → 渲染结果 → 更新统计
```

### 3. 分页流程
```
点击加载更多 → 页码+1 → 调用API → 追加结果 → 更新按钮状态
```

## 错误处理

### 1. 网络错误
- **捕获异常**: try-catch捕获网络请求异常
- **用户提示**: 显示友好的错误提示
- **重试机制**: 提供重新加载功能

### 2. 数据错误
- **格式验证**: 验证API返回数据格式
- **默认值**: 为缺失字段提供默认值
- **容错处理**: 部分数据错误不影响整体功能

### 3. 用户操作错误
- **输入验证**: 验证搜索输入
- **状态检查**: 防止重复操作
- **友好提示**: 提供操作指导

---

**功能状态**: ✅ 已完成  
**API集成**: 完整支持  
**文档版本**: v1.0  
**最后更新**: 2025-01-23
