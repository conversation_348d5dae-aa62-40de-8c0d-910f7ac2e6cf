package v1

import (
	"admin-server/internal/packed"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// CreateReq 创建角色请求体
type CreateReq struct {
	g.Meta    `path:"/sys_role/create" method:"post" tags:"SysRole" summary:"创建角色" permission:"system:role:add"`
	Name      string          `p:"name" v:"required|length:1,30#角色名称不能为空|角色名称长度为1-30位" dc:"角色名称"`
	Sort      int             `p:"sort" d:"1" v:"min:0#排序值不能小于0" dc:"排序"`
	Remark    string          `p:"remark" v:"length:0,255#备注长度不能超过255位" dc:"备注"`
	IsDisable *packed.Disable `p:"is_disable" v:"in:0,1" dc:"是否禁用"`
	MenuIds   []int64         `p:"menu_ids" dc:"菜单权限ID列表（可选，不填则后续单独分配）"`
}

type CreateRes struct {
	ID int64 `json:"id" dc:"角色ID"`
}

// DeleteReq 删除角色请求体
type DeleteReq struct {
	g.Meta `path:"/sys_role/{id}" method:"delete" tags:"SysRole" summary:"删除角色" permission:"system:role:del"`
	ID     int64 `p:"id" v:"required#请选择需要删除的角色" dc:"角色ID"`
}
type DeleteRes struct{}

// UpdateReq 更新角色请求体
type UpdateReq struct {
	g.Meta    `path:"/sys_role/{id}" method:"put" tags:"SysRole" summary:"更新角色信息" permission:"system:role:edit"`
	ID        int64           `p:"id" v:"required#请选择需要更新的角色" dc:"角色ID"`
	Name      string          `p:"name" v:"required|length:1,30#角色名称不能为空|角色名称长度为1-30位" dc:"角色名称"`
	Sort      int             `p:"sort" d:"1" v:"min:0#排序值不能小于0" dc:"排序"`
	Remark    string          `p:"remark" v:"length:0,255#备注长度不能超过255位" dc:"备注"`
	IsDisable *packed.Disable `p:"is_disable" v:"in:0,1" dc:"是否禁用"`
}
type UpdateRes struct{}

// GetListReq 获取角色列表请求体
type GetListReq struct {
	g.Meta    `path:"/sys_role/list" tags:"SysRole" method:"get" summary:"获取角色列表" permission:"system:role:list"`
	Page      int             `p:"page" d:"1" v:"min:1#页码最小为1" dc:"页码"`
	PageSize  int             `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间" dc:"每页数量"`
	Name      string          `p:"name" dc:"角色名称（模糊搜索）"`
	IsDisable *packed.Disable `p:"is_disable" v:"in:0,1" dc:"是否禁用"`
}

// RoleInfo 角色信息
type RoleInfo struct {
	ID        int64          `json:"id" dc:"角色ID"`
	Name      string         `json:"name" dc:"角色名称"`
	Sort      int            `json:"sort" dc:"排序"`
	Remark    string         `json:"remark" dc:"备注"`
	IsDisable packed.Disable `json:"is_disable" dc:"是否禁用"`
	CreatedAt *gtime.Time    `json:"created_at" dc:"创建时间"`
	UpdatedAt *gtime.Time    `json:"updated_at" dc:"更新时间"`
	MenuCount int            `json:"menu_count" dc:"权限数量"`
	UserCount int            `json:"user_count" dc:"用户数量"`
	MainMenus []string       `json:"main_menus" dc:"主要权限列表"`
}

type GetListRes struct {
	List     []*RoleInfo `json:"list" dc:"角色列表"`
	Total    int         `json:"total" dc:"总数"`
	Page     int         `json:"page" dc:"当前页码"`
	PageSize int         `json:"page_size" dc:"每页数量"`
}

// GetOneReq 获取单个角色信息请求体
type GetOneReq struct {
	g.Meta `path:"/sys_role/{id}" tags:"SysRole" method:"get" summary:"获取单个角色信息"`
	ID     int64 `p:"id" v:"required#请选择需要查询的角色" dc:"角色ID"`
}
type GetOneRes struct {
	*RoleInfo `dc:"角色信息"`
}

// AssignMenusReq 分配菜单权限请求体
type AssignMenusReq struct {
	g.Meta  `path:"/sys_role/{id}/menus" method:"put" tags:"SysRole" summary:"分配角色菜单权限" permission:"system:role:assignMenus"`
	ID      int64   `p:"id" v:"required#请选择需要分配权限的角色" dc:"角色ID"`
	MenuIds []int64 `p:"menu_ids" v:"required#请选择菜单权限" dc:"菜单ID列表"`
}
type AssignMenusRes struct{}

// GetRoleMenusReq 获取角色菜单权限请求体
type GetRoleMenusReq struct {
	g.Meta `path:"/sys_role/{id}/menus" tags:"SysRole" method:"get" summary:"获取角色菜单权限"`
	ID     int64 `p:"id" v:"required#请选择需要查询的角色" dc:"角色ID"`
}

// RoleMenuInfo 角色菜单权限信息
type RoleMenuInfo struct {
	RoleID   int64   `json:"role_id" dc:"角色ID"`
	RoleName string  `json:"role_name" dc:"角色名称"`
	MenuIds  []int64 `json:"menu_ids" dc:"菜单ID列表"`
}

type GetRoleMenusRes struct {
	*RoleMenuInfo `dc:"角色菜单权限信息"`
}

// ToggleStatusReq 切换角色状态请求体
type ToggleStatusReq struct {
	g.Meta `path:"/sys_role/{id}/toggle" method:"put" tags:"SysRole" summary:"切换角色启用/禁用状态" permission:"system:role:toggle"`
	ID     int64 `p:"id" v:"required#请选择需要切换状态的角色" dc:"角色ID"`
}
type ToggleStatusRes struct{}
