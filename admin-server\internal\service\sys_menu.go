package service

import (
	v1 "admin-server/api/sys_menu/v1"
	"context"
)

// 1.定义接口
type ISysMenu interface {
	GetList(ctx context.Context, req *v1.GetListReq) (list []*v1.MenuInfo, total int, err error)
	GetTree(ctx context.Context, req *v1.GetTreeReq) (tree []*v1.MenuTree, err error)
	GetOne(ctx context.Context, id int64) (menu *v1.MenuInfo, err error)
	Create(ctx context.Context, req *v1.CreateReq) (insertId int64, err error)
	Update(ctx context.Context, req *v1.UpdateReq) (err error)
	Delete(ctx context.Context, id int64) (err error)
	GetChildren(ctx context.Context, pid int64) (children []*v1.MenuInfo, err error)
	BuildTree(ctx context.Context, menus []*v1.MenuInfo, pid int64) (tree []*v1.MenuTree, err error)
	UpdateSort(ctx context.Context, id int64, sort int) error
}

// 2.定义接口变量
var localSysMenu ISysMenu

// 3.定义一个获取接口实例的函数
func SysMenu() ISysMenu {
	if localSysMenu == nil {
		panic("ISysMenu接口未实现或未注册")
	}
	return localSysMenu
}

// 4.定义一个接口实现的注册方法
func RegisterSysMenu(i ISysMenu) {
	localSysMenu = i
}
