# 热门搜索数量限制修改说明

## 修改概述

根据需求对热门搜索功能进行了优化，移除了多余的模拟数据，并将热门搜索显示数量限制为5个。

## 具体修改

### 1. API调用限制
**修改前**:
```javascript
const response = await fetch('/m/api/search/hot-keywords');
```

**修改后**:
```javascript
const response = await fetch('/m/api/search/hot-keywords?limit=5');
```

### 2. 默认数据精简
**修改前**:
```javascript
function getDefaultHotKeywords() {
    return [
        // 8个默认关键词
        { keyword: "智慧城市建设", ... },
        { keyword: "医院设备采购", ... },
        { keyword: "学校装修工程", ... },
        { keyword: "道路建设项目", ... },
        { keyword: "环保设备招标", ... },
        { keyword: "办公设备采购", ... },
        { keyword: "园林绿化工程", ... },
        { keyword: "安防监控系统", ... }
    ];
}
```

**修改后**:
```javascript
function getDefaultHotKeywords() {
    return [
        // 只保留5个默认关键词
        { keyword: "智慧城市建设", ... },
        { keyword: "医院设备采购", ... },
        { keyword: "学校装修工程", ... },
        { keyword: "道路建设项目", ... },
        { keyword: "环保设备招标", ... }
    ];
}
```

### 3. 渲染数量限制
**修改前**:
```javascript
keywords.slice(0, 8).forEach((item, index) => {
```

**修改后**:
```javascript
keywords.slice(0, 5).forEach((item, index) => {
```

## 优化效果

### 1. 界面简洁
- **减少视觉负担**: 只显示5个热门搜索，界面更简洁
- **突出重点**: 只显示最热门的搜索关键词
- **提升体验**: 减少用户选择困难

### 2. 性能优化
- **减少数据传输**: API只返回5个结果，减少网络传输
- **加快渲染**: 减少DOM操作，提升页面渲染速度
- **节省资源**: 减少内存占用

### 3. 数据一致性
- **API与前端一致**: API限制5个，前端也只显示5个
- **备用数据同步**: 默认数据也只保留5个，保持一致性

## 排名颜色保持

即使只显示5个热门搜索，排名颜色规则保持不变：
- **第1名**: 红色背景
- **第2名**: 橙色背景  
- **第3名**: 黄色背景
- **第4-5名**: 灰色背景

## 功能完整性

### 保留的功能
- ✅ 热门搜索加载
- ✅ 点击搜索功能
- ✅ 趋势图标显示
- ✅ 热门和新增标签
- ✅ 搜索统计上报
- ✅ 错误处理和重试

### 移除的内容
- ❌ 多余的模拟数据（第6-8个关键词）
- ❌ 不必要的数据传输和渲染

## 测试验证

### 1. 功能测试
1. 访问搜索页面
2. 确认热门搜索只显示5个
3. 测试点击搜索功能
4. 验证API调用参数正确

### 2. API测试
```bash
# 验证API返回5个结果
curl "http://localhost:8000/m/api/search/hot-keywords?limit=5"
```

### 3. 界面验证
- 检查热门搜索区域只显示5行
- 验证排名颜色正确
- 确认趋势图标和标签正常

## 扩展考虑

### 1. 可配置化
如果后续需要调整显示数量，可以：
```javascript
const HOT_KEYWORDS_LIMIT = 5; // 配置常量
const response = await fetch(`/m/api/search/hot-keywords?limit=${HOT_KEYWORDS_LIMIT}`);
```

### 2. 响应式调整
可以根据屏幕大小动态调整显示数量：
```javascript
const limit = window.innerWidth < 375 ? 3 : 5; // 小屏幕显示3个，大屏幕显示5个
```

### 3. 用户偏好
可以允许用户自定义显示数量：
```javascript
const userLimit = localStorage.getItem('hotKeywordsLimit') || 5;
```

---

**修改状态**: ✅ 已完成  
**显示数量**: 限制为5个  
**文档版本**: v1.1  
**最后更新**: 2025-01-23
