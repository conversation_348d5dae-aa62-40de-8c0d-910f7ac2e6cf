# 系统管理员 API 文档

## 概述

系统管理员模块提供了完整的管理员账户管理功能，包括创建、查询、更新、删除和密码修改等操作。

## 基础信息

- **基础路径**: `/sys_admin`
- **认证方式**: Bearer <PERSON>
- **数据格式**: JSON

## 数据类型说明

### AdminInfo 管理员信息

```json
{
  "id": 1,                           // 管理员ID
  "username": "admin",               // 账号
  "nickname": "管理员",               // 昵称
  "is_super": 0,                     // 是否是超级管理员 (0=否, 1=是)
  "is_disable": 0,                   // 是否禁用 (0=否, 1=是)
  "last_login_ip": "***********",    // 最后登录IP
  "last_login_time": "2024-01-01T10:00:00Z", // 最后登录时间
  "created_at": "2024-01-01T10:00:00Z",      // 创建时间
  "updated_at": "2024-01-01T10:00:00Z",      // 更新时间
  "roles": [                         // 角色列表
    {
      "id": 1,                       // 角色ID
      "name": "系统管理员"            // 角色名称
    }
  ]
}
```

### RoleInfo 角色信息

```json
{
  "id": 1,                           // 角色ID
  "name": "系统管理员"                // 角色名称
}
```

## API 接口

### 1. 创建管理员

**接口地址**: `POST /sys_admin/create`

**接口描述**: 创建新的管理员账户

**请求参数**:

```json
{
  "username": "admin",      // 必填，账号，长度3-30位
  "password": "123456",     // 必填，密码，长度6-30位
  "nickname": "管理员",      // 必填，昵称，长度2-20位
  "is_super": 0,           // 可选，是否是超级管理员 (0=否, 1=是)，默认0
  "is_disable": 0,         // 可选，是否禁用 (0=否, 1=是)，默认0
  "role_ids": [1, 2]       // 可选，角色ID列表
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1                // 新创建的管理员ID
  }
}
```

**错误响应**:

```json
{
  "code": 1,
  "message": "账号已存在",
  "data": null
}
```

### 2. 获取管理员列表

**接口地址**: `GET /sys_admin/list`

**接口描述**: 分页获取管理员列表，支持条件筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1，最小值1 |
| page_size | int | 否 | 每页数量，默认10，范围1-100 |
| username | string | 否 | 账号（模糊搜索） |
| nickname | string | 否 | 昵称（模糊搜索） |
| is_super | int | 否 | 是否是超级管理员 (0=否, 1=是) |
| is_disable | int | 否 | 是否禁用 (0=否, 1=是) |

**请求示例**:

```
GET /sys_admin/list?page=1&page_size=10&username=admin&is_disable=0
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "username": "admin",
        "nickname": "管理员",
        "is_super": 1,
        "is_disable": 0,
        "last_login_ip": "***********",
        "last_login_time": "2024-01-01T10:00:00Z",
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 1,              // 总记录数
    "page": 1,               // 当前页码
    "page_size": 10          // 每页数量
  }
}
```

### 3. 获取单个管理员信息

**接口地址**: `GET /sys_admin/{id}`

**接口描述**: 根据ID获取单个管理员的详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 管理员ID |

**请求示例**:

```
GET /sys_admin/1
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "username": "admin",
    "nickname": "管理员",
    "is_super": 1,
    "is_disable": 0,
    "last_login_ip": "***********",
    "last_login_time": "2024-01-01T10:00:00Z",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

**错误响应**:

```json
{
  "code": 1,
  "message": "管理员不存在",
  "data": null
}
```

### 4. 更新管理员信息

**接口地址**: `PUT /sys_admin/{id}`

**接口描述**: 更新管理员的基本信息，如果传入password则同时修改密码

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 管理员ID |

**请求参数**:

```json
{
  "username": "admin",      // 必填，账号，长度3-30位
  "password": "newpass123", // 可选，密码，长度6-30位，传入则修改密码
  "nickname": "管理员",      // 必填，昵称，长度2-20位
  "is_super": 0,           // 可选，是否是超级管理员 (0=否, 1=是)
  "is_disable": 0,         // 可选，是否禁用 (0=否, 1=是)
  "role_ids": [1, 2]       // 可选，角色ID列表
}
```

**参数说明**:
- `password` 字段为可选，如果不传入或传入空字符串，则不修改密码
- 如果传入 `password` 字段，则会同时更新密码（自动加密存储）
- 其他字段按正常逻辑更新

**请求示例**:

```
PUT /sys_admin/1
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

**错误响应**:

```json
{
  "code": 1,
  "message": "用户名已被使用",
  "data": null
}
```

### 5. 修改管理员密码

**接口地址**: `PUT /sys_admin/{id}/password`

**接口描述**: 修改管理员的登录密码

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 管理员ID |

**请求参数**:

```json
{
  "old_password": "123456",    // 必填，原密码
  "new_password": "654321"     // 必填，新密码，长度6-30位
}
```

**请求示例**:

```
PUT /sys_admin/1/password
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

**错误响应**:

```json
{
  "code": 1,
  "message": "原密码错误",
  "data": null
}
```

### 6. 禁用管理员

**接口地址**: `PUT /sys_admin/{id}/disable`

**接口描述**: 禁用管理员账户，禁用后管理员无法登录系统

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 管理员ID |

**请求示例**:

```
PUT /sys_admin/1/disable
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

**错误响应**:

```json
{
  "code": 1,
  "message": "管理员已被禁用",
  "data": null
}
```

### 7. 启用管理员

**接口地址**: `PUT /sys_admin/{id}/enable`

**接口描述**: 启用管理员账户，启用后管理员可以正常登录系统

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 管理员ID |

**请求示例**:

```
PUT /sys_admin/1/enable
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

**错误响应**:

```json
{
  "code": 1,
  "message": "管理员已是启用状态",
  "data": null
}
```

### 8. 删除管理员

**接口地址**: `DELETE /sys_admin/{id}`

**接口描述**: 软删除管理员账户（逻辑删除），同时清除该管理员的所有角色关联

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 管理员ID |

**请求示例**:

```
DELETE /sys_admin/1
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

**错误响应**:

```json
{
  "code": 1,
  "message": "管理员不存在",
  "data": null
}
```

### 9. 分配管理员角色

**接口地址**: `PUT /sys_admin/{id}/roles`

**接口描述**: 为管理员分配角色，支持多角色分配

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 管理员ID |

**请求参数**:

```json
{
  "role_ids": [1, 2, 3]  // 角色ID列表，可为空数组（清空所有角色）
}
```

**请求示例**:

```
PUT /sys_admin/1/roles
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 10. 获取管理员角色列表

**接口地址**: `GET /sys_admin/{id}/roles`

**接口描述**: 获取指定管理员的角色列表

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 管理员ID |

**请求示例**:

```
GET /sys_admin/1/roles
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "roles": [
      {
        "id": 1,
        "name": "系统管理员"
      },
      {
        "id": 2,
        "name": "内容管理员"
      }
    ]
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1 | 业务错误（具体错误信息见message字段） |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## API接口列表

| 方法 | 路径 | 功能 |
|------|------|------|
| POST | /sys_admin/create | 创建管理员 |
| GET | /sys_admin/list | 获取管理员列表 |
| GET | /sys_admin/{id} | 获取单个管理员信息 |
| PUT | /sys_admin/{id} | 更新管理员信息 |
| PUT | /sys_admin/{id}/password | 修改管理员密码 |
| PUT | /sys_admin/{id}/disable | 禁用管理员 |
| PUT | /sys_admin/{id}/enable | 启用管理员 |
| DELETE | /sys_admin/{id} | 删除管理员 |
| PUT | /sys_admin/{id}/roles | 分配管理员角色 |
| GET | /sys_admin/{id}/roles | 获取管理员角色列表 |

## 密码修改方式对比

系统提供两种密码修改方式，适用于不同的使用场景：

### 方式一：通过更新接口修改密码
- **接口**: `PUT /sys_admin/{id}`
- **特点**: 无需验证原密码，可同时更新其他信息
- **适用场景**: 管理员重置他人密码、批量更新信息
- **使用方法**: 在更新请求中包含 `password` 字段

### 方式二：专用密码修改接口
- **接口**: `PUT /sys_admin/{id}/password`
- **特点**: 需要验证原密码，更安全
- **适用场景**: 管理员自己修改密码
- **使用方法**: 提供原密码和新密码

## 注意事项

1. 所有接口都需要在请求头中携带有效的认证Token
2. 密码在存储时会进行MD5加密
3. 更新管理员信息时，如果传入password字段则同时修改密码，不传入则不修改密码
4. 删除操作为软删除，不会真正删除数据库记录，同时会清除该管理员的所有角色关联
5. 超级管理员具有所有权限，普通管理员权限由角色控制
6. 禁用的管理员无法登录系统
7. 用户名在系统中必须唯一
8. 分页查询默认按ID倒序排列

## 角色管理说明

1. **多对多关系**：一个管理员可以拥有多个角色，一个角色可以分配给多个管理员
2. **关联表**：管理员与角色的关系通过`sys_admin_role`表维护
3. **角色分配**：
   - 创建管理员时可以同时分配角色（通过`role_ids`参数）
   - 更新管理员时可以重新分配角色（通过`role_ids`参数）
   - 可以使用专门的角色分配接口单独管理角色
4. **角色清空**：传入空的`role_ids`数组可以清空管理员的所有角色
5. **权限继承**：管理员的最终权限是所有分配角色权限的并集
6. **超级管理员**：超级管理员不受角色限制，拥有所有权限
