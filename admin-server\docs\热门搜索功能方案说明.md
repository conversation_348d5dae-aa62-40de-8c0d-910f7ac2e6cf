# 热门搜索功能方案说明

## 功能概述

为移动端搜索页面设计并实现了完整的热门搜索功能，支持动态加载、实时更新、点击统计等特性，提升用户搜索体验和发现效率。

## 技术方案

### 1. 数据来源策略

#### A. 后端API方案（推荐）
```javascript
// API接口
GET /m/api/search/hot-keywords

// 响应格式
{
    "code": 0,
    "data": {
        "keywords": [
            {
                "keyword": "智慧城市建设",
                "search_count": 1250,
                "trend": "up",        // up/down/stable
                "is_hot": true,       // 是否热门标记
                "is_new": true,       // 是否新增标记
                "category": "建设工程"
            }
        ]
    }
}
```

#### B. 本地统计方案（备选）
```javascript
// 本地存储搜索统计
function trackSearchKeyword(keyword) {
    const searchStats = JSON.parse(localStorage.getItem('searchStats') || '{}');
    searchStats[keyword] = (searchStats[keyword] || 0) + 1;
    localStorage.setItem('searchStats', JSON.stringify(searchStats));
}
```

### 2. 前端实现

#### A. 页面结构
```html
<!-- 热门搜索区域 -->
<div class="px-4 py-4 border-b border-gray-100">
    <div class="flex items-center justify-between mb-3">
        <h3 class="text-sm font-semibold text-gray-800">
            <i class="fas fa-fire text-red-500 mr-2"></i>热门搜索
        </h3>
        <button onclick="refreshHotKeywords()">刷新</button>
    </div>
    
    <!-- 加载状态 -->
    <div id="hotKeywordsLoading">加载中...</div>
    
    <!-- 热门搜索列表 -->
    <div id="hotKeywordsList">
        <!-- 动态生成 -->
    </div>
    
    <!-- 错误状态 -->
    <div id="hotKeywordsError">加载失败</div>
</div>
```

#### B. 核心功能
```javascript
// 加载热门搜索
async function loadHotKeywords() {
    try {
        const response = await fetch('/m/api/search/hot-keywords');
        const result = await response.json();
        
        if (result.code === 0) {
            hotKeywords = result.data.keywords;
            renderHotKeywords(hotKeywords);
        }
    } catch (error) {
        // 使用默认数据
        hotKeywords = getDefaultHotKeywords();
        renderHotKeywords(hotKeywords);
    }
}

// 渲染热门搜索
function renderHotKeywords(keywords) {
    keywords.forEach((item, index) => {
        const keywordDiv = createKeywordElement(item, index);
        list.appendChild(keywordDiv);
    });
}
```

## 功能特性

### 1. 排名显示
- **前三名**: 使用红色、橙色、黄色突出显示
- **其他排名**: 使用灰色显示
- **排名数字**: 圆形背景，白色数字

### 2. 趋势指示
- **上升趋势**: 红色向上箭头 ↑
- **下降趋势**: 绿色向下箭头 ↓  
- **稳定趋势**: 灰色横线 —

### 3. 标签系统
- **热门标签**: 红色"热"标签
- **新增标签**: 橙色"新"标签
- **搜索次数**: 格式化显示（1.2k等）

### 4. 交互功能
- **点击搜索**: 点击关键词自动填入搜索框并搜索
- **悬停效果**: 鼠标悬停时背景色变化
- **刷新功能**: 手动刷新热门搜索列表

## 视觉设计

### 1. 排名颜色方案
```css
/* 第1名 */
.bg-red-500 { background-color: #ef4444; }

/* 第2名 */
.bg-orange-500 { background-color: #f97316; }

/* 第3名 */
.bg-yellow-500 { background-color: #eab308; }

/* 其他排名 */
.bg-gray-500 { background-color: #6b7280; }
```

### 2. 趋势图标
```html
<!-- 上升 -->
<i class="fas fa-arrow-up text-red-500"></i>

<!-- 下降 -->
<i class="fas fa-arrow-down text-green-500"></i>

<!-- 稳定 -->
<i class="fas fa-minus text-gray-400"></i>
```

### 3. 标签样式
```html
<!-- 热门标签 -->
<span class="text-xs text-red-500 bg-red-50 px-1 rounded">热</span>

<!-- 新增标签 -->
<span class="text-xs text-orange-500 bg-orange-50 px-1 rounded">新</span>
```

## 数据处理

### 1. 默认数据
```javascript
function getDefaultHotKeywords() {
    return [
        { keyword: "智慧城市建设", search_count: 1250, trend: "up", is_hot: true },
        { keyword: "医院设备采购", search_count: 980, trend: "up", is_new: true },
        { keyword: "学校装修工程", search_count: 856, trend: "stable" },
        { keyword: "道路建设项目", search_count: 742, trend: "down" },
        { keyword: "环保设备招标", search_count: 698, trend: "up" }
    ];
}
```

### 2. 数据格式化
```javascript
// 搜索次数格式化
function formatSearchCount(count) {
    if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'k';
    }
    return count.toString();
}
```

### 3. 容错处理
- **API失败**: 自动使用默认数据
- **数据缺失**: 提供默认值
- **网络错误**: 显示错误状态和重试按钮

## 统计功能

### 1. 点击统计
```javascript
function trackHotKeywordClick(keyword) {
    console.log('热门搜索点击:', keyword);
    
    // 发送统计数据到后端
    fetch('/m/api/search/track-click', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ keyword, timestamp: Date.now() })
    });
}
```

### 2. 搜索统计
```javascript
function trackSearchKeyword(keyword) {
    // 本地统计
    const searchStats = JSON.parse(localStorage.getItem('searchStats') || '{}');
    searchStats[keyword] = (searchStats[keyword] || 0) + 1;
    localStorage.setItem('searchStats', JSON.stringify(searchStats));
    
    // 服务器统计
    fetch('/m/api/search/track-search', {
        method: 'POST',
        body: JSON.stringify({ keyword })
    });
}
```

## 后端支持

### 1. 数据库设计
```sql
-- 搜索关键词统计表
CREATE TABLE search_keywords (
    id INT PRIMARY KEY AUTO_INCREMENT,
    keyword VARCHAR(100) NOT NULL,
    search_count INT DEFAULT 0,
    click_count INT DEFAULT 0,
    trend ENUM('up', 'down', 'stable') DEFAULT 'stable',
    is_hot BOOLEAN DEFAULT FALSE,
    is_new BOOLEAN DEFAULT FALSE,
    category VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_keyword (keyword),
    INDEX idx_search_count (search_count DESC)
);
```

### 2. API实现
```go
// 获取热门搜索关键词
func GetHotKeywords(ctx context.Context) ([]HotKeyword, error) {
    var keywords []HotKeyword
    
    err := dao.SearchKeywords.Ctx(ctx).
        Where("search_count > 0").
        OrderBy("search_count DESC, click_count DESC").
        Limit(10).
        Scan(&keywords)
        
    return keywords, err
}

// 更新搜索统计
func UpdateSearchStats(ctx context.Context, keyword string) error {
    return dao.SearchKeywords.Ctx(ctx).
        Data(g.Map{"search_count": gdb.Raw("search_count + 1")}).
        Where("keyword", keyword).
        UpdateOrInsert()
}
```

### 3. 定时任务
```go
// 定时更新热门搜索趋势
func UpdateHotKeywordsTrend() {
    // 计算过去24小时的搜索增长率
    // 更新趋势标记
    // 标记新增热门关键词
}
```

## 性能优化

### 1. 缓存策略
- **前端缓存**: 热门搜索数据缓存5分钟
- **后端缓存**: Redis缓存热门搜索结果
- **CDN缓存**: 静态资源CDN加速

### 2. 加载优化
- **异步加载**: 不阻塞页面主要功能
- **降级处理**: API失败时使用默认数据
- **懒加载**: 用户滚动到区域时才加载

### 3. 数据优化
- **分页加载**: 只显示前8-10个热门关键词
- **压缩传输**: API响应数据压缩
- **增量更新**: 只更新变化的数据

## 用户体验

### 1. 交互反馈
- **即时响应**: 点击后立即填入搜索框
- **视觉反馈**: 悬停和点击状态变化
- **加载提示**: 清晰的加载状态指示

### 2. 个性化
- **历史偏好**: 结合用户搜索历史
- **地域相关**: 根据用户城市推荐
- **分类筛选**: 按行业分类显示

### 3. 智能推荐
- **相关推荐**: 基于当前搜索推荐相关词
- **热度排序**: 按搜索热度和趋势排序
- **时效性**: 及时更新热门趋势

---

**功能状态**: ✅ 已完成  
**支持特性**: 动态加载、趋势显示、点击统计  
**文档版本**: v1.0  
**最后更新**: 2025-01-23
