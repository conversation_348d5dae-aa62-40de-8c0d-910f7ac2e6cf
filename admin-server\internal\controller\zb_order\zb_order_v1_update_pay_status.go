package zb_order

import (
	"context"
	v1 "admin-server/api/zb_order/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) UpdatePayStatus(ctx context.Context, req *v1.UpdatePayStatusReq) (res *v1.UpdatePayStatusRes, err error) {
	err = service.ZbOrder().UpdatePayStatus(ctx, req)
	if err != nil {
		return &v1.UpdatePayStatusRes{Success: false}, err
	}
	
	return &v1.UpdatePayStatusRes{Success: true}, nil
}
