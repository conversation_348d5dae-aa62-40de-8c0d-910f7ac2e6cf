<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>招标详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="http://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            -webkit-overflow-scrolling: touch;
        }
        .phone-container {
            min-height: 100vh;
            position: relative;
            background: white;
            -webkit-overflow-scrolling: touch;
        }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }

        /* 新增样式 */
        .section-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            overflow: hidden;
            margin-bottom: 16px;
        }

        .section-header {
            background: linear-gradient(135deg, var(--header-color-1), var(--header-color-2));
            padding: 16px 20px;
            position: relative;
        }

        .section-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100%;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            transform: skewX(-15deg);
            transform-origin: top right;
        }

        .section-title {
            color: white;
            font-weight: 600;
            font-size: 15px;
            display: flex;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .section-icon {
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            backdrop-filter: blur(10px);
        }

        .field-card {
            background: white;
            border: 1px solid #f1f5f9;
            border-radius: 12px;
            padding: 12px 16px;
            margin-bottom: 10px;
            position: relative;
            transition: all 0.2s ease;
        }

        .field-card:hover {
            border-color: #e2e8f0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            transform: translateY(-1px);
        }

        .field-card::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--accent-color);
            border-radius: 0 2px 2px 0;
        }

        .field-label {
            color: #374151;
            font-weight: 600;
            font-size: 13px;
            margin-bottom: 6px;
            display: flex;
            align-items: center;
        }

        .field-label::before {
            content: '●';
            color: var(--accent-color);
            margin-right: 8px;
            font-size: 8px;
        }

        .field-content {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.6;
        }

        .title-section {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-bottom: 1px solid #e2e8f0;
        }

        .article-title {
            background: linear-gradient(135deg, #1e293b, #334155);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .tag-gradient {
            background: linear-gradient(135deg, var(--tag-color-1), var(--tag-color-2));
            color: white;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .mini-capsule {
            position: absolute;
            top: 8px;
            right: 12px;
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .content-section { border-bottom: 1px solid #f3f4f6; }
        .vip-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .vip-modal-content {
            background: white;
            border-radius: 24px;
            padding: 32px 24px;
            margin: 20px;
            text-align: center;
            max-width: 340px;
            width: 100%;
            animation: modalSlideIn 0.3s ease-out;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            border: 1px solid rgba(255,255,255,0.2);
        }
        @keyframes modalSlideIn {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        /* 确保页面可以正常滚动 */
        html {
            height: 100%;
            -webkit-text-size-adjust: 100%;
        }

        * {
            -webkit-tap-highlight-color: transparent;
        }

        /* 修复iOS滚动问题 */
        .phone-container {
            position: relative;
            overflow: visible;
        }
        .vip-crown {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
        }
    </style>
</head>
<body>
<div class="phone-container">

    <!-- 顶部导航 -->
    <div class="gradient-bg px-4 pt-4 pb-4">
        <div class="flex items-center space-x-3">
            <h1 class="text-white text-lg font-semibold flex-1">招标详情</h1>
        </div>
    </div>

    <!-- 内容区域 -->
    <div class="bg-white flex-1 overflow-y-auto">

        <!-- 标题区域 -->
        <div class="title-section px-4 py-6">
            <div class="flex items-start justify-between mb-4">
                <div class="flex flex-wrap gap-2" id="categoryTags">
                    <!-- 动态加载的分类标签 -->
                </div>
                <div class="flex items-center text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                    <i class="fas fa-clock mr-1"></i>
                    <span id="publishTime">加载中...</span>
                </div>
            </div>
            <h2 class="article-title text-lg font-bold mb-4 leading-relaxed" id="articleTitle">加载中...</h2>
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 text-xs text-gray-600">
                    <div class="flex items-center bg-blue-50 px-3 py-1 rounded-full">
                        <i class="fas fa-eye text-blue-500 mr-1"></i>
                        <span id="viewCount">0</span>
                    </div>
                    <div class="flex items-center bg-green-50 px-3 py-1 rounded-full">
                        <i class="fas fa-user text-green-500 mr-1"></i>
                        <span id="authorName">加载中...</span>
                    </div>
                </div>
            </div>
        </div>



        <!-- 动态内容容器 -->
        <div id="dynamicContent" class="pt-4">
            <!-- 根据结构化数据动态生成的内容 -->
        </div>




    </div>

    <!-- VIP弹窗 -->
    {{if eq .is_vip 0}}
    <div id="vipModal" class="vip-modal">
        <div class="vip-modal-content">
            <div class="vip-crown">
                <i class="fas fa-crown text-white text-3xl"></i>
            </div>
            <h3 class="text-lg font-bold text-gray-800 mb-3">开通VIP会员</h3>
            <p class="text-gray-600 text-sm mb-6 leading-relaxed">
                查看完整招标详情需要开通VIP会员<br>
                解锁全部功能，获取更多商机
            </p>


            <!-- 操作按钮 -->
            <button class="w-full bg-gradient-to-r from-yellow-400 via-yellow-500 to-orange-500 text-white py-4 rounded-2xl text-base font-semibold mb-3 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 flex items-center justify-center" onclick="goToMemberPage()">
                <i class="fas fa-crown mr-2"></i>
                立即开通VIP会员
            </button>
            <p class="text-xs text-gray-500">开通即可查看完整招标信息</p>
        </div>
    </div>
    {{end}}

    <!-- 登录弹窗 -->
    {{if not .is_logged_in}}
        <div id="loginModal" class="vip-modal">
        <div class="vip-modal-content">
            <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-user text-white text-2xl"></i>
            </div>
            <h3 class="text-lg font-bold text-gray-800 mb-3">请先登录</h3>
            <p class="text-gray-600 text-sm mb-6 leading-relaxed">
                登录后即可查看招标详情<br>
                享受更多专业服务
            </p>

            <!-- 登录优势 -->
            <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 mb-6">
                <div class="space-y-2">
                    <div class="flex items-center text-sm text-gray-700">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>免费查看基础信息</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-700">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>保存浏览记录</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-700">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>个性化推荐</span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <button class="w-full bg-gradient-to-r from-blue-500 via-blue-600 to-purple-600 text-white py-4 rounded-2xl text-base font-semibold mb-3 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 flex items-center justify-center" onclick="goToLogin()">
                <i class="fas fa-sign-in-alt mr-2"></i>
                立即登录
            </button>
            <p class="text-xs text-gray-500">登录即可享受更多服务</p>
        </div>
    </div>
    {{end}}
</div>

<script>
    var articleContent = {{json .content}}

    // 页面加载完成后渲染数据
    document.addEventListener('DOMContentLoaded', function() {
        renderArticleData();
        // 加载微信JSSDK配置
        loadJssdkConfig();
    });

    // 渲染文章数据
    function renderArticleData() {
        if (!articleContent) {
            console.error('文章数据为空');
            return;
        }

        console.log('文章数据:', articleContent);

        // 解析content字段
        let parsedContent = null;
        if (articleContent.content) {
            try {
                parsedContent = JSON.parse(articleContent.content);
                console.log('解析后的内容:', parsedContent);
            } catch (error) {
                console.error('解析content字段失败:', error);
            }
        }

        // 渲染标题区域
        renderTitleSection();

        // 渲染动态内容（使用解析后的内容）
        renderDynamicContent(parsedContent);

        // 更新分享配置
        updateShareConfig();
    }

    // 渲染标题区域
    function renderTitleSection() {
        // 设置标题
        const titleElement = document.getElementById('articleTitle');
        if (titleElement && articleContent.title) {
            titleElement.textContent = articleContent.title;
        }

        // 设置浏览次数（注意字段名是viewCount而不是view_count）
        const viewCountElement = document.getElementById('viewCount');
        if (viewCountElement) {
            viewCountElement.textContent = articleContent.viewCount || 0;
        }

        // 设置作者
        const authorElement = document.getElementById('authorName');
        if (authorElement && articleContent.author) {
            authorElement.textContent = articleContent.author;
        }

        // 设置发布时间
        const publishTimeElement = document.getElementById('publishTime');
        if (publishTimeElement && articleContent.created_at) {
            publishTimeElement.textContent = formatTimeAgo(articleContent.created_at);
        }

        // 渲染分类标签
        renderCategoryTags();
    }

    // 渲染分类标签
    function renderCategoryTags() {
        const categoryTagsElement = document.getElementById('categoryTags');
        if (!categoryTagsElement) return;

        categoryTagsElement.innerHTML = '';

        // 添加分类标签
        if (articleContent.cate_name) {
            const cateTag = document.createElement('span');
            cateTag.className = 'tag-gradient px-3 py-1 rounded-full text-xs font-medium';
            cateTag.style.setProperty('--tag-color-1', '#3b82f6');
            cateTag.style.setProperty('--tag-color-2', '#1d4ed8');
            cateTag.textContent = articleContent.cate_name;
            categoryTagsElement.appendChild(cateTag);
        }

        // 添加城市标签
        if (articleContent.city_name) {
            const cityTag = document.createElement('span');
            cityTag.className = 'tag-gradient px-3 py-1 rounded-full text-xs font-medium';
            cityTag.style.setProperty('--tag-color-1', '#10b981');
            cityTag.style.setProperty('--tag-color-2', '#059669');
            cityTag.textContent = articleContent.city_name;
            categoryTagsElement.appendChild(cityTag);
        }
    }



    // 渲染动态内容
    function renderDynamicContent(parsedContent) {
        const contentElement = document.getElementById('dynamicContent');
        if (!contentElement) return;

        contentElement.innerHTML = '';

        if (parsedContent && Array.isArray(parsedContent)) {
            // 渲染所有结构化内容部分
            parsedContent.forEach((section, sectionIndex) => {
                // 创建章节容器
                const sectionDiv = document.createElement('div');
                sectionDiv.className = 'section-card mx-4';

                // 根据章节类型设置不同的图标和颜色
                let iconClass = 'fas fa-info-circle';
                let headerColor1 = '#3b82f6';
                let headerColor2 = '#1d4ed8';
                let accentColor = '#3b82f6';

                if (section.title.includes('概况')) {
                    iconClass = 'fas fa-file-alt';
                    headerColor1 = '#10b981';
                    headerColor2 = '#059669';
                    accentColor = '#10b981';
                } else if (section.title.includes('要求')) {
                    iconClass = 'fas fa-clipboard-check';
                    headerColor1 = '#f59e0b';
                    headerColor2 = '#d97706';
                    accentColor = '#f59e0b';
                } else if (section.title.includes('文件')) {
                    iconClass = 'fas fa-file-download';
                    headerColor1 = '#8b5cf6';
                    headerColor2 = '#7c3aed';
                    accentColor = '#8b5cf6';
                } else if (section.title.includes('联系')) {
                    iconClass = 'fas fa-phone';
                    headerColor1 = '#10b981';
                    headerColor2 = '#059669';
                    accentColor = '#10b981';
                } else if (section.title.includes('踏勘')) {
                    iconClass = 'fas fa-map-marker-alt';
                    headerColor1 = '#ef4444';
                    headerColor2 = '#dc2626';
                    accentColor = '#ef4444';
                }

                // 创建章节头部
                const sectionHeader = document.createElement('div');
                sectionHeader.className = 'section-header';
                sectionHeader.style.setProperty('--header-color-1', headerColor1);
                sectionHeader.style.setProperty('--header-color-2', headerColor2);

                const sectionTitle = document.createElement('div');
                sectionTitle.className = 'section-title';
                sectionTitle.innerHTML = `
                    <div class="section-icon">
                        <i class="${iconClass}"></i>
                    </div>
                    ${section.title}
                `;

                sectionHeader.appendChild(sectionTitle);
                sectionDiv.appendChild(sectionHeader);

                // 创建章节内容容器
                const sectionContent = document.createElement('div');
                sectionContent.className = 'p-4';

                // 创建字段容器
                const fieldsContainer = document.createElement('div');
                fieldsContainer.className = '';

                if (section.fields && Array.isArray(section.fields)) {
                    section.fields.forEach((field, fieldIndex) => {
                        if (field.value && field.value.trim()) {
                            const fieldDiv = document.createElement('div');
                            fieldDiv.className = 'field-card';
                            fieldDiv.style.setProperty('--accent-color', accentColor);

                            // 创建字段标题
                            const fieldLabel = document.createElement('div');
                            fieldLabel.className = 'field-label';
                            fieldLabel.style.setProperty('--accent-color', accentColor);
                            fieldLabel.textContent = field.label;

                            // 创建字段内容
                            const fieldContent = document.createElement('div');
                            fieldContent.className = 'field-content';

                            // 处理字段值，支持换行和列表格式
                            const value = field.value.trim();
                            if (value.includes('\n\n')) {
                                // 如果包含双换行，按段落分割
                                const paragraphs = value.split('\n\n');
                                paragraphs.forEach((paragraph, pIndex) => {
                                    if (paragraph.trim()) {
                                        const p = document.createElement('p');
                                        p.className = pIndex > 0 ? 'mt-2' : '';

                                        // 处理段落内的换行
                                        if (paragraph.includes('\n')) {
                                            const lines = paragraph.split('\n');
                                            lines.forEach((line, lIndex) => {
                                                if (line.trim()) {
                                                    if (lIndex > 0) {
                                                        p.appendChild(document.createElement('br'));
                                                    }
                                                    p.appendChild(document.createTextNode(line.trim()));
                                                }
                                            });
                                        } else {
                                            p.textContent = paragraph.trim();
                                        }

                                        fieldContent.appendChild(p);
                                    }
                                });
                            } else if (value.includes('\n')) {
                                // 如果包含换行，按行分割
                                const lines = value.split('\n');
                                lines.forEach((line, lIndex) => {
                                    if (line.trim()) {
                                        if (lIndex > 0) {
                                            fieldContent.appendChild(document.createElement('br'));
                                        }
                                        fieldContent.appendChild(document.createTextNode(line.trim()));
                                    }
                                });
                            } else {
                                // 单行内容
                                fieldContent.textContent = value;
                            }

                            fieldDiv.appendChild(fieldLabel);
                            fieldDiv.appendChild(fieldContent);
                            fieldsContainer.appendChild(fieldDiv);
                        }
                    });
                }

                sectionContent.appendChild(fieldsContainer);
                sectionDiv.appendChild(sectionContent);
                contentElement.appendChild(sectionDiv);
            });
        } else {
            // 如果没有结构化内容，显示原始内容或默认提示
            if (articleContent.content) {
                const rawContentDiv = document.createElement('div');
                rawContentDiv.className = 'text-sm text-gray-700 leading-relaxed';
                rawContentDiv.textContent = articleContent.content;
                contentElement.appendChild(rawContentDiv);
            } else {
                contentElement.innerHTML = '<p class="text-gray-500 text-center py-4">暂无详细内容</p>';
            }
        }
    }

    // 格式化时间
    function formatTimeAgo(dateString) {
        if (!dateString) return '未知时间';

        const now = new Date();
        const date = new Date(dateString);
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffMins < 1) return '刚刚发布';
        if (diffMins < 60) return `${diffMins}分钟前`;
        if (diffHours < 24) return `${diffHours}小时前`;
        if (diffDays < 7) return `${diffDays}天前`;

        return date.toLocaleDateString('zh-CN');
    }

    // 格式化日期时间
    function formatDateTime(dateString) {
        if (!dateString) return '';

        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // 更新分享配置
    function updateShareConfig() {
        // 更新页面标题
        if (articleContent.title) {
            document.title = articleContent.title + ' - 招标详情';
        }
    }

    function goToMemberPage() {
        // 跳转到会员开通页面
        window.location.href = '/m/vip';
    }



    // 微信登录
    function goToLogin() {
        // 获取当前页面URL作为回调地址
        const currentURL = window.location.pathname + window.location.search;
        // 跳转到微信授权登录
        window.location.href = '/m/auth/login?callback=' + encodeURIComponent(currentURL);
    }

    // ==================== 微信分享功能 ====================

    // 加载分享jssdk配置
    async function loadJssdkConfig() {
        try {
            // 获取当前页面的URL，iPhone需要特殊处理
            const currentUrl = window.location.href;

            console.log('正在获取JSSDK配置，当前URL:', currentUrl);

            // 请求JSSDK配置
            const response = await fetch('/m/api/get_wechat_jssdk', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    url: currentUrl
                })
            });

            const result = await response.json();

            if (result.code === 0 && result.data && result.data.signature) {
                const config = result.data.signature;

                console.log('JSSDK配置获取成功:', config);

                // iPhone兼容性：扩展jsApiList包含旧版API
                let jsApiList = config.jsApiList || [
                    'updateAppMessageShareData',
                    'updateTimelineShareData'
                ];

                // 配置微信JSSDK
                wx.config({
                    debug: config.debug || false,
                    appId: config.appId,
                    timestamp: config.timestamp,
                    nonceStr: config.nonceStr,
                    signature: config.signature,
                    jsApiList: jsApiList,
                    openTagList: config.openTagList || []
                });

                console.log('微信JSSDK配置完成');

            } else {
                console.error('JSSDK配置数据格式错误:', result);
                throw new Error(result.message || 'JSSDK配置获取失败');
            }

        } catch (error) {
            console.error('加载JSSDK配置失败:', error);
        }
    }

    // 微信JSSDK配置成功回调
    wx.ready(function () {
        console.log('微信JSSDK初始化成功');

        // 获取当前页面URL，iPhone需要特殊处理
        let shareUrl = window.location.href;

        console.log('分享URL:', shareUrl);

        // 分享配置对象
        const shareConfig = {
            title: articleContent && articleContent.title ? articleContent.title : '招标详情 - 最新招标信息',
            desc: getShareDescription(),
            link: shareUrl,
            imgUrl: '', // 可以设置默认分享图片
            success: function () {
                console.log('分享配置成功');
            },
            fail: function (error) {
                console.error('分享配置失败:', error);
            }
        };

        // 获取分享描述
        function getShareDescription() {
            if (!articleContent) return '查看详细的招标信息，把握商机不错过';

            // 尝试从结构化内容中获取项目内容作为描述
            if (articleContent.content) {
                try {
                    const parsedContent = JSON.parse(articleContent.content);
                    if (Array.isArray(parsedContent)) {
                        const basicInfo = parsedContent.find(section => section.title === '基础信息');
                        if (basicInfo && basicInfo.fields) {
                            const projectContent = basicInfo.fields.find(field =>
                                field.label.includes('项目内容')
                            );
                            if (projectContent && projectContent.value) {
                                // 截取前100个字符作为描述
                                const desc = projectContent.value.trim().substring(0, 100);
                                return desc + (projectContent.value.length > 100 ? '...' : '');
                            }
                        }
                    }
                } catch (error) {
                    console.error('解析分享描述失败:', error);
                }
            }

            // 默认描述
            return `${articleContent.cate_name || '招标信息'} - ${articleContent.city_name || ''}，查看详细信息`;
        }

        // 设置分享到朋友圈的内容
        wx.updateTimelineShareData({
            title: shareConfig.title,
            link: shareConfig.link,
            imgUrl: shareConfig.imgUrl,
            success: function () {
                console.log('朋友圈分享配置成功');
            },
            fail: function (error) {
                console.error('朋友圈分享配置失败:', error);
            }
        });

        // 设置分享给朋友的内容
        wx.updateAppMessageShareData({
            title: shareConfig.title,
            desc: shareConfig.desc,
            link: shareConfig.link,
            imgUrl: shareConfig.imgUrl,
            success: function () {
                console.log('好友分享配置成功');
            },
            fail: function (error) {
                console.error('好友分享配置失败:', error);
            }
        });
    });

    // 微信JSSDK配置失败回调
    wx.error(function (res) {
        console.error('微信JSSDK配置失败:', res);
        // 通用错误处理
        console.log('JSSDK配置失败，错误详情:', {
            errMsg: res.errMsg,
            userAgent: navigator.userAgent,
            url: window.location.href
        });
    });


</script>
</body>
</html>
