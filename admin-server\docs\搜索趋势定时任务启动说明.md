# 搜索趋势定时任务启动说明

## 修改概述

已成功将搜索趋势定时任务集成到应用启动流程中，定时任务将在服务器启动时自动初始化并开始运行。

## 修改步骤

### 1. 添加包导入
在 `admin-server/internal/cmd/cmd.go` 文件中添加task包导入：

```go
import (
    // ... 其他导入
    "admin-server/internal/middleware"
    "admin-server/internal/task"  // 新增
    "context"
    // ...
)
```

### 2. 初始化定时任务
在服务器启动前添加定时任务初始化：

```go
// 404
s.BindStatusHandler(404, middleware.NotFound)

// 初始化定时任务
task.InitSearchTrendTask()  // 新增

s.Run()
```

## 定时任务详情

### 1. 任务文件位置
```
admin-server/internal/task/search_trend.go
```

### 2. 任务功能
- **执行频率**: 每小时执行一次 (`0 0 * * * *`)
- **主要功能**: 更新热门搜索关键词的趋势和热门标记
- **具体操作**:
  - 重置所有关键词的热门标记
  - 标记搜索次数前3名为热门
  - 记录执行日志

### 3. 任务代码
```go
package task

import (
    "context"
    "admin-server/internal/service"
    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/os/gcron"
)

// 初始化搜索趋势更新定时任务
func InitSearchTrendTask() {
    // 每小时更新一次热门搜索趋势
    _, err := gcron.Add(context.Background(), "0 0 * * * *", func(ctx context.Context) {
        updateSearchTrend(ctx)
    })
    
    if err != nil {
        g.Log().Error(context.Background(), "搜索趋势定时任务启动失败:", err)
    } else {
        g.Log().Info(context.Background(), "搜索趋势定时任务启动成功")
    }
}

// 更新搜索趋势
func updateSearchTrend(ctx context.Context) {
    g.Log().Info(ctx, "开始更新搜索趋势...")
    
    err := service.Search().UpdateKeywordTrend(ctx)
    if err != nil {
        g.Log().Error(ctx, "更新搜索趋势失败:", err)
    } else {
        g.Log().Info(ctx, "搜索趋势更新完成")
    }
}
```

## 启动流程

### 1. 应用启动顺序
```
1. 应用启动
2. 数据库连接初始化
3. 路由注册
4. 中间件配置
5. 定时任务初始化 ← 新增步骤
6. HTTP服务器启动
```

### 2. 定时任务启动日志
启动成功时会看到以下日志：
```
[INFO] 搜索趋势定时任务启动成功
```

启动失败时会看到：
```
[ERROR] 搜索趋势定时任务启动失败: [错误信息]
```

### 3. 定时执行日志
每小时执行时会看到：
```
[INFO] 开始更新搜索趋势...
[INFO] 搜索趋势更新完成
```

或者出错时：
```
[INFO] 开始更新搜索趋势...
[ERROR] 更新搜索趋势失败: [错误信息]
```

## 测试验证

### 1. 启动验证
启动应用后检查日志，确认看到：
```
[INFO] 搜索趋势定时任务启动成功
```

### 2. 功能验证
等待一小时后，或者手动触发，检查数据库：
```sql
-- 查看热门标记是否正确更新
SELECT keyword, search_count, is_hot, updated_at 
FROM zb_search_keywords 
ORDER BY search_count DESC 
LIMIT 10;
```

### 3. 手动测试
可以创建一个测试接口来手动触发趋势更新：
```go
// 在controller中添加测试接口
func (c *ControllerV1) UpdateTrend(ctx context.Context, req *v1.UpdateTrendReq) (res *v1.UpdateTrendRes, err error) {
    err = service.Search().UpdateKeywordTrend(ctx)
    return &v1.UpdateTrendRes{Success: err == nil}, err
}
```

## Cron表达式说明

### 1. 当前配置
```
"0 0 * * * *"  // 每小时的0分0秒执行
```

### 2. 其他可选配置
```
"0 0 0 * * *"   // 每天0点执行
"0 0 */6 * * *" // 每6小时执行
"0 30 * * * *"  // 每小时的30分执行
"0 0 0 * * 1"   // 每周一0点执行
```

### 3. 格式说明
```
秒 分 时 日 月 周
*  *  *  *  *  *
```

## 性能考虑

### 1. 执行频率
- **当前**: 每小时执行一次
- **建议**: 根据数据量和业务需求调整
- **高频场景**: 可改为每30分钟或每15分钟
- **低频场景**: 可改为每天或每周执行

### 2. 数据库影响
- **操作类型**: UPDATE操作，影响较小
- **锁定时间**: 很短，不会影响正常查询
- **建议**: 在业务低峰期执行

### 3. 错误处理
- **失败重试**: 当前没有重试机制
- **错误通知**: 只记录日志，可扩展为邮件/短信通知
- **监控**: 可添加监控指标

## 扩展功能

### 1. 复杂趋势计算
```go
// 可以扩展为更复杂的趋势计算
func calculateTrend(ctx context.Context, keyword string) string {
    // 比较过去24小时和前24小时的搜索量
    // 计算增长率
    // 返回 "up", "down", "stable"
}
```

### 2. 多维度分析
```go
// 按城市、分类等维度分析热门搜索
func updateTrendByCategory(ctx context.Context) error {
    // 按分类更新热门搜索
}
```

### 3. 实时更新
```go
// 可以添加实时更新机制
func updateTrendRealtime(ctx context.Context, keyword string) {
    // 实时更新单个关键词的趋势
}
```

## 故障排除

### 1. 定时任务未启动
- 检查导入是否正确
- 检查初始化调用是否在正确位置
- 查看启动日志

### 2. 定时任务不执行
- 检查Cron表达式是否正确
- 查看系统时间是否正确
- 检查数据库连接是否正常

### 3. 执行出错
- 查看错误日志
- 检查数据库权限
- 验证业务逻辑

---

**集成状态**: ✅ 已完成  
**执行频率**: 每小时一次  
**启动方式**: 应用启动时自动初始化  
**文档版本**: v1.0  
**完成时间**: 2025-01-23
