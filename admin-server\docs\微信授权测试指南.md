# 微信授权测试指南

## 修复内容

已修复了以下编译错误：

### 1. Session 类型转换问题
**问题**: `r.Session.Get()` 返回 `(*gvar.Var, error)` 两个值，但代码只接收了一个值
**修复**: 使用 `r.Session.MustGet()` 方法，只返回一个 `*gvar.Var` 值

**修复前**:
```go
sessionUser, _ := r.Session.Get("wechat_user")
if sessionUser != nil {
    // ...
}
```

**修复后**:
```go
sessionUser := r.Session.MustGet("wechat_user")
if sessionUser != nil && !sessionUser.IsNil() {
    // ...
}
```

### 2. 类型断言问题
**问题**: `*gvar.Var` 类型不能直接进行类型断言
**修复**: 使用 `.Interface()` 方法获取底层值后再进行类型断言

**修复前**:
```go
if userMap, ok := sessionUser.(map[string]interface{}); ok {
    return userMap
}
```

**修复后**:
```go
if userMap, ok := sessionUser.Interface().(map[string]interface{}); ok {
    return userMap
}
```

## 测试准备

### 1. 配置文件
在 `config.yaml` 中添加微信配置：

```yaml
wechat:
  appid: "your_wechat_appid"
  secret: "your_wechat_secret"
  token: "your_wechat_token"
  aes_key: "your_wechat_aes_key"  # 可选
```

### 2. 数据库检查
确保 `zb_user` 表存在且结构正确：

```sql
-- 检查表结构
DESCRIBE zb_user;

-- 检查是否有测试数据
SELECT * FROM zb_user LIMIT 5;
```

## 测试步骤

### 1. 编译测试
```bash
cd admin-server
go build
```

如果没有编译错误，说明修复成功。

### 2. 启动服务
```bash
./admin-server
```

### 3. 访问测试页面

#### 测试无需授权的页面
访问: `http://localhost:8000/m/list`
- 应该能正常显示列表页面
- 不会跳转到微信授权

#### 测试需要授权的页面
访问: `http://localhost:8000/m/detail`
- 如果未配置微信或在非微信环境，会显示错误
- 如果配置正确，会跳转到微信授权页面

## 本地开发测试

由于微信授权需要在微信环境中测试，本地开发时可以：

### 1. 模拟用户信息
在控制器中临时添加模拟用户：

```go
// 在 Detail 方法中添加（仅用于测试）
func (c *ControllerMobile) Detail(r *ghttp.Request) {
    // 模拟用户信息（仅用于本地测试）
    if r.Header.Get("User-Agent") != "" && !strings.Contains(r.Header.Get("User-Agent"), "MicroMessenger") {
        // 非微信环境，使用模拟数据
        mockUser := map[string]interface{}{
            "id":               1,
            "nickname":         "测试用户",
            "avatar":           "https://example.com/avatar.jpg",
            "openid":           "test_openid_123",
            "effective_status": 1,
            "is_new_user":      false,
        }
        r.Session.Set("wechat_user", mockUser)
    }
    
    // 原有逻辑...
}
```

### 2. 跳过授权中间件
在路由配置中临时注释掉授权中间件：

```go
// 临时注释掉授权中间件用于测试
// authGroup.Middleware(middleware.WechatAuth())
authGroup.GET("/detail", mobileController.Detail)
```

## 生产环境测试

### 1. 域名配置
在微信公众平台配置网页授权域名：
- 登录微信公众平台
- 设置与开发 -> 公众号设置 -> 功能设置
- 网页授权域名：填入您的域名（不包含http://）

### 2. 测试流程
1. 在微信中访问 `https://yourdomain.com/m/detail`
2. 系统会跳转到微信授权页面
3. 用户确认授权后返回原页面
4. 检查数据库是否创建了用户记录

### 3. 日志检查
查看应用日志，确认授权流程：

```bash
# 查看日志
tail -f logs/app.log

# 关键日志信息：
# - 微信授权URL构建
# - 用户信息获取
# - 用户注册/登录
# - Session存储
```

## 调试技巧

### 1. 添加调试日志
在关键位置添加日志：

```go
// 在中间件中添加
g.Log().Info(ctx, "微信授权检查", g.Map{
    "has_code": code != "",
    "has_session": sessionUser != nil && !sessionUser.IsNil(),
    "current_url": currentURL,
})
```

### 2. 检查Session
```go
// 在控制器中检查session
sessionData := r.Session.GetMap()
g.Log().Info(r.GetCtx(), "Session数据:", sessionData)
```

### 3. 数据库查询
```sql
-- 查看最新注册的用户
SELECT * FROM zb_user ORDER BY created_at DESC LIMIT 10;

-- 查看特定openid的用户
SELECT * FROM zb_user WHERE openid = 'your_test_openid';
```

## 常见问题排查

### 1. 编译错误
- 检查导入路径是否正确
- 确认所有依赖包已安装
- 运行 `go mod tidy` 清理依赖

### 2. 运行时错误
- 检查配置文件格式
- 确认数据库连接正常
- 查看详细错误日志

### 3. 授权失败
- 检查微信配置是否正确
- 确认域名配置是否匹配
- 查看微信API返回的错误信息

### 4. 用户信息获取失败
- 检查access_token是否有效
- 确认用户是否已关注公众号
- 查看微信API调用日志

## 功能验证清单

- [ ] 代码编译无错误
- [ ] 服务启动正常
- [ ] 无需授权页面正常访问
- [ ] 需要授权页面跳转正确
- [ ] 微信授权流程完整
- [ ] 用户信息正确获取
- [ ] 数据库用户记录创建
- [ ] Session信息正确存储
- [ ] 页面模板数据传递正确
- [ ] 错误处理机制有效

## 下一步开发建议

1. **完善错误处理**: 添加更详细的错误提示页面
2. **用户体验优化**: 添加加载状态和友好提示
3. **安全加固**: 添加更多安全验证机制
4. **性能优化**: 实现用户信息缓存
5. **功能扩展**: 添加用户权限管理功能
