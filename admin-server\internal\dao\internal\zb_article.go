// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ZbArticleDao is the data access object for the table zb_article.
type ZbArticleDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  ZbArticleColumns   // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// ZbArticleColumns defines and stores column names for the table zb_article.
type ZbArticleColumns struct {
	Id             string //
	CityId         string // 开通城市id
	CateId         string // 招标类别id
	Title          string // 标题
	Intro          string // 内容简介
	FullContent    string // 完整版内容
	ShieidContent  string // 屏蔽内容
	ViewCount      string // 浏览次数
	SeoTitle       string // SEO标题
	SeoKeywords    string // SEO关键词
	SeoDescription string // SEO描述
	Pic            string // 缩略图
	Uid            string // 发布者id
	Author         string // 作者
	Ip             string // 发布ip
	IsDisable      string // 是否禁用: 0=否, 1=是
	IsDelete       string // 是否删除: 0=否, 1=是
	CreatedAt      string // 创建日期
	UpdatedAt      string // 更新日期
	DeletedAt      string // 删除时间
}

// zbArticleColumns holds the columns for the table zb_article.
var zbArticleColumns = ZbArticleColumns{
	Id:             "id",
	CityId:         "city_id",
	CateId:         "cate_id",
	Title:          "title",
	Intro:          "intro",
	FullContent:    "full_content",
	ShieidContent:  "shieid_content",
	ViewCount:      "view_count",
	SeoTitle:       "seo_title",
	SeoKeywords:    "seo_keywords",
	SeoDescription: "seo_description",
	Pic:            "pic",
	Uid:            "uid",
	Author:         "author",
	Ip:             "ip",
	IsDisable:      "is_disable",
	IsDelete:       "is_delete",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
	DeletedAt:      "deleted_at",
}

// NewZbArticleDao creates and returns a new DAO object for table data access.
func NewZbArticleDao(handlers ...gdb.ModelHandler) *ZbArticleDao {
	return &ZbArticleDao{
		group:    "default",
		table:    "zb_article",
		columns:  zbArticleColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ZbArticleDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ZbArticleDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ZbArticleDao) Columns() ZbArticleColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ZbArticleDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ZbArticleDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ZbArticleDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
