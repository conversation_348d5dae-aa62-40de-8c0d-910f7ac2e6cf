# 字典根据编码获取详情接口说明

## 功能概述

新增了一个根据字典编码（code）获取字典项详情的接口，方便通过字典编码快速查询字典项的完整信息。

## 接口信息

### 请求信息
- **接口路径**: `GET /sys_dict/code/{code}`
- **权限要求**: `system:dict:detail`
- **请求方法**: GET

### 请求参数

| 参数名 | 类型 | 位置 | 必填 | 说明 |
|--------|------|------|------|------|
| code | string | path | 是 | 字典编码 |

### 响应信息

#### 响应字段
- `dict`: 字典项详情对象，如果未找到则为null

#### 字典详情对象字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 主键ID |
| group_id | int64 | 字典分组ID |
| name | string | 字典名称 |
| value | string | 字典值 |
| code | string | 字典编码 |
| sort | int | 排序 |
| is_disable | int | 是否禁用：0=否，1=是 |
| is_system | int | 是否系统保留：0=否，1=是 |
| remark | string | 备注 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

#### 响应示例

**成功找到字典项**
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "dict": {
            "id": 1,
            "group_id": 1,
            "name": "男",
            "value": "1",
            "code": "gender_male",
            "sort": 1,
            "is_disable": 0,
            "is_system": 1,
            "remark": "性别-男",
            "created_at": "2025-01-23T10:00:00Z",
            "updated_at": "2025-01-23T10:00:00Z"
        }
    }
}
```

**未找到字典项**
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "dict": null
    }
}
```

## 技术实现

### 1. API层定义
**文件**: `api/sys_dict/sys_dict.go`
```go
type ISysDictV1 interface {
    // ... 其他方法
    GetByCode(ctx context.Context, req *v1.GetByCodeReq) (res *v1.GetByCodeRes, err error)
}
```

**文件**: `api/sys_dict/v1/sys_dict.go`
```go
// GetByCodeReq 根据字典编码获取字典项详情请求体
type GetByCodeReq struct {
    g.Meta `path:"/sys_dict/code/{code}" tags:"SysDict" method:"get" summary:"根据字典编码获取字典项详情" permission:"system:dict:detail"`
    Code   string `p:"code" v:"required#请输入字典编码" dc:"字典编码"`
}

type GetByCodeRes struct {
    Dict *DictDetail `json:"dict" dc:"字典项详情"`
}
```

### 2. Service层定义
**文件**: `internal/service/sys_dict.go`
```go
type ISysDict interface {
    // ... 其他方法
    GetDictDetailByCode(ctx context.Context, code string) (*entity.SysDict, error)
}
```

### 3. Logic层实现
**文件**: `internal/logic/sysDict/sys_dict.go`
```go
// GetDictDetailByCode 根据字典编码获取字典项详情
func (s *SsysDict) GetDictDetailByCode(ctx context.Context, code string) (*entity.SysDict, error) {
    var dict entity.SysDict
    err := dao.SysDict.Ctx(ctx).Where("code", code).Where("is_delete", packed.NO_DELETE).Scan(&dict)
    if err != nil {
        return nil, err
    }

    if dict.Id == 0 {
        return nil, nil
    }

    return &dict, nil
}
```

### 4. Controller层实现
**文件**: `internal/controller/sys_dict/sys_dict_v1_get_by_code.go`
```go
func (c *ControllerV1) GetByCode(ctx context.Context, req *v1.GetByCodeReq) (res *v1.GetByCodeRes, err error) {
    dict, err := service.SysDict().GetDictDetailByCode(ctx, req.Code)
    if err != nil {
        return nil, err
    }

    if dict == nil {
        return &v1.GetByCodeRes{}, nil
    }

    // 转换为API响应格式
    detail := &v1.DictDetail{
        ID:        dict.Id,
        GroupId:   dict.GroupId,
        Name:      dict.Name,
        Value:     dict.Value,
        Code:      dict.Code,
        Sort:      int(dict.Sort),
        IsDisable: int(dict.IsDisable),
        IsSystem:  int(dict.IsSystem),
        Remark:    dict.Remark,
        CreatedAt: dict.CreatedAt,
        UpdatedAt: dict.UpdatedAt,
    }

    return &v1.GetByCodeRes{
        Dict: detail,
    }, nil
}
```

## 使用场景

### 1. 前端根据编码获取字典项
```javascript
// 根据字典编码获取字典项详情
async function getDictByCode(code) {
    const response = await fetch(`/sys_dict/code/${code}`, {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    });
    const result = await response.json();
    
    if (result.code === 0 && result.data.dict) {
        return result.data.dict;
    }
    return null;
}

// 使用示例
const genderMale = await getDictByCode('gender_male');
if (genderMale) {
    console.log('字典项名称:', genderMale.name);
    console.log('字典项值:', genderMale.value);
}
```

### 2. 配置项查询
```javascript
// 获取系统配置相关的字典项
const systemConfig = await getDictByCode('system_config_type');
const userStatus = await getDictByCode('user_status_active');
```

### 3. 表单选项获取
```javascript
// 根据编码获取表单选项的详细信息
async function getFormOptionDetail(optionCode) {
    const option = await getDictByCode(optionCode);
    return {
        label: option?.name || '未知',
        value: option?.value || '',
        disabled: option?.is_disable === 1
    };
}
```

## 测试用例

### 1. 基础功能测试
```bash
# 测试存在的字典编码
curl -X GET "http://localhost:8000/sys_dict/code/gender_male" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 测试不存在的字典编码
curl -X GET "http://localhost:8000/sys_dict/code/non_existent_code" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 测试空编码（应该返回参数验证错误）
curl -X GET "http://localhost:8000/sys_dict/code/" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. 权限测试
```bash
# 测试无权限访问
curl -X GET "http://localhost:8000/sys_dict/code/gender_male"

# 测试权限不足的用户
curl -X GET "http://localhost:8000/sys_dict/code/gender_male" \
     -H "Authorization: Bearer INVALID_TOKEN"
```

### 3. 数据验证
- 验证返回的字典项信息是否完整
- 检查已删除的字典项是否被正确过滤
- 确认字典编码的唯一性查询

## 与现有接口的区别

### 1. 与GetOne接口的区别
- **GetOne**: 通过ID获取字典项详情
- **GetByCode**: 通过编码获取字典项详情
- **使用场景**: GetByCode更适合业务逻辑中根据编码查询的场景

### 2. 与GetByGroupCode接口的区别
- **GetByGroupCode**: 获取指定分组下的所有字典项列表
- **GetByCode**: 获取单个字典项的详细信息
- **返回格式**: GetByCode返回完整的字典项详情，包含时间戳等信息

## 性能考虑

### 1. 数据库索引
建议为字典编码字段添加索引以提高查询性能：
```sql
-- 如果没有索引，建议添加
ALTER TABLE sys_dict ADD INDEX idx_code (code);
```

### 2. 缓存策略
对于频繁查询的字典项，可以考虑添加缓存：
```go
// 示例：使用Redis缓存字典项
cacheKey := "dict_code_" + code
// 缓存时间可以设置较长，因为字典项变更不频繁
```

### 3. 查询优化
- 使用覆盖索引减少回表查询
- 合理设置查询超时时间
- 监控慢查询日志

## 注意事项

1. **编码唯一性**: 字典编码在系统中应该是唯一的
2. **软删除**: 查询时会自动过滤已删除的字典项
3. **权限控制**: 需要`system:dict:detail`权限才能访问
4. **参数验证**: 字典编码不能为空
5. **错误处理**: 完善数据库查询异常的处理

## 扩展功能

### 1. 批量查询
可以扩展支持批量根据编码查询：
```go
// 批量根据编码获取字典项
GetDictDetailsByCodes(ctx context.Context, codes []string) ([]entity.SysDict, error)
```

### 2. 模糊查询
可以支持编码的模糊查询：
```go
// 根据编码模糊查询字典项
GetDictsByCodeLike(ctx context.Context, codePattern string) ([]entity.SysDict, error)
```

### 3. 缓存版本
可以提供带缓存的版本：
```go
// 带缓存的字典项查询
GetDictDetailByCodeWithCache(ctx context.Context, code string) (*entity.SysDict, error)
```

---

**功能状态**: ✅ 已完成  
**测试状态**: 待测试  
**文档版本**: v1.0  
**最后更新**: 2025-01-23
