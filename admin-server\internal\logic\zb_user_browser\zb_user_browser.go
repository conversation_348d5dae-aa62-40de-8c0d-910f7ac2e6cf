package zb_user_browser

import (
	"context"
	"fmt"
	v1 "admin-server/api/zb_user_browser/v1"
	"admin-server/internal/dao"
	"admin-server/internal/model/entity"
	"admin-server/internal/service"
	"github.com/gogf/gf/v2/frame/g"
)

type sZbUserBrowser struct{}

func init() {
	service.RegisterZbUserBrowser(New())
}

func New() *sZbUserBrowser {
	return &sZbUserBrowser{}
}

// CreateBrowserRecord 创建用户浏览记录
func (s *sZbUserBrowser) CreateBrowserRecord(ctx context.Context, userId int64, articleId int64) error {
	// 检查是否已存在相同的浏览记录（同一用户同一文章）
	count, err := dao.ZbUserBrowser.Ctx(ctx).
		Where("user_id", userId).
		Where("article_id", articleId).
		Count()
	if err != nil {
		return err
	}

	if count > 0 {
		// 如果已存在，更新浏览时间
		_, err = dao.ZbUserBrowser.Ctx(ctx).
			Where("user_id", userId).
			Where("article_id", articleId).
			Data(g.Map{
				"updated_at": "NOW()",
			}).Update()
		if err != nil {
			g.Log().Error(ctx, "更新浏览记录失败:", err)
			return err
		}
		
		g.Log().Info(ctx, "更新浏览记录成功:", g.Map{
			"user_id":    userId,
			"article_id": articleId,
		})
	} else {
		// 如果不存在，创建新记录
		_, err = dao.ZbUserBrowser.Ctx(ctx).Data(g.Map{
			"user_id":    userId,
			"article_id": articleId,
		}).Insert()
		if err != nil {
			g.Log().Error(ctx, "创建浏览记录失败:", err)
			return err
		}
		
		g.Log().Info(ctx, "创建浏览记录成功:", g.Map{
			"user_id":    userId,
			"article_id": articleId,
		})
	}

	return nil
}

// GetMyBrowserList 获取我的浏览记录列表
func (s *sZbUserBrowser) GetMyBrowserList(ctx context.Context, req *v1.GetMyBrowserListReq) (*v1.GetMyBrowserListRes, error) {
	// 根据OpenID获取用户信息
	user, err := service.ZbUser().GetUserByOpenid(ctx, req.OpenId)
	if err != nil {
		g.Log().Error(ctx, "根据OpenID获取用户失败:", err)
		return nil, fmt.Errorf("用户不存在或获取用户信息失败")
	}

	// 构建查询条件
	model := dao.ZbUserBrowser.Ctx(ctx).Where("zb_user_browser.user_id", user.Id)

	// 获取总数
	total, err := model.Count()
	if err != nil {
		return nil, err
	}

	// 关联查询浏览记录、文章信息、分类信息、城市信息
	type BrowserWithInfo struct {
		entity.ZbUserBrowser
		ArticleTitle string `json:"article_title"`
		CategoryName string `json:"category_name"`
		CityName     string `json:"city_name"`
	}

	var browsersWithInfo []BrowserWithInfo
	err = model.Page(req.Page, req.PageSize).
		LeftJoin("zb_article a", "zb_user_browser.article_id = a.id").
		LeftJoin("zb_cate c", "a.cate_id = c.id").
		LeftJoin("zb_city city", "a.city_id = city.id").
		Fields("zb_user_browser.*, a.title as article_title, c.name as category_name, city.name as city_name").
		Order("zb_user_browser.updated_at DESC").
		Scan(&browsersWithInfo)
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	var list []v1.BrowserInfo
	for _, browserWithInfo := range browsersWithInfo {
		browserInfo := v1.BrowserInfo{
			Id:           browserWithInfo.Id,
			ArticleId:    browserWithInfo.ArticleId,
			ArticleTitle: browserWithInfo.ArticleTitle,
			CategoryName: browserWithInfo.CategoryName,
			CityName:     browserWithInfo.CityName,
			BrowseTime:   browserWithInfo.UpdatedAt, // 使用更新时间作为浏览时间
		}
		list = append(list, browserInfo)
	}

	return &v1.GetMyBrowserListRes{
		List:  list,
		Total: total,
	}, nil
}
