# 登录日志系统说明

## 概述

基于 `sys_login_log` 表实现的登录日志记录系统，自动记录管理员登录成功的详细信息，包括IP地址、操作系统、浏览器等信息。

## 🎯 **功能特性**

### 1. 自动记录登录信息
- ✅ 登录成功时自动记录
- ✅ 记录管理员ID和用户名
- ✅ 记录客户端真实IP地址
- ✅ 解析操作系统信息
- ✅ 解析浏览器信息
- ✅ 记录登录时间

### 2. 智能IP获取
- 支持代理环境下的真实IP获取
- 优先级：`X-Forwarded-For` > `X-Real-IP` > `ClientIP` > `RemoteAddr`
- 处理多级代理的IP链

### 3. User-Agent解析
- 自动识别主流操作系统（Windows、Mac、Linux、Android、iOS等）
- 自动识别主流浏览器（Chrome、Firefox、Safari、Edge、IE等）
- 支持版本号识别

## 📋 **数据库表结构**

```sql
CREATE TABLE `sys_login_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `admin_id` bigint NOT NULL COMMENT '管理员id',
  `username` varchar(50) NOT NULL COMMENT '登录账号',
  `ip` varchar(50) DEFAULT NULL COMMENT '登录ip',
  `os` varchar(50) DEFAULT NULL COMMENT '操作系统',
  `browser` varchar(50) DEFAULT NULL COMMENT '浏览器',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_username` (`username`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录日志表';
```

## 🔧 **实现组件**

### 1. 服务层 (`internal/service/sys_login_log.go`)
- `RecordLoginSuccess()` - 记录登录成功日志
- `GetLoginLogList()` - 获取登录日志列表
- `DeleteLoginLog()` - 删除登录日志
- `ClearLoginLog()` - 清空登录日志

### 2. 控制器层 (`internal/controller/sys_login_log/`)
- `GetList()` - 获取登录日志列表API
- `Delete()` - 删除登录日志API
- `Clear()` - 清空登录日志API

### 3. API接口 (`api/sys_login_log/v1/`)
- 定义请求和响应结构体
- 包含权限标识配置

## 🚀 **API接口**

### 1. 获取登录日志列表
```
GET /sys_login_log/list
```

**请求参数：**
```json
{
  "page": 1,
  "page_size": 10,
  "username": "admin"
}
```

**响应数据：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "admin_id": 1,
        "username": "admin",
        "ip": "*************",
        "os": "Windows 10",
        "browser": "Google Chrome",
        "created_at": "2024-01-01 10:00:00"
      }
    ],
    "total": 1
  }
}
```

### 2. 删除登录日志
```
DELETE /sys_login_log/delete
```

**请求参数：**
```json
{
  "ids": [1, 2, 3]
}
```

### 3. 清空登录日志
```
DELETE /sys_login_log/clear
```

## 🛡️ **权限控制**

### 权限标识
- `system:loginlog:list` - 查看登录日志
- `system:loginlog:delete` - 删除登录日志
- `system:loginlog:clear` - 清空登录日志

### 权限初始化
运行SQL脚本初始化权限：
```bash
mysql -u root -p your_database < tools/login_log_permission_init.sql
```

## 📊 **记录时机**

登录日志在以下时机自动记录：

1. **登录成功时** - 在 `sys_auth.go` 的 `Login()` 方法中
2. **更新登录信息后** - 在更新 `last_login_ip` 和 `last_login_time` 之后
3. **JWT生成成功后** - 确保登录流程完全成功

## 🔍 **解析能力**

### 操作系统识别
- Windows (10, 8.1, 8, 7, Vista, XP)
- Mac OS X / Mac OS
- Linux / Ubuntu
- Android
- iOS

### 浏览器识别
- Google Chrome
- Mozilla Firefox
- Safari
- Microsoft Edge
- Internet Explorer
- Opera

### IP地址获取
```go
// 优先级顺序
1. X-Forwarded-For (代理环境)
2. X-Real-IP (反向代理)
3. ClientIP (GoFrame方法)
4. RemoteAddr (连接地址)
```

## 📈 **性能优化**

### 1. 数据库索引
- `idx_admin_id` - 按管理员查询
- `idx_username` - 按用户名查询
- `idx_created_at` - 按时间排序

### 2. 异步记录
- 登录日志记录失败不影响登录流程
- 只记录错误日志，不阻断用户操作

### 3. 批量操作
- 支持批量删除日志
- 支持一键清空所有日志

## 🔧 **配置说明**

### 1. 日志保留策略
可以通过定时任务清理过期日志：

```sql
-- 删除30天前的登录日志
DELETE FROM sys_login_log 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 2. 监控告警
可以基于登录日志实现安全监控：

```sql
-- 检测异常登录（同一用户短时间内多次登录）
SELECT username, ip, COUNT(*) as login_count
FROM sys_login_log 
WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
GROUP BY username, ip
HAVING login_count > 10;
```

## 📝 **使用示例**

### 1. 查看登录日志
```bash
curl -X GET "http://localhost:8000/sys_login_log/list?page=1&page_size=10" \
  -H "Authorization: Bearer your_token"
```

### 2. 删除指定日志
```bash
curl -X DELETE "http://localhost:8000/sys_login_log/delete" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{"ids": [1, 2, 3]}'
```

### 3. 清空所有日志
```bash
curl -X DELETE "http://localhost:8000/sys_login_log/clear" \
  -H "Authorization: Bearer your_token"
```

## 🎉 **优势特点**

1. **自动化** - 无需手动调用，登录成功自动记录
2. **详细信息** - 记录IP、系统、浏览器等完整信息
3. **安全审计** - 便于追踪和分析登录行为
4. **权限控制** - 基于RBAC的访问控制
5. **性能优化** - 异步记录，不影响登录性能
6. **易于扩展** - 可轻松添加更多字段和功能

这个登录日志系统为管理员提供了完整的登录行为追踪和安全审计功能！
