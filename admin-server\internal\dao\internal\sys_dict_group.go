// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SysDictGroupDao is the data access object for the table sys_dict_group.
type SysDictGroupDao struct {
	table    string              // table is the underlying table name of the DAO.
	group    string              // group is the database configuration group name of the current DAO.
	columns  SysDictGroupColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler  // handlers for customized model modification.
}

// SysDictGroupColumns defines and stores column names for the table sys_dict_group.
type SysDictGroupColumns struct {
	Id        string //
	Name      string // 字典分组名称
	Code      string // 字典分组编码
	IsDisable string // 是否禁用: 0=否, 1=是
	IsDelete  string // 是否删除: 0=否, 1=是
	IsSystem  string // 是否系统保留 1是 0否
	Remark    string // 备注
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
	DeletedAt string // 删除时间
}

// sysDictGroupColumns holds the columns for the table sys_dict_group.
var sysDictGroupColumns = SysDictGroupColumns{
	Id:        "id",
	Name:      "name",
	Code:      "code",
	IsDisable: "is_disable",
	IsDelete:  "is_delete",
	IsSystem:  "is_system",
	Remark:    "remark",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
	DeletedAt: "deleted_at",
}

// NewSysDictGroupDao creates and returns a new DAO object for table data access.
func NewSysDictGroupDao(handlers ...gdb.ModelHandler) *SysDictGroupDao {
	return &SysDictGroupDao{
		group:    "default",
		table:    "sys_dict_group",
		columns:  sysDictGroupColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SysDictGroupDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SysDictGroupDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SysDictGroupDao) Columns() SysDictGroupColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SysDictGroupDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SysDictGroupDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SysDictGroupDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
