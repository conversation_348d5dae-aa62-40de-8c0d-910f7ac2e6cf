package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ZbGoodCreateReq 创建套餐请求
type ZbGoodCreateReq struct {
	g.Meta        `path:"/zb_good" method:"post" tags:"ZbGood" summary:"创建套餐" permission:"system:zb_good:add"`
	Name          string  `p:"name" v:"required|length:1,255#套餐名称不能为空|套餐名称长度不能超过255个字符" dc:"套餐名称"`
	Tag           string  `p:"tag" v:"length:0,255#标签长度不能超过255个字符" dc:"套餐标签"`
	OriginalPrice float64 `p:"original_price" v:"required|min:0#原始价格不能为空|原始价格不能小于0" dc:"原始价格"`
	Price         float64 `p:"price" v:"required|min:0#现时价格不能为空|现时价格不能小于0" dc:"现时价格"`
	Effective     int     `p:"effective" v:"required|min:1#有效期不能为空|有效期必须大于0" dc:"会员有效期（月）"`
	IsDisable     int     `p:"is_disable" v:"in:0,1#状态值必须是0或1" dc:"是否禁用：0=否，1=是"`
}

// ZbGoodCreateRes 创建套餐响应
type ZbGoodCreateRes struct {
	Id int `json:"id" dc:"套餐ID"`
}

// ZbGoodUpdateReq 更新套餐请求
type ZbGoodUpdateReq struct {
	g.Meta        `path:"/zb_good/{id}" method:"put" tags:"ZbGood" summary:"更新套餐" permission:"system:zb_good:edit"`
	Id            int     `p:"id" v:"required|min:1#套餐ID不能为空|套餐ID必须大于0" dc:"套餐ID"`
	Name          string  `p:"name" v:"required|length:1,255#套餐名称不能为空|套餐名称长度不能超过255个字符" dc:"套餐名称"`
	Tag           string  `p:"tag" v:"length:0,255#标签长度不能超过255个字符" dc:"套餐标签"`
	OriginalPrice float64 `p:"original_price" v:"required|min:0#原始价格不能为空|原始价格不能小于0" dc:"原始价格"`
	Price         float64 `p:"price" v:"required|min:0#现时价格不能为空|现时价格不能小于0" dc:"现时价格"`
	Effective     int     `p:"effective" v:"required|min:1#有效期不能为空|有效期必须大于0" dc:"会员有效期（月）"`
	IsDisable     int     `p:"is_disable" v:"in:0,1#状态值必须是0或1" dc:"是否禁用：0=否，1=是"`
}

// ZbGoodUpdateRes 更新套餐响应
type ZbGoodUpdateRes struct{}

// ZbGoodDeleteReq 删除套餐请求
type ZbGoodDeleteReq struct {
	g.Meta `path:"/zb_good/{id}" method:"delete" tags:"ZbGood" summary:"删除套餐" permission:"system:zb_good:del"`
	Id     int `p:"id" v:"required|min:1#套餐ID不能为空|套餐ID必须大于0" dc:"套餐ID"`
}

// ZbGoodDeleteRes 删除套餐响应
type ZbGoodDeleteRes struct{}

// ZbGoodGetOneReq 获取单个套餐请求
type ZbGoodGetOneReq struct {
	g.Meta `path:"/zb_good/{id}" method:"get" tags:"ZbGood" summary:"获取单个套餐" permission:"system:zb_good:info"`
	Id     int `p:"id" v:"required|min:1#套餐ID不能为空|套餐ID必须大于0" dc:"套餐ID"`
}

// ZbGoodGetOneRes 获取单个套餐响应
type ZbGoodGetOneRes struct {
	*ZbGoodInfo `json:",inline"`
}

// ZbGoodGetListReq 获取套餐列表请求
type ZbGoodGetListReq struct {
	g.Meta    `path:"/zb_good/list" method:"get" tags:"ZbGood" summary:"获取套餐列表" permission:"system:zb_good:list"`
	Page      int     `p:"page" v:"min:1#页码必须大于0" dc:"页码，默认1"`
	PageSize  int     `p:"page_size" v:"min:1|max:100#每页数量必须大于0|每页数量不能超过100" dc:"每页数量，默认10"`
	Name      string  `p:"name" dc:"套餐名称，模糊搜索"`
	IsDisable *int    `p:"is_disable" v:"in:0,1#状态值必须是0或1" dc:"是否禁用：0=否，1=是"`
	MinPrice  float64 `p:"min_price" v:"min:0#最低价格不能小于0" dc:"最低价格筛选"`
	MaxPrice  float64 `p:"max_price" v:"min:0#最高价格不能小于0" dc:"最高价格筛选"`
}

// ZbGoodGetListRes 获取套餐列表响应
type ZbGoodGetListRes struct {
	List     []*ZbGoodInfo `json:"list" dc:"套餐列表"`
	Total    int           `json:"total" dc:"总数"`
	Page     int           `json:"page" dc:"当前页码"`
	PageSize int           `json:"page_size" dc:"每页数量"`
}

// ZbGoodGetAllReq 获取所有套餐请求
type ZbGoodGetAllReq struct {
	g.Meta    `path:"/zb_good/all" method:"get" tags:"ZbGood" summary:"获取所有套餐" permission:"system:zb_good:list"`
	IsDisable *int `p:"is_disable" v:"in:0,1#状态值必须是0或1" dc:"是否禁用：0=否，1=是"`
}

// ZbGoodGetAllRes 获取所有套餐响应
type ZbGoodGetAllRes struct {
	List []*ZbGoodInfo `json:"list" dc:"套餐列表"`
}

// ZbGoodUpdateStatusReq 更新套餐状态请求
type ZbGoodUpdateStatusReq struct {
	g.Meta    `path:"/zb_good/{id}/status" method:"put" tags:"ZbGood" summary:"更新套餐状态" permission:"system:zb_good:status"`
	Id        int `p:"id" v:"required|min:1#套餐ID不能为空|套餐ID必须大于0" dc:"套餐ID"`
	IsDisable int `p:"is_disable" v:"required|in:0,1#状态不能为空|状态值必须是0或1" dc:"是否禁用：0=否，1=是"`
}

// ZbGoodUpdateStatusRes 更新套餐状态响应
type ZbGoodUpdateStatusRes struct{}

// ZbGoodBatchDeleteReq 批量删除套餐请求
type ZbGoodBatchDeleteReq struct {
	g.Meta `path:"/zb_good/batch" method:"delete" tags:"ZbGood" summary:"批量删除套餐" permission:"system:zb_good:batchDel"`
	Ids    []int `p:"ids" v:"required|min-length:1#套餐ID列表不能为空|至少选择一个套餐" dc:"套餐ID列表"`
}

// ZbGoodBatchDeleteRes 批量删除套餐响应
type ZbGoodBatchDeleteRes struct {
	Count int `json:"count" dc:"删除数量"`
}

// ZbGoodGetActiveGoodsReq 获取可用套餐请求
type ZbGoodGetActiveGoodsReq struct {
	g.Meta `path:"/zb_good/active" method:"get" tags:"ZbGood" summary:"获取可用套餐"`
}

// ZbGoodGetActiveGoodsRes 获取可用套餐响应
type ZbGoodGetActiveGoodsRes struct {
	List []*ZbGoodInfo `json:"list" dc:"可用套餐列表"`
}

// ZbGoodInfo 套餐信息
type ZbGoodInfo struct {
	Id            int         `json:"id" dc:"套餐ID"`
	Name          string      `json:"name" dc:"套餐名称"`
	Tag           string      `json:"tag" dc:"套餐标签"`
	OriginalPrice float64     `json:"original_price" dc:"原始价格"`
	Price         float64     `json:"price" dc:"现时价格"`
	Effective     int         `json:"effective" dc:"会员有效期（月）"`
	IsDisable     int         `json:"is_disable" dc:"是否禁用"`
	IsDelete      int         `json:"is_delete" dc:"是否删除"`
	CreatedAt     *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt     *gtime.Time `json:"updated_at" dc:"更新时间"`
	DeletedAt     *gtime.Time `json:"deleted_at" dc:"删除时间"`
	// 计算字段
	Discount     float64 `json:"discount" dc:"折扣率"`
	DiscountText string  `json:"discount_text" dc:"折扣文本"`
}
