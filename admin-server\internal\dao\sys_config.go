// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-server/internal/dao/internal"
)

// sysConfigDao is the data access object for the table sys_config.
// You can define custom methods on it to extend its functionality as needed.
type sysConfigDao struct {
	*internal.SysConfigDao
}

var (
	// SysConfig is a globally accessible object for table sys_config operations.
	SysConfig = sysConfigDao{internal.NewSysConfigDao()}
)

// Add your custom methods and functionality below.
