# 系统菜单 API 文档

## 概述

系统菜单模块提供了完整的菜单管理功能，支持树形结构的菜单管理，包括目录、菜单和按钮三种类型。

## 基础信息

- **基础路径**: `/sys_menu`
- **认证方式**: Bearer <PERSON>
- **数据格式**: JSON

## 数据类型说明

### MenuType 菜单类型

- `M`: 目录（Directory）
- `C`: 菜单（Menu）
- `A`: 按钮（Action/Button）

### MenuInfo 菜单信息

```json
{
  "id": 1,                           // 菜单ID
  "pid": 0,                          // 上级菜单ID（0表示顶级菜单）
  "menu_type": "M",                  // 菜单类型：M=目录，C=菜单，A=按钮
  "menu_name": "系统管理",            // 菜单名称
  "menu_icon": "system",             // 菜单图标
  "menu_sort": 1,                    // 菜单排序
  "perms": "system:manage",          // 权限标识
  "paths": "/system",                // 路由地址
  "component": "Layout",             // 前端组件
  "params": "",                      // 路由参数
  "is_cache": 0,                     // 是否缓存 (0=否, 1=是)
  "is_show": 1,                      // 是否显示 (0=否, 1=是)
  "is_disable": 0,                   // 是否禁用 (0=否, 1=是)
  "created_at": "2024-01-01T10:00:00Z", // 创建时间
  "updated_at": "2024-01-01T10:00:00Z"  // 更新时间
}
```

### MenuTree 菜单树节点

```json
{
  "id": 1,
  "pid": 0,
  "menu_type": "M",
  "menu_name": "系统管理",
  "menu_icon": "system",
  "menu_sort": 1,
  "perms": "system:manage",
  "paths": "/system",
  "component": "Layout",
  "params": "",
  "is_cache": 0,
  "is_show": 1,
  "is_disable": 0,
  "created_at": "2024-01-01T10:00:00Z",
  "updated_at": "2024-01-01T10:00:00Z",
  "children": [                      // 子菜单数组
    {
      "id": 2,
      "pid": 1,
      "menu_type": "C",
      "menu_name": "用户管理",
      // ... 其他字段
      "children": []
    }
  ]
}
```

## API 接口

### 1. 创建菜单

**接口地址**: `POST /sys_menu/create`

**接口描述**: 创建新的菜单项

**请求参数**:

```json
{
  "pid": 0,                          // 必填，上级菜单ID，0表示顶级菜单
  "menu_type": "M",                  // 必填，菜单类型：M=目录，C=菜单，A=按钮
  "menu_name": "系统管理",            // 必填，菜单名称，长度1-50位
  "menu_icon": "system",             // 可选，菜单图标
  "menu_sort": 1,                    // 可选，菜单排序，默认1，最小值0
  "perms": "system:manage",          // 权限标识，目录类型可选，菜单和按钮类型必填，长度1-100位，全局唯一
  "paths": "/system",                // 可选，路由地址
  "component": "Layout",             // 可选，前端组件
  "params": "",                      // 可选，路由参数
  "is_cache": 0,                     // 可选，是否缓存 (0=否, 1=是)
  "is_show": 1,                      // 可选，是否显示 (0=否, 1=是)
  "is_disable": 0                    // 可选，是否禁用 (0=否, 1=是)
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1                          // 新创建的菜单ID
  }
}
```

**错误响应**:

```json
{
  "code": 1,
  "message": "权限标识已存在",
  "data": null
}
```

### 2. 获取菜单列表

**接口地址**: `GET /sys_menu/list`

**接口描述**: 分页获取菜单列表，支持条件筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1，最小值1 |
| page_size | int | 否 | 每页数量，默认10，范围1-100 |
| menu_name | string | 否 | 菜单名称（模糊搜索） |
| menu_type | string | 否 | 菜单类型（M/C/A） |
| is_show | int | 否 | 是否显示 (0=否, 1=是) |
| is_disable | int | 否 | 是否禁用 (0=否, 1=是) |

**请求示例**:

```
GET /sys_menu/list?page=1&page_size=10&menu_name=系统&menu_type=M
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "pid": 0,
        "menu_type": "M",
        "menu_name": "系统管理",
        "menu_icon": "system",
        "menu_sort": 1,
        "perms": "system:manage",
        "paths": "/system",
        "component": "Layout",
        "params": "",
        "is_cache": 0,
        "is_show": 1,
        "is_disable": 0,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 1,              // 总记录数
    "page": 1,               // 当前页码
    "page_size": 10          // 每页数量
  }
}
```

### 3. 获取菜单树

**接口地址**: `GET /sys_menu/tree`

**接口描述**: 获取树形结构的菜单数据

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| is_show | int | 否 | 是否显示 (0=否, 1=是) |
| is_disable | int | 否 | 是否禁用 (0=否, 1=是) |

**请求示例**:

```
GET /sys_menu/tree?is_show=1&is_disable=0
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "tree": [
      {
        "id": 1,
        "pid": 0,
        "menu_type": "M",
        "menu_name": "系统管理",
        "menu_icon": "system",
        "menu_sort": 1,
        "perms": "system:manage",
        "paths": "/system",
        "component": "Layout",
        "params": "",
        "is_cache": 0,
        "is_show": 1,
        "is_disable": 0,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z",
        "children": [
          {
            "id": 2,
            "pid": 1,
            "menu_type": "C",
            "menu_name": "用户管理",
            "menu_icon": "user",
            "menu_sort": 1,
            "perms": "system:user:list",
            "paths": "/system/user",
            "component": "system/user/index",
            "params": "",
            "is_cache": 1,
            "is_show": 1,
            "is_disable": 0,
            "created_at": "2024-01-01T10:00:00Z",
            "updated_at": "2024-01-01T10:00:00Z",
            "children": [
              {
                "id": 3,
                "pid": 2,
                "menu_type": "A",
                "menu_name": "新增用户",
                "menu_icon": "",
                "menu_sort": 1,
                "perms": "system:user:create",
                "paths": "",
                "component": "",
                "params": "",
                "is_cache": 0,
                "is_show": 1,
                "is_disable": 0,
                "created_at": "2024-01-01T10:00:00Z",
                "updated_at": "2024-01-01T10:00:00Z",
                "children": []
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 4. 获取单个菜单信息

**接口地址**: `GET /sys_menu/{id}`

**接口描述**: 根据ID获取单个菜单的详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 菜单ID |

**请求示例**:

```
GET /sys_menu/1
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "pid": 0,
    "menu_type": "M",
    "menu_name": "系统管理",
    "menu_icon": "system",
    "menu_sort": 1,
    "perms": "system:manage",
    "paths": "/system",
    "component": "Layout",
    "params": "",
    "is_cache": 0,
    "is_show": 1,
    "is_disable": 0,
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

**错误响应**:

```json
{
  "code": 1,
  "message": "菜单不存在",
  "data": null
}
```

### 5. 更新菜单信息

**接口地址**: `PUT /sys_menu/{id}`

**接口描述**: 更新菜单的信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 菜单ID |

**请求参数**:

```json
{
  "pid": 0,                          // 必填，上级菜单ID
  "menu_type": "M",                  // 必填，菜单类型：M=目录，C=菜单，A=按钮
  "menu_name": "系统管理",            // 必填，菜单名称，长度1-50位
  "menu_icon": "system",             // 可选，菜单图标
  "menu_sort": 1,                    // 可选，菜单排序，最小值0
  "perms": "system:manage",          // 权限标识，目录类型可选，菜单和按钮类型必填，长度1-100位
  "paths": "/system",                // 可选，路由地址
  "component": "Layout",             // 可选，前端组件
  "params": "",                      // 可选，路由参数
  "is_cache": 0,                     // 可选，是否缓存 (0=否, 1=是)
  "is_show": 1,                      // 可选，是否显示 (0=否, 1=是)
  "is_disable": 0                    // 可选，是否禁用 (0=否, 1=是)
}
```

**请求示例**:

```
PUT /sys_menu/1
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

**错误响应**:

```json
{
  "code": 1,
  "message": "不能设置自己为父菜单",
  "data": null
}
```

### 6. 删除菜单

**接口地址**: `DELETE /sys_menu/{id}`

**接口描述**: 软删除菜单（逻辑删除）

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 菜单ID |

**请求示例**:

```
DELETE /sys_menu/1
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

**错误响应**:

```json
{
  "code": 1,
  "message": "存在子菜单，无法删除",
  "data": null
}
```

## API接口列表

| 方法 | 路径 | 功能 |
|------|------|------|
| POST | /sys_menu/create | 创建菜单 |
| GET | /sys_menu/list | 获取菜单列表 |
| GET | /sys_menu/tree | 获取菜单树 |
| GET | /sys_menu/{id} | 获取单个菜单信息 |
| PUT | /sys_menu/{id} | 更新菜单信息 |
| DELETE | /sys_menu/{id} | 删除菜单 |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1 | 业务错误（具体错误信息见message字段） |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有接口都需要在请求头中携带有效的认证Token
2. 权限标识（perms）验证规则：
   - 目录类型（M）：权限标识可选，可以为空（存储为NULL）
   - 菜单类型（C）：权限标识必填，不能为空
   - 按钮类型（A）：权限标识必填，不能为空
   - 如果提供权限标识，必须在系统中唯一
   - 空的权限标识在数据库中存储为NULL，避免唯一性冲突
3. 删除操作为软删除，不会真正删除数据库记录
4. 删除菜单前需要先删除所有子菜单
5. 不能设置自己为父菜单，避免循环引用
6. 菜单按照menu_sort字段升序排列，相同排序值按ID升序
7. 树形结构查询会自动按层级关系组织数据
8. 菜单类型说明：
   - M（目录）：通常用于分组，不对应具体页面
   - C（菜单）：对应具体的页面路由
   - A（按钮）：对应页面内的操作按钮权限

## 业务规则

### 菜单层级关系
- 顶级菜单的pid为0
- 子菜单的pid为父菜单的id
- 支持多级嵌套，建议不超过3级

### 权限标识规范
建议使用冒号分隔的层级结构，例如：
- `system:manage` - 系统管理模块
- `system:user:list` - 用户列表权限
- `system:user:create` - 用户创建权限
- `system:user:update` - 用户更新权限
- `system:user:delete` - 用户删除权限

### 路由地址规范
- 目录类型：通常以模块名开头，如 `/system`
- 菜单类型：具体的页面路径，如 `/system/user`
- 按钮类型：通常为空，因为不需要路由

### 组件路径规范
- 目录类型：通常使用 `Layout` 或自定义布局组件
- 菜单类型：具体的页面组件路径，如 `system/user/index`
- 按钮类型：通常为空
