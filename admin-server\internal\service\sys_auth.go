package service

import (
	v1 "admin-server/api/sys_auth/v1"
	"context"
)

// 1.定义接口
type ISysAuth interface {
	Login(ctx context.Context, req *v1.LoginReq) (res *v1.LoginRes, err error)
	RefreshToken(ctx context.Context, refreshToken string) (res *v1.RefreshTokenRes, err error)
	GetPermissions(ctx context.Context, adminId int64) (permissions []*v1.PermissionInfo, err error)
	Logout(ctx context.Context, token string) (err error)
	GetUserInfo(ctx context.Context, adminId int64) (res *v1.GetUserInfoRes, err error)
	GetMenus(ctx context.Context, adminId int64) (menus []*v1.MenuInfo, err error)
	ValidateToken(ctx context.Context, token string) (adminId int64, err error)
	GenerateTokens(ctx context.Context, adminId int64) (token, refreshToken string, expiresAt int64, err error)
	WechatServe(ctx context.Context) (message string, err error)
}

// 2.定义接口变量
var localSysAuth ISysAuth

// 3.定义一个获取接口实例的函数
func SysAuth() ISysAuth {
	if localSysAuth == nil {
		panic("ISysAuth接口未实现或未注册")
	}
	return localSysAuth
}

// 4.定义一个接口实现的注册方法
func RegisterSysAuth(i ISysAuth) {
	localSysAuth = i
}
