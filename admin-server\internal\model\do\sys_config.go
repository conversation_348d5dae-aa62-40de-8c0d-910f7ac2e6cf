// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysConfig is the golang structure of table sys_config for DAO operations like Where/Data.
type SysConfig struct {
	g.Meta           `orm:"table:sys_config, do:true"`
	Id               interface{} //
	GroupId          interface{} // 系统配置组id
	Key              interface{} // 配置键名
	Value            interface{} // 配置键值
	Name             interface{} // 配置名称
	Sort             interface{} // 排序
	InputType        interface{} // 数据输入类型：input、textarea、select、radio、switch、image
	ConfigSelectData interface{} // 配置项数据，select、radio、switch是一样的格式
	IsSystem         interface{} // 是否系统保留 1是 0否
	IsDelete         interface{} // 是否删除: 0=否, 1=是
	CreatedAt        *gtime.Time // 创建时间
	UpdatedAt        *gtime.Time // 更新时间
	DeletedAt        *gtime.Time // 删除时间
}
