package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// TriggerTrendUpdateReq 手动触发趋势更新请求
type TriggerTrendUpdateReq struct {
	g.Meta `path:"/m/api/search/trigger-trend-update" tags:"Search" method:"post" summary:"手动触发搜索趋势更新"`
}

// TriggerTrendUpdateRes 手动触发趋势更新响应
type TriggerTrendUpdateRes struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"执行消息"`
}

// GetCronStatusReq 获取定时任务状态请求
type GetCronStatusReq struct {
	g.Meta `path:"/m/api/search/cron-status" tags:"Search" method:"get" summary:"获取定时任务状态"`
}

// GetCronStatusRes 获取定时任务状态响应
type GetCronStatusRes struct {
	TaskCount int      `json:"task_count" dc:"定时任务数量"`
	Tasks     []string `json:"tasks" dc:"定时任务列表"`
}
