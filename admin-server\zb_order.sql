/*
 Navicat Premium Dump SQL

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 80012 (8.0.12)
 Source Host           : 127.0.0.1:3306
 Source Schema         : diao_tea

 Target Server Type    : MySQL
 Target Server Version : 80012 (8.0.12)
 File Encoding         : 65001

 Date: 25/07/2025 09:54:57
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for zb_order
-- ----------------------------
DROP TABLE IF EXISTS `zb_order`;
CREATE TABLE `zb_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_sn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `good_id` int(11) NOT NULL COMMENT '套餐id',
  `good_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '套餐名称',
  `user_id` int(11) NOT NULL COMMENT '会员id',
  `price` decimal(10, 0) NOT NULL DEFAULT 0 COMMENT '需支付金额',
  `amount` decimal(10, 0) NULL DEFAULT 0 COMMENT '支付金额',
  `pay_status` tinyint(4) NULL DEFAULT 0 COMMENT '支付状态 1已支付 0未支付',
  `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '【微信支付订单号】 微信支付侧订单的唯一标识。',
  `trade_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '【交易类型】 返回当前订单的交易类型，枚举值：',
  `trade_state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '【交易状态】 交易状态，详细业务流转状态处理请参考开发指引-订单状态流转图。枚举值：',
  `pay_result` json NULL COMMENT '支付返回信息json格式',
  `pay_at` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `id`(`id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
