/*
 Navicat Premium Dump SQL

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 80012 (8.0.12)
 Source Host           : 127.0.0.1:3306
 Source Schema         : diao_tea

 Target Server Type    : MySQL
 Target Server Version : 80012 (8.0.12)
 File Encoding         : 65001

 Date: 28/07/2025 14:53:35
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for zb_order
-- ----------------------------
DROP TABLE IF EXISTS `zb_order`;
CREATE TABLE `zb_order`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_sn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `good_id` int(11) NOT NULL COMMENT '套餐id',
  `good_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '套餐名称',
  `city_count` int(11) NOT NULL DEFAULT 1 COMMENT '选择的城市数量',
  `user_id` int(11) NOT NULL COMMENT '会员id',
  `good_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '套餐价格',
  `effective` int(11) NOT NULL DEFAULT 0 COMMENT '会员有效期；单位月',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '需支付金额',
  `amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '支付金额',
  `pay_status` tinyint(4) NULL DEFAULT 0 COMMENT '支付状态 1已支付 0未支付',
  `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '【微信支付订单号】 微信支付侧订单的唯一标识。',
  `trade_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '【交易类型】 返回当前订单的交易类型，枚举值：',
  `trade_state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '【交易状态】 交易状态，详细业务流转状态处理请参考开发指引-订单状态流转图。枚举值：',
  `pay_result` json NULL COMMENT '支付返回信息json格式',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单备注',
  `pay_at` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `id`(`id` ASC) USING BTREE,
  INDEX `idx_order_sn`(`order_sn` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_pay_status`(`pay_status` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of zb_order
-- ----------------------------
INSERT INTO `zb_order` VALUES (1, 'ZB202507251114227737', 2, '特惠会员399元', 2, 1, 298.00, 2, 596.00, 596.00, 1, NULL, 'ADMIN', NULL, NULL, '选择城市：内蒙古、北京', '2025-07-25 15:37:35', '2025-07-25 11:14:22', '2025-07-25 15:37:35');
INSERT INTO `zb_order` VALUES (3, 'ZB202507261037064862', 2, '特惠会员399元', 1, 1, 298.00, 2, 298.00, 298.00, 1, NULL, 'ADMIN', NULL, NULL, '选择城市：北京', '2025-07-26 10:37:33', '2025-07-26 10:37:07', '2025-07-26 10:37:33');
INSERT INTO `zb_order` VALUES (4, 'ZB202507261102111862', 2, '特惠会员399元', 32, 1, 298.00, 2, 9536.00, 9536.00, 1, NULL, 'ADMIN', NULL, NULL, '选择城市：北京、浙江、天津、安徽、上海、福建、重庆、江西、山东、河南、湖北、湖南、广东、海南、山西、青海、江苏、辽宁、吉林、河北、贵州、四川、云南、陕西、黑龙江、广西、宁夏、新疆、内蒙古、西藏、香港、澳门', '2025-07-28 11:54:04', '2025-07-26 11:02:11', '2025-07-28 11:54:04');
INSERT INTO `zb_order` VALUES (5, 'ZB202507261123415771', 2, '特惠会员399元', 1, 1, 298.00, 2, 298.00, 298.00, 1, NULL, 'ADMIN', NULL, NULL, '选择城市：内蒙古', '2025-07-28 11:54:47', '2025-07-26 11:23:41', '2025-07-28 11:54:47');

SET FOREIGN_KEY_CHECKS = 1;
