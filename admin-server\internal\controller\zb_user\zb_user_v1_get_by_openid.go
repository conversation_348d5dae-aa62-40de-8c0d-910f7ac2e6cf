package zb_user

import (
	"context"

	v1 "admin-server/api/zb_user/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetByOpenid(ctx context.Context, req *v1.GetByOpenidReq) (res *v1.GetByOpenidRes, err error) {
	user, err := service.ZbUser().GetUserByOpenid(ctx, req.Openid)
	if err != nil {
		return nil, err
	}

	if user == nil {
		return &v1.GetByOpenidRes{
			User: nil,
		}, nil
	}

	// 动态计算VIP状态
	vipStatus, daysLeft := service.ZbUser().CalculateVipStatus(ctx, user.EffectiveStart, user.EffectiveEnd)

	// 转换为API响应格式
	userDetail := &v1.UserDetail{
		ID:             user.Id,
		Nickname:       user.Nickname,
		Avatar:         user.Avatar,
		Openid:         user.Openid,
		IsDisable:      int(user.IsDisable),
		IsDelete:       int(user.IsDelete),
		EffectiveStart: user.EffectiveStart,
		EffectiveEnd:   user.EffectiveEnd,
		VipStatus:      vipStatus,
		VipDaysLeft:    daysLeft,
		CreatedAt:      user.CreatedAt,
		UpdatedAt:      user.UpdatedAt,
		DeletedAt:      user.DeletedAt,
	}

	return &v1.GetByOpenidRes{
		User: userDetail,
	}, nil
}
