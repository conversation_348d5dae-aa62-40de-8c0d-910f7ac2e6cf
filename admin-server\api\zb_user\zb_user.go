// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package zb_user

import (
	"context"

	v1 "admin-server/api/zb_user/v1"
)

type IZbUserV1 interface {
	GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error)
	GetOne(ctx context.Context, req *v1.GetOneReq) (res *v1.GetOneRes, err error)
	Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error)
	Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error)
	SetStatus(ctx context.Context, req *v1.SetStatusReq) (res *v1.SetStatusRes, err error)
	UpdateVipPeriod(ctx context.Context, req *v1.UpdateVipPeriodReq) (res *v1.UpdateVipPeriodRes, err error)
	GetVipList(ctx context.Context, req *v1.GetVipListReq) (res *v1.GetVipListRes, err error)
	GetExpiredVip(ctx context.Context, req *v1.GetExpiredVipReq) (res *v1.GetExpiredVipRes, err error)
	GetByOpenid(ctx context.Context, req *v1.GetByOpenidReq) (res *v1.GetByOpenidRes, err error)
	GetStats(ctx context.Context, req *v1.GetStatsReq) (res *v1.GetStatsRes, err error)
}
