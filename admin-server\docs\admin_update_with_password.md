# 管理员更新接口密码功能说明

## 功能概述

管理员更新接口现在支持可选的密码修改功能。在更新管理员基本信息时，如果传入了 `password` 字段，系统会同时更新密码；如果不传入或传入空字符串，则只更新其他信息而不修改密码。

## 功能特性

### 1. 灵活的密码更新
- **可选性**: 密码字段为可选，不强制修改
- **智能判断**: 根据是否传入密码字段决定是否更新密码
- **安全加密**: 传入的密码会自动进行MD5加密存储

### 2. 统一接口
- **一个接口**: 无需分别调用更新信息和修改密码接口
- **原子操作**: 所有更新在一个事务中完成
- **简化调用**: 前端只需调用一个接口即可完成所有更新

## API接口详情

### 接口地址
`PUT /sys_admin/{id}`

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 管理员ID（路径参数） |
| username | string | 是 | 账号，长度3-30位 |
| password | string | 否 | 密码，长度6-30位，传入则修改密码 |
| nickname | string | 是 | 昵称，长度2-20位 |
| is_super | int | 否 | 是否是超级管理员 (0=否, 1=是) |
| is_disable | int | 否 | 是否禁用 (0=否, 1=是) |

### 业务逻辑

1. **验证管理员存在性**
2. **检查用户名唯一性**（排除当前管理员）
3. **准备更新数据**
   - 基本信息：用户名、昵称、超级管理员标识、禁用状态
   - 密码处理：如果传入password且不为空，则加密后添加到更新数据中
4. **执行数据库更新**
5. **返回操作结果**

## 使用示例

### 1. 只更新基本信息（不修改密码）

```bash
curl -X PUT http://localhost:8000/sys_admin/2 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "username": "admin_updated",
    "nickname": "更新后的管理员",
    "is_super": 0,
    "is_disable": 0
  }'
```

### 2. 更新基本信息并修改密码

```bash
curl -X PUT http://localhost:8000/sys_admin/2 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "username": "admin_updated",
    "password": "newpassword123",
    "nickname": "更新后的管理员",
    "is_super": 0,
    "is_disable": 0
  }'
```

### 3. 只修改密码（其他信息保持不变）

```bash
curl -X PUT http://localhost:8000/sys_admin/2 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "username": "current_username",
    "password": "newpassword123",
    "nickname": "current_nickname",
    "is_super": 0,
    "is_disable": 0
  }'
```

## 前端集成示例

### JavaScript 示例

```javascript
// 更新管理员信息（可选密码）
async function updateAdmin(adminId, updateData) {
  try {
    const response = await fetch(`/sys_admin/${adminId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updateData)
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('更新成功:', result);
      return result;
    } else {
      const error = await response.json();
      throw new Error(error.message);
    }
  } catch (error) {
    console.error('更新失败:', error);
    throw error;
  }
}

// 使用示例1：只更新基本信息
updateAdmin(2, {
  username: 'admin_updated',
  nickname: '更新后的管理员',
  is_super: 0,
  is_disable: 0
});

// 使用示例2：同时更新密码
updateAdmin(2, {
  username: 'admin_updated',
  password: 'newpassword123',
  nickname: '更新后的管理员',
  is_super: 0,
  is_disable: 0
});
```

### React 组件示例

```jsx
import React, { useState } from 'react';

function AdminUpdateForm({ adminId, currentAdmin, onUpdate }) {
  const [formData, setFormData] = useState({
    username: currentAdmin.username,
    password: '', // 密码字段默认为空
    nickname: currentAdmin.nickname,
    is_super: currentAdmin.is_super,
    is_disable: currentAdmin.is_disable
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // 准备提交数据
    const submitData = { ...formData };
    
    // 如果密码为空，则从提交数据中移除
    if (!submitData.password.trim()) {
      delete submitData.password;
    }
    
    try {
      await updateAdmin(adminId, submitData);
      onUpdate('更新成功');
    } catch (error) {
      onUpdate('更新失败: ' + error.message);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div>
        <label>用户名:</label>
        <input
          type="text"
          value={formData.username}
          onChange={(e) => setFormData({...formData, username: e.target.value})}
          required
        />
      </div>
      
      <div>
        <label>密码（留空则不修改）:</label>
        <input
          type="password"
          value={formData.password}
          onChange={(e) => setFormData({...formData, password: e.target.value})}
          placeholder="留空则不修改密码"
        />
      </div>
      
      <div>
        <label>昵称:</label>
        <input
          type="text"
          value={formData.nickname}
          onChange={(e) => setFormData({...formData, nickname: e.target.value})}
          required
        />
      </div>
      
      <div>
        <label>
          <input
            type="checkbox"
            checked={formData.is_super === 1}
            onChange={(e) => setFormData({...formData, is_super: e.target.checked ? 1 : 0})}
          />
          超级管理员
        </label>
      </div>
      
      <div>
        <label>
          <input
            type="checkbox"
            checked={formData.is_disable === 1}
            onChange={(e) => setFormData({...formData, is_disable: e.target.checked ? 1 : 0})}
          />
          禁用账户
        </label>
      </div>
      
      <button type="submit">更新管理员</button>
    </form>
  );
}
```

## 与原有接口的关系

### 1. 保留原有密码修改接口
- `PUT /sys_admin/{id}/password` 接口仍然保留
- 用于需要验证原密码的场景
- 提供更严格的密码修改流程

### 2. 新的更新接口优势
- 无需验证原密码（适合管理员重置其他人密码）
- 可以同时更新多个字段
- 减少接口调用次数

### 3. 使用场景区分
- **管理员自己修改密码**: 使用 `PUT /sys_admin/{id}/password`（需要原密码验证）
- **管理员重置他人密码**: 使用 `PUT /sys_admin/{id}`（无需原密码验证）
- **批量更新信息**: 使用 `PUT /sys_admin/{id}`（可选择是否同时修改密码）

## 注意事项

1. **密码安全**: 传入的密码会自动进行MD5加密存储
2. **字段验证**: 如果传入password字段，会进行长度验证（6-30位）
3. **空值处理**: 空字符串和不传入password字段效果相同
4. **权限控制**: 需要适当的权限才能执行更新操作
5. **日志记录**: 建议记录密码修改操作到操作日志中

## 错误处理

常见错误情况：
- 管理员不存在
- 用户名已被其他管理员使用
- 密码长度不符合要求
- 权限不足
- 数据库操作失败

所有错误都会返回详细的错误信息，便于前端处理和用户理解。
