package sys_dict

import (
	"context"

	v1 "admin-server/api/sys_dict/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error) {
	err = service.SysDict().UpdateDict(ctx, req.ID, req.GroupId, req.Name, req.Value, req.Code, req.Remark, req.Sort, req.IsDisable)
	if err != nil {
		return nil, err
	}

	return &v1.UpdateRes{}, nil
}
