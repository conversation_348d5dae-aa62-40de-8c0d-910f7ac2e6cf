// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ZbOrder is the golang structure for table zb_order.
type ZbOrder struct {
	Id            int64       `json:"id"            orm:"id"            description:"主键ID"`
	OrderSn       string      `json:"orderSn"       orm:"order_sn"      description:"订单编号"`
	GoodId        int64       `json:"goodId"        orm:"good_id"       description:"套餐id"`
	GoodName      string      `json:"goodName"      orm:"good_name"     description:"套餐名称"`
	GoodPrice     float64     `json:"goodPrice"     orm:"good_price"    description:"套餐单价"`
	Effective     int         `json:"effective"     orm:"effective"     description:"会员有效期；单位月"`
	CityCount     int         `json:"cityCount"     orm:"city_count"    description:"选择的城市数量"`
	UserId        int64       `json:"userId"        orm:"user_id"       description:"会员id"`
	Price         float64     `json:"price"         orm:"price"         description:"需支付金额"`
	Amount        float64     `json:"amount"        orm:"amount"        description:"支付金额"`
	PayStatus     int         `json:"payStatus"     orm:"pay_status"    description:"支付状态 1已支付 0未支付"`
	TransactionId string      `json:"transactionId" orm:"transaction_id" description:"微信支付订单号"`
	TradeType     string      `json:"tradeType"     orm:"trade_type"    description:"交易类型"`
	TradeState    string      `json:"tradeState"    orm:"trade_state"   description:"交易状态"`
	PayResult     string      `json:"payResult"     orm:"pay_result"    description:"支付返回信息json格式"`
	Remark        string      `json:"remark"        orm:"remark"        description:"订单备注"`
	PayAt         *gtime.Time `json:"payAt"         orm:"pay_at"        description:"支付时间"`
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"    description:"创建时间"`
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"    description:"更新时间"`
}
