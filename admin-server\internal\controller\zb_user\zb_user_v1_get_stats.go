package zb_user

import (
	"context"

	v1 "admin-server/api/zb_user/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetStats(ctx context.Context, req *v1.GetStatsReq) (res *v1.GetStatsRes, err error) {
	totalUsers, vipUsers, disabledUsers, err := service.ZbUser().GetUserStats(ctx)
	if err != nil {
		return nil, err
	}

	// 计算活跃用户数（总用户数 - 禁用用户数）
	activeUsers := totalUsers - disabledUsers

	return &v1.GetStatsRes{
		TotalUsers:    totalUsers,
		VipUsers:      vipUsers,
		DisabledUsers: disabledUsers,
		ActiveUsers:   activeUsers,
	}, nil
}
