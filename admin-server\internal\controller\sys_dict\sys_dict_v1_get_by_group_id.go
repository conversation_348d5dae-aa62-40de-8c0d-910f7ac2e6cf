package sys_dict

import (
	"context"

	v1 "admin-server/api/sys_dict/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetByGroupId(ctx context.Context, req *v1.GetByGroupIdReq) (res *v1.GetByGroupIdRes, err error) {
	list, err := service.SysDict().GetDictsByGroupId(ctx, req.GroupId)
	if err != nil {
		return nil, err
	}

	// 转换为API响应格式
	options := make([]v1.DictOption, len(list))
	for i, dict := range list {
		options[i] = v1.DictOption{
			ID:    dict.Id,
			Name:  dict.Name,
			Value: dict.Value,
			Code:  dict.Code,
			Sort:  int(dict.Sort),
		}
	}

	return &v1.GetByGroupIdRes{
		List: options,
	}, nil
}
