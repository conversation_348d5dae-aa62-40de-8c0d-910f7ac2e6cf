// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// WechatMenu is the golang structure of table wechat_menu for DAO operations like Where/Data.
type WechatMenu struct {
	g.Meta    `orm:"table:wechat_menu, do:true"`
	Id        interface{} //
	Pid       interface{} // 父菜单ID，0表示一级菜单
	MenuName  interface{} // 菜单名称
	MenuType  interface{} // 菜单类型
	MenuKey   interface{} // 菜单KEY值
	MenuUrl   interface{} // 菜单链接
	Appid     interface{} // 小程序AppID
	Pagepath  interface{} // 小程序页面路径
	Sort      interface{} // 排序
	Level     interface{} // 菜单层级：1=一级菜单，2=二级菜单
	IsDisable interface{} // 是否禁用: 0=否, 1=是
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 更新时间
}
