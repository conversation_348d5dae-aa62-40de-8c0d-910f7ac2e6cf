// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysDict is the golang structure of table sys_dict for DAO operations like Where/Data.
type SysDict struct {
	g.Meta    `orm:"table:sys_dict, do:true"`
	Id        interface{} //
	GroupId   interface{} // 字典分组id
	Name      interface{} // 字典名称
	Value     interface{} // 字典值
	Code      interface{} // 字典标识
	Sort      interface{} // 排序
	IsDisable interface{} // 是否禁用: 0=否, 1=是
	IsDelete  interface{} // 是否删除: 0=否, 1=是
	IsSystem  interface{} // 是否系统保留 1是 0否
	Remark    interface{} // 备注
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 更新时间
	DeletedAt *gtime.Time // 删除时间
}
