package search

import (
	v1 "admin-server/api/search/v1"
	"context"
)

// 热门搜索相关接口
type ISearchV1 interface {
	GetHotKeywords(ctx context.Context, req *v1.GetHotKeywordsReq) (res *v1.GetHotKeywordsRes, err error)
	TrackSearch(ctx context.Context, req *v1.TrackSearchReq) (res *v1.TrackSearchRes, err error)
	TrackClick(ctx context.Context, req *v1.TrackClickReq) (res *v1.TrackClickRes, err error)
	TriggerTrendUpdate(ctx context.Context, req *v1.TriggerTrendUpdateReq) (res *v1.TriggerTrendUpdateRes, err error)
	GetCronStatus(ctx context.Context, req *v1.GetCronStatusReq) (res *v1.GetCronStatusRes, err error)
}
