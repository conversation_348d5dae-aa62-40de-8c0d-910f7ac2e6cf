// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ZbGoodDao is the data access object for the table zb_good.
type ZbGoodDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  ZbGoodColumns      // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// ZbGoodColumns defines and stores column names for the table zb_good.
type ZbGoodColumns struct {
	Id            string //
	Name          string // 名称
	Tag           string // 标签
	OriginalPrice string // 原始价格
	Price         string // 现时价格
	Effective     string // 会员有效期；单位月
	IsDisable     string // 是否禁用: 0=否, 1=是
	IsDelete      string // 是否删除: 0=否, 1=是
	CreatedAt     string // 创建时间
	UpdatedAt     string // 更新时间
	DeletedAt     string // 删除时间
}

// zbGoodColumns holds the columns for the table zb_good.
var zbGoodColumns = ZbGoodColumns{
	Id:            "id",
	Name:          "name",
	Tag:           "tag",
	OriginalPrice: "original_price",
	Price:         "price",
	Effective:     "effective",
	IsDisable:     "is_disable",
	IsDelete:      "is_delete",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
	DeletedAt:     "deleted_at",
}

// NewZbGoodDao creates and returns a new DAO object for table data access.
func NewZbGoodDao(handlers ...gdb.ModelHandler) *ZbGoodDao {
	return &ZbGoodDao{
		group:    "default",
		table:    "zb_good",
		columns:  zbGoodColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ZbGoodDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ZbGoodDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ZbGoodDao) Columns() ZbGoodColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ZbGoodDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ZbGoodDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ZbGoodDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
