// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ZbArticle is the golang structure of table zb_article for DAO operations like Where/Data.
type ZbArticle struct {
	g.Meta         `orm:"table:zb_article, do:true"`
	Id             interface{} //
	CityId         interface{} // 开通城市id
	CateId         interface{} // 招标类别id
	Title          interface{} // 标题
	Intro          interface{} // 内容简介
	FullContent    interface{} // 完整版内容
	ShieidContent  interface{} // 屏蔽内容
	ViewCount      interface{} // 浏览次数
	SeoTitle       interface{} // SEO标题
	SeoKeywords    interface{} // SEO关键词
	SeoDescription interface{} // SEO描述
	Pic            interface{} // 缩略图
	Uid            interface{} // 发布者id
	Author         interface{} // 作者
	Ip             interface{} // 发布ip
	IsDisable      interface{} // 是否禁用: 0=否, 1=是
	IsDelete       interface{} // 是否删除: 0=否, 1=是
	CreatedAt      *gtime.Time // 创建日期
	UpdatedAt      *gtime.Time // 更新日期
	DeletedAt      *gtime.Time // 删除时间
}
