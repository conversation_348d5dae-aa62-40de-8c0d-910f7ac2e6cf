package sys_login_log

import (
	"admin-server/api/sys_login_log"
	v1 "admin-server/api/sys_login_log/v1"
	"admin-server/internal/service"
	"context"
)

var (
	// ControllerV1 控制器实例
	ControllerV1 = cSysLoginLogV1{}
)

type cSysLoginLogV1 struct{}

// NewV1 创建V1版本控制器
func NewV1() sys_login_log.ISysLoginLogV1 {
	return &ControllerV1
}

// GetList 获取登录日志列表
func (c *cSysLoginLogV1) GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error) {
	list, total, err := service.SysLoginLog().GetLoginLogList(ctx, req.Page, req.PageSize, req.Username)
	if err != nil {
		return nil, err
	}

	// 转换为API响应格式
	loginLogs := make([]v1.LoginLogInfo, len(list))
	for i, log := range list {
		loginLogs[i] = v1.LoginLogInfo{
			ID:        log.Id,
			AdminId:   log.AdminId,
			Username:  log.Username,
			Ip:        log.Ip,
			Os:        log.Os,
			Browser:   log.Browser,
			CreatedAt: log.CreatedAt,
		}
	}

	return &v1.GetListRes{
		List:  loginLogs,
		Total: total,
	}, nil
}

// Delete 删除登录日志
func (c *cSysLoginLogV1) Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error) {
	err = service.SysLoginLog().DeleteLoginLog(ctx, req.IDs)
	if err != nil {
		return nil, err
	}

	return &v1.DeleteRes{}, nil
}

// Clear 清空登录日志
func (c *cSysLoginLogV1) Clear(ctx context.Context, req *v1.ClearReq) (res *v1.ClearRes, err error) {
	err = service.SysLoginLog().ClearLoginLog(ctx)
	if err != nil {
		return nil, err
	}

	return &v1.ClearRes{}, nil
}
