# 订单表新增套餐单价字段说明

## 📋 修改概述

为 `zb_order` 表新增 `good_price` 套餐单价字段，用于记录下单时的套餐原价，便于后续分析和统计。

## 🔧 数据库修改

### 1. 新增字段
```sql
ALTER TABLE `zb_order` ADD COLUMN `good_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '套餐单价' AFTER `good_name`;
```

### 2. 字段说明
- **字段名**: `good_price`
- **类型**: `decimal(10,2)`
- **默认值**: `0.00`
- **位置**: 在 `good_name` 字段后面
- **用途**: 记录套餐的单价（不含城市数量计算）

### 3. 字段关系
- `good_price`: 套餐单价
- `price`: 需支付金额 = `good_price` × `city_count`
- `amount`: 实际支付金额

## 💻 代码修改

### 1. Entity模型更新
```go
// ZbOrder 订单实体
type ZbOrder struct {
    Id            int64       `json:"id"`
    OrderSn       string      `json:"orderSn"`
    GoodId        int64       `json:"goodId"`
    GoodName      string      `json:"goodName"`
    GoodPrice     float64     `json:"goodPrice"`     // 新增：套餐单价
    CityCount     int         `json:"cityCount"`
    UserId        int64       `json:"userId"`
    Price         float64     `json:"price"`         // 需支付金额
    Amount        float64     `json:"amount"`        // 实际支付金额
    // ... 其他字段
}
```

### 2. DAO层更新
```go
// ZbOrderColumns 字段定义
type ZbOrderColumns struct {
    Id            string // 主键ID
    OrderSn       string // 订单编号
    GoodId        string // 套餐id
    GoodName      string // 套餐名称
    GoodPrice     string // 套餐单价 (新增)
    CityCount     string // 选择的城市数量
    // ... 其他字段
}

// 字段映射
var zbOrderColumns = ZbOrderColumns{
    Id:            "id",
    OrderSn:       "order_sn",
    GoodId:        "good_id",
    GoodName:      "good_name",
    GoodPrice:     "good_price",  // 新增
    CityCount:     "city_count",
    // ... 其他字段
}
```

### 3. API响应结构更新
```go
// OrderInfo 订单信息结构
type OrderInfo struct {
    Id            int64       `json:"id"`
    OrderSn       string      `json:"order_sn"`
    GoodId        int64       `json:"good_id"`
    GoodName      string      `json:"good_name"`
    GoodPrice     float64     `json:"good_price"`    // 新增：套餐单价
    CityCount     int         `json:"city_count"`
    UserId        int64       `json:"user_id"`
    UserNickname  string      `json:"user_nickname"`
    Price         float64     `json:"price"`         // 需支付金额
    Amount        float64     `json:"amount"`        // 实际支付金额
    // ... 其他字段
}
```

### 4. Logic层修改

#### 创建订单时保存套餐单价
```go
// Create 创建订单
result, err := dao.ZbOrder.Ctx(ctx).Data(g.Map{
    "order_sn":   orderSn,
    "good_id":    good.Id,
    "good_name":  good.Name,
    "good_price": good.Price,        // 新增：保存套餐单价
    "city_count": len(req.CityIds),
    "user_id":    user.Id,
    "price":      totalPrice,        // 总价 = good_price × city_count
    "amount":     0,
    "pay_status": 0,
    "remark":     req.Remark,
}).Insert()
```

#### 查询时返回套餐单价
```go
// GetList 和 GetDetail 方法
orderInfo := v1.OrderInfo{
    Id:            orderWithUser.Id,
    OrderSn:       orderWithUser.OrderSn,
    GoodId:        orderWithUser.GoodId,
    GoodName:      orderWithUser.GoodName,
    GoodPrice:     orderWithUser.GoodPrice,  // 新增：返回套餐单价
    CityCount:     orderWithUser.CityCount,
    Price:         orderWithUser.Price,
    // ... 其他字段
}
```

## 📊 API响应示例

### 1. 订单列表响应
```json
{
    "code": 0,
    "data": {
        "list": [
            {
                "id": 1,
                "order_sn": "ZB20250123150405XXXX",
                "good_id": 1,
                "good_name": "VIP套餐",
                "good_price": 100.00,
                "city_count": 3,
                "user_id": 123,
                "user_nickname": "张三",
                "price": 300.00,
                "amount": 300.00,
                "pay_status": 1,
                "cities": [
                    {"city_id": 1, "city_name": "北京"},
                    {"city_id": 2, "city_name": "上海"},
                    {"city_id": 3, "city_name": "深圳"}
                ]
            }
        ],
        "total": 1
    }
}
```

### 2. 价格关系说明
```json
{
    "good_price": 100.00,    // 套餐单价
    "city_count": 3,         // 选择城市数量
    "price": 300.00,         // 需支付金额 = 100.00 × 3
    "amount": 300.00         // 实际支付金额
}
```

## 🎯 业务价值

### 1. 价格透明度
- 清楚显示套餐原价和总价的关系
- 便于用户理解计价规则
- 支持价格审计和核查

### 2. 数据分析
- 统计不同价位套餐的销售情况
- 分析价格敏感度
- 支持营销策略制定

### 3. 财务管理
- 准确的收入分析
- 套餐盈利能力评估
- 价格策略效果跟踪

## 🧪 测试验证

### 1. 数据库验证
```sql
-- 查看新增字段
DESCRIBE zb_order;

-- 验证数据
SELECT 
    id,
    order_sn,
    good_name,
    good_price,
    city_count,
    price,
    amount
FROM zb_order 
ORDER BY created_at DESC 
LIMIT 5;
```

### 2. API测试
```bash
# 测试订单列表
curl "http://localhost:8000/api/zb_order/list?page=1&page_size=10" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 测试订单详情
curl "http://localhost:8000/api/zb_order/detail?id=1" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 创建订单测试
```bash
# 测试创建订单
curl -X POST "http://localhost:8000/m/api/zb_order/create" \
     -H "Content-Type: application/json" \
     -d '{
       "openid": "test_openid_123",
       "good_id": 1,
       "good_name": "VIP套餐",
       "price": 100.00,
       "city_ids": [1, 2, 3],
       "remark": "测试订单"
     }'
```

## ⚠️ 注意事项

### 1. 数据一致性
- 确保 `good_price` 与套餐表中的价格一致
- 验证 `price = good_price × city_count` 的计算正确性

### 2. 历史数据
- 新增字段后，历史订单的 `good_price` 为 `0.00`
- 可以通过脚本更新历史数据：
```sql
UPDATE zb_order o 
JOIN zb_good g ON o.good_id = g.id 
SET o.good_price = g.price 
WHERE o.good_price = 0.00;
```

### 3. 价格变更
- 套餐价格变更不影响已下单的记录
- 订单中的 `good_price` 保持下单时的价格

## 📈 后续扩展

### 1. 价格历史
可以考虑添加价格变更历史表：
```sql
CREATE TABLE zb_good_price_history (
    id int PRIMARY KEY AUTO_INCREMENT,
    good_id int NOT NULL,
    old_price decimal(10,2),
    new_price decimal(10,2),
    changed_at timestamp DEFAULT CURRENT_TIMESTAMP
);
```

### 2. 优惠信息
可以添加优惠相关字段：
```sql
ALTER TABLE zb_order ADD COLUMN discount_amount decimal(10,2) DEFAULT 0.00 COMMENT '优惠金额';
ALTER TABLE zb_order ADD COLUMN discount_type varchar(50) COMMENT '优惠类型';
```

### 3. 价格分析
可以基于 `good_price` 进行更深入的数据分析：
- 不同价位套餐的转化率
- 价格弹性分析
- 最优定价策略

---

**修改状态**: ✅ 已完成  
**新增字段**: good_price (套餐单价)  
**影响范围**: Entity、DAO、API、Logic层  
**向后兼容**: 是  
**文档版本**: v1.0  
**修改时间**: 2025-01-23
