package zbCate

import (
	v1 "admin-server/api/zb_cate/v1"
	"admin-server/internal/dao"
	"admin-server/internal/model/do"
	"admin-server/internal/model/entity"
	"admin-server/internal/packed"
	"admin-server/internal/service"
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

func init() {
	service.RegisterZbCate(&sZbCate{})
}

type sZbCate struct{}

func (s *sZbCate) GetAllForMobile(ctx context.Context) (res *v1.ZbCateGetAllRes, err error) {
	// 构建查询条件
	query := dao.ZbCate.Ctx(ctx).Where("is_delete", 0).Where("is_disable", packed.ENABLE)

	// 获取所有数据
	var entities []*entity.ZbCate
	err = query.OrderAsc("sort").OrderAsc("id").Scan(&entities)
	if err != nil {
		g.Log().Error(ctx, "查询所有类别失败:", err)
		return nil, gerror.New("查询所有类别失败")
	}

	// 转换数据
	var list []*v1.ZbCateInfo
	if err = gconv.Structs(entities, &list); err != nil {
		g.Log().Error(ctx, "数据转换失败:", err)
		return nil, gerror.New("数据转换失败")
	}

	res = &v1.ZbCateGetAllRes{
		List: list,
	}
	return res, nil
}

// Create 创建招标类别
func (s *sZbCate) Create(ctx context.Context, req *v1.ZbCateCreateReq) (res *v1.ZbCateCreateRes, err error) {
	// 检查类别名称是否重复
	count, err := dao.ZbCate.Ctx(ctx).Where("name", req.Name).Where("is_delete", 0).Count()
	if err != nil {
		g.Log().Error(ctx, "检查类别名称重复失败:", err)
		return nil, gerror.New("检查类别名称重复失败")
	}
	if count > 0 {
		return nil, gerror.New("类别名称已存在")
	}

	now := gtime.Now()
	cateData := do.ZbCate{
		Name:      req.Name,
		Sort:      req.Sort,
		IsDisable: req.IsDisable,
		IsDelete:  0,
		CreatedAt: now,
		UpdatedAt: now,
	}

	insertResult, err := dao.ZbCate.Ctx(ctx).Insert(cateData)
	if err != nil {
		g.Log().Error(ctx, "创建类别失败:", err)
		return nil, gerror.New("创建类别失败")
	}

	insertId, err := insertResult.LastInsertId()
	if err != nil {
		g.Log().Error(ctx, "获取插入ID失败:", err)
		return nil, gerror.New("获取插入ID失败")
	}

	g.Log().Info(ctx, "创建类别成功:", "id:", insertId, "name:", req.Name)
	res = &v1.ZbCateCreateRes{
		Id: int(insertId),
	}
	return res, nil
}

// Update 更新招标类别
func (s *sZbCate) Update(ctx context.Context, req *v1.ZbCateUpdateReq) (res *v1.ZbCateUpdateRes, err error) {
	// 检查类别是否存在
	var existingEntity *entity.ZbCate
	err = dao.ZbCate.Ctx(ctx).Where("id", req.Id).Where("is_delete", 0).Scan(&existingEntity)
	if err != nil {
		g.Log().Error(ctx, "查询类别失败:", err)
		return nil, gerror.New("查询类别失败")
	}
	if existingEntity == nil {
		return nil, gerror.New("类别不存在")
	}

	// 检查类别名称是否重复（排除自己）
	count, err := dao.ZbCate.Ctx(ctx).Where("name", req.Name).Where("id !=", req.Id).Where("is_delete", 0).Count()
	if err != nil {
		g.Log().Error(ctx, "检查类别名称重复失败:", err)
		return nil, gerror.New("检查类别名称重复失败")
	}
	if count > 0 {
		return nil, gerror.New("类别名称已存在")
	}

	cateData := do.ZbCate{
		Name:      req.Name,
		Sort:      req.Sort,
		IsDisable: req.IsDisable,
		UpdatedAt: gtime.Now(),
	}

	_, err = dao.ZbCate.Ctx(ctx).Where("id", req.Id).Update(cateData)
	if err != nil {
		g.Log().Error(ctx, "更新类别失败:", err)
		return nil, gerror.New("更新类别失败")
	}

	g.Log().Info(ctx, "更新类别成功:", "id:", req.Id, "name:", req.Name)
	res = &v1.ZbCateUpdateRes{}
	return res, nil
}

// Delete 删除招标类别
func (s *sZbCate) Delete(ctx context.Context, req *v1.ZbCateDeleteReq) (res *v1.ZbCateDeleteRes, err error) {
	// 检查类别是否存在
	var existingEntity *entity.ZbCate
	err = dao.ZbCate.Ctx(ctx).Where("id", req.Id).Where("is_delete", 0).Scan(&existingEntity)
	if err != nil {
		g.Log().Error(ctx, "查询类别失败:", err)
		return nil, gerror.New("查询类别失败")
	}
	if existingEntity == nil {
		return nil, gerror.New("类别不存在")
	}

	// 软删除
	_, err = dao.ZbCate.Ctx(ctx).Where("id", req.Id).Update(do.ZbCate{
		IsDelete:  1,
		DeletedAt: gtime.Now(),
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "删除类别失败:", err)
		return nil, gerror.New("删除类别失败")
	}

	g.Log().Info(ctx, "删除类别成功:", "id:", req.Id)
	res = &v1.ZbCateDeleteRes{}
	return res, nil
}

// GetOne 获取单个招标类别
func (s *sZbCate) GetOne(ctx context.Context, req *v1.ZbCateGetOneReq) (res *v1.ZbCateGetOneRes, err error) {
	var entity *entity.ZbCate
	err = dao.ZbCate.Ctx(ctx).Where("id", req.Id).Where("is_delete", 0).Scan(&entity)
	if err != nil {
		g.Log().Error(ctx, "查询类别失败:", err)
		return nil, gerror.New("查询类别失败")
	}
	if entity == nil {
		return nil, gerror.New("类别不存在")
	}

	cateInfo := &v1.ZbCateInfo{}
	if err = gconv.Struct(entity, cateInfo); err != nil {
		g.Log().Error(ctx, "数据转换失败:", err)
		return nil, gerror.New("数据转换失败")
	}

	res = &v1.ZbCateGetOneRes{
		ZbCateInfo: cateInfo,
	}
	return res, nil
}

// GetList 获取招标类别列表
func (s *sZbCate) GetList(ctx context.Context, req *v1.ZbCateGetListReq) (res *v1.ZbCateGetListRes, err error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	query := dao.ZbCate.Ctx(ctx).Where("is_delete", 0)

	if req.Name != "" {
		query = query.WhereLike("name", "%"+req.Name+"%")
	}
	if req.IsDisable != nil {
		query = query.Where("is_disable", *req.IsDisable)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		g.Log().Error(ctx, "查询类别总数失败:", err)
		return nil, gerror.New("查询类别总数失败")
	}

	// 获取列表数据
	var entities []*entity.ZbCate
	err = query.OrderAsc("sort").OrderAsc("id").Page(req.Page, req.PageSize).Scan(&entities)
	if err != nil {
		g.Log().Error(ctx, "查询类别列表失败:", err)
		return nil, gerror.New("查询类别列表失败")
	}

	// 转换数据
	var list []*v1.ZbCateInfo
	if err = gconv.Structs(entities, &list); err != nil {
		g.Log().Error(ctx, "数据转换失败:", err)
		return nil, gerror.New("数据转换失败")
	}

	res = &v1.ZbCateGetListRes{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}
	return res, nil
}

// GetAll 获取所有招标类别
func (s *sZbCate) GetAll(ctx context.Context, req *v1.ZbCateGetAllReq) (res *v1.ZbCateGetAllRes, err error) {
	// 构建查询条件
	query := dao.ZbCate.Ctx(ctx).Where("is_delete", 0)
	if req.IsDisable != nil {
		query = query.Where("is_disable", *req.IsDisable)
	}

	// 获取所有数据
	var entities []*entity.ZbCate
	err = query.OrderAsc("sort").OrderAsc("id").Scan(&entities)
	if err != nil {
		g.Log().Error(ctx, "查询所有类别失败:", err)
		return nil, gerror.New("查询所有类别失败")
	}

	// 转换数据
	var list []*v1.ZbCateInfo
	if err = gconv.Structs(entities, &list); err != nil {
		g.Log().Error(ctx, "数据转换失败:", err)
		return nil, gerror.New("数据转换失败")
	}

	res = &v1.ZbCateGetAllRes{
		List: list,
	}
	return res, nil
}

// UpdateSort 更新招标类别排序
func (s *sZbCate) UpdateSort(ctx context.Context, req *v1.ZbCateUpdateSortReq) (res *v1.ZbCateUpdateSortRes, err error) {
	// 检查类别是否存在
	var existingEntity *entity.ZbCate
	err = dao.ZbCate.Ctx(ctx).Where("id", req.Id).Where("is_delete", 0).Scan(&existingEntity)
	if err != nil {
		g.Log().Error(ctx, "查询类别失败:", err)
		return nil, gerror.New("查询类别失败")
	}
	if existingEntity == nil {
		return nil, gerror.New("类别不存在")
	}

	_, err = dao.ZbCate.Ctx(ctx).Where("id", req.Id).Update(do.ZbCate{
		Sort:      req.Sort,
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "更新类别排序失败:", err)
		return nil, gerror.New("更新类别排序失败")
	}

	g.Log().Info(ctx, "更新类别排序成功:", "id:", req.Id, "sort:", req.Sort)
	res = &v1.ZbCateUpdateSortRes{}
	return res, nil
}

// UpdateStatus 更新招标类别状态
func (s *sZbCate) UpdateStatus(ctx context.Context, req *v1.ZbCateUpdateStatusReq) (res *v1.ZbCateUpdateStatusRes, err error) {
	// 检查类别是否存在
	var existingEntity *entity.ZbCate
	err = dao.ZbCate.Ctx(ctx).Where("id", req.Id).Where("is_delete", 0).Scan(&existingEntity)
	if err != nil {
		g.Log().Error(ctx, "查询类别失败:", err)
		return nil, gerror.New("查询类别失败")
	}
	if existingEntity == nil {
		return nil, gerror.New("类别不存在")
	}

	_, err = dao.ZbCate.Ctx(ctx).Where("id", req.Id).Update(do.ZbCate{
		IsDisable: req.IsDisable,
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "更新类别状态失败:", err)
		return nil, gerror.New("更新类别状态失败")
	}

	g.Log().Info(ctx, "更新类别状态成功:", "id:", req.Id, "is_disable:", req.IsDisable)
	res = &v1.ZbCateUpdateStatusRes{}
	return res, nil
}

// BatchDelete 批量删除招标类别
func (s *sZbCate) BatchDelete(ctx context.Context, req *v1.ZbCateBatchDeleteReq) (res *v1.ZbCateBatchDeleteRes, err error) {
	// 检查类别是否存在
	count, err := dao.ZbCate.Ctx(ctx).WhereIn("id", req.Ids).Where("is_delete", 0).Count()
	if err != nil {
		g.Log().Error(ctx, "查询类别失败:", err)
		return nil, gerror.New("查询类别失败")
	}
	if count == 0 {
		return nil, gerror.New("没有找到要删除的类别")
	}

	// 批量软删除
	result, err := dao.ZbCate.Ctx(ctx).WhereIn("id", req.Ids).Where("is_delete", 0).Update(do.ZbCate{
		IsDelete:  1,
		DeletedAt: gtime.Now(),
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "批量删除类别失败:", err)
		return nil, gerror.New("批量删除类别失败")
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		g.Log().Error(ctx, "获取影响行数失败:", err)
		return nil, gerror.New("获取影响行数失败")
	}

	g.Log().Info(ctx, "批量删除类别成功:", "count:", rowsAffected, "ids:", req.Ids)
	res = &v1.ZbCateBatchDeleteRes{
		Count: int(rowsAffected),
	}
	return res, nil
}
