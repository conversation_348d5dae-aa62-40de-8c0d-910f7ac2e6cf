// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package dao

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// internalZbSearchKeywordsDao is internal type for wrapping dao logic.
type internalZbSearchKeywordsDao struct {
	table   string                      // table is the underlying table name of the DAO.
	group   string                      // group is the database configuration group name of current DAO.
	columns ZbSearchKeywordsColumns     // columns contains all the column names of Table for convenient usage.
}

// ZbSearchKeywordsColumns defines and stores column names for table zb_search_keywords.
type ZbSearchKeywordsColumns struct {
	Id          string // 主键ID
	Keyword     string // 搜索关键词
	SearchCount string // 搜索次数
	ClickCount  string // 点击次数
	Trend       string // 趋势：up上升，down下降，stable稳定
	IsHot       string // 是否热门：0否，1是
	IsNew       string // 是否新增：0否，1是
	Category    string // 分类
	CreatedAt   string // 创建时间
	UpdatedAt   string // 更新时间
}

// zbSearchKeywordsColumns holds the columns for table zb_search_keywords.
var zbSearchKeywordsColumns = ZbSearchKeywordsColumns{
	Id:          "id",
	Keyword:     "keyword",
	SearchCount: "search_count",
	ClickCount:  "click_count",
	Trend:       "trend",
	IsHot:       "is_hot",
	IsNew:       "is_new",
	Category:    "category",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// NewZbSearchKeywordsDao creates and returns a new DAO object for table data access.
func NewZbSearchKeywordsDao() *internalZbSearchKeywordsDao {
	return &internalZbSearchKeywordsDao{
		group:   "default",
		table:   "zb_search_keywords",
		columns: zbSearchKeywordsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *internalZbSearchKeywordsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *internalZbSearchKeywordsDao) Table() string {
	return dao.table
}

// Columns returns the columns of current dao.
func (dao *internalZbSearchKeywordsDao) Columns() ZbSearchKeywordsColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *internalZbSearchKeywordsDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *internalZbSearchKeywordsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *internalZbSearchKeywordsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}

// ZbSearchKeywords is the dao object for table zb_search_keywords.
var ZbSearchKeywords = NewZbSearchKeywordsDao()
