# 微信公众号配置API - 前端对接文档

## 接口概览

| 接口名称 | 方法 | 路径 | 说明 |
|---------|------|------|------|
| 获取配置 | GET | `/wechat/config` | 获取微信公众号配置 |
| 保存配置 | POST | `/wechat/config` | 保存微信公众号配置 |
| 生成Token | POST | `/wechat/config/generate-token` | 生成微信Token |
| 生成AES密钥 | POST | `/wechat/config/generate-aes-key` | 生成消息加解密密钥 |

## 数据模型

### WechatConfigInfo

```typescript
interface WechatConfigInfo {
  id: number;                    // 配置ID
  appid: string;                 // 微信公众号AppID
  app_secret: string;            // 微信公众号AppSecret
  auto_reply_enabled: number;    // 关注自动回复开关：0=关闭，1=开启
  server_url: string;            // 服务器地址URL
  token: string;                 // 微信Token
  encoding_aes_key: string;      // 消息加解密密钥
  encrypt_mode: string;          // 消息加解密方式
  created_at: string | null;     // 创建时间
  updated_at: string | null;     // 更新时间
}
```

### 加解密方式枚举

```typescript
enum EncryptMode {
  PLAINTEXT = 'plaintext',    // 明文模式
  COMPATIBLE = 'compatible',  // 兼容模式
  SAFE = 'safe'              // 安全模式
}
```

## 接口详情

### 1. 获取配置

```typescript
// 请求
interface GetConfigRequest {}

// 响应
interface GetConfigResponse extends WechatConfigInfo {}
```

### 2. 保存配置

```typescript
// 请求
interface SaveConfigRequest {
  appid: string;               // 18位AppID
  app_secret: string;          // 32位AppSecret
  auto_reply_enabled: number;  // 0或1
  server_url: string;          // 有效的URL
  token: string;               // 3-32位Token
  encoding_aes_key: string;    // 43位AES密钥
  encrypt_mode: string;        // plaintext/compatible/safe
}

// 响应
interface SaveConfigResponse {
  success: boolean;            // 保存是否成功
  message: string;             // 保存结果消息
}
```

### 3. 生成Token

```typescript
// 请求
interface GenerateTokenRequest {}

// 响应
interface GenerateTokenResponse {
  token: string;               // 生成的Token
}
```

### 4. 生成AES密钥

```typescript
// 请求
interface GenerateAESKeyRequest {}

// 响应
interface GenerateAESKeyResponse {
  encoding_aes_key: string;    // 生成的43位AES密钥
}
```

## Vue 3 + TypeScript 示例

```typescript
import { ref, reactive } from 'vue'
import axios from 'axios'

// 微信配置管理组合式函数
export function useWechatConfig() {
  const config = ref<WechatConfigInfo | null>(null)
  const loading = ref(false)
  const saving = ref(false)

  // 获取配置
  const getConfig = async () => {
    loading.value = true
    try {
      const response = await axios.get('/wechat/config')
      config.value = response.data.data
      return response.data.data
    } catch (error) {
      console.error('获取微信配置失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 保存配置
  const saveConfig = async (data: SaveConfigRequest) => {
    saving.value = true
    try {
      const response = await axios.post('/wechat/config', data)
      // 保存成功后重新获取配置
      if (response.data.data.success) {
        await getConfig()
      }
      return response.data.data
    } catch (error) {
      console.error('保存微信配置失败:', error)
      throw error
    } finally {
      saving.value = false
    }
  }

  // 生成Token
  const generateToken = async (): Promise<string> => {
    try {
      const response = await axios.post('/wechat/config/generate-token')
      return response.data.data.token
    } catch (error) {
      console.error('生成Token失败:', error)
      throw error
    }
  }

  // 生成AES密钥
  const generateAESKey = async (): Promise<string> => {
    try {
      const response = await axios.post('/wechat/config/generate-aes-key')
      return response.data.data.encoding_aes_key
    } catch (error) {
      console.error('生成AES密钥失败:', error)
      throw error
    }
  }

  return {
    config,
    loading,
    saving,
    getConfig,
    saveConfig,
    generateToken,
    generateAESKey
  }
}
```

## React + TypeScript 示例

```typescript
import { useState, useCallback } from 'react'
import axios from 'axios'

export function useWechatConfig() {
  const [config, setConfig] = useState<WechatConfigInfo | null>(null)
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  const getConfig = useCallback(async () => {
    setLoading(true)
    try {
      const response = await axios.get('/wechat/config')
      setConfig(response.data.data)
      return response.data.data
    } catch (error) {
      console.error('获取微信配置失败:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }, [])

  const saveConfig = useCallback(async (data: SaveConfigRequest) => {
    setSaving(true)
    try {
      const response = await axios.post('/wechat/config', data)
      if (response.data.data.success) {
        await getConfig()
      }
      return response.data.data
    } catch (error) {
      console.error('保存微信配置失败:', error)
      throw error
    } finally {
      setSaving(false)
    }
  }, [getConfig])

  const generateToken = useCallback(async (): Promise<string> => {
    try {
      const response = await axios.post('/wechat/config/generate-token')
      return response.data.data.token
    } catch (error) {
      console.error('生成Token失败:', error)
      throw error
    }
  }, [])

  const generateAESKey = useCallback(async (): Promise<string> => {
    try {
      const response = await axios.post('/wechat/config/generate-aes-key')
      return response.data.data.encoding_aes_key
    } catch (error) {
      console.error('生成AES密钥失败:', error)
      throw error
    }
  }, [])

  return {
    config,
    loading,
    saving,
    getConfig,
    saveConfig,
    generateToken,
    generateAESKey
  }
}
```

## 表单验证规则

```typescript
// 表单验证规则
export const wechatConfigRules = {
  appid: [
    { required: true, message: 'AppID不能为空' },
    { len: 18, message: 'AppID长度必须为18位' },
    { pattern: /^wx[a-zA-Z0-9]{16}$/, message: 'AppID格式不正确' }
  ],
  app_secret: [
    { required: true, message: 'AppSecret不能为空' },
    { len: 32, message: 'AppSecret长度必须为32位' },
    { pattern: /^[a-zA-Z0-9]{32}$/, message: 'AppSecret格式不正确' }
  ],
  server_url: [
    { required: true, message: '服务器地址不能为空' },
    { type: 'url', message: '请输入有效的URL地址' }
  ],
  token: [
    { required: true, message: 'Token不能为空' },
    { min: 3, max: 32, message: 'Token长度必须在3-32位之间' }
  ],
  encoding_aes_key: [
    { required: true, message: '消息加解密密钥不能为空' },
    { len: 43, message: '消息加解密密钥长度必须为43位' },
    { pattern: /^[a-zA-Z0-9]{43}$/, message: '消息加解密密钥格式不正确' }
  ],
  encrypt_mode: [
    { required: true, message: '消息加解密方式不能为空' }
  ]
}
```

## 使用示例

### 页面组件示例（Vue 3）

```vue
<template>
  <div class="wechat-config">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <el-form-item label="AppID" prop="appid">
        <el-input v-model="form.appid" placeholder="请输入微信公众号AppID" />
      </el-form-item>
      
      <el-form-item label="AppSecret" prop="app_secret">
        <el-input v-model="form.app_secret" type="password" placeholder="请输入微信公众号AppSecret" />
      </el-form-item>
      
      <el-form-item label="服务器地址" prop="server_url">
        <el-input v-model="form.server_url" placeholder="请输入服务器地址URL" />
      </el-form-item>
      
      <el-form-item label="Token" prop="token">
        <el-input v-model="form.token" placeholder="请输入微信Token">
          <template #append>
            <el-button @click="handleGenerateToken">生成</el-button>
          </template>
        </el-input>
      </el-form-item>
      
      <el-form-item label="消息加解密密钥" prop="encoding_aes_key">
        <el-input v-model="form.encoding_aes_key" placeholder="请输入消息加解密密钥">
          <template #append>
            <el-button @click="handleGenerateAESKey">生成</el-button>
          </template>
        </el-input>
      </el-form-item>
      
      <el-form-item label="加解密方式" prop="encrypt_mode">
        <el-select v-model="form.encrypt_mode" placeholder="请选择消息加解密方式">
          <el-option label="明文模式" value="plaintext" />
          <el-option label="兼容模式" value="compatible" />
          <el-option label="安全模式" value="safe" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="关注自动回复">
        <el-switch v-model="form.auto_reply_enabled" :active-value="1" :inactive-value="0" />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="handleSave" :loading="saving">保存配置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useWechatConfig } from './composables/useWechatConfig'
import { wechatConfigRules } from './rules/wechatConfigRules'

const { config, loading, saving, getConfig, saveConfig, generateToken, generateAESKey } = useWechatConfig()

const formRef = ref()
const form = ref({
  appid: '',
  app_secret: '',
  auto_reply_enabled: 0,
  server_url: '',
  token: '',
  encoding_aes_key: '',
  encrypt_mode: 'safe'
})

const rules = wechatConfigRules

// 生成Token
const handleGenerateToken = async () => {
  try {
    const token = await generateToken()
    form.value.token = token
  } catch (error) {
    // 错误处理
  }
}

// 生成AES密钥
const handleGenerateAESKey = async () => {
  try {
    const aesKey = await generateAESKey()
    form.value.encoding_aes_key = aesKey
  } catch (error) {
    // 错误处理
  }
}

// 保存配置
const handleSave = async () => {
  try {
    await formRef.value.validate()
    const result = await saveConfig(form.value)
    if (result.success) {
      // 保存成功提示
    }
  } catch (error) {
    // 错误处理
  }
}

// 初始化
onMounted(async () => {
  try {
    const configData = await getConfig()
    if (configData.id > 0) {
      Object.assign(form.value, configData)
    }
  } catch (error) {
    // 错误处理
  }
})
</script>
```

## 注意事项

1. **数据验证**: 前端需要进行严格的数据格式验证
2. **敏感信息**: AppSecret等敏感信息需要安全处理
3. **错误处理**: 完善的错误处理和用户提示
4. **权限控制**: 确保用户有相应的配置权限
5. **实时更新**: 保存成功后及时更新本地数据
