package sysConfigGroup

import (
	v1 "admin-server/api/sys_config_group/v1"
	"admin-server/internal/dao"
	"admin-server/internal/model/do"
	"admin-server/internal/model/entity"
	"admin-server/internal/packed"
	"admin-server/internal/service"
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
)

func init() {
	service.RegisterSysConfigGroup(&SsysConfigGroup{})
}

type SsysConfigGroup struct {
}

func (s SsysConfigGroup) Create(ctx context.Context, req *v1.CreateReq) (insertId int64, err error) {
	// 检查分组编码是否已存在
	exists, err := s.CheckGroupCodeExists(ctx, req.Code, 0)
	if err != nil {
		return 0, err
	}
	if exists {
		return 0, gerror.New("分组编码已存在")
	}

	insertId, err = dao.SysConfigGroup.Ctx(ctx).Data(do.SysConfigGroup{
		Name:      req.Name,
		Code:      req.Code,
		Sort:      req.Sort,
		Remark:    req.Remark,
		IsSystem:  req.IsSystem,
		IsDisable: req.IsDisable,
	}).InsertAndGetId()

	return insertId, err
}

func (s SsysConfigGroup) Update(ctx context.Context, req *v1.UpdateReq) (err error) {
	// 检查配置分组是否存在
	exists, err := s.CheckGroupExists(ctx, req.ID)
	if err != nil {
		return err
	}
	if !exists {
		return gerror.New("配置分组不存在")
	}

	// 检查分组编码是否被其他分组使用
	codeExists, err := s.CheckGroupCodeExists(ctx, req.Code, req.ID)
	if err != nil {
		return err
	}
	if codeExists {
		return gerror.New("分组编码已被使用")
	}

	// 更新配置分组信息
	_, err = dao.SysConfigGroup.Ctx(ctx).Where("id", req.ID).Data(do.SysConfigGroup{
		Name:      req.Name,
		Code:      req.Code,
		Sort:      req.Sort,
		Remark:    req.Remark,
		IsSystem:  req.IsSystem,
		IsDisable: req.IsDisable,
	}).Update()

	return err
}

func (s SsysConfigGroup) GetList(ctx context.Context, req *v1.GetListReq) (list []*v1.ConfigGroupInfo, total int, err error) {
	// 构建查询条件
	m := dao.SysConfigGroup.Ctx(ctx).Where("is_delete", packed.NO_DELETE)

	// 添加搜索条件
	if req.Name != "" {
		m = m.WhereLike("name", "%"+req.Name+"%")
	}
	if req.Code != "" {
		m = m.WhereLike("code", "%"+req.Code+"%")
	}
	if req.IsSystem != nil {
		m = m.Where("is_system", *req.IsSystem)
	}
	if req.IsDisable != nil {
		m = m.Where("is_disable", *req.IsDisable)
	}

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	var configGroups []entity.SysConfigGroup
	err = m.Page(req.Page, req.PageSize).OrderAsc("sort").OrderAsc("id").Scan(&configGroups)
	if err != nil {
		return nil, 0, err
	}

	// 转换为ConfigGroupInfo格式，包含配置项数量
	list = make([]*v1.ConfigGroupInfo, len(configGroups))
	for i, group := range configGroups {
		groupInfo := s.entityToConfigGroupInfo(&group)

		// 获取配置项数量
		configCount, err := s.GetConfigCount(ctx, group.Id)
		if err != nil {
			configCount = 0 // 如果获取失败，使用默认值
		}
		groupInfo.ConfigCount = configCount

		list[i] = groupInfo
	}

	return list, total, nil
}

func (s SsysConfigGroup) GetOne(ctx context.Context, id int64) (configGroup *v1.ConfigGroupInfo, err error) {
	var sysConfigGroup entity.SysConfigGroup
	err = dao.SysConfigGroup.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Scan(&sysConfigGroup)
	if err != nil {
		return nil, err
	}

	if sysConfigGroup.Id == 0 {
		return nil, gerror.New("配置分组不存在")
	}

	configGroup = s.entityToConfigGroupInfo(&sysConfigGroup)

	// 获取配置项数量
	configCount, err := s.GetConfigCount(ctx, sysConfigGroup.Id)
	if err != nil {
		configCount = 0
	}
	configGroup.ConfigCount = configCount

	return configGroup, nil
}

func (s SsysConfigGroup) Delete(ctx context.Context, id int64) (err error) {
	// 检查配置分组是否存在
	var group entity.SysConfigGroup
	err = dao.SysConfigGroup.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Scan(&group)
	if err != nil {
		return err
	}
	if group.Id == 0 {
		return gerror.New("配置分组不存在")
	}

	// 检查是否为系统保留分组
	if group.IsSystem == int(packed.YES_SYSTEM) {
		return gerror.New("系统保留分组不可删除")
	}

	// 检查是否有配置项使用此分组
	configCount, err := s.GetConfigCount(ctx, id)
	if err != nil {
		return err
	}
	if configCount > 0 {
		return gerror.New("该配置分组下还有配置项，无法删除")
	}

	// 软删除配置分组
	_, err = dao.SysConfigGroup.Ctx(ctx).Where("id", id).Data(do.SysConfigGroup{
		IsDelete: packed.IS_DELETE,
	}).Update()

	return err
}

func (s SsysConfigGroup) ToggleStatus(ctx context.Context, id int64) (err error) {
	// 检查配置分组是否存在
	var group entity.SysConfigGroup
	err = dao.SysConfigGroup.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Scan(&group)
	if err != nil {
		return err
	}
	if group.Id == 0 {
		return gerror.New("配置分组不存在")
	}

	// 切换状态：0变1，1变0
	newStatus := packed.ENABLE
	if group.IsDisable == int(packed.ENABLE) {
		newStatus = packed.DISABLE
	}

	// 更新状态
	_, err = dao.SysConfigGroup.Ctx(ctx).Where("id", id).Data(do.SysConfigGroup{
		IsDisable: newStatus,
	}).Update()

	return err
}

func (s SsysConfigGroup) CheckGroupExists(ctx context.Context, id int64) (exists bool, err error) {
	count, err := dao.SysConfigGroup.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s SsysConfigGroup) CheckGroupCodeExists(ctx context.Context, code string, excludeId int64) (exists bool, err error) {
	m := dao.SysConfigGroup.Ctx(ctx).Where("code", code).Where("is_delete", packed.NO_DELETE)
	if excludeId > 0 {
		m = m.Where("id !=", excludeId)
	}

	count, err := m.Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s SsysConfigGroup) GetConfigCount(ctx context.Context, groupId int64) (count int, err error) {
	count, err = dao.SysConfig.Ctx(ctx).Where("group_id", groupId).Where("is_delete", packed.NO_DELETE).Count()
	if err != nil {
		return 0, err
	}
	return count, nil
}

// 辅助方法：将entity转换为ConfigGroupInfo
func (s SsysConfigGroup) entityToConfigGroupInfo(group *entity.SysConfigGroup) *v1.ConfigGroupInfo {
	return &v1.ConfigGroupInfo{
		ID:          group.Id,
		Name:        group.Name,
		Code:        group.Code,
		Sort:        group.Sort,
		Remark:      group.Remark,
		IsSystem:    packed.System(group.IsSystem),
		IsDisable:   packed.Disable(group.IsDisable),
		CreatedAt:   group.CreatedAt,
		UpdatedAt:   group.UpdatedAt,
		ConfigCount: 0, // 将在调用处填充
	}
}
