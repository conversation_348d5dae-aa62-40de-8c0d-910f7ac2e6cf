# 订单功能开发完成说明

## 📋 功能概述

按照项目开发规范，完成了订单管理功能的开发，包括前端下单页面集成和后台管理功能。

## 🏗️ 架构设计

严格按照GoFrame四层架构实现：

```
API层 (zb_order.go, v1/zb_order.go)
    ↓
Controller层 (zb_order_v1_*.go)
    ↓
Logic层 (zb_order/zb_order.go)
    ↓
Service层 (zb_order.go)
    ↓
DAO层 (zb_order.go, zb_order_city.go)
    ↓
Model层 (entity/zb_order.go, entity/zb_order_city.go)
```

## 📊 数据库设计

### 主要表结构

#### `zb_order` (订单主表)
- `id` - 主键ID
- `order_sn` - 订单编号 (格式: ZB20250123150405XXXX)
- `good_id` - 套餐ID
- `good_name` - 套餐名称
- `city_count` - 选择的城市数量
- `user_id` - 用户ID
- `price` - 需支付金额 (套餐价格 × 城市数量)
- `amount` - 实际支付金额
- `pay_status` - 支付状态 (0未支付 1已支付)
- 微信支付相关字段
- 时间字段

#### `zb_order_city` (订单城市关联表)
- `id` - 主键ID
- `order_id` - 订单ID
- `city_id` - 城市ID
- `city_name` - 城市名称

## 🚀 API接口

### 后台管理接口

#### 1. 获取订单列表
```http
GET /api/zb_order/list
Authorization: Bearer {token}

参数:
- page: 页码 (默认1)
- page_size: 每页数量 (默认10, 最大50)
- order_sn: 订单编号 (模糊搜索)
- user_id: 用户ID
- pay_status: 支付状态 (0未支付 1已支付)
- start_time: 开始时间 (YYYY-MM-DD)
- end_time: 结束时间 (YYYY-MM-DD)

响应:
{
    "code": 0,
    "data": {
        "list": [...],
        "total": 100
    }
}
```

#### 2. 获取订单详情
```http
GET /api/zb_order/detail?id=1
Authorization: Bearer {token}

响应:
{
    "code": 0,
    "data": {
        "id": 1,
        "order_sn": "ZB20250123150405XXXX",
        "good_name": "VIP套餐",
        "cities": [
            {"city_id": 1, "city_name": "北京"},
            {"city_id": 2, "city_name": "上海"}
        ],
        ...
    }
}
```

#### 3. 更新支付状态
```http
PUT /api/zb_order/pay-status
Authorization: Bearer {token}

请求体:
{
    "id": 1,
    "pay_status": 1,
    "amount": 200.00,
    "transaction_id": "wx_transaction_id",
    "trade_type": "JSAPI",
    "trade_state": "SUCCESS"
}
```

### 移动端接口

#### 创建订单
```http
POST /m/api/zb_order/create
Authorization: Bearer {token}

请求体:
{
    "good_id": 1,
    "good_name": "VIP套餐",
    "price": 100.00,
    "city_ids": [1, 2, 3],
    "remark": "选择城市：北京、上海、深圳"
}

响应:
{
    "code": 0,
    "data": {
        "order_id": 123,
        "order_sn": "ZB20250123150405XXXX"
    }
}
```

## 💻 前端集成

### 下单页面功能

#### 1. 城市选择
- 支持多选城市
- 显示已选城市数量和名称
- 全选/清空功能

#### 2. 套餐选择
- 单选套餐
- 显示套餐价格和优惠信息
- 自动推荐最优惠套餐

#### 3. 价格计算
```javascript
// 总价 = 套餐价格 × 城市数量
const totalPrice = selectedPackage.price * selectedCities.length;
```

#### 4. 订单创建
```javascript
async function createOrder(paymentDetails) {
    const orderData = {
        good_id: selectedPackage.id,
        good_name: selectedPackage.name,
        price: selectedPackage.price,
        city_ids: selectedCities.map(city => city.id),
        remark: `选择城市：${paymentDetails.selectedCities.join('、')}`
    };

    const response = await fetch('/m/api/zb_order/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + getToken()
        },
        body: JSON.stringify(orderData)
    });

    return response.json();
}
```

## 🔐 权限配置

### 后台管理权限
- `zb_order:list` - 查看订单列表
- `zb_order:detail` - 查看订单详情
- `zb_order:pay-status` - 更新支付状态

### 移动端权限
- 创建订单需要用户登录 (token验证)

## 📁 文件结构

```
admin-server/
├── api/zb_order/
│   ├── zb_order.go                    # API接口定义
│   └── v1/zb_order.go                 # 请求响应结构
├── internal/
│   ├── controller/zb_order/
│   │   ├── zb_order.go                # 控制器基础
│   │   ├── zb_order_v1_create.go      # 创建订单
│   │   ├── zb_order_v1_get_list.go    # 获取列表
│   │   ├── zb_order_v1_get_detail.go  # 获取详情
│   │   └── zb_order_v1_update_pay_status.go # 更新支付状态
│   ├── logic/zb_order/
│   │   └── zb_order.go                # 业务逻辑实现
│   ├── service/
│   │   └── zb_order.go                # 服务接口
│   ├── dao/
│   │   ├── zb_order.go                # 订单DAO
│   │   └── zb_order_city.go           # 订单城市DAO
│   └── model/entity/
│       ├── zb_order.go                # 订单实体
│       └── zb_order_city.go           # 订单城市实体
└── resource/template/mobile/
    └── package.html                   # 下单页面(已集成API)
```

## 🧪 测试用例

### 1. 创建订单测试
```bash
curl -X POST "http://localhost:8000/m/api/zb_order/create" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{
       "good_id": 1,
       "good_name": "VIP套餐",
       "price": 100.00,
       "city_ids": [1, 2, 3],
       "remark": "测试订单"
     }'
```

### 2. 查询订单列表测试
```bash
curl "http://localhost:8000/api/zb_order/list?page=1&page_size=10" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 查询订单详情测试
```bash
curl "http://localhost:8000/api/zb_order/detail?id=1" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

## 🔄 业务流程

### 下单流程
1. 用户选择城市 (多选)
2. 用户选择套餐 (单选)
3. 系统计算总价 (套餐价格 × 城市数量)
4. 用户点击"立即支付"
5. 系统创建订单记录
6. 返回订单号，准备支付

### 后台管理流程
1. 管理员查看订单列表
2. 支持多条件搜索订单
3. 查看订单详情和选择的城市
4. 更新订单支付状态 (对接支付回调)

## ⚠️ 注意事项

### 1. 数据一致性
- 使用事务确保订单主表和城市关联表数据一致性
- 订单编号唯一性保证

### 2. 安全性
- 移动端创建订单需要token验证
- 后台管理需要权限验证
- 价格计算在后端进行，防止前端篡改

### 3. 扩展性
- 支持后续添加更多支付方式
- 订单状态可扩展 (如需要)
- 支持订单备注和描述

## 📈 后续优化

### 1. 支付集成
- 集成微信支付
- 支付回调处理
- 支付状态同步

### 2. 订单管理
- 订单导出功能
- 订单统计报表
- 订单状态流转

### 3. 用户体验
- 订单查询页面 (用户端)
- 支付进度提示
- 订单通知功能

---

**开发状态**: ✅ 已完成  
**架构模式**: GoFrame四层架构  
**权限配置**: 已配置  
**测试状态**: 待测试  
**文档版本**: v1.0  
**完成时间**: 2025-01-23
