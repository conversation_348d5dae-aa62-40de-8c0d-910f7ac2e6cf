package sysResourcesGroup

import (
	"admin-server/internal/dao"
	"admin-server/internal/model/do"
	"admin-server/internal/model/entity"
	"admin-server/internal/packed"
	"admin-server/internal/service"
	"context"
	"github.com/gogf/gf/v2/errors/gerror"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

type sSysResourcesGroup struct{}

func init() {
	service.RegisterSysResourcesGroup(New())
}

func New() *sSysResourcesGroup {
	return &sSysResourcesGroup{}
}

// GetResourcesGroupList 获取资源分组列表
func (s *sSysResourcesGroup) GetResourcesGroupList(ctx context.Context, page, pageSize int, name, resourceType string) (list []entity.SysResourcesGroup, total int, err error) {
	query := dao.SysResourcesGroup.Ctx(ctx).Where("is_delete", packed.NO_DELETE)

	// 按名称筛选
	if name != "" {
		query = query.WhereLike("name", "%"+name+"%")
	}

	// 按资源类型筛选
	if resourceType != "" {
		query = query.Where("type", resourceType)
	}

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.OrderDesc("created_at").Limit(offset, pageSize).Scan(&list)
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

// GetResourcesGroupDetail 获取资源分组详情
func (s *sSysResourcesGroup) GetResourcesGroupDetail(ctx context.Context, id int64) (*entity.SysResourcesGroup, error) {
	var resourcesGroup entity.SysResourcesGroup
	err := dao.SysResourcesGroup.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Scan(&resourcesGroup)
	if err != nil {
		return nil, err
	}

	if resourcesGroup.Id == 0 {
		return nil, nil
	}

	return &resourcesGroup, nil
}

// CreateResourcesGroup 创建资源分组
func (s *sSysResourcesGroup) CreateResourcesGroup(ctx context.Context, name, resourceType string) error {
	// 检查名称是否已存在
	exists, err := s.CheckResourcesGroupNameExists(ctx, name, 0)
	if err != nil {
		return err
	}
	if exists {
		return gerror.New("资源分组名称已存在")
	}

	resourcesGroup := &do.SysResourcesGroup{
		Name:      name,
		Type:      resourceType,
		IsDelete:  packed.NO_DELETE,
		CreatedAt: gtime.Now(),
		UpdatedAt: gtime.Now(),
	}

	_, err = dao.SysResourcesGroup.Ctx(ctx).Insert(resourcesGroup)
	if err != nil {
		g.Log().Error(ctx, "创建资源分组失败:", err)
		return err
	}

	g.Log().Info(ctx, "创建资源分组成功:", "name:", name, "type:", resourceType)
	return nil
}

// UpdateResourcesGroup 更新资源分组
func (s *sSysResourcesGroup) UpdateResourcesGroup(ctx context.Context, id int64, name, resourceType string) error {
	// 检查资源分组是否存在
	resourcesGroup, err := s.GetResourcesGroupDetail(ctx, id)
	if err != nil {
		return err
	}
	if resourcesGroup == nil {
		return gerror.New("资源分组不存在")
	}

	// 检查名称是否已存在（排除自己）
	exists, err := s.CheckResourcesGroupNameExists(ctx, name, id)
	if err != nil {
		return err
	}
	if exists {
		return gerror.New("资源分组名称已存在")
	}

	updateData := &do.SysResourcesGroup{
		Name:      name,
		Type:      resourceType,
		UpdatedAt: gtime.Now(),
	}

	_, err = dao.SysResourcesGroup.Ctx(ctx).Where("id", id).Update(updateData)
	if err != nil {
		g.Log().Error(ctx, "更新资源分组失败:", err)
		return err
	}

	g.Log().Info(ctx, "更新资源分组成功:", "id:", id, "name:", name)
	return nil
}

// DeleteResourcesGroup 删除资源分组
func (s *sSysResourcesGroup) DeleteResourcesGroup(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	// 检查是否有关联的资源
	var resourcesCount int
	resourcesCount, err := dao.SysResources.Ctx(ctx).WhereIn("group_id", ids).Where("is_delete", packed.NO_DELETE).Count()
	if err != nil {
		return err
	}
	if resourcesCount > 0 {
		return gerror.New("存在关联的资源，无法删除")
	}

	// 软删除
	_, err = dao.SysResourcesGroup.Ctx(ctx).WhereIn("id", ids).Update(do.SysResourcesGroup{
		IsDelete:  packed.IS_DELETE,
		DeletedAt: gtime.Now(),
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "删除资源分组失败:", err)
		return err
	}

	g.Log().Info(ctx, "删除资源分组成功:", "ids:", ids)
	return nil
}

// GetAllResourcesGroups 获取所有资源分组（用于下拉选择）
func (s *sSysResourcesGroup) GetAllResourcesGroups(ctx context.Context, resourceType string) ([]entity.SysResourcesGroup, error) {
	var list []entity.SysResourcesGroup
	query := dao.SysResourcesGroup.Ctx(ctx).Where("is_delete", packed.NO_DELETE)

	// 按资源类型筛选
	if resourceType != "" {
		query = query.Where("type", resourceType)
	}

	err := query.OrderDesc("created_at").Scan(&list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

// CheckResourcesGroupNameExists 检查资源分组名称是否存在
func (s *sSysResourcesGroup) CheckResourcesGroupNameExists(ctx context.Context, name string, excludeId int64) (bool, error) {
	query := dao.SysResourcesGroup.Ctx(ctx).Where("name", name).Where("is_delete", packed.NO_DELETE)
	if excludeId > 0 {
		query = query.WhereNot("id", excludeId)
	}

	count, err := query.Count()
	if err != nil {
		return false, err
	}

	return count > 0, nil
}
