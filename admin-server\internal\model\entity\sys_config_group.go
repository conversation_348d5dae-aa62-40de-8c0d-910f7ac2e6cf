// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SysConfigGroup is the golang structure for table sys_config_group.
type SysConfigGroup struct {
	Id        int64       `json:"id"        orm:"id"         description:""`               //
	Name      string      `json:"name"      orm:"name"       description:"系统配置分组名称"`       // 系统配置分组名称
	Code      string      `json:"code"      orm:"code"       description:"系统配置分组编码"`       // 系统配置分组编码
	Sort	  int		 `json:"sort"	  orm:"sort"	   description:"排序"`				   // 排序
	Remark    string      `json:"remark"    orm:"remark"     description:"备注"` // 备注
	IsSystem  int         `json:"isSystem"  orm:"is_system"  description:"是否系统保留 1是 0否"`   // 是否系统保留 1是 0否
	IsDisable int         `json:"isDisable" orm:"is_disable" description:"是否禁用: 0=否, 1=是"` // 是否禁用: 0=否, 1=是
	IsDelete  int         `json:"isDelete"  orm:"is_delete"  description:"是否删除: 0=否, 1=是"` // 是否删除: 0=否, 1=是
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`           // 创建时间
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:"更新时间"`           // 更新时间
	DeletedAt *gtime.Time `json:"deletedAt" orm:"deleted_at" description:"删除时间"`           // 删除时间
}
