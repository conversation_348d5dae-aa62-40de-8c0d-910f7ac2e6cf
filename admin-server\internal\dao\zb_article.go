// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-server/internal/dao/internal"
)

// zbArticleDao is the data access object for the table zb_article.
// You can define custom methods on it to extend its functionality as needed.
type zbArticleDao struct {
	*internal.ZbArticleDao
}

var (
	// ZbArticle is a globally accessible object for table zb_article operations.
	ZbArticle = zbArticleDao{internal.NewZbArticleDao()}
)

// Add your custom methods and functionality below.
