# 移动端API使用说明

## 概述

移动端可以直接使用现有的API接口，无需单独创建移动端专用接口。所有的API都支持跨域访问，可以在移动端页面中直接调用。

## 可用的API接口

### 1. 套餐管理API

#### 获取可用套餐
- **接口**: `GET /zb_good/active`
- **说明**: 获取所有启用的套餐列表
- **用途**: 移动端套餐列表页面
- **示例**:
```javascript
fetch('/zb_good/active')
  .then(response => response.json())
  .then(data => {
    if (data.code === 0) {
      console.log('套餐列表:', data.data.list);
    }
  });
```

#### 获取套餐详情
- **接口**: `GET /zb_good/{id}`
- **说明**: 获取指定套餐的详细信息
- **用途**: 移动端套餐详情页面
- **示例**:
```javascript
fetch('/zb_good/1')
  .then(response => response.json())
  .then(data => {
    if (data.code === 0) {
      console.log('套餐详情:', data.data);
    }
  });
```

#### 获取所有套餐
- **接口**: `GET /zb_good/all`
- **说明**: 获取所有套餐（包括禁用的）
- **参数**: `is_disable` (可选) - 0=启用，1=禁用
- **示例**:
```javascript
// 只获取启用的套餐
fetch('/zb_good/all?is_disable=0')
  .then(response => response.json())
  .then(data => {
    if (data.code === 0) {
      console.log('启用的套餐:', data.data.list);
    }
  });
```

### 2. 招标类别API

#### 获取所有类别
- **接口**: `GET /zb_cate/all`
- **说明**: 获取所有招标类别
- **参数**: `is_disable` (可选) - 0=启用，1=禁用
- **用途**: 移动端类别选择、筛选等
- **示例**:
```javascript
// 获取所有启用的类别
fetch('/zb_cate/all?is_disable=0')
  .then(response => response.json())
  .then(data => {
    if (data.code === 0) {
      console.log('类别列表:', data.data.list);
    }
  });
```

### 3. 开通城市API

#### 获取城市树形结构
- **接口**: `GET /zb_city/tree`
- **说明**: 获取城市的树形结构
- **参数**: `is_disable` (可选) - 0=启用，1=禁用
- **用途**: 移动端城市选择
- **示例**:
```javascript
fetch('/zb_city/tree?is_disable=0')
  .then(response => response.json())
  .then(data => {
    if (data.code === 0) {
      console.log('城市树:', data.data.list);
    }
  });
```

#### 获取所有城市
- **接口**: `GET /zb_city/all`
- **说明**: 获取所有城市（平铺结构）
- **参数**: `is_disable` (可选) - 0=启用，1=禁用
- **示例**:
```javascript
fetch('/zb_city/all?is_disable=0')
  .then(response => response.json())
  .then(data => {
    if (data.code === 0) {
      console.log('城市列表:', data.data.list);
    }
  });
```

## 移动端页面中的使用示例

### 在列表页面中使用多个API

```javascript
// 同时获取套餐、类别和城市数据
async function loadPageData() {
  try {
    const [goodsRes, catesRes, citiesRes] = await Promise.all([
      fetch('/zb_good/active'),
      fetch('/zb_cate/all?is_disable=0'),
      fetch('/zb_city/all?is_disable=0')
    ]);

    const goods = await goodsRes.json();
    const cates = await catesRes.json();
    const cities = await citiesRes.json();

    if (goods.code === 0 && cates.code === 0 && cities.code === 0) {
      // 渲染页面数据
      renderGoods(goods.data.list);
      renderCategories(cates.data.list);
      renderCities(cities.data.list);
    }
  } catch (error) {
    console.error('加载数据失败:', error);
  }
}
```

### 带筛选条件的数据获取

```javascript
// 根据用户选择的条件筛选套餐
function filterGoods(minPrice, maxPrice, categoryName) {
  const params = new URLSearchParams();
  if (minPrice) params.append('min_price', minPrice);
  if (maxPrice) params.append('max_price', maxPrice);
  if (categoryName) params.append('name', categoryName);
  
  fetch(`/zb_good/list?${params.toString()}`)
    .then(response => response.json())
    .then(data => {
      if (data.code === 0) {
        renderFilteredGoods(data.data.list);
      }
    });
}
```

## 错误处理

### 统一的错误处理函数

```javascript
function handleApiResponse(response) {
  return response.json().then(data => {
    if (data.code === 0) {
      return data.data;
    } else {
      throw new Error(data.message || '请求失败');
    }
  });
}

// 使用示例
fetch('/zb_good/active')
  .then(handleApiResponse)
  .then(data => {
    console.log('套餐数据:', data.list);
  })
  .catch(error => {
    console.error('获取套餐失败:', error.message);
    // 显示错误提示给用户
  });
```

## 跨域配置

所有的API接口都已经配置了CORS支持，可以在移动端页面中直接调用，无需额外配置。

## 数据缓存建议

### 使用localStorage缓存数据

```javascript
// 缓存套餐数据
function getCachedGoods() {
  const cached = localStorage.getItem('goods_cache');
  const cacheTime = localStorage.getItem('goods_cache_time');
  
  // 缓存5分钟
  if (cached && cacheTime && (Date.now() - parseInt(cacheTime)) < 5 * 60 * 1000) {
    return JSON.parse(cached);
  }
  
  return null;
}

function setCachedGoods(goods) {
  localStorage.setItem('goods_cache', JSON.stringify(goods));
  localStorage.setItem('goods_cache_time', Date.now().toString());
}

// 获取套餐数据（带缓存）
async function getGoods() {
  let goods = getCachedGoods();
  
  if (!goods) {
    const response = await fetch('/zb_good/active');
    const data = await response.json();
    
    if (data.code === 0) {
      goods = data.data.list;
      setCachedGoods(goods);
    }
  }
  
  return goods;
}
```

## 性能优化建议

1. **数据缓存**: 对不经常变化的数据（如类别、城市）进行本地缓存
2. **懒加载**: 详情页面数据在需要时才加载
3. **防抖处理**: 搜索功能使用防抖避免频繁请求
4. **错误重试**: 网络错误时提供重试机制

## 注意事项

1. **API路径**: 所有API路径都是相对路径，会自动使用当前域名
2. **参数格式**: GET请求参数使用URL查询字符串，POST请求使用JSON格式
3. **响应格式**: 所有API都返回统一的JSON格式：`{code: 0, message: "success", data: {...}}`
4. **错误码**: `code=0` 表示成功，其他值表示错误
5. **数据类型**: 注意数字类型的字段（如价格、ID等）的处理

## 移动端特殊考虑

1. **网络状况**: 移动端网络可能不稳定，需要添加加载状态和错误处理
2. **数据量**: 移动端应该优先使用返回数据较少的接口（如 `/active` 而不是 `/list`）
3. **缓存策略**: 合理使用缓存减少网络请求
4. **用户体验**: 提供友好的加载提示和错误信息
