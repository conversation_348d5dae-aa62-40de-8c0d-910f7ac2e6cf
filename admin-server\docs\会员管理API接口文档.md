# 会员管理API接口文档

## 接口概述

会员管理模块提供了完整的CRUD功能（不包括添加功能），支持会员信息的查询、更新、删除等操作。所有接口都需要JWT认证和相应的权限验证。

## 基础信息

- **基础URL**: `http://your-domain.com`
- **认证方式**: JWT Token（在请求头中添加 `Authorization: Bearer {token}`）
- **数据格式**: JSON
- **字符编码**: UTF-8

## 数据模型

### 会员信息结构 (ZbUser)

```json
{
  "id": 1,                                    // 会员ID
  "nickname": "张三",                         // 昵称
  "avatar": "http://example.com/avatar.jpg",  // 头像URL
  "openid": "wx_openid_123456",              // 微信OpenID
  "isDisable": 0,                            // 是否禁用：0=否，1=是
  "isDelete": 0,                             // 是否删除：0=否，1=是
  "effectiveStart": "2025-01-01T00:00:00Z",  // 有效开始日期
  "effectiveEnd": "2025-12-31T00:00:00Z",    // 有效结束日期
  "createdAt": "2025-01-01T10:00:00Z",       // 注册时间
  "updatedAt": "2025-01-01T10:00:00Z",       // 更新时间
  "deletedAt": null,                         // 删除时间
  "effectiveStatus": 1                       // 是否有效：0=无效，1=有效
}
```

## API接口列表

### 1. 获取会员列表

**接口地址**: `GET /zb_user/list`

**接口描述**: 获取会员列表，支持分页和多条件筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页数量，默认10，最大100 |
| nickname | string | 否 | 昵称（模糊搜索） |
| openid | string | 否 | 微信OpenID（模糊搜索） |
| isDisable | int | 否 | 是否禁用：0=否，1=是 |
| effectiveStatus | int | 否 | 是否有效：0=无效，1=有效 |
| startTime | string | 否 | 开始时间（注册时间范围查询，格式：2006-01-02 15:04:05） |
| endTime | string | 否 | 结束时间（注册时间范围查询，格式：2006-01-02 15:04:05） |

**请求示例**:
```bash
GET /zb_user/list?page=1&pageSize=10&nickname=张&isDisable=0
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "list": [
      {
        "id": 1,
        "nickname": "张三",
        "avatar": "http://example.com/avatar.jpg",
        "openid": "wx_openid_123456",
        "isDisable": 0,
        "isDelete": 0,
        "effectiveStart": "2025-01-01T00:00:00Z",
        "effectiveEnd": "2025-12-31T00:00:00Z",
        "createdAt": "2025-01-01T10:00:00Z",
        "updatedAt": "2025-01-01T10:00:00Z",
        "deletedAt": null,
        "effectiveStatus": 1
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10
  }
}
```

### 2. 获取会员详情

**接口地址**: `GET /zb_user/detail`

**接口描述**: 根据会员ID获取会员详细信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 会员ID |

**请求示例**:
```bash
GET /zb_user/detail?id=1
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "user": {
      "id": 1,
      "nickname": "张三",
      "avatar": "http://example.com/avatar.jpg",
      "openid": "wx_openid_123456",
      "isDisable": 0,
      "isDelete": 0,
      "effectiveStart": "2025-01-01T00:00:00Z",
      "effectiveEnd": "2025-12-31T00:00:00Z",
      "createdAt": "2025-01-01T10:00:00Z",
      "updatedAt": "2025-01-01T10:00:00Z",
      "deletedAt": null,
      "effectiveStatus": 1
    }
  }
}
```

### 3. 更新会员信息

**接口地址**: `PUT /zb_user/update`

**接口描述**: 更新会员基本信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 会员ID |
| nickname | string | 否 | 昵称 |
| avatar | string | 否 | 头像URL |
| isDisable | int | 否 | 是否禁用：0=否，1=是 |
| effectiveStart | string | 否 | 有效开始日期（格式：2006-01-02） |
| effectiveEnd | string | 否 | 有效结束日期（格式：2006-01-02） |
| effectiveStatus | int | 否 | 是否有效：0=无效，1=有效 |

**请求示例**:
```json
{
  "id": 1,
  "nickname": "李四",
  "avatar": "http://example.com/new_avatar.jpg",
  "isDisable": 0
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "message": "更新成功"
  }
}
```

### 4. 删除会员

**接口地址**: `DELETE /zb_user/delete`

**接口描述**: 删除指定会员（软删除）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 会员ID |

**请求示例**:
```json
{
  "id": 1
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "message": "删除成功"
  }
}
```

### 5. 批量删除会员

**接口地址**: `DELETE /zb_user/batch_delete`

**接口描述**: 批量删除多个会员（软删除）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | array | 是 | 会员ID列表 |

**请求示例**:
```json
{
  "ids": [1, 2, 3, 4, 5]
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "message": "批量删除成功"
  }
}
```

### 6. 更新会员状态

**接口地址**: `PUT /zb_user/status`

**接口描述**: 更新会员的启用/禁用状态

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 会员ID |
| isDisable | int | 是 | 是否禁用：0=否，1=是 |

**请求示例**:
```json
{
  "id": 1,
  "isDisable": 1
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "message": "禁用成功"
  }
}
```

### 7. 更新会员VIP有效期

**接口地址**: `PUT /zb_user/vip_period`

**接口描述**: 更新会员的VIP有效期，系统会自动计算有效状态

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 会员ID |
| effectiveStart | string | 是 | 有效开始日期（格式：2006-01-02） |
| effectiveEnd | string | 是 | 有效结束日期（格式：2006-01-02） |

**请求示例**:
```json
{
  "id": 1,
  "effectiveStart": "2025-01-01",
  "effectiveEnd": "2025-12-31"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "message": "VIP有效期更新成功"
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 操作成功 |
| 50 | 参数错误 |
| 51 | 数据不存在 |
| 500 | 服务器内部错误 |
| 401 | 未授权（Token无效或过期） |
| 403 | 权限不足 |

## 错误响应示例

```json
{
  "code": 51,
  "message": "会员不存在",
  "data": null
}
```

## 注意事项

1. **认证要求**: 所有接口都需要在请求头中携带有效的JWT Token
2. **权限验证**: 需要相应的会员管理权限才能访问这些接口
3. **软删除**: 删除操作为软删除，数据不会真正从数据库中删除
4. **日期格式**: 日期参数统一使用 `2006-01-02` 格式
5. **分页限制**: 每页最大数量为100条记录
6. **VIP状态计算**: 更新VIP有效期时，系统会自动根据当前时间计算有效状态

## 使用示例

### JavaScript/Axios示例

```javascript
// 获取会员列表
const getUsers = async () => {
  try {
    const response = await axios.get('/zb_user/list', {
      params: {
        page: 1,
        pageSize: 10,
        nickname: '张'
      },
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log(response.data);
  } catch (error) {
    console.error('获取会员列表失败:', error);
  }
};

// 更新会员信息
const updateUser = async (userData) => {
  try {
    const response = await axios.put('/zb_user/update', userData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    console.log(response.data);
  } catch (error) {
    console.error('更新会员信息失败:', error);
  }
};
```

### cURL示例

```bash
# 获取会员列表
curl -X GET "http://your-domain.com/zb_user/list?page=1&pageSize=10" \
  -H "Authorization: Bearer your-jwt-token"

# 更新会员信息
curl -X PUT "http://your-domain.com/zb_user/update" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{"id": 1, "nickname": "新昵称"}'

# 删除会员
curl -X DELETE "http://your-domain.com/zb_user/delete" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{"id": 1}'

# 更新VIP有效期
curl -X PUT "http://your-domain.com/zb_user/vip_period" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{"id": 1, "effectiveStart": "2025-01-01", "effectiveEnd": "2025-12-31"}'
```

## 业务逻辑说明

### VIP有效状态计算规则

系统会根据以下规则自动计算会员的VIP有效状态：

1. **有效条件**: 当前时间在有效开始日期和有效结束日期之间（包含边界）
2. **自动更新**: 更新VIP有效期时，系统会自动重新计算有效状态
3. **状态值**:
   - `0`: 无效（当前时间不在有效期内）
   - `1`: 有效（当前时间在有效期内）

### 软删除机制

- 删除操作不会真正删除数据库记录
- 通过设置 `is_delete = 1` 和 `deleted_at` 时间戳来标记删除
- 查询时会自动过滤已删除的记录
- 已删除的记录不会在列表中显示

### 权限要求

访问会员管理接口需要以下权限：

- **查看权限**: 获取会员列表和详情
- **编辑权限**: 更新会员信息、状态、VIP有效期
- **删除权限**: 删除和批量删除会员

## 开发注意事项

### 1. 数据验证

- 所有必填参数都会进行服务端验证
- 日期格式必须严格按照 `2006-01-02` 格式
- ID参数必须为正整数
- 状态值必须为0或1

### 2. 并发处理

- 更新操作使用乐观锁机制
- 建议在前端实现防重复提交
- 批量操作会在事务中执行

### 3. 性能优化

- 列表查询支持索引优化
- 分页查询避免深度分页
- 建议合理设置pageSize大小

### 4. 错误处理

- 统一的错误响应格式
- 详细的错误信息提示
- 支持国际化错误消息

## 更新日志

### v1.0.0 (2025-07-17)
- 初始版本发布
- 实现基础CRUD功能
- 支持会员状态管理
- 支持VIP有效期管理
- 完善的权限验证机制
