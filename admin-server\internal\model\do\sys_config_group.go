// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysConfigGroup is the golang structure of table sys_config_group for DAO operations like Where/Data.
type SysConfigGroup struct {
	g.Meta    `orm:"table:sys_config_group, do:true"`
	Id        interface{} //
	Name      interface{} // 系统配置分组名称
	Code      interface{} // 系统配置分组编码
	Sort	  interface{} // 排序
	Remark    interface{} // 备注
	IsSystem  interface{} // 是否系统保留 1是 0否
	IsDisable interface{} // 是否禁用: 0=否, 1=是
	IsDelete  interface{} // 是否删除: 0=否, 1=是
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 更新时间
	DeletedAt *gtime.Time // 删除时间
}
