package cmd

import (
	"admin-server/internal/controller/mobile"
	"admin-server/internal/controller/search"
	"admin-server/internal/controller/sys_admin"
	"admin-server/internal/controller/sys_auth"
	"admin-server/internal/controller/sys_config"
	"admin-server/internal/controller/sys_config_group"
	"admin-server/internal/controller/sys_dict"
	"admin-server/internal/controller/sys_dict_group"
	"admin-server/internal/controller/sys_login_log"
	"admin-server/internal/controller/sys_menu"
	"admin-server/internal/controller/sys_operate_log"
	"admin-server/internal/controller/sys_resources"
	"admin-server/internal/controller/sys_resources_group"
	"admin-server/internal/controller/sys_role"
	"admin-server/internal/controller/wechat_config"
	"admin-server/internal/controller/wechat_menu"
	"admin-server/internal/controller/zb_article"
	"admin-server/internal/controller/zb_cate"
	"admin-server/internal/controller/zb_city"
	"admin-server/internal/controller/zb_good"
	"admin-server/internal/controller/zb_order"
	"admin-server/internal/controller/zb_user"
	"admin-server/internal/controller/zb_user_browser"
	"admin-server/internal/middleware"
	"admin-server/internal/task"
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"
)

var (
	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "start http server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			s := g.Server()
			s.Group("/", func(group *ghttp.RouterGroup) {
				group.Middleware(func(r *ghttp.Request) {
					r.Response.CORSDefault()
					r.Middleware.Next()
				})
				group.Middleware(ghttp.MiddlewareHandlerResponse)

				// 不需要认证的接口（登录、刷新token）
				group.POST("/auth/login", sys_auth.NewV1().Login)
				group.POST("/auth/refresh", sys_auth.NewV1().RefreshToken)
				// 微信公众号服务器url
				group.ALL("/auth/wechat/serve", sys_auth.NewV1().WechatServe)
			})

			// 配置静态文件服务
			s.AddStaticPath("/uploads", "./uploads")
			s.AddStaticPath("/resource", "./resource")

			// 需要认证的接口
			s.Group("/", func(group *ghttp.RouterGroup) {
				group.Middleware(func(r *ghttp.Request) {
					r.Response.CORSDefault()
					r.Middleware.Next()
				})
				group.Middleware(ghttp.MiddlewareHandlerResponse)
				// jwt auth 中间件
				group.Middleware(middleware.Auth)
				// 权限验证中间件
				group.Middleware(middleware.Permission)
				// 操作日志中间件
				group.Middleware(middleware.OperateLog())

				// 认证相关的需要token的接口
				group.GET("/auth/permissions", sys_auth.NewV1().GetPermissions)
				group.GET("/auth/menus", sys_auth.NewV1().GetMenus)
				group.GET("/auth/userinfo", sys_auth.NewV1().GetUserInfo)
				group.POST("/auth/logout", sys_auth.NewV1().Logout)

				// 其他需要认证和权限验证的接口
				group.Bind(
					sys_admin.NewV1(),
					sys_config.NewV1(),
					sys_config_group.NewV1(),
					sys_menu.NewV1(),
					sys_role.NewV1(),
					sys_login_log.NewV1(),
					sys_operate_log.NewV1(),
					sys_dict_group.NewV1(),
					sys_dict.NewV1(),
					sys_resources_group.NewV1(),
					sys_resources.NewV1(),
					wechat_menu.NewV1(),
					wechat_config.NewV1(),
					zb_article.NewV1(),
					zb_city.NewV1(),
					zb_cate.NewV1(),
					zb_good.NewV1(),
					zb_user.NewV1(),
					zb_order.NewV1(),
				)
			})

			// 手机端页面
			s.Group("/m", func(group *ghttp.RouterGroup) {
				group.Middleware(func(r *ghttp.Request) {
					r.Response.CORSDefault()
					r.Middleware.Next()
				})

				// 手机端页面路由
				mobileController := mobile.NewMobile()
				group.GET("/list", mobileController.List)
				group.GET("/detail", mobileController.Detail)
				group.GET("/vip", mobileController.PackageView)
				group.GET("/search", mobileController.SearchView)
				group.GET("/orders", mobileController.OrderView)
				group.GET("/history", mobileController.HistoryView)
				group.GET("/subscriptions", mobileController.SubscriptionsView)

				// 微信授权相关路由
				group.GET("/auth/login", mobileController.WechatLogin)     // 主动授权登录
				group.GET("/auth/callback", mobileController.AuthCallback) // 授权回调
			})

			// 手机端api（无需token验证）
			s.Group("/m/api", func(group *ghttp.RouterGroup) {
				group.Middleware(func(r *ghttp.Request) {
					r.Response.CORSDefault()
					r.Middleware.Next()
				})
				group.Middleware(ghttp.MiddlewareHandlerResponse)

				// 只提供招标类别接口，无需token验证
				group.GET("/zb_cate/all", zb_cate.NewV1().GetAll)
				group.GET("/zb_city/tree", zb_city.NewV1().GetTree)
				group.GET("/zb_good/all", zb_good.NewV1().GetAll)
				group.POST("/get_wechat_jssdk", mobile.NewMobile().GetWechatJssdk)
				group.GET("/zb_article/mobileList", zb_article.NewV1().GetListForMobile)
				group.GET("/zb_article/stats", zb_article.NewV1().GetStats)

				// 搜索相关接口
				group.Bind(search.NewV1())
				// 订单相关接口
				group.POST("/zb_order/create", zb_order.NewV1().Create)
				group.GET("/zb_order/my-list", zb_order.NewV1().GetMyList)
				group.GET("/zb_order/my-stats", zb_order.NewV1().GetMyStats)
				group.GET("/zb_order/my-subscriptions", zb_order.NewV1().GetMySubscriptions)
				// 用户浏览记录接口
				group.Bind(zb_user_browser.NewV1())
			})

			// 404
			s.BindStatusHandler(404, middleware.NotFound)

			// 初始化定时任务
			task.InitSearchTrendTask()

			s.Run()
			return nil
		},
	}
)
