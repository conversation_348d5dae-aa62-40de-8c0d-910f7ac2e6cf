// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysLoginLog is the golang structure of table sys_login_log for DAO operations like Where/Data.
type SysLoginLog struct {
	g.Meta    `orm:"table:sys_login_log, do:true"`
	Id        interface{} //
	AdminId   interface{} // 管理员id
	Username  interface{} // 登录账号
	Ip        interface{} // 登录ip
	Os        interface{} // 操作系统
	Browser   interface{} // 浏览器
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 更新时间
}
