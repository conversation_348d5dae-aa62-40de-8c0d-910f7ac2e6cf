# 管理员禁用/启用功能说明

## 功能概述

管理员禁用/启用功能允许系统管理员控制其他管理员账户的访问权限，而无需删除账户。这是一个重要的安全管理功能。

## 功能特性

### 1. 禁用功能
- **目的**: 临时阻止管理员登录系统
- **场景**: 管理员违规、临时离职、安全审查等
- **效果**: 管理员无法登录，但账户数据保留

### 2. 启用功能
- **目的**: 恢复管理员的登录权限
- **场景**: 审查结束、重新入职、解除限制等
- **效果**: 管理员可以正常登录系统

## API接口

### 禁用管理员

**接口**: `PUT /sys_admin/{id}/disable`

**功能**: 将指定管理员设置为禁用状态

**业务逻辑**:
1. 验证管理员是否存在
2. 检查管理员是否已被禁用
3. 更新 `is_disable` 字段为 1
4. 返回操作结果

**错误处理**:
- 管理员不存在
- 管理员已被禁用
- 数据库操作失败

### 启用管理员

**接口**: `PUT /sys_admin/{id}/enable`

**功能**: 将指定管理员设置为启用状态

**业务逻辑**:
1. 验证管理员是否存在
2. 检查管理员是否已是启用状态
3. 更新 `is_disable` 字段为 0
4. 返回操作结果

**错误处理**:
- 管理员不存在
- 管理员已是启用状态
- 数据库操作失败

## 数据库字段

```sql
`is_disable` TINYINT NOT NULL DEFAULT 0 COMMENT '是否禁用: 0=否, 1=是'
```

**字段值说明**:
- `0` (ENABLE): 启用状态，管理员可以正常登录
- `1` (DISABLE): 禁用状态，管理员无法登录

## 使用示例

### 1. 禁用管理员

```bash
curl -X PUT http://localhost:8000/sys_admin/2/disable \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**成功响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

**失败响应**:
```json
{
  "code": 1,
  "message": "管理员已被禁用",
  "data": null
}
```

### 2. 启用管理员

```bash
curl -X PUT http://localhost:8000/sys_admin/2/enable \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**成功响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

**失败响应**:
```json
{
  "code": 1,
  "message": "管理员已是启用状态",
  "data": null
}
```

## 前端集成建议

### 1. 列表页面
- 在管理员列表中显示状态标识
- 提供快速禁用/启用按钮
- 使用不同颜色区分状态

### 2. 状态显示
```javascript
// 状态显示示例
function getStatusDisplay(isDisable) {
  return isDisable === 1 
    ? '<span class="status-disabled">已禁用</span>'
    : '<span class="status-enabled">正常</span>';
}
```

### 3. 操作按钮
```javascript
// 动态显示操作按钮
function getActionButton(admin) {
  if (admin.is_disable === 1) {
    return `<button onclick="enableAdmin(${admin.id})">启用</button>`;
  } else {
    return `<button onclick="disableAdmin(${admin.id})">禁用</button>`;
  }
}
```

### 4. 操作函数
```javascript
async function disableAdmin(id) {
  try {
    const response = await fetch(`/sys_admin/${id}/disable`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      alert('管理员已禁用');
      location.reload(); // 刷新页面
    } else {
      const error = await response.json();
      alert(error.message);
    }
  } catch (error) {
    alert('操作失败: ' + error.message);
  }
}

async function enableAdmin(id) {
  try {
    const response = await fetch(`/sys_admin/${id}/enable`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      alert('管理员已启用');
      location.reload(); // 刷新页面
    } else {
      const error = await response.json();
      alert(error.message);
    }
  } catch (error) {
    alert('操作失败: ' + error.message);
  }
}
```

## 安全考虑

### 1. 权限控制
- 只有超级管理员或有相应权限的管理员才能执行禁用/启用操作
- 管理员不能禁用自己的账户
- 超级管理员账户应有特殊保护

### 2. 操作日志
- 记录所有禁用/启用操作
- 包含操作人、被操作人、操作时间、操作原因等信息
- 便于审计和追踪

### 3. 通知机制
- 被禁用的管理员应收到通知
- 重要操作应有邮件或短信提醒
- 管理员尝试登录时显示禁用原因

## 业务流程

### 禁用流程
1. 管理员选择要禁用的账户
2. 系统验证操作权限
3. 确认禁用操作
4. 更新数据库状态
5. 记录操作日志
6. 发送通知（可选）

### 启用流程
1. 管理员选择要启用的账户
2. 系统验证操作权限
3. 确认启用操作
4. 更新数据库状态
5. 记录操作日志
6. 发送通知（可选）

## 注意事项

1. **状态一致性**: 确保前端显示与后端状态一致
2. **操作确认**: 重要操作应有二次确认
3. **批量操作**: 可考虑支持批量禁用/启用
4. **状态过滤**: 列表查询支持按状态筛选
5. **定期审查**: 建议定期审查禁用账户的必要性

## 扩展功能

### 1. 定时启用
- 支持设置自动启用时间
- 临时禁用功能

### 2. 禁用原因
- 记录禁用原因
- 分类管理禁用类型

### 3. 批量操作
- 支持批量禁用/启用
- 导入/导出功能

### 4. 审批流程
- 重要操作需要审批
- 多级审批机制
