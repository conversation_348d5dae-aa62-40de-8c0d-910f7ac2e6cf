package middleware

import (
	"admin-server/internal/service"
	"encoding/json"
	"fmt"
	"net/url"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gctx"
)

// WechatAuth 微信授权中间件
func WechatAuth() func(r *ghttp.Request) {
	return func(r *ghttp.Request) {
		ctx := r.GetCtx()

		// 获取当前请求的完整URL
		currentURL := getCurrentURL(r)

		// 检查是否已经有code参数（微信回调）
		code := r.Get("code").String()
		if code != "" {
			// 处理微信回调，获取用户信息
			user, err := handleWechatCallback(r, code)
			if err != nil {
				g.Log().Error(ctx, "微信授权回调处理失败:", err)
				r.Response.WriteStatus(500, "授权失败")
				return
			}

			// 将用户信息存储到session或context中
			r.Session.Set("wechat_user", user)

			// 重定向到原始页面（去掉code和state参数）
			originalURL := removeCodeFromURL(currentURL)
			r.Response.RedirectTo(originalURL)
			return
		}

		// 检查session中是否已有用户信息
		sessionUser := r.Session.MustGet("wechat_user")
		if sessionUser != nil && !sessionUser.IsNil() {
			// 用户已授权，继续处理请求
			r.Middleware.Next()
			return
		}

		// 需要微信授权，构建授权URL
		authURL, err := buildWechatAuthURL(currentURL)
		if err != nil {
			g.Log().Error(ctx, "构建微信授权URL失败:", err)
			r.Response.WriteStatus(500, "授权失败")
			return
		}

		// 重定向到微信授权页面
		r.Response.RedirectTo(authURL)
	}
}

// getCurrentURL 获取当前请求的完整URL
func getCurrentURL(r *ghttp.Request) string {
	scheme := "http"
	if r.TLS != nil || r.Header.Get("X-Forwarded-Proto") == "https" {
		scheme = "https"
	}

	host := r.Host
	if host == "" {
		host = r.Header.Get("Host")
	}

	return fmt.Sprintf("%s://%s%s", scheme, host, r.RequestURI)
}

// buildWechatAuthURL 构建微信授权URL
func buildWechatAuthURL(redirectURL string) (string, error) {
	ctx := gctx.New()
	wechatConfig, err := service.WechatConfig().GetConfig(ctx)
	if err != nil {
		return "", fmt.Errorf("微信AppID未配置")
	}
	// 从配置中获取微信应用信息
	appID := wechatConfig.Appid
	if appID == "" {
		return "", fmt.Errorf("微信AppID未配置")
	}

	// 构建授权URL
	baseURL := "https://open.weixin.qq.com/connect/oauth2/authorize"
	params := url.Values{}
	params.Set("appid", appID)
	params.Set("redirect_uri", redirectURL)
	params.Set("response_type", "code")
	params.Set("scope", "snsapi_userinfo") // 获取用户基本信息
	params.Set("state", "STATE")           // 可以设置为随机字符串用于安全验证

	authURL := fmt.Sprintf("%s?%s#wechat_redirect", baseURL, params.Encode())
	return authURL, nil
}

// handleWechatCallback 处理微信授权回调
func handleWechatCallback(r *ghttp.Request, code string) (map[string]interface{}, error) {
	ctx := r.GetCtx()

	// 通过code获取access_token
	tokenInfo, err := getAccessToken(code)
	if err != nil {
		return nil, fmt.Errorf("获取access_token失败: %v", err)
	}

	openid := tokenInfo["openid"].(string)
	accessToken := tokenInfo["access_token"].(string)

	// 获取用户信息
	userInfo, err := getUserInfo(accessToken, openid)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}

	// 处理用户注册或登录
	user, err := handleUserRegisterOrLogin(ctx, userInfo, r.GetClientIp())
	if err != nil {
		return nil, fmt.Errorf("处理用户注册或登录失败: %v", err)
	}

	return user, nil
}

// getAccessToken 通过code获取access_token
func getAccessToken(code string) (map[string]interface{}, error) {
	ctx := gctx.New()
	wechatConfig, err2 := service.WechatConfig().GetConfig(gctx.New())
	if err2 != nil {
		return nil, err2
	}
	appID := wechatConfig.Appid
	appSecret := wechatConfig.AppSecret

	url := fmt.Sprintf("https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code",
		appID, appSecret, code)

	client := g.Client()
	response, err := client.Get(ctx, url)
	if err != nil {
		return nil, err
	}
	defer response.Close()

	result := response.ReadAllString()

	var tokenInfo map[string]interface{}
	if err := json.Unmarshal([]byte(result), &tokenInfo); err != nil {
		return nil, err
	}

	if errCode, exists := tokenInfo["errcode"]; exists && errCode != nil {
		return nil, fmt.Errorf("微信API错误: %v", tokenInfo["errmsg"])
	}

	return tokenInfo, nil
}

// getUserInfo 获取用户信息
func getUserInfo(accessToken, openid string) (map[string]interface{}, error) {
	ctx := gctx.New()

	url := fmt.Sprintf("https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s&lang=zh_CN",
		accessToken, openid)

	client := g.Client()
	response, err := client.Get(ctx, url)
	if err != nil {
		return nil, err
	}
	defer response.Close()

	result := response.ReadAllString()

	var userInfo map[string]interface{}
	if err := json.Unmarshal([]byte(result), &userInfo); err != nil {
		return nil, err
	}

	if errCode, exists := userInfo["errcode"]; exists && errCode != nil {
		return nil, fmt.Errorf("微信API错误: %v", userInfo["errmsg"])
	}

	return userInfo, nil
}

// handleUserRegisterOrLogin 处理用户注册或登录
func handleUserRegisterOrLogin(ctx g.Ctx, wechatUserInfo map[string]interface{}, clientIP string) (map[string]interface{}, error) {
	openid := wechatUserInfo["openid"].(string)
	nickname := wechatUserInfo["nickname"].(string)
	avatar := wechatUserInfo["headimgurl"].(string)

	// 查询用户是否已存在
	existingUser, err := service.ZbUser().GetUserByOpenid(ctx, openid)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %v", err)
	}

	if existingUser != nil {
		// 用户已存在，更新最后登录信息
		err = service.ZbUser().UpdateLastLogin(ctx, existingUser.Id, clientIP)
		if err != nil {
			g.Log().Warning(ctx, "更新用户最后登录信息失败:", err)
		}

		// 检查用户状态
		if existingUser.IsDisable == 1 {
			return nil, fmt.Errorf("用户已被禁用")
		}

		if existingUser.IsDelete == 1 {
			return nil, fmt.Errorf("用户已被删除")
		}

		// 返回用户信息
		return map[string]interface{}{
			"id":       existingUser.Id,
			"nickname": existingUser.Nickname,
			"avatar":   existingUser.Avatar,
			"openid":   existingUser.Openid,
		}, nil
	}

	// 用户不存在，创建新用户
	newUser, err := service.ZbUser().CreateFromWechat(ctx, openid, nickname, avatar, clientIP)
	if err != nil {
		return nil, fmt.Errorf("创建用户失败: %v", err)
	}

	return map[string]interface{}{
		"id":       newUser.Id,
		"nickname": newUser.Nickname,
		"avatar":   newUser.Avatar,
		"openid":   newUser.Openid,
	}, nil
}

// removeCodeFromURL 从URL中移除code和state参数
func removeCodeFromURL(originalURL string) string {
	u, err := url.Parse(originalURL)
	if err != nil {
		return originalURL
	}

	query := u.Query()
	query.Del("code")
	query.Del("state")
	u.RawQuery = query.Encode()

	return u.String()
}

// GetCurrentWechatUser 获取当前微信用户信息
func GetCurrentWechatUser(r *ghttp.Request) map[string]interface{} {
	sessionUser := r.Session.MustGet("wechat_user")
	if sessionUser == nil || sessionUser.IsNil() {
		return nil
	}

	// 将 *gvar.Var 转换为 map[string]interface{}
	if userMap, ok := sessionUser.Interface().(map[string]interface{}); ok {
		return userMap
	}

	return nil
}
