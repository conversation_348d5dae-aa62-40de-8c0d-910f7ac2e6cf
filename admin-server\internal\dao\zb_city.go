// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-server/internal/dao/internal"
)

// zbCityDao is the data access object for the table zb_city.
// You can define custom methods on it to extend its functionality as needed.
type zbCityDao struct {
	*internal.ZbCityDao
}

var (
	// ZbCity is a globally accessible object for table zb_city operations.
	ZbCity = zbCityDao{internal.NewZbCityDao()}
)

// Add your custom methods and functionality below.
