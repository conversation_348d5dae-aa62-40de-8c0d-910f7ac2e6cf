package sys_dict

import (
	"context"

	v1 "admin-server/api/sys_dict/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error) {
	err = service.SysDict().CreateDict(ctx, req.GroupId, req.Name, req.Value, req.Code, req.Remark, req.Sort, req.IsDisable)
	if err != nil {
		return nil, err
	}

	return &v1.CreateRes{}, nil
}
