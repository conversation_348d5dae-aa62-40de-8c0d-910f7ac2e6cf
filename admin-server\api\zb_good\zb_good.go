package zb_good

import (
	v1 "admin-server/api/zb_good/v1"
	"context"
)

type IZbGoodV1 interface {
	Create(ctx context.Context, req *v1.ZbGoodCreateReq) (res *v1.ZbGoodCreateRes, err error)
	Update(ctx context.Context, req *v1.ZbGoodUpdateReq) (res *v1.ZbGoodUpdateRes, err error)
	Delete(ctx context.Context, req *v1.ZbGoodDeleteReq) (res *v1.ZbGoodDeleteRes, err error)
	GetOne(ctx context.Context, req *v1.ZbGoodGetOneReq) (res *v1.ZbGoodGetOneRes, err error)
	GetList(ctx context.Context, req *v1.ZbGoodGetListReq) (res *v1.ZbGoodGetListRes, err error)
	GetAll(ctx context.Context, req *v1.ZbGoodGetAllReq) (res *v1.ZbGoodGetAllRes, err error)
	UpdateStatus(ctx context.Context, req *v1.ZbGoodUpdateStatusReq) (res *v1.ZbGoodUpdateStatusRes, err error)
	BatchDelete(ctx context.Context, req *v1.ZbGoodBatchDeleteReq) (res *v1.ZbGoodBatchDeleteRes, err error)
	GetActiveGoods(ctx context.Context, req *v1.ZbGoodGetActiveGoodsReq) (res *v1.ZbGoodGetActiveGoodsRes, err error)
}
