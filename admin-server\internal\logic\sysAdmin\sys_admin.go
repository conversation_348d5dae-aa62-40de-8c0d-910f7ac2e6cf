package sysAdmin

import (
	v1 "admin-server/api/sys_admin/v1"
	"admin-server/internal/dao"
	"admin-server/internal/model/do"
	"admin-server/internal/model/entity"
	"admin-server/internal/packed"
	"admin-server/internal/service"
	"context"

	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/errors/gerror"
)

func init() {
	service.RegisterSysAdmin(&SsysAdmin{})
}

type SsysAdmin struct {
}

func (s SsysAdmin) Create(ctx context.Context, req *v1.CreateReq) (insertId int64, err error) {
	// 检测账号是否已存在
	if err = s.checkUser(ctx, req.Username); err != nil {
		return 0, err
	}

	insertId, err = dao.SysAdmins.Ctx(ctx).Data(do.SysAdmins{
		Username:  req.Username,
		Nickname:  req.Nickname,
		Password:  s.encryptPassword(req.Password),
		IsSuper:   req.<PERSON>upe<PERSON>,
		IsDisable: req.IsDisable,
	}).InsertAndGetId()
	if err != nil {
		return 0, err
	}

	// 分配角色
	if len(req.RoleIds) > 0 {
		err = s.AssignRoles(ctx, insertId, req.RoleIds)
		if err != nil {
			return 0, err
		}
	}

	return insertId, nil
}

func (s SsysAdmin) Update(ctx context.Context, req *v1.UpdateReq) (err error) {
	// 检查管理员是否存在
	count, err := dao.SysAdmins.Ctx(ctx).Where("id", req.ID).Where("is_delete", packed.NO_DELETE).Count()
	if err != nil {
		return err
	}
	if count == 0 {
		return gerror.New("管理员不存在")
	}

	// 检查用户名是否被其他管理员使用
	count, err = dao.SysAdmins.Ctx(ctx).Where("username", req.Username).Where("id !=", req.ID).Where("is_delete", packed.NO_DELETE).Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return gerror.New("用户名已被使用")
	}

	// 准备更新数据
	updateData := do.SysAdmins{
		Username:  req.Username,
		Nickname:  req.Nickname,
		IsSuper:   req.IsSuper,
		IsDisable: req.IsDisable,
	}

	// 如果传入了密码，则同时更新密码
	if req.Password != "" {
		updateData.Password = s.encryptPassword(req.Password)
	}

	// 更新管理员信息
	_, err = dao.SysAdmins.Ctx(ctx).Where("id", req.ID).Data(updateData).Update()
	if err != nil {
		return err
	}

	// 更新角色分配
	err = s.AssignRoles(ctx, req.ID, req.RoleIds)
	return err
}

func (s SsysAdmin) GetList(ctx context.Context, req *v1.GetListReq) (list []*v1.AdminInfo, total int, err error) {
	// 构建查询条件
	m := dao.SysAdmins.Ctx(ctx).Where("is_delete", packed.NO_DELETE)

	// 添加搜索条件
	if req.Username != "" {
		m = m.WhereLike("username", "%"+req.Username+"%")
	}
	if req.Nickname != "" {
		m = m.WhereLike("nickname", "%"+req.Nickname+"%")
	}
	if req.IsSuper != nil {
		m = m.Where("is_super", *req.IsSuper)
	}
	if req.IsDisable != nil {
		m = m.Where("is_disable", *req.IsDisable)
	}

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	var admins []entity.SysAdmins
	err = m.Page(req.Page, req.PageSize).OrderDesc("id").Scan(&admins)
	if err != nil {
		return nil, 0, err
	}

	// 转换为AdminInfo格式（不包含密码）
	list = make([]*v1.AdminInfo, len(admins))
	for i, admin := range admins {
		adminInfo := &v1.AdminInfo{
			ID:            admin.Id,
			Username:      admin.Username,
			Nickname:      admin.Nickname,
			IsSuper:       packed.Super(admin.IsSuper),
			IsDisable:     packed.Disable(admin.IsDisable),
			LastLoginIp:   admin.LastLoginIp,
			LastLoginTime: admin.LastLoginTime,
			CreatedAt:     admin.CreatedAt,
			UpdatedAt:     admin.UpdatedAt,
		}

		// 获取角色信息
		roles, err := s.GetRoles(ctx, admin.Id)
		if err == nil {
			adminInfo.Roles = roles
		} else {
			adminInfo.Roles = []*v1.RoleInfo{}
		}

		list[i] = adminInfo
	}

	return list, total, nil
}

func (s SsysAdmin) GetOne(ctx context.Context, id int64) (admin *v1.AdminInfo, err error) {
	var sysAdmin entity.SysAdmins
	err = dao.SysAdmins.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Scan(&sysAdmin)
	if err != nil {
		return nil, err
	}

	if sysAdmin.Id == 0 {
		return nil, gerror.New("管理员不存在")
	}

	// 转换为AdminInfo格式（不包含密码）
	admin = &v1.AdminInfo{
		ID:            sysAdmin.Id,
		Username:      sysAdmin.Username,
		Nickname:      sysAdmin.Nickname,
		IsSuper:       packed.Super(sysAdmin.IsSuper),
		IsDisable:     packed.Disable(sysAdmin.IsDisable),
		LastLoginIp:   sysAdmin.LastLoginIp,
		LastLoginTime: sysAdmin.LastLoginTime,
		CreatedAt:     sysAdmin.CreatedAt,
		UpdatedAt:     sysAdmin.UpdatedAt,
	}

	// 获取角色信息
	roles, err := s.GetRoles(ctx, sysAdmin.Id)
	if err == nil {
		admin.Roles = roles
	} else {
		admin.Roles = []*v1.RoleInfo{}
	}

	return admin, nil
}

func (s SsysAdmin) ChangePassword(ctx context.Context, req *v1.ChangePasswordReq) (err error) {
	// 获取管理员信息
	var admin entity.SysAdmins
	err = dao.SysAdmins.Ctx(ctx).Where("id", req.ID).Where("is_delete", packed.NO_DELETE).Scan(&admin)
	if err != nil {
		return err
	}

	if admin.Id == 0 {
		return gerror.New("管理员不存在")
	}

	// 验证原密码
	if admin.Password != s.encryptPassword(req.OldPassword) {
		return gerror.New("原密码错误")
	}

	// 更新密码
	_, err = dao.SysAdmins.Ctx(ctx).Where("id", req.ID).Data(do.SysAdmins{
		Password: s.encryptPassword(req.NewPassword),
	}).Update()

	return err
}

func (s SsysAdmin) Disable(ctx context.Context, id int64) (err error) {
	// 检查管理员是否存在
	count, err := dao.SysAdmins.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Count()
	if err != nil {
		return err
	}
	if count == 0 {
		return gerror.New("管理员不存在")
	}

	// 检查管理员是否已经被禁用
	var admin entity.SysAdmins
	err = dao.SysAdmins.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Scan(&admin)
	if err != nil {
		return err
	}
	if admin.IsDisable == packed.DISABLE {
		return gerror.New("管理员已被禁用")
	}

	// 禁用管理员
	_, err = dao.SysAdmins.Ctx(ctx).Where("id", id).Data(do.SysAdmins{
		IsDisable: packed.DISABLE,
	}).Update()

	return err
}

func (s SsysAdmin) Enable(ctx context.Context, id int64) (err error) {
	// 检查管理员是否存在
	count, err := dao.SysAdmins.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Count()
	if err != nil {
		return err
	}
	if count == 0 {
		return gerror.New("管理员不存在")
	}

	// 检查管理员是否已经被启用
	var admin entity.SysAdmins
	err = dao.SysAdmins.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Scan(&admin)
	if err != nil {
		return err
	}
	if admin.IsDisable == packed.ENABLE {
		return gerror.New("管理员已是启用状态")
	}

	// 启用管理员
	_, err = dao.SysAdmins.Ctx(ctx).Where("id", id).Data(do.SysAdmins{
		IsDisable: packed.ENABLE,
	}).Update()

	return err
}

func (s SsysAdmin) Delete(ctx context.Context, id int64) (err error) {
	// 检查管理员是否存在
	count, err := dao.SysAdmins.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Count()
	if err != nil {
		return err
	}
	if count == 0 {
		return gerror.New("管理员不存在")
	}

	// 清除管理员的角色关联
	_, err = dao.SysAdminRole.Ctx(ctx).Where("admin_id", id).Delete()
	if err != nil {
		return err
	}

	// 软删除管理员
	_, err = dao.SysAdmins.Ctx(ctx).Where("id", id).Data(do.SysAdmins{
		IsDelete: packed.IS_DELETE,
	}).Update()

	return err
}

func (s SsysAdmin) checkUser(ctx context.Context, username string) error {
	count, err := dao.SysAdmins.Ctx(ctx).Where("username", username).Where("is_delete", packed.NO_DELETE).Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return gerror.New("账号已存在")
	}
	return nil
}

func (u *SsysAdmin) encryptPassword(password string) string {
	return gmd5.MustEncryptString(password)
}

// AssignRoles 分配角色
func (s SsysAdmin) AssignRoles(ctx context.Context, adminId int64, roleIds []int64) (err error) {
	// 先删除原有的角色关联
	_, err = dao.SysAdminRole.Ctx(ctx).Where("admin_id", adminId).Delete()
	if err != nil {
		return err
	}

	// 如果没有角色ID，直接返回
	if len(roleIds) == 0 {
		return nil
	}

	// 批量插入新的角色关联
	var data []do.SysAdminRole
	for _, roleId := range roleIds {
		data = append(data, do.SysAdminRole{
			AdminId: adminId,
			RoleId:  roleId,
		})
	}

	_, err = dao.SysAdminRole.Ctx(ctx).Data(data).Insert()
	return err
}

// GetRoles 获取管理员角色列表
func (s SsysAdmin) GetRoles(ctx context.Context, adminId int64) (roles []*v1.RoleInfo, err error) {
	// 查询管理员的角色关联
	var adminRoles []entity.SysAdminRole
	err = dao.SysAdminRole.Ctx(ctx).Where("admin_id", adminId).Scan(&adminRoles)
	if err != nil {
		return nil, err
	}

	if len(adminRoles) == 0 {
		return []*v1.RoleInfo{}, nil
	}

	// 提取角色ID
	var roleIds []int64
	for _, adminRole := range adminRoles {
		roleIds = append(roleIds, adminRole.RoleId)
	}

	// 查询角色信息
	var roleEntities []entity.SysRole
	err = dao.SysRole.Ctx(ctx).Where("id", roleIds).Where("is_delete", packed.NO_DELETE).Scan(&roleEntities)
	if err != nil {
		return nil, err
	}

	// 转换为RoleInfo格式
	roles = make([]*v1.RoleInfo, len(roleEntities))
	for i, role := range roleEntities {
		roles[i] = &v1.RoleInfo{
			ID:   role.Id,
			Name: role.Name,
		}
	}

	return roles, nil
}
