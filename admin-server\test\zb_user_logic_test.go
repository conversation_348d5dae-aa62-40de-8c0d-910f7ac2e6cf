package test

import (
	"admin-server/internal/service"
	"context"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestZbUserLogic 测试会员业务逻辑
func TestZbUserLogic(t *testing.T) {
	// 注意：这些测试需要数据库连接，在实际环境中运行

	gtest.C(t, func(t *gtest.T) {
		// 测试获取会员列表
		list, total, err := service.ZbUser().GetUserList(
			context.Background(),
			1,  // page
			10, // pageSize
			"", // nickname
			"", // openid
			-1, // isDisable
			-1, // vipStatus
			-1, // hasVipPeriod
			"", // startTime
			"", // endTime
		)

		// 如果没有数据库连接，这里会报错，这是正常的
		if err == nil {
			t.AssertGE(total, 0)
			t.AssertLE(len(list), 10)
		}
	})

	gtest.C(t, func(t *gtest.T) {
		// 测试检查会员是否存在
		exists, err := service.ZbUser().CheckUserExists(context.Background(), 1)

		// 如果没有数据库连接，这里会报错，这是正常的
		if err == nil {
			t.AssertIn(exists, []bool{true, false})
		}
	})

	gtest.C(t, func(t *gtest.T) {
		// 测试获取即将过期的VIP会员
		list, err := service.ZbUser().GetExpiredVipUsers(context.Background(), 7)

		// 如果没有数据库连接，这里会报错，这是正常的
		if err == nil {
			t.AssertGE(len(list), 0)
		}
	})
}

// TestZbUserVipPeriodLogic 测试VIP有效期逻辑
func TestZbUserVipPeriodLogic(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 测试更新VIP有效期（模拟测试，不实际执行数据库操作）
		err := service.ZbUser().UpdateVipPeriod(
			context.Background(),
			999999, // 使用一个不存在的ID
			"2025-01-01",
			"2025-12-31",
		)

		// 应该返回"会员不存在"的错误
		t.AssertNE(err, nil)
		t.AssertContains(err.Error(), "会员不存在")
	})

	gtest.C(t, func(t *gtest.T) {
		// 测试日期格式错误
		err := service.ZbUser().UpdateVipPeriod(
			context.Background(),
			1,
			"invalid-date", // 无效日期格式
			"2025-12-31",
		)

		// 应该返回日期格式错误
		t.AssertNE(err, nil)
		t.AssertContains(err.Error(), "开始日期格式错误")
	})

	gtest.C(t, func(t *gtest.T) {
		// 测试结束日期早于开始日期
		err := service.ZbUser().UpdateVipPeriod(
			context.Background(),
			1,
			"2025-12-31",
			"2025-01-01", // 结束日期早于开始日期
		)

		// 应该返回日期逻辑错误
		t.AssertNE(err, nil)
		t.AssertContains(err.Error(), "结束日期不能早于开始日期")
	})
}

// BenchmarkZbUserGetList 性能测试 - 获取会员列表
func BenchmarkZbUserGetList(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _, _ = service.ZbUser().GetUserList(
			context.Background(),
			1,  // page
			10, // pageSize
			"", // nickname
			"", // openid
			-1, // isDisable
			-1, // vipStatus
			-1, // hasVipPeriod
			"", // startTime
			"", // endTime
		)
	}
}

// TestZbUserServiceRegistration 测试服务注册
func TestZbUserServiceRegistration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 测试服务是否正确注册
		zbUserService := service.ZbUser()
		t.AssertNE(zbUserService, nil)
	})
}
