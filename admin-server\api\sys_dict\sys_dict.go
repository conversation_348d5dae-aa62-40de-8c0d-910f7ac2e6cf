package sys_dict

import (
	v1 "admin-server/api/sys_dict/v1"
	"context"
)

type ISysDictV1 interface {
	GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error)
	GetOne(ctx context.Context, req *v1.GetOneReq) (res *v1.GetOneRes, err error)
	GetByCode(ctx context.Context, req *v1.GetByCodeReq) (res *v1.GetByCodeRes, err error)
	Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error)
	Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error)
	Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error)
	SetStatus(ctx context.Context, req *v1.SetStatusReq) (res *v1.SetStatusRes, err error)
	GetByGroupCode(ctx context.Context, req *v1.GetByGroupCodeReq) (res *v1.GetByGroupCodeRes, err error)
	GetByGroupId(ctx context.Context, req *v1.GetByGroupIdReq) (res *v1.GetByGroupIdRes, err error)
	UpdateSort(ctx context.Context, req *v1.UpdateSortReq) (res *v1.UpdateSortRes, err error)
}
