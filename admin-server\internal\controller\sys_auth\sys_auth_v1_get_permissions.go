package sys_auth

import (
	"context"

	v1 "admin-server/api/sys_auth/v1"
	"admin-server/internal/service"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

func (c *ControllerV1) GetPermissions(ctx context.Context, req *v1.GetPermissionsReq) (res *v1.GetPermissionsRes, err error) {
	// 从上下文中获取当前登录的管理员ID
	adminId := g.RequestFromCtx(ctx).GetCtxVar("admin_id").Int64()
	if adminId == 0 {
		return nil, gerror.New("未登录或登录已过期")
	}

	permissions, err := service.SysAuth().GetPermissions(ctx, adminId)
	if err != nil {
		return nil, err
	}

	return &v1.GetPermissionsRes{
		Permissions: permissions,
	}, nil
}
