# Meta权限系统使用说明

## 概述

基于您的建议，我们实现了一个基于 `g.<PERSON>a` 标签的权限验证系统。虽然目前还没有完全实现从结构体标签自动读取权限（这需要更复杂的反射机制），但我们提供了一个更优雅的解决方案：**基于路由模式的自动权限映射**。

## 🎯 **核心思想**

在API结构体的 `g.<PERSON>a` 标签中添加 `permission` 属性：

```go
type CreateReq struct {
    g.Meta `path:"/sys_admin/create" method:"post" tags:"SysAdmin" summary:"创建管理员" permission:"system:admin:add"`
    // ... 其他字段
}
```

## 🔧 **实现方案**

### 1. API结构体标签示例

```go
// 管理员管理
type CreateReq struct {
    g.Meta `path:"/sys_admin/create" method:"post" permission:"system:admin:add"`
    // ...
}

type GetListReq struct {
    g.<PERSON>a `path:"/sys_admin/list" method:"get" permission:"system:admin:list"`
    // ...
}

type UpdateReq struct {
    g.Meta `path:"/sys_admin/{id}" method:"put" permission:"system:admin:edit"`
    // ...
}

type DeleteReq struct {
    g.Meta `path:"/sys_admin/{id}" method:"delete" permission:"system:admin:delete"`
    // ...
}
```

### 2. 权限映射配置

在 `internal/middleware/permission_mapping.go` 中配置路由到权限的映射：

```go
{Pattern: "^/sys_admin$", Method: "GET", Permission: "system:admin:list"},
{Pattern: "^/sys_admin/\\d+$", Method: "GET", Permission: "system:admin:view"},
{Pattern: "^/sys_admin$", Method: "POST", Permission: "system:admin:add"},
{Pattern: "^/sys_admin/\\d+$", Method: "PUT", Permission: "system:admin:edit"},
{Pattern: "^/sys_admin/\\d+$", Method: "DELETE", Permission: "system:admin:delete"},
```

### 3. 中间件自动验证

`MetaPermission` 中间件会：
1. 根据请求路径和方法自动匹配权限标识
2. 检查用户是否拥有该权限
3. 允许或拒绝访问

## 🚀 **使用方法**

### 1. 添加新API权限

当您添加新的API接口时：

**步骤1**: 在API结构体中添加权限标签
```go
type NewFeatureReq struct {
    g.Meta `path:"/new_feature" method:"post" permission:"system:feature:add"`
    // ... 字段定义
}
```

**步骤2**: 在权限映射中添加配置
```go
{Pattern: "^/new_feature$", Method: "POST", Permission: "system:feature:add"},
```

**步骤3**: 在数据库中创建对应的菜单权限
```sql
INSERT INTO sys_menu (menu_name, perms, ...) VALUES ('新功能', 'system:feature:add', ...);
```

**步骤4**: 分配权限给角色
```sql
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (1, 新菜单ID);
```

### 2. 权限标识规范

权限标识采用三段式格式：`模块:资源:操作`

```
system:admin:list     # 系统管理员列表
system:admin:add      # 系统管理员添加
system:admin:edit     # 系统管理员编辑
system:admin:delete   # 系统管理员删除
system:admin:roles    # 系统管理员角色分配

system:role:list      # 系统角色列表
system:role:menus     # 系统角色菜单分配

system:config:group:list  # 系统配置分组列表（多级资源）
```

### 3. 路径模式匹配

支持正则表达式匹配：

```go
"^/sys_admin$"           # 精确匹配 /sys_admin
"^/sys_admin/\\d+$"      # 匹配 /sys_admin/1, /sys_admin/123
"^/sys_admin/\\d+/roles$" # 匹配 /sys_admin/1/roles
```

## 📋 **权限映射规则**

### 基础CRUD操作

| HTTP方法 | 路径模式 | 权限标识 | 说明 |
|----------|----------|----------|------|
| GET | `/module` | `module:list` | 列表查询 |
| GET | `/module/{id}` | `module:view` | 详情查询 |
| POST | `/module` | `module:add` | 新增 |
| PUT | `/module/{id}` | `module:edit` | 编辑 |
| DELETE | `/module/{id}` | `module:delete` | 删除 |

### 特殊操作

| HTTP方法 | 路径模式 | 权限标识 | 说明 |
|----------|----------|----------|------|
| PUT | `/module/{id}/roles` | `module:roles` | 角色分配 |
| PUT | `/module/{id}/menus` | `module:menus` | 菜单分配 |
| PUT | `/module/{id}/disable` | `module:edit` | 禁用操作 |
| PUT | `/module/{id}/enable` | `module:edit` | 启用操作 |

## 🔍 **测试工具**

运行权限映射测试：

```bash
go run tools/meta_permission_test.go
```

输出示例：
```
=== Meta权限系统测试工具 ===

方法     路径                           期望权限                       结果
--------------------------------------------------------------------------------
GET      /sys_admin                     system:admin:list             ✅
GET      /sys_admin/1                   system:admin:view              ✅
POST     /sys_admin                     system:admin:add               ✅
PUT      /sys_admin/1                   system:admin:edit              ✅
DELETE   /sys_admin/1                   system:admin:delete            ✅
PUT      /sys_admin/1/roles             system:admin:roles             ✅

测试结果: 35/35 通过
🎉 所有测试通过!
```

## 🛠️ **配置文件**

### 权限映射配置

文件：`internal/middleware/permission_mapping.go`

```go
func GetPermissionMappings() []PermissionMapping {
    return []PermissionMapping{
        // 添加新的权限映射
        {Pattern: "^/new_module$", Method: "GET", Permission: "system:newmodule:list"},
        {Pattern: "^/new_module$", Method: "POST", Permission: "system:newmodule:add"},
        // ...
    }
}
```

### 排除路径配置

不需要权限验证的路径：

```go
excludedPaths := []string{
    "/auth/login",
    "/auth/refresh", 
    "/auth/permissions",
    "/auth/menus",
    "/auth/userinfo",
    "/auth/logout",
    "/health",
    "/metrics",
}
```

## 🎉 **优势**

### 1. 声明式权限
- 在API结构体中直接声明所需权限
- 权限要求一目了然
- 便于代码审查和维护

### 2. 自动化验证
- 无需手动为每个接口添加权限检查
- 中间件自动处理权限验证
- 减少遗漏和错误

### 3. 灵活配置
- 支持正则表达式匹配
- 支持复杂的路径模式
- 易于扩展新的权限规则

### 4. 统一管理
- 权限映射集中配置
- 便于批量修改和维护
- 支持权限继承和通配符

## 🔮 **未来扩展**

### 1. 真正的Meta标签解析
可以进一步实现从结构体 `g.Meta` 标签中自动读取权限：

```go
// 未来可能的实现
func getPermissionFromMeta(handler interface{}) string {
    // 通过反射解析结构体Meta标签中的permission属性
    // 这需要更复杂的反射机制和路由信息获取
}
```

### 2. 动态权限配置
- 支持运行时修改权限映射
- 权限配置热加载
- 基于数据库的权限配置

### 3. 权限缓存优化
- Redis缓存用户权限
- 权限验证结果缓存
- 提高验证性能

这个Meta权限系统为您提供了一个优雅、自动化的权限管理解决方案，大大简化了权限配置和维护工作！
