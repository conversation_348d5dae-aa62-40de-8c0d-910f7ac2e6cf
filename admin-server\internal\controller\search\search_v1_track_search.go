package search

import (
	"context"
	v1 "admin-server/api/search/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) TrackSearch(ctx context.Context, req *v1.TrackSearchReq) (res *v1.TrackSearchRes, err error) {
	// 记录搜索统计
	err = service.Search().TrackSearch(ctx, req.Keyword, req.CityId)
	if err != nil {
		return &v1.TrackSearchRes{Success: false}, err
	}

	return &v1.TrackSearchRes{Success: true}, nil
}
