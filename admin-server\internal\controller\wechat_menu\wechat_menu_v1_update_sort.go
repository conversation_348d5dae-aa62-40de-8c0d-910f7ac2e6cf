package wechat_menu

import (
	"context"

	"admin-server/api/wechat_menu/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) UpdateSort(ctx context.Context, req *v1.WechatMenuUpdateSortReq) (res *v1.WechatMenuUpdateSortRes, err error) {
	err = service.WechatMenu().UpdateSort(ctx, req.Id, req.Sort)
	if err != nil {
		return nil, err
	}

	res = &v1.WechatMenuUpdateSortRes{}
	return res, nil
}
