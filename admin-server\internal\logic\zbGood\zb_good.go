package zbGood

import (
	v1 "admin-server/api/zb_good/v1"
	"admin-server/internal/dao"
	"admin-server/internal/model/do"
	"admin-server/internal/model/entity"
	"admin-server/internal/service"
	"context"
	"fmt"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

func init() {
	service.RegisterZbGood(&sZbGood{})
}

type sZbGood struct{}

// Create 创建套餐
func (s *sZbGood) Create(ctx context.Context, req *v1.ZbGoodCreateReq) (res *v1.ZbGoodCreateRes, err error) {
	// 检查套餐名称是否重复
	count, err := dao.ZbGood.Ctx(ctx).Where("name", req.Name).Where("is_delete", 0).Count()
	if err != nil {
		g.Log().Error(ctx, "检查套餐名称重复失败:", err)
		return nil, gerror.New("检查套餐名称重复失败")
	}
	if count > 0 {
		return nil, gerror.New("套餐名称已存在")
	}

	// 验证价格逻辑
	if req.Price > req.OriginalPrice {
		return nil, gerror.New("现时价格不能大于原始价格")
	}

	now := gtime.Now()
	goodData := do.ZbGood{
		Name:          req.Name,
		Tag:           req.Tag,
		OriginalPrice: req.OriginalPrice,
		Price:         req.Price,
		Effective:     req.Effective,
		IsDisable:     req.IsDisable,
		IsDelete:      0,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	insertResult, err := dao.ZbGood.Ctx(ctx).Insert(goodData)
	if err != nil {
		g.Log().Error(ctx, "创建套餐失败:", err)
		return nil, gerror.New("创建套餐失败")
	}

	insertId, err := insertResult.LastInsertId()
	if err != nil {
		g.Log().Error(ctx, "获取插入ID失败:", err)
		return nil, gerror.New("获取插入ID失败")
	}

	g.Log().Info(ctx, "创建套餐成功:", "id:", insertId, "name:", req.Name)
	res = &v1.ZbGoodCreateRes{
		Id: int(insertId),
	}
	return res, nil
}

// Update 更新套餐
func (s *sZbGood) Update(ctx context.Context, req *v1.ZbGoodUpdateReq) (res *v1.ZbGoodUpdateRes, err error) {
	// 检查套餐是否存在
	var existingEntity *entity.ZbGood
	err = dao.ZbGood.Ctx(ctx).Where("id", req.Id).Where("is_delete", 0).Scan(&existingEntity)
	if err != nil {
		g.Log().Error(ctx, "查询套餐失败:", err)
		return nil, gerror.New("查询套餐失败")
	}
	if existingEntity == nil {
		return nil, gerror.New("套餐不存在")
	}

	// 检查套餐名称是否重复（排除自己）
	count, err := dao.ZbGood.Ctx(ctx).Where("name", req.Name).Where("id !=", req.Id).Where("is_delete", 0).Count()
	if err != nil {
		g.Log().Error(ctx, "检查套餐名称重复失败:", err)
		return nil, gerror.New("检查套餐名称重复失败")
	}
	if count > 0 {
		return nil, gerror.New("套餐名称已存在")
	}

	// 验证价格逻辑
	if req.Price > req.OriginalPrice {
		return nil, gerror.New("现时价格不能大于原始价格")
	}

	goodData := do.ZbGood{
		Name:          req.Name,
		Tag:           req.Tag,
		OriginalPrice: req.OriginalPrice,
		Price:         req.Price,
		Effective:     req.Effective,
		IsDisable:     req.IsDisable,
		UpdatedAt:     gtime.Now(),
	}

	_, err = dao.ZbGood.Ctx(ctx).Where("id", req.Id).Update(goodData)
	if err != nil {
		g.Log().Error(ctx, "更新套餐失败:", err)
		return nil, gerror.New("更新套餐失败")
	}

	g.Log().Info(ctx, "更新套餐成功:", "id:", req.Id, "name:", req.Name)
	res = &v1.ZbGoodUpdateRes{}
	return res, nil
}

// Delete 删除套餐
func (s *sZbGood) Delete(ctx context.Context, req *v1.ZbGoodDeleteReq) (res *v1.ZbGoodDeleteRes, err error) {
	// 检查套餐是否存在
	var existingEntity *entity.ZbGood
	err = dao.ZbGood.Ctx(ctx).Where("id", req.Id).Where("is_delete", 0).Scan(&existingEntity)
	if err != nil {
		g.Log().Error(ctx, "查询套餐失败:", err)
		return nil, gerror.New("查询套餐失败")
	}
	if existingEntity == nil {
		return nil, gerror.New("套餐不存在")
	}

	// 软删除
	_, err = dao.ZbGood.Ctx(ctx).Where("id", req.Id).Update(do.ZbGood{
		IsDelete:  1,
		DeletedAt: gtime.Now(),
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "删除套餐失败:", err)
		return nil, gerror.New("删除套餐失败")
	}

	g.Log().Info(ctx, "删除套餐成功:", "id:", req.Id)
	res = &v1.ZbGoodDeleteRes{}
	return res, nil
}

// GetOne 获取单个套餐
func (s *sZbGood) GetOne(ctx context.Context, req *v1.ZbGoodGetOneReq) (res *v1.ZbGoodGetOneRes, err error) {
	var entity *entity.ZbGood
	err = dao.ZbGood.Ctx(ctx).Where("id", req.Id).Where("is_delete", 0).Scan(&entity)
	if err != nil {
		g.Log().Error(ctx, "查询套餐失败:", err)
		return nil, gerror.New("查询套餐失败")
	}
	if entity == nil {
		return nil, gerror.New("套餐不存在")
	}

	goodInfo := &v1.ZbGoodInfo{}
	if err = gconv.Struct(entity, goodInfo); err != nil {
		g.Log().Error(ctx, "数据转换失败:", err)
		return nil, gerror.New("数据转换失败")
	}

	// 计算折扣信息
	s.EnrichGoodInfo(goodInfo)

	res = &v1.ZbGoodGetOneRes{
		ZbGoodInfo: goodInfo,
	}
	return res, nil
}

// GetList 获取套餐列表
func (s *sZbGood) GetList(ctx context.Context, req *v1.ZbGoodGetListReq) (res *v1.ZbGoodGetListRes, err error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	query := dao.ZbGood.Ctx(ctx).Where("is_delete", 0)

	if req.Name != "" {
		query = query.WhereLike("name", "%"+req.Name+"%")
	}
	if req.IsDisable != nil {
		query = query.Where("is_disable", *req.IsDisable)
	}
	if req.MinPrice > 0 {
		query = query.Where("price >=", req.MinPrice)
	}
	if req.MaxPrice > 0 {
		query = query.Where("price <=", req.MaxPrice)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		g.Log().Error(ctx, "查询套餐总数失败:", err)
		return nil, gerror.New("查询套餐总数失败")
	}

	// 获取列表数据
	var entities []*entity.ZbGood
	err = query.OrderAsc("price").OrderAsc("id").Page(req.Page, req.PageSize).Scan(&entities)
	if err != nil {
		g.Log().Error(ctx, "查询套餐列表失败:", err)
		return nil, gerror.New("查询套餐列表失败")
	}

	// 转换数据
	var list []*v1.ZbGoodInfo
	if err = gconv.Structs(entities, &list); err != nil {
		g.Log().Error(ctx, "数据转换失败:", err)
		return nil, gerror.New("数据转换失败")
	}

	// 计算折扣信息
	for _, good := range list {
		s.EnrichGoodInfo(good)
	}

	res = &v1.ZbGoodGetListRes{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}
	return res, nil
}

// GetAll 获取所有套餐
func (s *sZbGood) GetAll(ctx context.Context, req *v1.ZbGoodGetAllReq) (res *v1.ZbGoodGetAllRes, err error) {
	// 构建查询条件
	query := dao.ZbGood.Ctx(ctx).Where("is_delete", 0)
	if req.IsDisable != nil {
		query = query.Where("is_disable", *req.IsDisable)
	}

	// 获取所有数据
	var entities []*entity.ZbGood
	err = query.OrderAsc("price").OrderAsc("id").Scan(&entities)
	if err != nil {
		g.Log().Error(ctx, "查询所有套餐失败:", err)
		return nil, gerror.New("查询所有套餐失败")
	}

	// 转换数据
	var list []*v1.ZbGoodInfo
	if err = gconv.Structs(entities, &list); err != nil {
		g.Log().Error(ctx, "数据转换失败:", err)
		return nil, gerror.New("数据转换失败")
	}

	// 计算折扣信息
	for _, good := range list {
		s.EnrichGoodInfo(good)
	}

	res = &v1.ZbGoodGetAllRes{
		List: list,
	}
	return res, nil
}

// UpdateStatus 更新套餐状态
func (s *sZbGood) UpdateStatus(ctx context.Context, req *v1.ZbGoodUpdateStatusReq) (res *v1.ZbGoodUpdateStatusRes, err error) {
	// 检查套餐是否存在
	var existingEntity *entity.ZbGood
	err = dao.ZbGood.Ctx(ctx).Where("id", req.Id).Where("is_delete", 0).Scan(&existingEntity)
	if err != nil {
		g.Log().Error(ctx, "查询套餐失败:", err)
		return nil, gerror.New("查询套餐失败")
	}
	if existingEntity == nil {
		return nil, gerror.New("套餐不存在")
	}

	_, err = dao.ZbGood.Ctx(ctx).Where("id", req.Id).Update(do.ZbGood{
		IsDisable: req.IsDisable,
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "更新套餐状态失败:", err)
		return nil, gerror.New("更新套餐状态失败")
	}

	g.Log().Info(ctx, "更新套餐状态成功:", "id:", req.Id, "is_disable:", req.IsDisable)
	res = &v1.ZbGoodUpdateStatusRes{}
	return res, nil
}

// BatchDelete 批量删除套餐
func (s *sZbGood) BatchDelete(ctx context.Context, req *v1.ZbGoodBatchDeleteReq) (res *v1.ZbGoodBatchDeleteRes, err error) {
	// 检查套餐是否存在
	count, err := dao.ZbGood.Ctx(ctx).WhereIn("id", req.Ids).Where("is_delete", 0).Count()
	if err != nil {
		g.Log().Error(ctx, "查询套餐失败:", err)
		return nil, gerror.New("查询套餐失败")
	}
	if count == 0 {
		return nil, gerror.New("没有找到要删除的套餐")
	}

	// 批量软删除
	result, err := dao.ZbGood.Ctx(ctx).WhereIn("id", req.Ids).Where("is_delete", 0).Update(do.ZbGood{
		IsDelete:  1,
		DeletedAt: gtime.Now(),
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "批量删除套餐失败:", err)
		return nil, gerror.New("批量删除套餐失败")
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		g.Log().Error(ctx, "获取影响行数失败:", err)
		return nil, gerror.New("获取影响行数失败")
	}

	g.Log().Info(ctx, "批量删除套餐成功:", "count:", rowsAffected, "ids:", req.Ids)
	res = &v1.ZbGoodBatchDeleteRes{
		Count: int(rowsAffected),
	}
	return res, nil
}

// GetActiveGoods 获取可用套餐
func (s *sZbGood) GetActiveGoods(ctx context.Context, req *v1.ZbGoodGetActiveGoodsReq) (res *v1.ZbGoodGetActiveGoodsRes, err error) {
	// 获取启用的套餐
	var entities []*entity.ZbGood
	err = dao.ZbGood.Ctx(ctx).Where("is_delete", 0).Where("is_disable", 0).OrderAsc("price").OrderAsc("id").Scan(&entities)
	if err != nil {
		g.Log().Error(ctx, "查询可用套餐失败:", err)
		return nil, gerror.New("查询可用套餐失败")
	}

	// 转换数据
	var list []*v1.ZbGoodInfo
	if err = gconv.Structs(entities, &list); err != nil {
		g.Log().Error(ctx, "数据转换失败:", err)
		return nil, gerror.New("数据转换失败")
	}

	// 计算折扣信息
	for _, good := range list {
		s.EnrichGoodInfo(good)
	}

	res = &v1.ZbGoodGetActiveGoodsRes{
		List: list,
	}
	return res, nil
}

// CalculateDiscount 计算折扣
func (s *sZbGood) CalculateDiscount(originalPrice, price float64) (discount float64, discountText string) {
	if originalPrice <= 0 || price <= 0 {
		return 0, "无折扣"
	}

	if price >= originalPrice {
		return 0, "无折扣"
	}

	discount = price / originalPrice
	discountPercent := discount * 10

	if discountPercent >= 9.5 {
		discountText = "无折扣"
	} else if discountPercent >= 9.0 {
		discountText = "9.5折"
	} else if discountPercent >= 8.5 {
		discountText = "9折"
	} else if discountPercent >= 8.0 {
		discountText = "8.5折"
	} else if discountPercent >= 7.5 {
		discountText = "8折"
	} else if discountPercent >= 7.0 {
		discountText = "7.5折"
	} else if discountPercent >= 6.5 {
		discountText = "7折"
	} else if discountPercent >= 6.0 {
		discountText = "6.5折"
	} else if discountPercent >= 5.5 {
		discountText = "6折"
	} else if discountPercent >= 5.0 {
		discountText = "5.5折"
	} else {
		discountText = fmt.Sprintf("%.1f折", discountPercent)
	}

	return discount, discountText
}

// EnrichGoodInfo 丰富套餐信息
func (s *sZbGood) EnrichGoodInfo(good *v1.ZbGoodInfo) {
	if good == nil {
		return
	}

	// 计算折扣信息
	discount, discountText := s.CalculateDiscount(good.OriginalPrice, good.Price)
	good.Discount = discount
	good.DiscountText = discountText
}
