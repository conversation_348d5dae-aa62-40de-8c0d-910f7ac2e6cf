package wechat_menu

import (
	"context"

	"admin-server/api/wechat_menu/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) Publish(ctx context.Context, req *v1.WechatMenuPublishReq) (res *v1.WechatMenuPublishRes, err error) {
	success, message, err := service.WechatMenu().Publish(ctx)
	if err != nil {
		return nil, err
	}

	res = &v1.WechatMenuPublishRes{
		Success: success,
		Message: message,
	}
	return res, nil
}
