package sysResources

import (
	"admin-server/internal/dao"
	"admin-server/internal/model/do"
	"admin-server/internal/model/entity"
	"admin-server/internal/packed"
	"admin-server/internal/service"
	"context"
	"crypto/md5"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/grand"
)

type sSysResources struct{}

func init() {
	service.RegisterSysResources(New())
}

const (
	MB = 1 << 20 // 1MB = 1048576 bytes
)

func New() *sSysResources {
	return &sSysResources{}
}

// GetResourcesList 获取资源列表
func (s *sSysResources) GetResourcesList(ctx context.Context, page, pageSize int, groupId int64, originName, mimeType string, storageMode int) (list []entity.SysResources, total int, err error) {
	query := dao.SysResources.Ctx(ctx).Where("is_delete", packed.NO_DELETE)

	// 按分组筛选
	if groupId > 0 {
		query = query.Where("group_id", groupId)
	}

	// 按源文件名筛选
	if originName != "" {
		query = query.WhereLike("origin_name", "%"+originName+"%")
	}

	// 按资源类型筛选
	if mimeType != "" {
		query = query.WhereLike("mime_type", "%"+mimeType+"%")
	}

	// 按存储模式筛选
	if storageMode > 0 {
		query = query.Where("storage_mode", storageMode)
	}

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.OrderDesc("created_at").Limit(offset, pageSize).Scan(&list)
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

// GetResourcesDetail 获取资源详情
func (s *sSysResources) GetResourcesDetail(ctx context.Context, id int64) (*entity.SysResources, error) {
	var resources entity.SysResources
	err := dao.SysResources.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Scan(&resources)
	if err != nil {
		return nil, err
	}

	if resources.Id == 0 {
		return nil, nil
	}

	return &resources, nil
}

// CreateResources 创建资源
func (s *sSysResources) CreateResources(ctx context.Context, groupId int64, storageMode int, originName, objectName, hash, mimeType, storagePath, suffix, sizeByte, sizeInfo, url, remark string) error {
	// 检查分组是否存在
	resourcesGroup, err := service.SysResourcesGroup().GetResourcesGroupDetail(ctx, groupId)
	if err != nil {
		return err
	}
	if resourcesGroup == nil {
		return gerror.New("资源分组不存在")
	}

	// 检查hash是否已存在（如果提供了hash）
	if hash != "" {
		exists, err := s.CheckResourcesHashExists(ctx, hash, 0)
		if err != nil {
			return err
		}
		if exists {
			return gerror.New("文件已存在")
		}
	}

	resources := &do.SysResources{
		GroupId:     groupId,
		StorageMode: storageMode,
		OriginName:  originName,
		ObjectName:  objectName,
		Hash:        hash,
		MimeType:    mimeType,
		StoragePath: storagePath,
		Suffix:      suffix,
		SizeByte:    sizeByte,
		SizeInfo:    sizeInfo,
		Url:         url,
		Remark:      remark,
		IsDelete:    packed.NO_DELETE,
		CreatedAt:   gtime.Now(),
		UpdatedAt:   gtime.Now(),
	}

	_, err = dao.SysResources.Ctx(ctx).Insert(resources)
	if err != nil {
		g.Log().Error(ctx, "创建资源失败:", err)
		return err
	}

	g.Log().Info(ctx, "创建资源成功:", "originName:", originName, "url:", url)
	return nil
}

// UpdateResources 更新资源
func (s *sSysResources) UpdateResources(ctx context.Context, id int64, groupId int64, storageMode int, originName, objectName, hash, mimeType, storagePath, suffix, sizeByte, sizeInfo, url, remark string) error {
	// 检查资源是否存在
	resources, err := s.GetResourcesDetail(ctx, id)
	if err != nil {
		return err
	}
	if resources == nil {
		return gerror.New("资源不存在")
	}

	// 检查分组是否存在
	resourcesGroup, err := service.SysResourcesGroup().GetResourcesGroupDetail(ctx, groupId)
	if err != nil {
		return err
	}
	if resourcesGroup == nil {
		return gerror.New("资源分组不存在")
	}

	// 检查hash是否已存在（排除自己）
	if hash != "" && hash != resources.Hash {
		exists, err := s.CheckResourcesHashExists(ctx, hash, id)
		if err != nil {
			return err
		}
		if exists {
			return gerror.New("文件已存在")
		}
	}

	updateData := &do.SysResources{
		GroupId:     groupId,
		StorageMode: storageMode,
		OriginName:  originName,
		ObjectName:  objectName,
		Hash:        hash,
		MimeType:    mimeType,
		StoragePath: storagePath,
		Suffix:      suffix,
		SizeByte:    sizeByte,
		SizeInfo:    sizeInfo,
		Url:         url,
		Remark:      remark,
		UpdatedAt:   gtime.Now(),
	}

	_, err = dao.SysResources.Ctx(ctx).Where("id", id).Update(updateData)
	if err != nil {
		g.Log().Error(ctx, "更新资源失败:", err)
		return err
	}

	g.Log().Info(ctx, "更新资源成功:", "id:", id, "originName:", originName)
	return nil
}

// DeleteResources 删除资源
func (s *sSysResources) DeleteResources(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	// 软删除
	_, err := dao.SysResources.Ctx(ctx).WhereIn("id", ids).Update(do.SysResources{
		IsDelete:  packed.IS_DELETE,
		DeletedAt: gtime.Now(),
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "删除资源失败:", err)
		return err
	}

	g.Log().Info(ctx, "删除资源成功:", "ids:", ids)
	return nil
}

// GetResourcesByGroup 根据分组获取资源
func (s *sSysResources) GetResourcesByGroup(ctx context.Context, groupId int64, page, pageSize int) (list []entity.SysResources, total int, err error) {
	query := dao.SysResources.Ctx(ctx).
		Where("group_id", groupId).
		Where("is_delete", packed.NO_DELETE)

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.OrderDesc("created_at").Limit(offset, pageSize).Scan(&list)
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

// CheckResourcesHashExists 检查资源hash是否存在
func (s *sSysResources) CheckResourcesHashExists(ctx context.Context, hash string, excludeId int64) (bool, error) {
	if hash == "" {
		return false, nil
	}

	query := dao.SysResources.Ctx(ctx).Where("hash", hash).Where("is_delete", packed.NO_DELETE)
	if excludeId > 0 {
		query = query.WhereNot("id", excludeId)
	}

	count, err := query.Count()
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// UploadFile 图片文件上传
func (s *sSysResources) UploadFile(ctx context.Context, groupId int64, file interface{}) (*entity.SysResources, error) {
	// 添加调试信息
	g.Log().Infof(ctx, "UploadFile接收到的文件类型: %T", file)

	// 检查分组是否存在
	resourcesGroup, err := service.SysResourcesGroup().GetResourcesGroupDetail(ctx, groupId)
	if err != nil {
		return nil, err
	}
	if resourcesGroup == nil {
		return nil, gerror.New("资源分组不存在")
	}

	// 处理不同类型的文件输入
	var fileData []byte
	var fileName string
	var contentType string

	switch v := file.(type) {
	case *ghttp.UploadFile:
		// GoFrame的UploadFile类型
		src, err := v.Open()
		if err != nil {
			return nil, err
		}
		defer src.Close()

		fileData, err = io.ReadAll(src)
		if err != nil {
			return nil, err
		}
		fileName = v.Filename
		contentType = v.Header.Get("Content-Type")
	default:
		return nil, gerror.New("不支持的文件类型，支持: *multipart.FileHeader, *ghttp.UploadFile, []byte, io.Reader, string(文件路径)")
	}

	// 检查是否允许上传图片类型
	uploadAllowImage, err := service.SysConfig().GetByKey(ctx, "upload_allow_image")
	if err != nil {
		return nil, err
	}
	// 创建允许的扩展名map（提高查找效率）
	allowedExtensions := make(map[string]bool)
	for _, ext := range strings.Split(uploadAllowImage.Value, ",") {
		ext = strings.ToLower(strings.TrimSpace(ext))
		if ext != "" {
			allowedExtensions[ext] = true
		}
	}
	g.Log().Info(ctx, "允许上传的图片类型:", allowedExtensions)

	// 获取上传文件的扩展名
	fileExt := strings.ToLower(strings.TrimPrefix(filepath.Ext(fileName), "."))
	g.Log().Info(ctx, "上传文件的扩展名:", fileExt)
	// 验证扩展名
	if !allowedExtensions[fileExt] {
		// 获取允许的扩展名列表用于错误信息
		allowedList := make([]string, 0, len(allowedExtensions))
		for ext := range allowedExtensions {
			allowedList = append(allowedList, ext)
		}
		return nil, gerror.Newf("不允许上传 %s 类型的文件，允许的格式: %v", fileExt, allowedList)
	}

	// 1. 获取文件大小限制配置（单位：字节）
	uploadAllowSize, err := service.SysConfig().GetByKey(ctx, "upload_allow_image_size")
	if err != nil {
		return nil, err
	}

	// 设置默认大小限制（5MB）如果配置为空
	maxSize := 5 // 默认5MB
	if uploadAllowSize.Value != "" {
		if size, err := strconv.Atoi(uploadAllowSize.Value); err == nil {
			maxSize = size
		} else {
			return nil, gerror.Newf("无效的文件大小配置: %s", uploadAllowSize.Value)
		}
	}
	g.Log().Info(ctx, "文件大小限制:", maxSize)
	// 2. 验证文件大小
	// 格式化文件大小
	fileSize := int64(len(fileData))
	sizeInfo := s.formatFileSize(fileSize)
	fileSizeMB := float64(fileSize) / MB
	if fileSizeMB > float64(maxSize) {
		return nil, gerror.Newf("文件大小(%.2fMB)超过限制(%dMB)", fileSizeMB, maxSize)
	}

	// 计算文件hash
	hash := md5.New()
	hash.Write(fileData)
	fileHash := fmt.Sprintf("%x", hash.Sum(nil))

	// 检查文件是否已存在
	exists, err := s.CheckResourcesHashExists(ctx, fileHash, 0)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, gerror.New("文件已存在")
	}

	// 生成文件名
	originName := fileName
	suffix := filepath.Ext(originName)
	objectName := fmt.Sprintf("%d_%s%s", time.Now().UnixNano(), grand.Str("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", 8), suffix)

	// 创建上传目录
	uploadDir := "./uploads/" + time.Now().Format("2006/01/02")
	if err := gfile.Mkdir(uploadDir); err != nil {
		return nil, err
	}

	// 保存文件
	filePath := filepath.Join(uploadDir, objectName)
	dst, err := os.Create(filePath)
	if err != nil {
		return nil, err
	}
	defer dst.Close()

	if _, err := dst.Write(fileData); err != nil {
		return nil, err
	}

	// 生成URL
	baseURL, _ := g.Cfg().Get(ctx, "server.storageAddress")

	url := baseURL.String() + "/uploads/" + time.Now().Format("2006/01/02") + "/" + objectName

	// 创建资源记录
	resources := &do.SysResources{
		GroupId:     groupId,
		StorageMode: 1, // 本地存储
		OriginName:  originName,
		ObjectName:  objectName,
		Hash:        fileHash,
		MimeType:    contentType,
		StoragePath: uploadDir,
		Suffix:      suffix,
		SizeByte:    gconv.String(fileSize),
		SizeInfo:    sizeInfo,
		Url:         url,
		IsDelete:    packed.NO_DELETE,
		CreatedAt:   gtime.Now(),
		UpdatedAt:   gtime.Now(),
	}

	result, err := dao.SysResources.Ctx(ctx).Insert(resources)
	if err != nil {
		// 删除已上传的文件
		os.Remove(filePath)
		g.Log().Error(ctx, "创建资源记录失败:", err)
		return nil, err
	}

	// 获取插入的ID
	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	// 返回资源信息
	return &entity.SysResources{
		Id:          id,
		GroupId:     groupId,
		StorageMode: 1,
		OriginName:  originName,
		ObjectName:  objectName,
		Hash:        fileHash,
		MimeType:    contentType,
		StoragePath: uploadDir,
		Suffix:      suffix,
		SizeByte:    gconv.String(fileSize),
		SizeInfo:    sizeInfo,
		Url:         url,
		IsDelete:    packed.NO_DELETE,
		CreatedAt:   gtime.Now(),
		UpdatedAt:   gtime.Now(),
	}, nil
}

// FileUpload 文件上传
func (s *sSysResources) FileUpload(ctx context.Context, groupId int64, file interface{}) (*entity.SysResources, error) {
	// 添加调试信息
	g.Log().Infof(ctx, "FileUpload接收到的文件类型: %T", file)

	// 检查分组是否存在
	resourcesGroup, err := service.SysResourcesGroup().GetResourcesGroupDetail(ctx, groupId)
	if err != nil {
		return nil, err
	}
	if resourcesGroup == nil {
		return nil, gerror.New("资源分组不存在")
	}

	// 处理不同类型的文件输入
	var fileData []byte
	var fileName string
	var contentType string

	switch v := file.(type) {
	case *ghttp.UploadFile:
		// GoFrame的UploadFile类型
		src, err := v.Open()
		if err != nil {
			return nil, err
		}
		defer src.Close()

		fileData, err = io.ReadAll(src)
		if err != nil {
			return nil, err
		}
		fileName = v.Filename
		contentType = v.Header.Get("Content-Type")
	default:
		return nil, gerror.New("不支持的文件类型，支持: *multipart.FileHeader, *ghttp.UploadFile, []byte, io.Reader, string(文件路径)")
	}

	// 检查是否允许上传图片类型
	uploadAllowImage, err := service.SysConfig().GetByKey(ctx, "upload_allow_file")
	if err != nil {
		return nil, err
	}
	// 创建允许的扩展名map（提高查找效率）
	allowedExtensions := make(map[string]bool)
	for _, ext := range strings.Split(uploadAllowImage.Value, ",") {
		ext = strings.ToLower(strings.TrimSpace(ext))
		if ext != "" {
			allowedExtensions[ext] = true
		}
	}
	g.Log().Info(ctx, "允许上传的文件类型:", allowedExtensions)

	// 获取上传文件的扩展名
	fileExt := strings.ToLower(strings.TrimPrefix(filepath.Ext(fileName), "."))
	g.Log().Info(ctx, "上传文件的扩展名:", fileExt)
	// 验证扩展名
	if !allowedExtensions[fileExt] {
		// 获取允许的扩展名列表用于错误信息
		allowedList := make([]string, 0, len(allowedExtensions))
		for ext := range allowedExtensions {
			allowedList = append(allowedList, ext)
		}
		return nil, gerror.Newf("不允许上传 %s 类型的文件，允许的格式: %v", fileExt, allowedList)
	}

	// 1. 获取文件大小限制配置（单位：字节）
	uploadAllowSize, err := service.SysConfig().GetByKey(ctx, "upload_allow_file_size")
	if err != nil {
		return nil, err
	}

	// 设置默认大小限制（5MB）如果配置为空
	maxSize := 5 // 默认5MB
	if uploadAllowSize.Value != "" {
		if size, err := strconv.Atoi(uploadAllowSize.Value); err == nil {
			maxSize = size
		} else {
			return nil, gerror.Newf("无效的文件大小配置: %s", uploadAllowSize.Value)
		}
	}
	g.Log().Info(ctx, "文件大小限制:", maxSize)
	// 2. 验证文件大小
	// 格式化文件大小
	fileSize := int64(len(fileData))
	sizeInfo := s.formatFileSize(fileSize)
	fileSizeMB := float64(fileSize) / MB
	if fileSizeMB > float64(maxSize) {
		return nil, gerror.Newf("文件大小(%.2fMB)超过限制(%dMB)", fileSizeMB, maxSize)
	}

	// 计算文件hash
	hash := md5.New()
	hash.Write(fileData)
	fileHash := fmt.Sprintf("%x", hash.Sum(nil))

	// 检查文件是否已存在
	exists, err := s.CheckResourcesHashExists(ctx, fileHash, 0)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, gerror.New("文件已存在")
	}

	// 生成文件名
	originName := fileName
	suffix := filepath.Ext(originName)
	objectName := fmt.Sprintf("%d_%s%s", time.Now().UnixNano(), grand.Str("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", 8), suffix)

	// 创建上传目录
	uploadDir := "./uploads/" + time.Now().Format("2006/01/02")
	if err := gfile.Mkdir(uploadDir); err != nil {
		return nil, err
	}

	// 保存文件
	filePath := filepath.Join(uploadDir, objectName)
	dst, err := os.Create(filePath)
	if err != nil {
		return nil, err
	}
	defer dst.Close()

	if _, err := dst.Write(fileData); err != nil {
		return nil, err
	}

	// 生成URL
	baseURL, _ := g.Cfg().Get(ctx, "server.storageAddress")

	url := baseURL.String() + "/uploads/" + time.Now().Format("2006/01/02") + "/" + objectName

	// 创建资源记录
	resources := &do.SysResources{
		GroupId:     groupId,
		StorageMode: 1, // 本地存储
		OriginName:  originName,
		ObjectName:  objectName,
		Hash:        fileHash,
		MimeType:    contentType,
		StoragePath: uploadDir,
		Suffix:      suffix,
		SizeByte:    gconv.String(fileSize),
		SizeInfo:    sizeInfo,
		Url:         url,
		IsDelete:    packed.NO_DELETE,
		CreatedAt:   gtime.Now(),
		UpdatedAt:   gtime.Now(),
	}

	result, err := dao.SysResources.Ctx(ctx).Insert(resources)
	if err != nil {
		// 删除已上传的文件
		os.Remove(filePath)
		g.Log().Error(ctx, "创建资源记录失败:", err)
		return nil, err
	}

	// 获取插入的ID
	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	// 返回资源信息
	return &entity.SysResources{
		Id:          id,
		GroupId:     groupId,
		StorageMode: 1,
		OriginName:  originName,
		ObjectName:  objectName,
		Hash:        fileHash,
		MimeType:    contentType,
		StoragePath: uploadDir,
		Suffix:      suffix,
		SizeByte:    gconv.String(fileSize),
		SizeInfo:    sizeInfo,
		Url:         url,
		IsDelete:    packed.NO_DELETE,
		CreatedAt:   gtime.Now(),
		UpdatedAt:   gtime.Now(),
	}, nil
}

// formatFileSize 格式化文件大小
func (s *sSysResources) formatFileSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}
