# 手机端页面使用说明

## 概述

手机端页面提供了移动设备友好的套餐展示界面，包括列表页面和详情页面。页面采用响应式设计，适配各种移动设备屏幕。

## 页面路由

### 1. 套餐列表页面
- **URL**: `http://localhost:8000/m/list`
- **功能**: 展示所有可用的套餐列表
- **特性**: 
  - 响应式设计，适配手机屏幕
  - 自动加载可用套餐数据
  - 显示套餐名称、价格、折扣信息
  - 点击套餐可跳转到详情页面

### 2. 套餐详情页面
- **URL**: `http://localhost:8000/m/detail?id={套餐ID}`
- **功能**: 展示单个套餐的详细信息
- **特性**:
  - 详细的套餐信息展示
  - 价格对比和折扣显示
  - 套餐特权列表
  - 返回按钮和购买按钮

## 页面特性

### 设计特点
1. **移动优先设计**: 专为手机屏幕优化
2. **现代UI风格**: 使用渐变色和圆角设计
3. **流畅动画**: 悬停效果和过渡动画
4. **加载状态**: 友好的加载提示
5. **错误处理**: 完善的错误提示和重试机制

### 功能特性
1. **自动数据加载**: 页面加载时自动获取套餐数据
2. **价格计算**: 自动显示折扣信息和节省金额
3. **状态判断**: 根据套餐状态显示不同信息
4. **导航功能**: 页面间的跳转和返回功能

## 数据接口

### 列表页面使用的接口
- **接口**: `GET /zb_good/active`
- **说明**: 获取所有可用的套餐列表
- **返回**: 启用状态的套餐数据

### 详情页面使用的接口
- **接口**: `GET /zb_good/{id}`
- **说明**: 获取指定ID的套餐详情
- **参数**: 套餐ID
- **返回**: 完整的套餐信息

## 页面结构

### 列表页面结构
```
Header (标题栏)
├── 套餐列表标题

Container (内容区域)
├── Loading (加载状态)
├── 套餐列表
│   ├── 套餐项1
│   │   ├── 套餐名称和标签
│   │   ├── 价格信息（现价、原价、折扣）
│   │   └── 有效期和查看详情链接
│   ├── 套餐项2
│   └── ...
├── 空状态提示
└── 错误状态提示
```

### 详情页面结构
```
Header (标题栏)
├── 返回按钮
└── 套餐详情标题

Container (内容区域)
├── Loading (加载状态)
├── 套餐详情卡片
│   ├── 套餐头部（名称、标签）
│   ├── 价格区域（现价、原价、折扣）
│   ├── 信息区域（有效期、状态、节省金额）
│   ├── 特权列表
│   └── 操作按钮（返回、购买）
└── 错误状态提示
```

## 样式特点

### 颜色方案
- **主色调**: 渐变紫色 (#667eea → #764ba2)
- **价格色**: 红色 (#e74c3c)
- **成功色**: 绿色 (#27ae60)
- **文字色**: 深灰 (#333) / 浅灰 (#666, #999)
- **背景色**: 浅灰 (#f5f5f5)

### 交互效果
- **悬停效果**: 卡片上浮和阴影变化
- **按钮效果**: 缩放和颜色变化
- **加载动画**: 旋转的加载指示器

## 使用方法

### 1. 访问列表页面
直接在浏览器中访问：`http://localhost:8000/m/list`

### 2. 查看套餐详情
- 方法1: 在列表页面点击任意套餐
- 方法2: 直接访问：`http://localhost:8000/m/detail?id=1`

### 3. 返回操作
- 在详情页面点击左上角的返回按钮
- 或点击底部的"返回列表"按钮

## 注意事项

### 开发注意事项
1. **模板路径**: 模板文件位于 `resource/template/mobile/` 目录
2. **静态资源**: CSS样式直接内嵌在HTML中，便于维护
3. **API依赖**: 页面依赖套餐管理的API接口
4. **错误处理**: 包含完善的错误处理和用户提示

### 部署注意事项
1. **模板目录**: 确保 `resource/template/mobile/` 目录存在
2. **API可用性**: 确保套餐管理API正常工作
3. **路由注册**: 确保在 `cmd.go` 中正确注册了手机端路由

## 扩展建议

### 功能扩展
1. **搜索功能**: 添加套餐搜索和筛选
2. **分类展示**: 按套餐类型分类展示
3. **收藏功能**: 用户收藏感兴趣的套餐
4. **比较功能**: 套餐对比功能
5. **支付集成**: 集成支付功能

### 性能优化
1. **缓存机制**: 添加数据缓存减少API调用
2. **懒加载**: 图片和内容的懒加载
3. **压缩优化**: CSS和JS的压缩优化
4. **CDN加速**: 静态资源CDN加速

### 用户体验
1. **下拉刷新**: 添加下拉刷新功能
2. **无限滚动**: 列表页面无限滚动加载
3. **分享功能**: 套餐分享功能
4. **评价系统**: 用户评价和反馈系统

## 技术栈

- **后端框架**: GoFrame v2
- **模板引擎**: GoFrame内置模板引擎
- **前端技术**: HTML5 + CSS3 + JavaScript (ES6+)
- **设计风格**: 移动优先的响应式设计
- **数据交互**: Fetch API + JSON

## 维护说明

### 文件位置
- **控制器**: `internal/controller/mobile/mobile.go`
- **模板文件**: `resource/template/mobile/list.html`, `resource/template/mobile/detail.html`
- **路由配置**: `internal/cmd/cmd.go`

### 修改建议
1. **样式修改**: 直接修改HTML文件中的CSS部分
2. **功能扩展**: 在控制器中添加新的方法和路由
3. **数据处理**: 修改JavaScript部分的数据处理逻辑
4. **模板变量**: 通过控制器传递更多模板变量
