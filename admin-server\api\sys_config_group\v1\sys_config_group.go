package v1

import (
	"admin-server/internal/packed"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// CreateReq 创建配置分组请求体
type CreateReq struct {
	g.Meta    `path:"/sys_config_group/create" method:"post" tags:"SysConfigGroup" summary:"创建配置分组" permission:"system:config_group:add"`
	Name      string          `p:"name" v:"required|length:1,30#分组名称不能为空|分组名称长度为1-30位" dc:"分组名称"`
	Code      string          `p:"code" v:"required|length:1,30#分组编码不能为空|分组编码长度为1-30位" dc:"分组编码"`
	Sort      int             `p:"sort" d:"1" v:"min:0#排序值不能小于0" dc:"排序"`
	Remark    string          `p:"remark" v:"length:0,255#备注长度不能超过255位" dc:"备注"`
	IsSystem  *packed.System  `p:"is_system" v:"in:0,1" dc:"是否系统保留"`
	IsDisable *packed.Disable `p:"is_disable" v:"in:0,1" dc:"是否禁用"`
}

type CreateRes struct {
	ID int64 `json:"id" dc:"配置分组ID"`
}

// DeleteReq 删除配置分组请求体
type DeleteReq struct {
	g.Meta `path:"/sys_config_group/{id}" method:"delete" tags:"SysConfigGroup" summary:"删除配置分组" permission:"system:config_group:del"`
	ID     int64 `p:"id" v:"required#请选择需要删除的配置分组" dc:"配置分组ID"`
}
type DeleteRes struct{}

// UpdateReq 更新配置分组请求体
type UpdateReq struct {
	g.Meta    `path:"/sys_config_group/{id}" method:"put" tags:"SysConfigGroup" summary:"更新配置分组信息" permission:"system:config_group:edit"`
	ID        int64           `p:"id" v:"required#请选择需要更新的配置分组" dc:"配置分组ID"`
	Name      string          `p:"name" v:"required|length:1,30#分组名称不能为空|分组名称长度为1-30位" dc:"分组名称"`
	Code      string          `p:"code" v:"required|length:1,30#分组编码不能为空|分组编码长度为1-30位" dc:"分组编码"`
	Sort      int             `p:"sort" d:"1" v:"min:0#排序值不能小于0" dc:"排序"`
	Remark    string          `p:"remark" v:"length:0,255#备注长度不能超过255位" dc:"备注"`
	IsSystem  *packed.System  `p:"is_system" v:"in:0,1" dc:"是否系统保留"`
	IsDisable *packed.Disable `p:"is_disable" v:"in:0,1" dc:"是否禁用"`
}
type UpdateRes struct{}

// GetListReq 获取配置分组列表请求体
type GetListReq struct {
	g.Meta    `path:"/sys_config_group/list" tags:"SysConfigGroup" method:"get" summary:"获取配置分组列表" permission:"system:config_group:list"`
	Page      int             `p:"page" d:"1" v:"min:1#页码最小为1" dc:"页码"`
	PageSize  int             `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间" dc:"每页数量"`
	Name      string          `p:"name" dc:"分组名称（模糊搜索）"`
	Code      string          `p:"code" dc:"分组编码（模糊搜索）"`
	IsSystem  *packed.System  `p:"is_system" v:"in:0,1" dc:"是否系统保留"`
	IsDisable *packed.Disable `p:"is_disable" v:"in:0,1" dc:"是否禁用"`
}

// ConfigGroupInfo 配置分组信息
type ConfigGroupInfo struct {
	ID          int64          `json:"id" dc:"配置分组ID"`
	Name        string         `json:"name" dc:"分组名称"`
	Code        string         `json:"code" dc:"分组编码"`
	Sort        int            `json:"sort" dc:"排序"`
	Remark      string         `json:"remark" dc:"备注"`
	IsSystem    packed.System  `json:"is_system" dc:"是否系统保留"`
	IsDisable   packed.Disable `json:"is_disable" dc:"是否禁用"`
	CreatedAt   *gtime.Time    `json:"created_at" dc:"创建时间"`
	UpdatedAt   *gtime.Time    `json:"updated_at" dc:"更新时间"`
	ConfigCount int            `json:"config_count" dc:"配置项数量"`
}

type GetListRes struct {
	List     []*ConfigGroupInfo `json:"list" dc:"配置分组列表"`
	Total    int                `json:"total" dc:"总数"`
	Page     int                `json:"page" dc:"当前页码"`
	PageSize int                `json:"page_size" dc:"每页数量"`
}

// GetOneReq 获取单个配置分组信息请求体
type GetOneReq struct {
	g.Meta `path:"/sys_config_group/{id}" tags:"SysConfigGroup" method:"get" summary:"获取单个配置分组信息"`
	ID     int64 `p:"id" v:"required#请选择需要查询的配置分组" dc:"配置分组ID"`
}
type GetOneRes struct {
	*ConfigGroupInfo `dc:"配置分组信息"`
}

// ToggleStatusReq 切换配置分组状态请求体
type ToggleStatusReq struct {
	g.Meta `path:"/sys_config_group/{id}/toggle" method:"put" tags:"SysConfigGroup" summary:"切换配置分组启用/禁用状态" permission:"system:config_group:toggle"`
	ID     int64 `p:"id" v:"required#请选择需要切换状态的配置分组" dc:"配置分组ID"`
}
type ToggleStatusRes struct{}
