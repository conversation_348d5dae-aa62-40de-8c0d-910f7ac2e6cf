<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>我的订单</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            -webkit-overflow-scrolling: touch;
        }
        .phone-container {
            overflow-y: auto;
            position: relative;
            background: white;
            min-height: 100vh;
            -webkit-overflow-scrolling: touch;
        }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .mini-capsule { 
            position: absolute; 
            top: 8px; 
            right: 12px; 
            width: 24px; 
            height: 24px; 
            background: rgba(255,255,255,0.9); 
            border-radius: 12px; 
            display: flex; 
            align-items: center; 
            justify-content: center;
            z-index: 10;
        }
        .order-item { transition: transform 0.2s ease; }
        .order-item:hover { transform: translateY(-1px); }

        /* 登录弹窗样式 */
        .vip-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .vip-modal-content {
            background: white;
            border-radius: 20px;
            padding: 30px 20px;
            margin: 20px;
            text-align: center;
            max-width: 320px;
            width: 100%;
            animation: modalSlideIn 0.3s ease-out;
        }
        @keyframes modalSlideIn {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 顶部导航 -->
        <div class="gradient-bg px-4 pt-4 pb-4">
            <div class="flex items-center space-x-3">
                <h1 class="text-white text-lg font-semibold flex-1">我的订单</h1>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="bg-white flex-1 overflow-y-auto">
            <!-- 统计信息 -->
            <div class="px-4 py-4 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-100">
                <div class="flex items-center justify-center">
                    <div class="flex items-center space-x-8">
                        <div class="text-center">
                            <div class="text-lg font-bold text-green-600" id="totalPaidOrders">0</div>
                            <div class="text-xs text-gray-600">订单总数</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-orange-600" id="totalAmount">¥0</div>
                            <div class="text-xs text-gray-600">总金额</div>
                        </div>
                    </div>
                </div>
            </div>



            <!-- 订单列表 -->
            <div id="orderList" class="px-4 py-2 space-y-3">
                <!-- 订单项将通过JavaScript动态生成 -->
                <div id="loadingState" class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-2"></i>
                    <p class="text-gray-500 text-sm">加载中...</p>
                </div>

                <!-- 空状态 -->
                <div id="emptyState" class="text-center py-12" style="display: none;">
                    <i class="fas fa-shopping-bag text-gray-300 text-4xl mb-4"></i>
                    <p class="text-gray-500 text-sm mb-2">暂无已支付订单</p>
                    <p class="text-gray-400 text-xs">您还没有任何已支付的订单记录</p>
                </div>
            </div>

            <!-- 加载更多 -->
            <div id="loadMoreContainer" class="px-4 py-6 text-center" style="display: none;">
                <button id="loadMoreBtn" onclick="loadMoreOrders()" class="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-2 rounded-full text-sm font-medium">
                    加载更多订单
                </button>
            </div>
        </div>
    </div>

    {{if not .is_logged_in}}
        <!-- 登录弹窗 -->
        <div id="loginModal" class="vip-modal">
        <div class="vip-modal-content">
            <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-user text-white text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-3">请先登录</h3>
            <p class="text-gray-600 text-sm mb-6 leading-relaxed">
                登录后即可开通会员套餐<br>
                享受更多专业服务
            </p>

            <!-- 登录优势 -->
            <div class="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6">
                <div class="space-y-2">
                    <div class="flex items-center text-sm text-gray-700">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>专享会员特权</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-700">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>多城市招标信息</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-700">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>个性化推荐服务</span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <button class="w-full bg-gradient-to-r from-purple-500 to-blue-600 text-white py-3 rounded-lg text-sm font-medium mb-3" onclick="goToLogin()">
                立即登录
            </button>
            <p class="text-xs text-gray-500">登录即可享受更多服务</p>
        </div>
    </div>
    {{end}}
    <script>
        // 模拟接口数据
        const mockApiResponse = {
            "code": 0,
            "message": "OK",
            "data": {
                "list": [
                    {
                        "id": 1,
                        "order_sn": "ZB202507251114227737",
                        "good_id": 2,
                        "good_name": "特惠会员399元",
                        "good_price": 298,
                        "effective": 6,
                        "city_count": 2,
                        "user_id": 1,
                        "user_nickname": "　　　　　　　　",
                        "price": 596,
                        "pay_status": 1, // 1表示已支付
                        "remark": "选择城市：内蒙古、北京",
                        "pay_at": "2025-07-25 11:20:15",
                        "created_at": "2025-07-25 11:14:22",
                        "updated_at": "2025-07-25 11:14:22",
                        "cities": [
                            {
                                "city_id": 29,
                                "city_name": "内蒙古"
                            },
                            {
                                "city_id": 1,
                                "city_name": "北京"
                            }
                        ]
                    },
                    {
                        "id": 2,
                        "order_sn": "ZB202507241008123456",
                        "good_id": 1,
                        "good_name": "VIP年度会员",
                        "good_price": 798,
                        "effective": 12,
                        "city_count": 1,
                        "user_id": 1,
                        "user_nickname": "　　　　　　　　",
                        "price": 798,
                        "pay_status": 1,
                        "remark": "选择城市：上海",
                        "pay_at": "2025-07-24 10:15:30",
                        "created_at": "2025-07-24 10:08:12",
                        "updated_at": "2025-07-24 10:08:12",
                        "cities": [
                            {
                                "city_id": 2,
                                "city_name": "上海"
                            }
                        ]
                    },
                    {
                        "id": 3,
                        "order_sn": "ZB202507231015789012",
                        "good_id": 3,
                        "good_name": "全国会员套餐",
                        "good_price": 1998,
                        "effective": 24,
                        "city_count": 15,
                        "user_id": 1,
                        "user_nickname": "　　　　　　　　",
                        "price": 1998,
                        "pay_status": 1,
                        "remark": "选择城市：北京、上海、广州、深圳、杭州、南京、苏州、成都、重庆、武汉、西安、天津、青岛、大连、厦门",
                        "pay_at": "2025-07-23 10:20:45",
                        "created_at": "2025-07-23 10:15:30",
                        "updated_at": "2025-07-23 10:15:30",
                        "cities": [
                            {"city_id": 1, "city_name": "北京"},
                            {"city_id": 2, "city_name": "上海"},
                            {"city_id": 3, "city_name": "广州"},
                            {"city_id": 4, "city_name": "深圳"},
                            {"city_id": 5, "city_name": "杭州"},
                            {"city_id": 6, "city_name": "南京"},
                            {"city_id": 7, "city_name": "苏州"},
                            {"city_id": 8, "city_name": "成都"},
                            {"city_id": 9, "city_name": "重庆"},
                            {"city_id": 10, "city_name": "武汉"},
                            {"city_id": 11, "city_name": "西安"},
                            {"city_id": 12, "city_name": "天津"},
                            {"city_id": 13, "city_name": "青岛"},
                            {"city_id": 14, "city_name": "大连"},
                            {"city_id": 15, "city_name": "厦门"}
                        ]
                    }
                ],
                "total": 3
            }
        };

        // 格式化时间
        function formatTime(timeStr) {
            const now = new Date();
            const time = new Date(timeStr);
            const diff = now - time;
            const minutes = Math.floor(diff / (1000 * 60));
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const days = Math.floor(diff / (1000 * 60 * 60 * 24));

            if (minutes < 60) {
                return `${minutes}分钟前`;
            } else if (hours < 24) {
                return `${hours}小时前`;
            } else {
                return `${days}天前`;
            }
        }

        // 获取商品图标
        function getGoodIcon(goodName) {
            if (goodName.includes('会员') || goodName.includes('VIP')) {
                return '<i class="fas fa-crown text-white text-lg"></i>';
            } else if (goodName.includes('文件') || goodName.includes('招标')) {
                return '<i class="fas fa-file-download text-white text-lg"></i>';
            } else {
                return '<i class="fas fa-shopping-bag text-white text-lg"></i>';
            }
        }

        // 获取商品图标背景色
        function getGoodIconBg(goodName) {
            if (goodName.includes('会员') || goodName.includes('VIP')) {
                return 'bg-gradient-to-br from-yellow-400 to-orange-500';
            } else if (goodName.includes('文件') || goodName.includes('招标')) {
                return 'bg-gradient-to-br from-blue-400 to-purple-500';
            } else {
                return 'bg-gradient-to-br from-green-400 to-blue-500';
            }
        }

        // 格式化城市显示
        function formatCityDisplay(cities) {
            if (cities.length === 0) return '';

            const cityNames = cities.map(city => city.city_name);

            // 如果城市数量少于等于3个，直接显示
            if (cityNames.length <= 3) {
                return cityNames.join('、');
            }

            // 如果城市数量在4-6个之间，显示前3个+数量
            if (cityNames.length <= 6) {
                return `${cityNames.slice(0, 3).join('、')} 等${cityNames.length}个城市`;
            }

            // 如果城市数量超过6个，显示前2个+数量
            return `${cityNames.slice(0, 2).join('、')} 等${cityNames.length}个城市`;
        }

        // 渲染订单项
        function renderOrderItem(order) {
            const cityDisplay = formatCityDisplay(order.cities);
            const timeAgo = formatTime(order.pay_at || order.created_at);
            const icon = getGoodIcon(order.good_name);
            const iconBg = getGoodIconBg(order.good_name);

            return `
                <div class="order-item bg-white rounded-xl p-4 card-shadow border border-gray-100">
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex items-center space-x-2">
                            <span class="bg-green-100 text-green-600 px-2 py-1 rounded-md text-xs font-medium">已支付</span>
                            <span class="text-xs text-gray-500">订单号: ${order.order_sn}</span>
                        </div>
                        <span class="text-xs text-gray-400">${timeAgo}</span>
                    </div>

                    <div class="flex items-start space-x-3 mb-3">
                        <div class="w-12 h-12 ${iconBg} rounded-lg flex items-center justify-center">
                            ${icon}
                        </div>
                        <div class="flex-1 min-w-0">
                            <h4 class="text-sm font-semibold text-gray-800 mb-1">${order.good_name}</h4>
                            <p class="text-xs text-gray-600 mb-1">${order.remark}</p>
                            <div class="flex items-center space-x-2 mb-2">
                                <span class="text-xs text-gray-500">有效期:</span>
                                <span class="text-xs text-blue-600 font-medium">${order.effective || 0}个月</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-lg font-bold text-green-600">¥${order.price}</span>
                                <span class="text-xs text-green-600">已激活</span>
                            </div>
                        </div>
                    </div>

                    ${order.cities.length > 0 ? `
                    <div class="bg-blue-50 border-l-4 border-blue-400 p-3 rounded mb-3">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <p class="text-xs text-blue-800 font-medium mb-1">覆盖城市</p>
                                <p class="text-xs text-blue-700 leading-relaxed">${cityDisplay}</p>
                            </div>
                            ${order.cities.length > 3 ? `
                            <button class="text-xs text-blue-600 underline ml-2 whitespace-nowrap" onclick="showAllCities('${order.order_sn}', ${JSON.stringify(order.cities).replace(/"/g, '&quot;')})">
                                查看全部
                            </button>
                            ` : ''}
                        </div>
                    </div>
                    ` : ''}

                    <div class="mt-3">
                        <button class="w-full bg-gradient-to-r from-green-500 to-blue-500 text-white py-3 rounded-lg text-sm font-medium" onclick="handleOrderAction('renew', '${order.order_sn}')">续费会员</button>
                    </div>
                </div>
            `;
        }

        // 渲染订单列表
        function renderOrderList(orders) {
            const orderListContainer = document.getElementById('orderList');
            const loadingState = document.getElementById('loadingState');
            const emptyState = document.getElementById('emptyState');
            const loadMoreContainer = document.getElementById('loadMoreContainer');

            // 检查DOM元素是否存在
            if (!orderListContainer) {
                console.error('orderList元素未找到');
                return;
            }

            // 隐藏加载状态
            if (loadingState) {
                loadingState.style.display = 'none';
            }

            if (orders.length === 0) {
                // 显示空状态
                if (emptyState) {
                    emptyState.style.display = 'block';
                }
                if (loadMoreContainer) {
                    loadMoreContainer.style.display = 'none';
                }
                return;
            }

            // 隐藏空状态
            if (emptyState) {
                emptyState.style.display = 'none';
            }

            // 渲染订单列表
            const orderHtml = orders.map(order => renderOrderItem(order)).join('');
            orderListContainer.innerHTML = orderHtml;

            // 显示/隐藏加载更多按钮
            if (loadMoreContainer) {
                if (hasMoreData && userInfo && userInfo !== 'undefined') {
                    loadMoreContainer.style.display = 'block';
                    const loadMoreBtn = document.getElementById('loadMoreBtn');
                    if (loadMoreBtn) {
                        loadMoreBtn.innerHTML = '加载更多订单';
                        loadMoreBtn.disabled = false;
                    }
                } else {
                    loadMoreContainer.style.display = 'none';
                }
            }

            // 更新统计信息（仅在使用模拟数据时）
            if (!userInfo || userInfo === 'undefined') {
                updateStatistics(orders);
            }
        }

        // 加载更多订单
        async function loadMoreOrders() {
            if (isLoading || !hasMoreData) return;

            currentPage++;

            // 更新按钮状态
            const loadMoreBtn = document.getElementById('loadMoreBtn');
            if (loadMoreBtn) {
                loadMoreBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>加载中...';
                loadMoreBtn.disabled = true;
            }

            try {
                await fetchOrders(currentPage);
            } catch (error) {
                console.error('加载更多订单失败:', error);
                // 恢复按钮状态
                if (loadMoreBtn) {
                    loadMoreBtn.innerHTML = '加载更多订单';
                    loadMoreBtn.disabled = false;
                }
                // 回退页码
                currentPage--;
            }
        }

        // 更新统计信息
        function updateStatistics(orders) {
            const totalPaidOrders = orders.length;
            const totalAmount = orders.reduce((sum, order) => sum + order.price, 0);

            // 更新统计数字
            const totalPaidOrdersEl = document.getElementById('totalPaidOrders');
            const totalAmountEl = document.getElementById('totalAmount');

            if (totalPaidOrdersEl) {
                totalPaidOrdersEl.textContent = totalPaidOrders;
            }
            if (totalAmountEl) {
                totalAmountEl.textContent = `¥${totalAmount}`;
            }
        }

        // 全局变量
        let currentPage = 1;
        let pageSize = 10;
        let isLoading = false;
        let hasMoreData = true;
        let allOrders = [];

        // 获取我的订单列表
        async function fetchMyOrderList(page = 1, pageSize = 10) {
            if (!userInfo || userInfo === 'undefined') {
                console.log('用户未登录，无法获取订单列表');
                return null;
            }

            try {
                const url = `/m/api/zb_order/my-list?openid=${userInfo}&page=${page}&page_size=${pageSize}`;
                console.log('请求订单列表:', url);

                const response = await fetch(url);
                const data = await response.json();

                console.log('订单列表响应:', data);

                if (data.code === 0) {
                    return data.data;
                } else {
                    throw new Error(data.message || '获取订单列表失败');
                }
            } catch (error) {
                console.error('获取订单列表失败:', error);
                throw error;
            }
        }

        // 获取我的订单统计
        async function fetchMyOrderStats() {
            if (!userInfo || userInfo === 'undefined') {
                console.log('用户未登录，无法获取订单统计');
                return null;
            }

            try {
                const url = `/m/api/zb_order/my-stats?openid=${userInfo}`;
                console.log('请求订单统计:', url);

                const response = await fetch(url);
                const data = await response.json();

                console.log('订单统计响应:', data);

                if (data.code === 0) {
                    return data.data;
                } else {
                    throw new Error(data.message || '获取订单统计失败');
                }
            } catch (error) {
                console.error('获取订单统计失败:', error);
                throw error;
            }
        }

        // 更新统计信息
        function updateStatisticsFromAPI(stats) {
            if (!stats) return;

            // 更新统计数字
            const totalPaidOrdersEl = document.getElementById('totalPaidOrders');
            const totalAmountEl = document.getElementById('totalAmount');

            if (totalPaidOrdersEl) {
                totalPaidOrdersEl.textContent = stats.total_order_count || 0;
            }
            if (totalAmountEl) {
                totalAmountEl.textContent = `¥${stats.paid_total_amount || 0}`;
            }
        }

        // 获取订单数据
        async function fetchOrders(page = 1) {
            if (isLoading) return;

            try {
                isLoading = true;

                // 显示加载状态
                if (page === 1) {
                    document.getElementById('loadingState').style.display = 'block';
                }

                // 并行获取订单列表和统计数据
                const [orderListData, statsData] = await Promise.all([
                    fetchMyOrderList(page, pageSize),
                    page === 1 ? fetchMyOrderStats() : null
                ]);

                if (orderListData) {
                    // 更新统计信息（仅第一页时）
                    if (page === 1 && statsData) {
                        updateStatisticsFromAPI(statsData);
                    }

                    // 处理订单列表
                    if (page === 1) {
                        allOrders = orderListData.list || [];
                    } else {
                        allOrders = allOrders.concat(orderListData.list || []);
                    }

                    // 检查是否还有更多数据
                    hasMoreData = (orderListData.list || []).length === pageSize;

                    // 渲染订单列表
                    renderOrderList(allOrders);
                } else {
                    // 用户未登录，使用模拟数据
                    if (page === 1) {
                        const data = mockApiResponse;
                        const paidOrders = data.data.list.filter(order => order.pay_status === 1);
                        renderOrderList(paidOrders);
                    }
                }
            } catch (error) {
                console.error('获取订单数据失败:', error);
                const loadingState = document.getElementById('loadingState');
                if (loadingState) {
                    loadingState.innerHTML = `
                        <i class="fas fa-exclamation-triangle text-red-400 text-2xl mb-2"></i>
                        <p class="text-red-500 text-sm">加载失败，请重试</p>
                        <button onclick="fetchOrders(1)" class="mt-2 px-4 py-2 bg-blue-500 text-white rounded text-sm">重试</button>
                    `;
                    loadingState.style.display = 'block';
                }
            } finally {
                isLoading = false;
            }
        }

        // 显示全部城市
        function showAllCities(orderSn, cities) {
            const cityNames = cities.map(city => city.city_name).join('、');
            const cityCount = cities.length;

            // 创建弹窗显示所有城市
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
            modal.innerHTML = `
                <div class="bg-white rounded-xl p-6 max-w-sm w-full max-h-96 overflow-y-auto">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">覆盖城市</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-lg"></i>
                        </button>
                    </div>
                    <div class="mb-4">
                        <p class="text-sm text-gray-600 mb-3">订单号：${orderSn}</p>
                        <p class="text-sm text-blue-600 mb-3">共覆盖 ${cityCount} 个城市</p>
                    </div>
                    <div class="grid grid-cols-2 gap-2">
                        ${cities.map(city => `
                            <div class="bg-blue-50 text-blue-700 px-3 py-2 rounded-lg text-sm text-center">
                                ${city.city_name}
                            </div>
                        `).join('')}
                    </div>
                    <div class="mt-6">
                        <button onclick="this.closest('.fixed').remove()" class="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-2 rounded-lg text-sm font-medium">
                            确定
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // 点击背景关闭弹窗
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        // 订单操作功能
        function handleOrderAction(action, orderId) {
            switch(action) {
                case 'renew':
                    console.log('续费会员，订单号:', orderId);
                    // 跳转到VIP页面
                    window.location.href = '/m/vip';
                    break;
                default:
                    console.log('未知操作:', action);
            }
        }

        // 页面加载完成后获取数据
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，userInfo:', userInfo);

            // 检查用户登录状态
            if (userInfo && userInfo !== 'undefined') {
                console.log('用户已登录，获取真实订单数据');
                fetchOrders(1);
            } else {
                console.log('用户未登录，使用模拟数据');
                // 使用模拟数据
                const data = mockApiResponse;
                const paidOrders = data.data.list.filter(order => order.pay_status === 1);
                renderOrderList(paidOrders);

                // 隐藏加载状态
                document.getElementById('loadingState').style.display = 'none';
            }
        });

        // 微信登录
        function goToLogin() {
            // 获取当前页面URL作为回调地址
            const currentURL = window.location.pathname + window.location.search;
            // 跳转到微信授权登录
            window.location.href = '/m/auth/login?callback=' + encodeURIComponent(currentURL);

            console.log('跳转到微信登录，回调URL:', currentURL);
        }

        // 用户信息
        {{if not .is_logged_in}}
        var userInfo = undefined
        {{else}}
        var userInfo = {{json .user.openid}}
        {{end}}

        // 调试函数：显示当前用户信息和API状态
        function debugUserInfo() {
            console.log('🔍 用户信息调试:', {
                userInfo: userInfo || 'undefined',
                userInfo_type: typeof userInfo,
                userInfo_length: userInfo ? userInfo.length : 0,
                is_logged_in: userInfo && userInfo !== 'undefined'
            });

            if (!userInfo || userInfo === 'undefined') {
                console.warn('⚠️ 用户未登录，将使用模拟数据');
            } else {
                console.log('✅ 用户已登录，OpenID:', userInfo);
                console.log('📡 将调用以下API:');
                console.log('  - 订单列表:', `/m/api/zb_order/my-list?openid=${userInfo}&page=1&page_size=10`);
                console.log('  - 订单统计:', `/m/api/zb_order/my-stats?openid=${userInfo}`);
            }
        }

        // 页面加载时调用调试函数
        debugUserInfo();
    </script>
</body>
</html>
