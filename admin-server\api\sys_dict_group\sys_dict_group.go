package sys_dict_group

import (
	v1 "admin-server/api/sys_dict_group/v1"
	"context"
)

type ISysDictGroupV1 interface {
	GetList(ctx context.Context, req *v1.GetListReq) (res *v1.Get<PERSON>istRes, err error)
	GetOne(ctx context.Context, req *v1.GetOneReq) (res *v1.Get<PERSON>neRes, err error)
	Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error)
	Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error)
	Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error)
	SetStatus(ctx context.Context, req *v1.SetStatusReq) (res *v1.SetStatusRes, err error)
	GetAll(ctx context.Context, req *v1.GetAllReq) (res *v1.Get<PERSON>ll<PERSON><PERSON>, err error)
}
