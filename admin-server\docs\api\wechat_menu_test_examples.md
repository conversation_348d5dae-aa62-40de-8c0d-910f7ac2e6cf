# 微信菜单管理API测试示例

## 测试环境

- **服务器地址**: `http://localhost:8000`
- **认证方式**: JWT Token
- **Content-Type**: `application/json`

## 获取Token

首先需要登录获取Token：

```bash
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "123456"
  }'
```

响应示例：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2025-01-13T10:00:00+08:00"
  }
}
```

## 测试用例

### 1. 创建一级菜单（点击类型）

```bash
curl -X POST "http://localhost:8000/wechat/menu" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 0,
    "menu_name": "产品中心",
    "menu_type": "click",
    "menu_key": "product_center",
    "sort": 1
  }'
```

预期响应：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1
  }
}
```

### 2. 创建一级菜单（跳转链接类型）

```bash
curl -X POST "http://localhost:8000/wechat/menu" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 0,
    "menu_name": "官方网站",
    "menu_type": "view",
    "menu_url": "https://www.example.com",
    "sort": 2
  }'
```

### 3. 创建一级菜单（小程序类型）

```bash
curl -X POST "http://localhost:8000/wechat/menu" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 0,
    "menu_name": "小程序商城",
    "menu_type": "miniprogram",
    "appid": "wx1234567890abcdef",
    "pagepath": "pages/index/index",
    "sort": 3
  }'
```

### 4. 创建二级菜单

```bash
curl -X POST "http://localhost:8000/wechat/menu" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 1,
    "menu_name": "产品列表",
    "menu_type": "view",
    "menu_url": "https://www.example.com/products",
    "sort": 1
  }'
```

### 5. 获取菜单列表

```bash
curl -X GET "http://localhost:8000/wechat/menu/list?page=1&page_size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

预期响应：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "pid": 0,
        "menu_name": "产品中心",
        "menu_type": "click",
        "menu_key": "product_center",
        "menu_url": "",
        "appid": "",
        "pagepath": "",
        "sort": 1,
        "level": 1,
        "is_disable": 0,
        "created_at": "2025-01-12T10:00:00+08:00",
        "updated_at": "2025-01-12T10:00:00+08:00"
      }
    ],
    "total": 4,
    "page": 1,
    "page_size": 10
  }
}
```

### 6. 获取菜单树形结构

```bash
curl -X GET "http://localhost:8000/wechat/menu/tree" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

预期响应：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "pid": 0,
        "menu_name": "产品中心",
        "menu_type": "click",
        "menu_key": "product_center",
        "sort": 1,
        "level": 1,
        "is_disable": 0,
        "children": [
          {
            "id": 4,
            "pid": 1,
            "menu_name": "产品列表",
            "menu_type": "view",
            "menu_url": "https://www.example.com/products",
            "sort": 1,
            "level": 2,
            "is_disable": 0
          }
        ]
      },
      {
        "id": 2,
        "pid": 0,
        "menu_name": "官方网站",
        "menu_type": "view",
        "menu_url": "https://www.example.com",
        "sort": 2,
        "level": 1,
        "is_disable": 0
      }
    ]
  }
}
```

### 7. 获取单个菜单详情

```bash
curl -X GET "http://localhost:8000/wechat/menu/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 8. 更新菜单

```bash
curl -X PUT "http://localhost:8000/wechat/menu/1" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 0,
    "menu_name": "产品中心（更新）",
    "menu_type": "click",
    "menu_key": "product_center_new",
    "menu_url": "",
    "appid": "",
    "pagepath": "",
    "sort": 1
  }'
```

### 9. 更新菜单排序

```bash
curl -X PUT "http://localhost:8000/wechat/menu/1/sort" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "sort": 5
  }'
```

### 10. 更新菜单状态（禁用）

```bash
curl -X PUT "http://localhost:8000/wechat/menu/1/status" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "is_disable": 1
  }'
```

### 11. 更新菜单状态（启用）

```bash
curl -X PUT "http://localhost:8000/wechat/menu/1/status" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "is_disable": 0
  }'
```

### 12. 发布菜单到微信服务器

```bash
curl -X POST "http://localhost:8000/wechat/menu/publish" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

预期响应：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "message": "菜单发布成功"
  }
}
```

### 13. 删除菜单（先删除子菜单）

```bash
# 删除子菜单
curl -X DELETE "http://localhost:8000/wechat/menu/4" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 删除父菜单
curl -X DELETE "http://localhost:8000/wechat/menu/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 错误测试用例

### 1. 创建菜单时缺少必填字段

```bash
curl -X POST "http://localhost:8000/wechat/menu" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 0,
    "menu_name": "测试菜单",
    "menu_type": "click"
  }'
```

预期错误响应：
```json
{
  "code": 400,
  "message": "click类型菜单必须填写菜单KEY值",
  "data": null
}
```

### 2. 删除有子菜单的父菜单

```bash
curl -X DELETE "http://localhost:8000/wechat/menu/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

预期错误响应：
```json
{
  "code": 400,
  "message": "该菜单下存在子菜单，无法删除",
  "data": null
}
```

### 3. 访问不存在的菜单

```bash
curl -X GET "http://localhost:8000/wechat/menu/999" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

预期错误响应：
```json
{
  "code": 404,
  "message": "菜单不存在",
  "data": null
}
```

## 批量测试脚本

创建一个完整的测试脚本 `test_wechat_menu.sh`：

```bash
#!/bin/bash

# 配置
BASE_URL="http://localhost:8000"
TOKEN="YOUR_TOKEN_HERE"

echo "=== 微信菜单管理API测试 ==="

# 1. 创建一级菜单
echo "1. 创建一级菜单..."
curl -s -X POST "$BASE_URL/wechat/menu" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 0,
    "menu_name": "产品中心",
    "menu_type": "click",
    "menu_key": "product_center",
    "sort": 1
  }' | jq .

# 2. 创建二级菜单
echo "2. 创建二级菜单..."
curl -s -X POST "$BASE_URL/wechat/menu" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 1,
    "menu_name": "产品列表",
    "menu_type": "view",
    "menu_url": "https://www.example.com/products",
    "sort": 1
  }' | jq .

# 3. 获取菜单树
echo "3. 获取菜单树..."
curl -s -X GET "$BASE_URL/wechat/menu/tree" \
  -H "Authorization: Bearer $TOKEN" | jq .

# 4. 发布菜单到微信服务器
echo "4. 发布菜单到微信服务器..."
curl -s -X POST "$BASE_URL/wechat/menu/publish" \
  -H "Authorization: Bearer $TOKEN" | jq .

echo "=== 测试完成 ==="
```

使用方法：
1. 将YOUR_TOKEN_HERE替换为实际的token
2. 给脚本执行权限：`chmod +x test_wechat_menu.sh`
3. 运行脚本：`./test_wechat_menu.sh`
