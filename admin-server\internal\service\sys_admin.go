package service

import (
	v1 "admin-server/api/sys_admin/v1"
	"context"
)

// 1.定义接口
type ISysAdmin interface {
	GetList(ctx context.Context, req *v1.GetListReq) (list []*v1.AdminInfo, total int, err error)
	GetOne(ctx context.Context, id int64) (admin *v1.AdminInfo, err error)
	Create(ctx context.Context, req *v1.CreateReq) (insertId int64, err error)
	Update(ctx context.Context, req *v1.UpdateReq) (err error)
	ChangePassword(ctx context.Context, req *v1.ChangePasswordReq) (err error)
	Disable(ctx context.Context, id int64) (err error)
	Enable(ctx context.Context, id int64) (err error)
	Delete(ctx context.Context, id int64) (err error)
	AssignRoles(ctx context.Context, adminId int64, roleIds []int64) (err error)
	GetRoles(ctx context.Context, adminId int64) (roles []*v1.RoleInfo, err error)
}

// 2.定义接口变量
var localSysAdmin ISysAdmin

// 3.定义一个获取接口实例的函数
func SysAdmin() ISysAdmin {
	if localSysAdmin == nil {
		panic("ISysAdmin接口未实现或未注册")
	}
	return localSysAdmin
}

// 4.定义一个接口实现的注册方法
func RegisterSysAdmin(i ISysAdmin) {
	localSysAdmin = i
}
