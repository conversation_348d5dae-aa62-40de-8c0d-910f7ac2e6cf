package zb_user

import (
	"context"

	v1 "admin-server/api/zb_user/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) UpdateVipPeriod(ctx context.Context, req *v1.UpdateVipPeriodReq) (res *v1.UpdateVipPeriodRes, err error) {
	err = service.ZbUser().UpdateVipPeriod(ctx, req.ID, req.EffectiveStart, req.EffectiveEnd)
	if err != nil {
		return nil, err
	}

	return &v1.UpdateVipPeriodRes{}, nil
}
