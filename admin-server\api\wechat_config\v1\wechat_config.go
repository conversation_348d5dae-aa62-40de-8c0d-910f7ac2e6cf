package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// WechatConfigGetReq 获取微信配置请求
type WechatConfigGetReq struct {
	g.Meta `path:"/wechat/config" method:"get" tags:"WechatConfig" summary:"获取微信公众号配置" permission:"system:officialAccount:config"`
}

// WechatConfigGetRes 获取微信配置响应
type WechatConfigGetRes struct {
	*WechatConfigInfo `json:",inline"`
}

// WechatConfigSaveReq 保存微信配置请求
type WechatConfigSaveReq struct {
	g.Meta           `path:"/wechat/config" method:"post" tags:"WechatConfig" summary:"保存微信公众号配置" permission:"system:officialAccount:configSave"`
	Appid            string `p:"appid" v:"required|length:18,18#AppID不能为空|AppID长度必须为18位" dc:"微信公众号AppID"`
	AppSecret        string `p:"app_secret" v:"required|length:32,32#AppSecret不能为空|AppSecret长度必须为32位" dc:"微信公众号AppSecret"`
	AutoReplyEnabled int    `p:"auto_reply_enabled" v:"in:0,1#关注自动回复开关值必须是0或1" dc:"关注自动回复开关：0=关闭，1=开启"`
	AutoReplyText    string `p:"auto_reply_text" v:"required" dc:"关注自动回复语"`
	ServerUrl        string `p:"server_url" v:"required|url#服务器地址不能为空|服务器地址格式不正确" dc:"服务器地址URL"`
	Token            string `p:"token" v:"required|length:3,32#Token不能为空|Token长度必须在3-32位之间" dc:"微信Token"`
	EncodingAesKey   string `p:"encoding_aes_key" v:"required|length:43,43#消息加解密密钥不能为空|消息加解密密钥长度必须为43位" dc:"消息加解密密钥"`
	EncryptMode      string `p:"encrypt_mode" v:"required|in:plaintext,compatible,safe#消息加解密方式不能为空|消息加解密方式必须是plaintext,compatible,safe之一" dc:"消息加解密方式"`
}

// WechatConfigSaveRes 保存微信配置响应
type WechatConfigSaveRes struct {
	Success bool   `json:"success" dc:"保存是否成功"`
	Message string `json:"message" dc:"保存结果消息"`
}

// WechatConfigInfo 微信配置信息
type WechatConfigInfo struct {
	Id               int         `json:"id" dc:"配置ID"`
	Appid            string      `json:"appid" dc:"微信公众号AppID"`
	AppSecret        string      `json:"app_secret" dc:"微信公众号AppSecret"`
	AutoReplyEnabled int         `json:"auto_reply_enabled" dc:"关注自动回复开关"`
	AutoReplyText    string      `json:"auto_reply_text" dc:"关注自动回复开关"`
	ServerUrl        string      `json:"server_url" dc:"服务器地址URL"`
	Token            string      `json:"token" dc:"微信Token"`
	EncodingAesKey   string      `json:"encoding_aes_key" dc:"消息加解密密钥"`
	EncryptMode      string      `json:"encrypt_mode" dc:"消息加解密方式"`
	CreatedAt        *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt        *gtime.Time `json:"updated_at" dc:"更新时间"`
}
