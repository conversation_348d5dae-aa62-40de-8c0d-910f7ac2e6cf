package zb_article

import (
	"context"

	v1 "admin-server/api/zb_article/v1"
	"admin-server/internal/service"
)

// GetListForMobile 获取信息列表 （手机端）
func (c *ControllerV1) GetListForMobile(ctx context.Context, req *v1.GetListMobileReq) (res *v1.GetListRes, err error) {
	list, total, err := service.ZbArticle().GetArticleListWithNamesForMobile(
		ctx,
		req.Page,
		req.PageSize,
		req.CityId,
		req.CateId,
		req.Title,
	)
	if err != nil {
		return nil, err
	}

	// 转换为API响应格式
	articles := make([]v1.ArticleInfo, len(list))
	for i, article := range list {
		articles[i] = v1.ArticleInfo{
			ID:        article.Id,
			CityId:    article.CityId,
			CityName:  article.CityName,
			CateId:    article.CateId,
			CateName:  article.CateName,
			Title:     article.Title,
			Intro:     article.Intro,
			Pic:       article.Pic,
			Author:    article.Author,
			ViewCount: article.ViewCount,
			IsDisable: int(article.IsDisable),
			IsDelete:  int(article.IsDelete),
			CreatedAt: article.CreatedAt,
			UpdatedAt: article.UpdatedAt,
		}
	}

	return &v1.GetListRes{
		List:  articles,
		Total: total,
	}, nil
}
