package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// 热门搜索关键词数据结构
type HotKeyword struct {
	Id          int64       `json:"id"          dc:"主键ID"`
	Keyword     string      `json:"keyword"     dc:"搜索关键词"`
	SearchCount int         `json:"search_count" dc:"搜索次数"`
	ClickCount  int         `json:"click_count"  dc:"点击次数"`
	Trend       string      `json:"trend"       dc:"趋势：up上升，down下降，stable稳定"`
	IsHot       bool        `json:"is_hot"      dc:"是否热门"`
	IsNew       bool        `json:"is_new"      dc:"是否新增"`
	Category    string      `json:"category"    dc:"分类"`
	CreatedAt   *gtime.Time `json:"created_at"  dc:"创建时间"`
	UpdatedAt   *gtime.Time `json:"updated_at"  dc:"更新时间"`
}

// GetHotKeywordsReq 获取热门搜索关键词请求
type GetHotKeywordsReq struct {
	g.<PERSON>a `path:"/search/hot-keywords" tags:"Search" method:"get" summary:"获取热门搜索关键词"`
	Limit  int `json:"limit" d:"10" v:"min:1|max:20" dc:"返回数量限制，默认10，最大20"`
}

// GetHotKeywordsRes 获取热门搜索关键词响应
type GetHotKeywordsRes struct {
	Keywords []HotKeyword `json:"keywords" dc:"热门搜索关键词列表"`
}

// TrackSearchReq 搜索统计请求
type TrackSearchReq struct {
	g.Meta  `path:"/search/track-search" tags:"Search" method:"post" summary:"记录搜索统计"`
	Keyword string `json:"keyword" v:"required|length:1,100" dc:"搜索关键词"`
	CityId  int64  `json:"city_id" dc:"城市ID"`
}

// TrackSearchRes 搜索统计响应
type TrackSearchRes struct {
	Success bool `json:"success" dc:"是否成功"`
}

// TrackClickReq 点击统计请求
type TrackClickReq struct {
	g.Meta  `path:"/search/track-click" tags:"Search" method:"post" summary:"记录点击统计"`
	Keyword string `json:"keyword" v:"required|length:1,100" dc:"搜索关键词"`
}

// TrackClickRes 点击统计响应
type TrackClickRes struct {
	Success bool `json:"success" dc:"是否成功"`
}
