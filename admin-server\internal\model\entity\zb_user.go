// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ZbUser is the golang structure for table zb_user.
type ZbUser struct {
	Id              int64         `json:"id"              orm:"id"               description:""`                                                //
	Nickname        string      `json:"nickname"        orm:"nickname"         description:"昵称"`                                              // 昵称
	Avatar          string      `json:"avatar"          orm:"avatar"           description:"头像"`                                              // 头像
	Openid          string      `json:"openid"          orm:"openid"           description:"会员微信openid"`                                      // 会员微信openid
	IsDisable       int         `json:"isDisable"       orm:"is_disable"       description:"是否禁用: 0=否, 1=是"`                                  // 是否禁用: 0=否, 1=是
	IsDelete        int         `json:"isDelete"        orm:"is_delete"        description:"是否删除: 0=否, 1=是"`                                  // 是否删除: 0=否, 1=是
	EffectiveStart  *gtime.Time `json:"effectiveStart"  orm:"effective_start"  description:"有效开始日期"`                                          // 有效开始日期
	EffectiveEnd    *gtime.Time `json:"effectiveEnd"    orm:"effective_end"    description:"有效结束日期"`                                          // 有效结束日期
	CreatedAt       *gtime.Time `json:"createdAt"       orm:"created_at"       description:"注册时间"`                                            // 注册时间
	UpdatedAt       *gtime.Time `json:"updatedAt"       orm:"updated_at"       description:"更新时间"`                                            // 更新时间
	DeletedAt       *gtime.Time `json:"deletedAt"       orm:"deleted_at"       description:"删除时间"`                                            // 删除时间
	EffectiveStatus int         `json:"effectiveStatus" orm:"effective_status" description:"是否有效 1有效 0无效  根据会员购买的套餐先计算有效开始日期和结束日期，然后在计算有效状态"` // 是否有效 1有效 0无效  根据会员购买的套餐先计算有效开始日期和结束日期，然后在计算有效状态
}
