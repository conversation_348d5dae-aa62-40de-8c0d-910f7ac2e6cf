<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>{{.title}}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            -webkit-overflow-scrolling: touch;
        }
        .phone-container {
            min-height: 100vh;
            position: relative;
            -webkit-overflow-scrolling: touch;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .mini-capsule { 
            position: absolute; 
            top: 8px; 
            right: 12px; 
            width: 24px; 
            height: 24px; 
            background: rgba(255,255,255,0.9); 
            border-radius: 12px; 
            display: flex; 
            align-items: center; 
            justify-content: center;
            z-index: 10;
        }
        .error-icon {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .error-code {
            font-size: 4rem;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: center;
            margin-bottom: 20px;
        }
        .bounce-in {
            animation: bounceIn 0.8s ease-out;
        }
        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }
        .fade-in {
            animation: fadeInUp 0.6s ease-out 0.3s both;
        }
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 顶部导航 -->
        <div class="gradient-bg px-4 pt-4 pb-4">
            <div class="flex items-center space-x-3">
                <button class="text-white" onclick="goBack()">
                    <i class="fas fa-arrow-left text-lg"></i>
                </button>
                <h1 class="text-white text-lg font-semibold flex-1">{{.title}}</h1>
            </div>
        </div>

        <!-- 错误内容区域 -->
        <div class="bg-white flex-1 overflow-y-auto">
            <div class="px-4 py-8 text-center">
                <!-- 错误信息 -->
                <div class="fade-in">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4" id="errorTitle">{{.error}}</h2>
                    <p class="text-gray-600 text-sm mb-8 leading-relaxed" id="errorMessage">
                        请检查网址是否正确，或返回首页重新浏览
                    </p>
                </div>

                <!-- 错误类型卡片 -->
                <div class="grid grid-cols-2 gap-4 mb-8 fade-in">
                    <div class="bg-gradient-to-br from-red-50 to-pink-50 rounded-xl p-4 text-center border border-red-100">
                        <i class="fas fa-unlink text-red-500 text-2xl mb-2"></i>
                        <h3 class="text-sm font-semibold text-red-800 mb-1">链接失效</h3>
                        <p class="text-xs text-red-600">页面链接已失效</p>
                    </div>
                    <div class="bg-gradient-to-br from-orange-50 to-yellow-50 rounded-xl p-4 text-center border border-orange-100">
                        <i class="fas fa-server text-orange-500 text-2xl mb-2"></i>
                        <h3 class="text-sm font-semibold text-orange-800 mb-1">服务异常</h3>
                        <p class="text-xs text-orange-600">服务器暂时异常</p>
                    </div>
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4 text-center border border-blue-100">
                        <i class="fas fa-network-wired text-blue-500 text-2xl mb-2"></i>
                        <h3 class="text-sm font-semibold text-blue-800 mb-1">网络问题</h3>
                        <p class="text-xs text-blue-600">网络连接异常</p>
                    </div>
                    <div class="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl p-4 text-center border border-purple-100">
                        <i class="fas fa-tools text-purple-500 text-2xl mb-2"></i>
                        <h3 class="text-sm font-semibold text-purple-800 mb-1">维护中</h3>
                        <p class="text-xs text-purple-600">系统维护升级</p>
                    </div>
                </div>

                <!-- 建议操作 -->
                <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mb-8 fade-in">
                    <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center justify-center">
                        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                        解决建议
                    </h3>
                    <div class="space-y-2 text-xs text-gray-700">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>检查网址拼写是否正确</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>刷新页面重新尝试</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>检查网络连接状态</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>稍后再试或联系客服</span>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="space-y-3 fade-in">
                    <button class="w-full bg-gradient-to-r from-purple-500 to-blue-500 text-white py-3 rounded-lg text-sm font-medium" onclick="refreshPage()">
                        <i class="fas fa-redo mr-2"></i>
                        刷新页面
                    </button>
                    <button class="w-full bg-gradient-to-r from-green-500 to-teal-500 text-white py-3 rounded-lg text-sm font-medium" onclick="goHome()">
                        <i class="fas fa-home mr-2"></i>
                        返回首页
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 设置错误时间
        document.getElementById('errorTime').textContent = new Date().toLocaleString('zh-CN');

        // 返回上一页
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                goHome();
            }
        }

        // 刷新页面
        function refreshPage() {
            window.location.reload();
        }

        // 返回首页
        function goHome() {
            window.location.href = '/m/list';
        }

        // 根据URL参数设置错误信息
        function setErrorInfo() {
            const urlParams = new URLSearchParams(window.location.search);
            const errorType = urlParams.get('type') || '404';
            const errorCode = document.getElementById('errorCode');
            const errorTitle = document.getElementById('errorTitle');
            const errorMessage = document.getElementById('errorMessage');
            const errorId = document.getElementById('errorId');

            switch(errorType) {
                case '403':
                    errorCode.textContent = '403';
                    errorTitle.textContent = '访问被拒绝';
                    errorMessage.innerHTML = '抱歉，您没有权限访问此页面<br>请联系管理员或检查您的访问权限';
                    errorId.textContent = 'ERR_403_' + new Date().toISOString().slice(0,10).replace(/-/g,'') + '_001';
                    break;
                case '500':
                    errorCode.textContent = '500';
                    errorTitle.textContent = '服务器错误';
                    errorMessage.innerHTML = '服务器遇到了一个错误<br>我们正在努力修复，请稍后再试';
                    errorId.textContent = 'ERR_500_' + new Date().toISOString().slice(0,10).replace(/-/g,'') + '_001';
                    break;
                case 'network':
                    errorCode.textContent = 'NET';
                    errorTitle.textContent = '网络连接失败';
                    errorMessage.innerHTML = '无法连接到服务器<br>请检查您的网络连接后重试';
                    errorId.textContent = 'ERR_NET_' + new Date().toISOString().slice(0,10).replace(/-/g,'') + '_001';
                    break;
                default:
                    // 默认404错误，已在HTML中设置
                    break;
            }
        }

        // 页面加载时设置错误信息
        window.onload = function() {
            setErrorInfo();
        };
    </script>
</body>
</html>
