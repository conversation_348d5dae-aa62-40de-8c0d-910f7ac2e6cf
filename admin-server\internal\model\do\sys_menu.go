// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysMenu is the golang structure of table sys_menu for DAO operations like Where/Data.
type SysMenu struct {
	g.Meta    `orm:"table:sys_menu, do:true"`
	Id        interface{} //
	Pid       interface{} // 上级菜单id
	MenuType  interface{} // 菜单类型: M=目录，C=菜单，A=按钮
	MenuName  interface{} // 菜单名称
	MenuIcon  interface{} // 菜单图表
	MenuSort  interface{} // 菜单排序
	Perms     interface{} // 权限标识
	Paths     interface{} // 路由地址
	Component interface{} // 前端组件
	Params    interface{} // 路由参数
	IsCache   interface{} // 是否缓存: 0=否, 1=是
	IsShow    interface{} // 是否显示: 0=否, 1=是
	IsDisable interface{} // 是否禁用: 0=否, 1=是
	IsDelete  interface{} // 是否删除: 0=否, 1=是
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 更新时间
	DeletedAt *gtime.Time // 删除时间
}
