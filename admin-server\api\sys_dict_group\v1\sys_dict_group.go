package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GetListReq 获取字典分组列表请求体
type GetListReq struct {
	g.Meta    `path:"/sys_dict_group/list" tags:"SysDictGroup" method:"get" summary:"获取字典分组列表" permission:"system:dictgroup:list"`
	Page      int    `p:"page" d:"1" v:"min:1#页码最小为1" dc:"页码"`
	PageSize  int    `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间" dc:"每页数量"`
	Name      string `p:"name" dc:"分组名称（模糊搜索）"`
	Code      string `p:"code" dc:"分组编码（模糊搜索）"`
	IsDisable int    `p:"is_disable" d:"-1" dc:"是否禁用：0=否，1=是，-1=全部"`
}

type GetListRes struct {
	List  []DictGroupInfo `json:"list" dc:"字典分组列表"`
	Total int             `json:"total" dc:"总数"`
}

// GetOneReq 获取字典分组详情请求体
type GetOneReq struct {
	g.Meta `path:"/sys_dict_group/{id}" tags:"SysDictGroup" method:"get" summary:"获取字典分组详情"`
	ID     int64 `p:"id" v:"required#请选择需要查询的记录" dc:"字典分组ID"`
}

type GetOneRes struct {
	DictGroup *DictGroupDetail `json:"dict_group" dc:"字典分组详情"`
}

// CreateReq 创建字典分组请求体
type CreateReq struct {
	g.Meta `path:"/sys_dict_group/create" method:"post" tags:"SysDictGroup" summary:"创建字典分组" permission:"system:dictgroup:create"`
	Name   string `p:"name" v:"required|length:1,50#请输入分组名称|分组名称长度为1-50个字符" dc:"分组名称"`
	Code   string `p:"code" v:"required|length:1,50#请输入分组编码|分组编码长度为1-50个字符" dc:"分组编码"`
	Remark string `p:"remark" v:"max-length:255#备注长度不能超过255个字符" dc:"备注"`
}

type CreateRes struct{}

// UpdateReq 更新字典分组请求体
type UpdateReq struct {
	g.Meta `path:"/sys_dict_group/update/{id}" method:"put" tags:"SysDictGroup" summary:"更新字典分组" permission:"system:dictgroup:update"`
	ID     int64  `p:"id" v:"required#请选择需要更新的记录" dc:"字典分组ID"`
	Name   string `p:"name" v:"required|length:1,50#请输入分组名称|分组名称长度为1-50个字符" dc:"分组名称"`
	Code   string `p:"code" v:"required|length:1,50#请输入分组编码|分组编码长度为1-50个字符" dc:"分组编码"`
	Remark string `p:"remark" v:"max-length:255#备注长度不能超过255个字符" dc:"备注"`
}

type UpdateRes struct{}

// DeleteReq 删除字典分组请求体
type DeleteReq struct {
	g.Meta `path:"/sys_dict_group/delete" method:"delete" tags:"SysDictGroup" summary:"删除字典分组" permission:"system:dictgroup:delete"`
	IDs    []int64 `p:"ids" v:"required#请选择需要删除的记录" dc:"字典分组ID列表"`
}

type DeleteRes struct{}

// SetStatusReq 设置字典分组状态请求体
type SetStatusReq struct {
	g.Meta    `path:"/sys_dict_group/status/{id}" method:"put" tags:"SysDictGroup" summary:"设置字典分组状态" permission:"system:dictgroup:status"`
	ID        int64 `p:"id" v:"required#请选择需要设置的记录" dc:"字典分组ID"`
	IsDisable int   `p:"is_disable" v:"required|in:0,1#请选择状态|状态值错误" dc:"是否禁用：0=否，1=是"`
}

type SetStatusRes struct{}

// GetAllReq 获取所有字典分组请求体
type GetAllReq struct {
	g.Meta `path:"/sys_dict_group/all" tags:"SysDictGroup" method:"get" summary:"获取所有字典分组"`
}

type GetAllRes struct {
	List []DictGroupOption `json:"list" dc:"字典分组选项列表"`
}

// DictGroupInfo 字典分组列表信息
type DictGroupInfo struct {
	ID        int64       `json:"id" dc:"主键ID"`
	Name      string      `json:"name" dc:"分组名称"`
	Code      string      `json:"code" dc:"分组编码"`
	IsDisable int         `json:"is_disable" dc:"是否禁用：0=否，1=是"`
	IsSystem  int         `json:"is_system" dc:"是否系统保留：0=否，1=是"`
	Remark    string      `json:"remark" dc:"备注"`
	CreatedAt *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt *gtime.Time `json:"updated_at" dc:"更新时间"`
}

// DictGroupDetail 字典分组详情信息
type DictGroupDetail struct {
	ID        int64       `json:"id" dc:"主键ID"`
	Name      string      `json:"name" dc:"分组名称"`
	Code      string      `json:"code" dc:"分组编码"`
	IsDisable int         `json:"is_disable" dc:"是否禁用：0=否，1=是"`
	IsSystem  int         `json:"is_system" dc:"是否系统保留：0=否，1=是"`
	Remark    string      `json:"remark" dc:"备注"`
	CreatedAt *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt *gtime.Time `json:"updated_at" dc:"更新时间"`
}

// DictGroupOption 字典分组选项
type DictGroupOption struct {
	ID   int64  `json:"id" dc:"主键ID"`
	Name string `json:"name" dc:"分组名称"`
	Code string `json:"code" dc:"分组编码"`
}
