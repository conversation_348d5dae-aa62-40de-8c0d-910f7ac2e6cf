package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GetMyBrowserListReq 获取我的浏览记录列表请求
type GetMyBrowserListReq struct {
	g.Meta   `path:"/zb_user_browser/my-list" tags:"UserBrowser" method:"get" summary:"获取我的浏览记录列表"`
	OpenId   string `json:"openid"    v:"required|length:1,100" dc:"用户OpenID"`
	Page     int    `json:"page"      v:"min:1" d:"1" dc:"页码"`
	PageSize int    `json:"page_size" v:"min:1,max:50" d:"10" dc:"每页数量"`
}

// GetMyBrowserListRes 获取我的浏览记录列表响应
type GetMyBrowserListRes struct {
	List  []BrowserInfo `json:"list"  dc:"浏览记录列表"`
	Total int           `json:"total" dc:"总数"`
}

// BrowserInfo 浏览记录信息
type BrowserInfo struct {
	Id           int64       `json:"id"            dc:"浏览记录ID"`
	ArticleId    int64       `json:"article_id"    dc:"文章ID"`
	ArticleTitle string      `json:"article_title" dc:"文章标题"`
	CategoryName string      `json:"category_name" dc:"文章分类名称"`
	CityName     string      `json:"city_name"     dc:"文章城市名称"`
	BrowseTime   *gtime.Time `json:"browse_time"   dc:"浏览时间"`
}
