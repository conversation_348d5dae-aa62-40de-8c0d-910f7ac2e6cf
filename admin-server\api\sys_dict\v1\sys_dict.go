package v1

import (
	"admin-server/internal/packed"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GetListReq 获取字典项列表请求体
type GetListReq struct {
	g.Meta    `path:"/sys_dict/list" tags:"SysDict" method:"get" summary:"获取字典项列表" permission:"system:dict:list"`
	Page      int    `p:"page" d:"1" v:"min:1#页码最小为1" dc:"页码"`
	PageSize  int    `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间" dc:"每页数量"`
	GroupId   int64  `p:"group_id" dc:"字典分组ID"`
	Name      string `p:"name" dc:"字典名称（模糊搜索）"`
	Code      string `p:"code" dc:"字典编码（模糊搜索）"`
	IsDisable int    `p:"is_disable" d:"-1" dc:"是否禁用：0=否，1=是，-1=全部"`
}

type GetListRes struct {
	List  []DictInfo `json:"list" dc:"字典项列表"`
	Total int        `json:"total" dc:"总数"`
}

// GetOneReq 获取字典项详情请求体
type GetOneReq struct {
	g.Meta `path:"/sys_dict/{id}" tags:"SysDict" method:"get" summary:"获取字典项详情"`
	ID     int64 `p:"id" v:"required#请选择需要查询的记录" dc:"字典项ID"`
}

type GetOneRes struct {
	Dict *DictDetail `json:"dict" dc:"字典项详情"`
}

// GetByCodeReq 根据字典编码获取字典项详情请求体
type GetByCodeReq struct {
	g.Meta `path:"/sys_dict/code/{code}" tags:"SysDict" method:"get" summary:"根据字典编码获取字典项详情" permission:"system:dict:detail"`
	Code   string `p:"code" v:"required#请输入字典编码" dc:"字典编码"`
}

type GetByCodeRes struct {
	Dict *DictDetail `json:"dict" dc:"字典项详情"`
}

// CreateReq 创建字典项请求体
type CreateReq struct {
	g.Meta    `path:"/sys_dict/create" method:"post" tags:"SysDict" summary:"创建字典项" permission:"system:dict:create"`
	GroupId   int64          `p:"group_id" v:"required#请选择字典分组" dc:"字典分组ID"`
	Name      string         `p:"name" v:"required|length:1,50#请输入字典名称|字典名称长度为1-50个字符" dc:"字典名称"`
	Value     string         `p:"value" v:"max-length:255#字典值长度不能超过255个字符" dc:"字典值"`
	Code      string         `p:"code" v:"required|length:1,255#请输入字典编码|字典编码长度为1-255个字符" dc:"字典编码"`
	Sort      int            `p:"sort" d:"0" dc:"排序"`
	IsDisable packed.Disable `p:"is_disable" d:"0" dc:"是否禁用：0=否，1=是"`
	Remark    string         `p:"remark" v:"max-length:255#备注长度不能超过255个字符" dc:"备注"`
}

type CreateRes struct{}

// UpdateReq 更新字典项请求体
type UpdateReq struct {
	g.Meta    `path:"/sys_dict/update/{id}" method:"put" tags:"SysDict" summary:"更新字典项" permission:"system:dict:update"`
	ID        int64          `p:"id" v:"required#请选择需要更新的记录" dc:"字典项ID"`
	GroupId   int64          `p:"group_id" v:"required#请选择字典分组" dc:"字典分组ID"`
	Name      string         `p:"name" v:"required|length:1,50#请输入字典名称|字典名称长度为1-50个字符" dc:"字典名称"`
	Value     string         `p:"value" v:"max-length:255#字典值长度不能超过255个字符" dc:"字典值"`
	Code      string         `p:"code" v:"required|length:1,255#请输入字典编码|字典编码长度为1-255个字符" dc:"字典编码"`
	Sort      int            `p:"sort" d:"0" dc:"排序"`
	IsDisable packed.Disable `p:"is_disable" d:"0" dc:"是否禁用：0=否，1=是"`
	Remark    string         `p:"remark" v:"max-length:255#备注长度不能超过255个字符" dc:"备注"`
}

type UpdateRes struct{}

// DeleteReq 删除字典项请求体
type DeleteReq struct {
	g.Meta `path:"/sys_dict/delete" method:"delete" tags:"SysDict" summary:"删除字典项" permission:"system:dict:delete"`
	IDs    []int64 `p:"ids" v:"required#请选择需要删除的记录" dc:"字典项ID列表"`
}

type DeleteRes struct{}

// SetStatusReq 设置字典项状态请求体
type SetStatusReq struct {
	g.Meta    `path:"/sys_dict/status/{id}" method:"put" tags:"SysDict" summary:"设置字典项状态" permission:"system:dict:status"`
	ID        int64 `p:"id" v:"required#请选择需要设置的记录" dc:"字典项ID"`
	IsDisable int   `p:"is_disable" v:"required|in:0,1#请选择状态|状态值错误" dc:"是否禁用：0=否，1=是"`
}

type SetStatusRes struct{}

// GetByGroupCodeReq 根据分组编码获取字典项请求体
type GetByGroupCodeReq struct {
	g.Meta    `path:"/sys_dict/group/{group_code}" tags:"SysDict" method:"get" summary:"根据分组编码获取字典项" permission:"system:dict:listByGroup"`
	GroupCode string `p:"group_code" v:"required#请输入分组编码" dc:"字典分组编码"`
}

type GetByGroupCodeRes struct {
	List []DictOption `json:"list" dc:"字典项选项列表"`
}

// GetByGroupIdReq 根据分组ID获取字典项请求体
type GetByGroupIdReq struct {
	g.Meta  `path:"/sys_dict/group_id/{group_id}" tags:"SysDict" method:"get" summary:"根据分组ID获取字典项" permission:"system:dict:listByGroup"`
	GroupId int64 `p:"group_id" v:"required#请输入分组ID" dc:"字典分组ID"`
}

type GetByGroupIdRes struct {
	List []DictOption `json:"list" dc:"字典项选项列表"`
}

// UpdateSortReq 更新字典项排序请求体
type UpdateSortReq struct {
	g.Meta `path:"/sys_dict/sort/{id}" method:"put" tags:"SysDict" summary:"更新字典项排序" permission:"system:dict:updateSort"`
	ID     int64 `p:"id" v:"required#请选择需要更新的记录" dc:"字典项ID"`
	Sort   int   `p:"sort" v:"required#请输入排序值" dc:"排序"`
}

type UpdateSortRes struct{}

// DictInfo 字典项列表信息
type DictInfo struct {
	ID        int64       `json:"id" dc:"主键ID"`
	GroupId   int64       `json:"group_id" dc:"字典分组ID"`
	Name      string      `json:"name" dc:"字典名称"`
	Value     string      `json:"value" dc:"字典值"`
	Code      string      `json:"code" dc:"字典编码"`
	Sort      int         `json:"sort" dc:"排序"`
	IsDisable int         `json:"is_disable" dc:"是否禁用：0=否，1=是"`
	IsSystem  int         `json:"is_system" dc:"是否系统保留：0=否，1=是"`
	Remark    string      `json:"remark" dc:"备注"`
	CreatedAt *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt *gtime.Time `json:"updated_at" dc:"更新时间"`
}

// DictDetail 字典项详情信息
type DictDetail struct {
	ID        int64       `json:"id" dc:"主键ID"`
	GroupId   int64       `json:"group_id" dc:"字典分组ID"`
	Name      string      `json:"name" dc:"字典名称"`
	Value     string      `json:"value" dc:"字典值"`
	Code      string      `json:"code" dc:"字典编码"`
	Sort      int         `json:"sort" dc:"排序"`
	IsDisable int         `json:"is_disable" dc:"是否禁用：0=否，1=是"`
	IsSystem  int         `json:"is_system" dc:"是否系统保留：0=否，1=是"`
	Remark    string      `json:"remark" dc:"备注"`
	CreatedAt *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt *gtime.Time `json:"updated_at" dc:"更新时间"`
}

// DictOption 字典项选项
type DictOption struct {
	ID    int64  `json:"id" dc:"主键ID"`
	Name  string `json:"name" dc:"字典名称"`
	Value string `json:"value" dc:"字典值"`
	Code  string `json:"code" dc:"字典编码"`
	Sort  int    `json:"sort" dc:"排序"`
}
