package middleware

import (
	"admin-server/internal/service"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// Auth JWT认证中间件
func Auth(r *ghttp.Request) {
	// 获取Authorization header
	authHeader := r.Header.Get("Authorization")
	g.Log().Info(r.Context(), "Auth middleware - Authorization header:", authHeader)

	if authHeader == "" {
		r.Response.WriteJsonExit(g.Map{
			"code":    61,
			"message": "未提供认证令牌",
			"data":    nil,
		})
		return
	}

	// 检查Bearer前缀
	if !strings.HasPrefix(authHeader, "Bearer ") {
		r.Response.WriteJsonExit(g.Map{
			"code":    61,
			"message": "认证令牌格式错误",
			"data":    nil,
		})
		return
	}

	// 提取token
	token := strings.TrimPrefix(authHeader, "Bearer ")
	g.Log().Info(r.Context(), "Auth middleware - extracted token:", token[:20]+"...")

	if token == "" {
		r.Response.WriteJsonExit(g.Map{
			"code":    61,
			"message": "认证令牌不能为空",
			"data":    nil,
		})
		return
	}

	// 验证token
	adminId, err := service.SysAuth().ValidateToken(r.Context(), token)
	g.Log().Info(r.Context(), "Auth middleware - ValidateToken result:", adminId, err)

	if err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code":    61,
			"message": "未登录或登录已过期",
			"data":    nil,
		})
		return
	}

	// 将管理员ID设置到上下文中
	r.SetCtxVar("admin_id", adminId)
	g.Log().Info(r.Context(), "Auth middleware - set admin_id to context:", adminId)

	// 继续执行下一个中间件或处理器
	r.Middleware.Next()
}

// AuthOptional 可选的JWT认证中间件（不强制要求登录）
func AuthOptional(r *ghttp.Request) {
	// 获取Authorization header
	authHeader := r.Header.Get("Authorization")
	if authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
		// 提取token
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token != "" {
			// 验证token
			adminId, err := service.SysAuth().ValidateToken(r.Context(), token)
			if err == nil {
				// 将管理员ID设置到上下文中
				r.SetCtxVar("admin_id", adminId)
			}
		}
	}

	// 继续执行下一个中间件或处理器
	r.Middleware.Next()
}
