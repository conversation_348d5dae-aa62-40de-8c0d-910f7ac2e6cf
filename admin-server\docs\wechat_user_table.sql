-- 微信用户表
CREATE TABLE `wechat_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(64) NOT NULL COMMENT '微信openid',
  `unionid` varchar(64) DEFAULT NULL COMMENT '微信unionid',
  `nickname` varchar(255) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `sex` tinyint(1) DEFAULT 0 COMMENT '性别：0=未知，1=男，2=女',
  `city` varchar(100) DEFAULT NULL COMMENT '城市',
  `province` varchar(100) DEFAULT NULL COMMENT '省份',
  `country` varchar(100) DEFAULT NULL COMMENT '国家',
  `language` varchar(20) DEFAULT NULL COMMENT '语言',
  `subscribe` tinyint(1) DEFAULT 0 COMMENT '是否关注：0=未关注，1=已关注',
  `subscribe_time` datetime DEFAULT NULL COMMENT '关注时间',
  `subscribe_scene` varchar(50) DEFAULT NULL COMMENT '关注场景',
  `qr_scene` varchar(100) DEFAULT NULL COMMENT '二维码场景值',
  `qr_scene_str` varchar(200) DEFAULT NULL COMMENT '二维码场景描述',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活：0=禁用，1=启用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`),
  KEY `idx_unionid` (`unionid`),
  KEY `idx_subscribe` (`subscribe`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信用户表';
