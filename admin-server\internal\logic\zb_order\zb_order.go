package zb_order

import (
	zbCityV1 "admin-server/api/zb_city/v1"
	zbGoodV1 "admin-server/api/zb_good/v1"
	v1 "admin-server/api/zb_order/v1"
	"admin-server/internal/dao"
	"admin-server/internal/model/entity"
	"admin-server/internal/service"
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/grand"
)

type sZbOrder struct{}

func init() {
	service.RegisterZbOrder(&sZbOrder{})
}

// Create 创建订单
func (s *sZbOrder) Create(ctx context.Context, req *v1.CreateReq) (*v1.CreateRes, error) {
	// 验证城市ID列表
	if len(req.CityIds) == 0 {
		return nil, fmt.Errorf("请至少选择一个城市")
	}
	if len(req.CityIds) > 50 {
		return nil, fmt.Errorf("最多只能选择50个城市")
	}

	// 根据OpenID获取用户信息
	user, err := service.ZbUser().GetUserByOpenid(ctx, req.OpenId)
	if err != nil {
		g.Log().Error(ctx, "根据OpenID获取用户失败:", err)
		return nil, fmt.Errorf("用户不存在或用户已被删除")
	}

	// 套餐请求结构体
	ZbGoodGetOneReq := &zbGoodV1.ZbGoodGetOneReq{
		Id: int(req.GoodId),
	}
	// 根据套餐id获取套餐详情
	good, err := service.ZbGood().GetOne(ctx, ZbGoodGetOneReq)
	if err != nil {
		g.Log().Error(ctx, "获取套餐失败:", err)
		return nil, fmt.Errorf("套餐不存在或已删除")
	}

	// 生成订单编号
	orderSn := s.GenerateOrderSn()

	// 计算总价格：套餐价格 × 城市数量
	totalPrice := good.Price * float64(len(req.CityIds))

	// 使用事务创建订单
	var orderId int64
	err = dao.ZbOrder.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 创建订单主记录
		result, err := dao.ZbOrder.Ctx(ctx).Data(g.Map{
			"order_sn":   orderSn,
			"good_id":    good.Id,
			"good_name":  good.Name,
			"good_price": good.Price,
			"effective":  good.Effective, // 套餐有效期
			"city_count": len(req.CityIds),
			"user_id":    user.Id,
			"price":      totalPrice,
			"amount":     0,
			"pay_status": 0, // 未支付
			"remark":     req.Remark,
		}).Insert()
		if err != nil {
			return err
		}

		// 获取订单ID
		orderId, err = result.LastInsertId()
		if err != nil {
			return err
		}

		// 批量插入订单城市关联记录
		var cityData []g.Map
		for _, cityId := range req.CityIds {
			// 获取城市名称
			cityName, err := s.getCityName(ctx, cityId)
			if err != nil {
				return fmt.Errorf("获取城市名称失败: %v", err)
			}

			cityData = append(cityData, g.Map{
				"order_id":  orderId,
				"city_id":   cityId,
				"city_name": cityName,
			})
		}

		if len(cityData) > 0 {
			_, err = dao.ZbOrderCity.Ctx(ctx).Data(cityData).Insert()
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		g.Log().Error(ctx, "创建订单失败:", err)
		return nil, err
	}

	g.Log().Info(ctx, "订单创建成功:", g.Map{
		"order_id": orderId,
		"order_sn": orderSn,
		"user_id":  user.Id,
		"openid":   req.OpenId,
		"price":    totalPrice,
	})

	return &v1.CreateRes{
		OrderId: orderId,
		OrderSn: orderSn,
	}, nil
}

// GetList 获取订单列表
func (s *sZbOrder) GetList(ctx context.Context, req *v1.GetListReq) (*v1.GetListRes, error) {
	model := dao.ZbOrder.Ctx(ctx)

	// 添加查询条件
	if req.OrderSn != "" {
		model = model.Where("order_sn LIKE ?", "%"+req.OrderSn+"%")
	}
	if req.UserId > 0 {
		model = model.Where("user_id", req.UserId)
	}
	if req.PayStatus != nil {
		model = model.Where("pay_status", *req.PayStatus)
	}
	if req.StartTime != "" {
		model = model.Where("created_at >= ?", req.StartTime+" 00:00:00")
	}
	if req.EndTime != "" {
		model = model.Where("created_at <= ?", req.EndTime+" 23:59:59")
	}

	// 获取总数
	total, err := model.Count()
	if err != nil {
		return nil, err
	}

	// 分页查询订单列表（关联用户表获取昵称）
	type OrderWithUser struct {
		entity.ZbOrder
		UserNickname string `json:"user_nickname"`
	}

	var ordersWithUser []OrderWithUser
	err = model.Page(req.Page, req.PageSize).
		LeftJoin("zb_user u", "zb_order.user_id = u.id").
		Fields("zb_order.*, u.nickname as user_nickname").
		Order("zb_order.created_at DESC").
		Scan(&ordersWithUser)
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	var list []v1.OrderInfo
	for _, orderWithUser := range ordersWithUser {
		// 获取订单关联的城市
		cities, err := s.getOrderCities(ctx, orderWithUser.Id)
		if err != nil {
			g.Log().Warning(ctx, "获取订单城市失败:", err)
			cities = []v1.CityInfo{} // 设置为空数组
		}

		orderInfo := v1.OrderInfo{
			Id:            orderWithUser.Id,
			OrderSn:       orderWithUser.OrderSn,
			GoodId:        orderWithUser.GoodId,
			GoodName:      orderWithUser.GoodName,
			GoodPrice:     orderWithUser.GoodPrice,
			Effective:     orderWithUser.Effective,
			CityCount:     orderWithUser.CityCount,
			UserId:        orderWithUser.UserId,
			UserNickname:  orderWithUser.UserNickname,
			Price:         orderWithUser.Price,
			Amount:        orderWithUser.Amount,
			PayStatus:     orderWithUser.PayStatus,
			TransactionId: orderWithUser.TransactionId,
			TradeType:     orderWithUser.TradeType,
			TradeState:    orderWithUser.TradeState,
			Remark:        orderWithUser.Remark,
			PayAt:         orderWithUser.PayAt,
			CreatedAt:     orderWithUser.CreatedAt,
			UpdatedAt:     orderWithUser.UpdatedAt,
			Cities:        cities,
		}
		list = append(list, orderInfo)
	}

	return &v1.GetListRes{
		List:  list,
		Total: total,
	}, nil
}

// GetDetail 获取订单详情
func (s *sZbOrder) GetDetail(ctx context.Context, id int64) (*v1.GetDetailRes, error) {
	// 关联查询订单和用户信息
	type OrderWithUser struct {
		entity.ZbOrder
		UserNickname string `json:"user_nickname"`
	}

	var orderWithUser OrderWithUser
	err := dao.ZbOrder.Ctx(ctx).
		LeftJoin("zb_user u", "zb_order.user_id = u.id").
		Fields("zb_order.*, u.nickname as user_nickname").
		Where("zb_order.id", id).
		Scan(&orderWithUser)
	if err != nil {
		return nil, err
	}

	if orderWithUser.Id == 0 {
		return nil, fmt.Errorf("订单不存在")
	}

	// 获取订单关联的城市
	cities, err := s.getOrderCities(ctx, orderWithUser.Id)
	if err != nil {
		return nil, err
	}

	return &v1.GetDetailRes{
		OrderInfo: v1.OrderInfo{
			Id:            orderWithUser.Id,
			OrderSn:       orderWithUser.OrderSn,
			GoodId:        orderWithUser.GoodId,
			GoodName:      orderWithUser.GoodName,
			GoodPrice:     orderWithUser.GoodPrice,
			Effective:     orderWithUser.Effective,
			CityCount:     orderWithUser.CityCount,
			UserId:        orderWithUser.UserId,
			UserNickname:  orderWithUser.UserNickname,
			Price:         orderWithUser.Price,
			Amount:        orderWithUser.Amount,
			PayStatus:     orderWithUser.PayStatus,
			TransactionId: orderWithUser.TransactionId,
			TradeType:     orderWithUser.TradeType,
			TradeState:    orderWithUser.TradeState,
			Remark:        orderWithUser.Remark,
			PayAt:         orderWithUser.PayAt,
			CreatedAt:     orderWithUser.CreatedAt,
			UpdatedAt:     orderWithUser.UpdatedAt,
			Cities:        cities,
		},
	}, nil
}

// UpdatePayStatus 更新支付状态（后台代替支付）
func (s *sZbOrder) UpdatePayStatus(ctx context.Context, req *v1.UpdatePayStatusReq) error {
	// 检查订单是否存在
	var order entity.ZbOrder
	err := dao.ZbOrder.Ctx(ctx).Where("id", req.Id).Scan(&order)
	if err != nil {
		return err
	}

	if order.Id == 0 {
		return fmt.Errorf("订单不存在")
	}

	// 检查订单是否已支付
	if order.PayStatus == 1 {
		return fmt.Errorf("订单已支付，无需重复操作")
	}

	// 使用事务处理订单支付和用户VIP有效期更新
	err = dao.ZbOrder.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 1. 更新订单支付状态
		orderData := g.Map{
			"pay_status": 1,           // 设置为已支付
			"trade_type": "ADMIN",     // 后台代替支付
			"amount":     order.Price, // 使用订单原价格作为支付金额
			"pay_at":     gtime.Now(), // 设置支付时间
		}

		_, err := dao.ZbOrder.Ctx(ctx).Where("id", req.Id).Data(orderData).Update()
		if err != nil {
			return err
		}

		// 2. 根据订单user_id获取用户信息
		var user entity.ZbUser
		err = dao.ZbUser.Ctx(ctx).Where("id", order.UserId).Scan(&user)
		if err != nil {
			return err
		}

		if user.Id == 0 {
			return fmt.Errorf("用户不存在")
		}

		// 3. 计算VIP有效期
		now := gtime.Now()
		effectiveStart := now
		effectiveEnd := now.AddDate(0, order.Effective, 0) // 往后延迟effective个月

		// 4. 根据用户当前VIP状态更新有效期
		userData := g.Map{}

		if user.EffectiveStart == nil && user.EffectiveEnd == nil {
			// 情况1: 用户从未开通过VIP
			userData["effective_start"] = effectiveStart
			userData["effective_end"] = effectiveEnd
			g.Log().Info(ctx, "用户首次开通VIP", g.Map{
				"user_id":         user.Id,
				"effective_start": effectiveStart,
				"effective_end":   effectiveEnd,
			})
		} else if now.After(user.EffectiveStart) && now.Before(user.EffectiveEnd) {
			// 情况2: 用户VIP有效期还未过期，延长结束时间
			newEffectiveEnd := user.EffectiveEnd.AddDate(0, order.Effective, 0)
			userData["effective_end"] = newEffectiveEnd
			g.Log().Info(ctx, "用户VIP续期", g.Map{
				"user_id":           user.Id,
				"old_effective_end": user.EffectiveEnd,
				"new_effective_end": newEffectiveEnd,
			})
		} else if now.After(user.EffectiveEnd) {
			// 情况3: 用户VIP有效期已过期，重新设置有效期
			userData["effective_start"] = effectiveStart
			userData["effective_end"] = effectiveEnd
			g.Log().Info(ctx, "用户VIP已过期，重新开通", g.Map{
				"user_id":             user.Id,
				"old_effective_end":   user.EffectiveEnd,
				"new_effective_start": effectiveStart,
				"new_effective_end":   effectiveEnd,
			})
		} else {
			return fmt.Errorf("用户VIP状态异常，无法处理")
		}

		// 5. 更新用户VIP有效期
		if len(userData) > 0 {
			_, err = dao.ZbUser.Ctx(ctx).Where("id", user.Id).Data(userData).Update()
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		g.Log().Error(ctx, "更新支付状态失败:", err)
		return err
	}

	g.Log().Info(ctx, "后台代替支付成功:", g.Map{
		"order_id":   req.Id,
		"order_sn":   order.OrderSn,
		"user_id":    order.UserId,
		"amount":     order.Price,
		"effective":  order.Effective,
		"trade_type": "ADMIN",
	})

	return nil
}

// GetMySubscriptions 获取我的订阅列表
func (s *sZbOrder) GetMySubscriptions(ctx context.Context, req *v1.GetMySubscriptionsReq) (*v1.GetMySubscriptionsRes, error) {
	// 根据OpenID获取用户信息
	user, err := service.ZbUser().GetUserByOpenid(ctx, req.OpenId)
	if err != nil {
		g.Log().Error(ctx, "根据OpenID获取用户失败:", err)
		return nil, fmt.Errorf("用户不存在或获取用户信息失败")
	}

	// 构建查询条件 - 只查询已支付的订单
	model := dao.ZbOrder.Ctx(ctx).Where("user_id", user.Id).Where("pay_status", 1)

	// 获取总数
	total, err := model.Count()
	if err != nil {
		return nil, err
	}

	// 查询订单列表
	var orders []entity.ZbOrder
	err = model.Page(req.Page, req.PageSize).
		Order("pay_at DESC, created_at DESC").
		Scan(&orders)
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	var list []v1.SubscriptionInfo
	for _, order := range orders {
		// 获取订单关联的城市
		cities, err := s.getOrderCities(ctx, order.Id)
		if err != nil {
			g.Log().Error(ctx, "获取订单城市失败:", err)
			cities = []v1.CityInfo{} // 设置为空数组，不影响其他数据
		}

		subscriptionInfo := v1.SubscriptionInfo{
			Id:        order.Id,
			OrderSn:   order.OrderSn,
			GoodId:    order.GoodId,
			GoodName:  order.GoodName,
			CityCount: order.CityCount,
			UserId:    order.UserId,
			GoodPrice: order.GoodPrice,
			Effective: order.Effective,
			Price:     order.Price,
			Amount:    order.Amount,
			PayStatus: order.PayStatus,
			Remark:    order.Remark,
			PayAt:     order.PayAt,
			CreatedAt: order.CreatedAt,
			UpdatedAt: order.UpdatedAt,
			Cities:    cities,
		}
		list = append(list, subscriptionInfo)
	}

	return &v1.GetMySubscriptionsRes{
		List:  list,
		Total: total,
	}, nil
}

// GenerateOrderSn 生成订单编号
func (s *sZbOrder) GenerateOrderSn() string {
	return fmt.Sprintf("ZB%s%04d",
		time.Now().Format("20060102150405"),
		grand.N(1000, 9999))
}

// GetOrderWithCities 获取订单及其城市信息
func (s *sZbOrder) GetOrderWithCities(ctx context.Context, orderId int64) (*entity.ZbOrder, []entity.ZbOrderCity, error) {
	var order entity.ZbOrder
	err := dao.ZbOrder.Ctx(ctx).Where("id", orderId).Scan(&order)
	if err != nil {
		return nil, nil, err
	}

	var cities []entity.ZbOrderCity
	err = dao.ZbOrderCity.Ctx(ctx).Where("order_id", orderId).Scan(&cities)
	if err != nil {
		return nil, nil, err
	}

	return &order, cities, nil
}

// 获取订单关联的城市列表
func (s *sZbOrder) getOrderCities(ctx context.Context, orderId int64) ([]v1.CityInfo, error) {
	var cities []entity.ZbOrderCity
	err := dao.ZbOrderCity.Ctx(ctx).Where("order_id", orderId).Scan(&cities)
	if err != nil {
		return nil, err
	}

	var result []v1.CityInfo
	for _, city := range cities {
		result = append(result, v1.CityInfo{
			CityId:   city.CityId,
			CityName: city.CityName,
		})
	}

	return result, nil
}

// 获取城市名称
func (s *sZbOrder) getCityName(ctx context.Context, cityId int64) (string, error) {
	ZbCityGetOneReq := &zbCityV1.ZbCityGetOneReq{Id: int(cityId)}
	city, cityErr := service.ZbCity().GetOne(ctx, ZbCityGetOneReq)
	if cityErr != nil {
		return "", gerror.New("获取城市名称失败")
	}
	if city.Name == "" {
		return fmt.Sprintf("城市%d", cityId), nil // 如果找不到城市名称，返回默认值
	}
	return city.Name, nil
}

// Delete 删除订单（只能删除未支付的订单）
func (s *sZbOrder) Delete(ctx context.Context, req *v1.DeleteReq) (*v1.DeleteRes, error) {
	// 检查订单是否存在
	var order entity.ZbOrder
	err := dao.ZbOrder.Ctx(ctx).Where("id", req.Id).Scan(&order)
	if err != nil {
		return nil, err
	}

	if order.Id == 0 {
		return nil, fmt.Errorf("订单不存在")
	}

	// 检查订单是否已支付
	if order.PayStatus == 1 {
		return nil, fmt.Errorf("已支付的订单不能删除")
	}

	// 使用事务删除订单和关联的城市记录
	err = dao.ZbOrder.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 删除订单城市关联记录
		_, err := dao.ZbOrderCity.Ctx(ctx).Where("order_id", req.Id).Delete()
		if err != nil {
			return err
		}

		// 删除订单主记录
		_, err = dao.ZbOrder.Ctx(ctx).Where("id", req.Id).Delete()
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		g.Log().Error(ctx, "删除订单失败:", err)
		return nil, err
	}

	g.Log().Info(ctx, "订单删除成功:", g.Map{
		"order_id": req.Id,
		"order_sn": order.OrderSn,
		"user_id":  order.UserId,
	})

	return &v1.DeleteRes{Success: true}, nil
}

// GetMyList 获取我的订单列表（移动端）
func (s *sZbOrder) GetMyList(ctx context.Context, req *v1.GetMyListReq) (*v1.GetMyListRes, error) {
	// 根据OpenID获取用户信息
	user, err := service.ZbUser().GetUserByOpenid(ctx, req.OpenId)
	if err != nil {
		g.Log().Error(ctx, "根据OpenID获取用户失败:", err)
		return nil, fmt.Errorf("用户不存在或获取用户信息失败")
	}

	// 构建查询条件
	model := dao.ZbOrder.Ctx(ctx).Where("user_id", user.Id)

	// 获取总数
	total, err := model.Count()
	if err != nil {
		return nil, err
	}

	// 分页查询订单列表
	var orders []entity.ZbOrder
	err = model.Page(req.Page, req.PageSize).
		Order("created_at DESC").
		Scan(&orders)
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	var list []v1.OrderInfo
	for _, order := range orders {
		// 获取订单关联的城市
		cities, err := s.getOrderCities(ctx, order.Id)
		if err != nil {
			g.Log().Warning(ctx, "获取订单城市失败:", err)
			cities = []v1.CityInfo{} // 设置为空数组
		}

		orderInfo := v1.OrderInfo{
			Id:            order.Id,
			OrderSn:       order.OrderSn,
			GoodId:        order.GoodId,
			GoodName:      order.GoodName,
			GoodPrice:     order.GoodPrice,
			Effective:     order.Effective,
			CityCount:     order.CityCount,
			UserId:        order.UserId,
			UserNickname:  user.Nickname, // 直接使用查询到的用户昵称
			Price:         order.Price,
			Amount:        order.Amount,
			PayStatus:     order.PayStatus,
			TransactionId: order.TransactionId,
			TradeType:     order.TradeType,
			TradeState:    order.TradeState,
			Remark:        order.Remark,
			PayAt:         order.PayAt,
			CreatedAt:     order.CreatedAt,
			UpdatedAt:     order.UpdatedAt,
			Cities:        cities,
		}
		list = append(list, orderInfo)
	}

	return &v1.GetMyListRes{
		List:  list,
		Total: total,
	}, nil
}

// GetMyStats 获取我的订单统计（移动端）
func (s *sZbOrder) GetMyStats(ctx context.Context, req *v1.GetMyStatsReq) (*v1.GetMyStatsRes, error) {
	// 根据OpenID获取用户信息
	user, err := service.ZbUser().GetUserByOpenid(ctx, req.OpenId)
	if err != nil {
		g.Log().Error(ctx, "根据OpenID获取用户失败:", err)
		return nil, fmt.Errorf("用户不存在或获取用户信息失败")
	}

	// 构建查询条件
	model := dao.ZbOrder.Ctx(ctx).Where("user_id", user.Id)

	// 统计总订单数
	totalOrderCount, err := model.Count()
	if err != nil {
		return nil, err
	}

	// 统计已支付订单数, 忽略掉后台代替支付的订单
	paidOrderCount, err := model.Where("pay_status", 1).Where("trade_type !=?", "ADMIN").Count()
	if err != nil {
		return nil, err
	}

	// 统计未支付订单数
	unpaidOrderCount := totalOrderCount - paidOrderCount

	// 统计已支付总金额
	paidTotalAmount, err := model.Where("pay_status", 1).Where("trade_type !=?", "ADMIN").Sum("amount")
	if err != nil {
		return nil, err
	}

	g.Log().Info(ctx, "用户订单统计查询成功:", g.Map{
		"user_id":            user.Id,
		"openid":             req.OpenId,
		"total_order_count":  totalOrderCount,
		"paid_order_count":   paidOrderCount,
		"unpaid_order_count": unpaidOrderCount,
		"paid_total_amount":  paidTotalAmount,
	})

	return &v1.GetMyStatsRes{
		PaidOrderCount:   paidOrderCount,
		PaidTotalAmount:  paidTotalAmount,
		UnpaidOrderCount: unpaidOrderCount,
		TotalOrderCount:  totalOrderCount,
	}, nil
}
