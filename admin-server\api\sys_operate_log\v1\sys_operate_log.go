package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// GetListReq 获取操作日志列表请求体
type GetListReq struct {
	g.Meta    `path:"/sys_operate_log/list" tags:"SysOperateLog" method:"get" summary:"获取操作日志列表" permission:"system:operatelog:list"`
	Page      int    `p:"page" d:"1" v:"min:1#页码最小为1" dc:"页码"`
	PageSize  int    `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间" dc:"每页数量"`
	Username  string `p:"username" dc:"操作人账号（模糊搜索）"`
	Title     string `p:"title" dc:"操作标题（模糊搜索）"`
	Method    string `p:"method" dc:"请求方法：GET/POST/PUT/DELETE"`
	Status    int    `p:"status" dc:"执行状态：1=成功，2=失败"`
	StartDate string `p:"start_date" dc:"开始日期，格式：2024-01-01"`
	EndDate   string `p:"end_date" dc:"结束日期，格式：2024-01-31"`
}

type GetListRes struct {
	List  []OperateLogInfo `json:"list" dc:"操作日志列表"`
	Total int              `json:"total" dc:"总数"`
}

// GetOneReq 获取操作日志详情请求体
type GetOneReq struct {
	g.Meta `path:"/sys_operate_log/{id}" tags:"SysOperateLog" method:"get" summary:"获取操作日志详情" permission:"system:operatelog:view"`
	ID     int64 `p:"id" v:"required#请选择需要查询的记录" dc:"操作日志ID"`
}

type GetOneRes struct {
	OperateLogDetail *OperateLogDetail `json:"operate_log" dc:"操作日志详情"`
}

// DeleteReq 删除操作日志请求体
type DeleteReq struct {
	g.Meta `path:"/sys_operate_log/delete" method:"delete" tags:"SysOperateLog" summary:"删除操作日志" permission:"system:operatelog:delete"`
	IDs    []int64 `p:"ids" v:"required#请选择需要删除的记录" dc:"操作日志ID列表"`
}

type DeleteRes struct{}

// ClearReq 清空操作日志请求体
type ClearReq struct {
	g.Meta `path:"/sys_operate_log/clear" method:"delete" tags:"SysOperateLog" summary:"清空操作日志" permission:"system:operatelog:clear"`
}

type ClearRes struct{}

// OperateLogInfo 操作日志列表信息
type OperateLogInfo struct {
	ID        int64       `json:"id" dc:"主键ID"`
	RequestId string      `json:"request_id" dc:"请求ID"`
	AdminId   int64       `json:"admin_id" dc:"操作人ID"`
	Username  string      `json:"username" dc:"操作人账号"`
	Method    string      `json:"method" dc:"请求方法"`
	Title     string      `json:"title" dc:"操作标题"`
	Ip        string      `json:"ip" dc:"请求IP"`
	Status    int         `json:"status" dc:"执行状态：1=成功，2=失败"`
	TaskTime  int64       `json:"task_time" dc:"执行耗时（毫秒）"`
	CreatedAt *gtime.Time `json:"created_at" dc:"操作时间"`
}

// OperateLogDetail 操作日志详情信息
type OperateLogDetail struct {
	ID        int64       `json:"id" dc:"主键ID"`
	RequestId string      `json:"request_id" dc:"请求ID"`
	AdminId   int64       `json:"admin_id" dc:"操作人ID"`
	Username  string      `json:"username" dc:"操作人账号"`
	Method    string      `json:"method" dc:"请求方法"`
	Title     string      `json:"title" dc:"操作标题"`
	Ip        string      `json:"ip" dc:"请求IP"`
	ReqHeader string      `json:"req_header" dc:"请求头（JSON字符串）"`
	ReqBody   string      `json:"req_body" dc:"请求体（JSON字符串）"`
	ResHeader string      `json:"res_header" dc:"响应头（JSON字符串）"`
	ResBody   string      `json:"res_body" dc:"响应体（JSON字符串）"`
	Status    int         `json:"status" dc:"执行状态：1=成功，2=失败"`
	StartTime int         `json:"start_time" dc:"开始时间戳"`
	EndTime   int         `json:"end_time" dc:"结束时间戳"`
	TaskTime  int         `json:"task_time" dc:"执行耗时（毫秒）"`
	CreatedAt *gtime.Time `json:"created_at" dc:"操作时间"`
	UpdatedAt *gtime.Time `json:"updated_at" dc:"更新时间"`
}
