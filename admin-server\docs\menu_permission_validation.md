# 菜单权限标识验证规则

## 概述

根据业务需求，菜单的权限标识（perms）字段的验证规则根据菜单类型而有所不同。

## 验证规则

### 1. 目录类型（M - Directory）
- **权限标识**: 可选
- **说明**: 目录通常用于分组和导航，不需要具体的权限控制
- **示例**: 可以为空字符串或提供有意义的标识如 "system:manage"

### 2. 菜单类型（C - Component/Menu）
- **权限标识**: 必填
- **说明**: 菜单对应具体的页面，需要权限控制访问
- **示例**: "system:user:list", "system:role:manage"

### 3. 按钮类型（A - Action/Button）
- **权限标识**: 必填
- **说明**: 按钮对应具体的操作，需要权限控制执行
- **示例**: "system:user:create", "system:user:update", "system:user:delete"

## 实现细节

### API层验证
在API接口定义中，权限标识字段的验证规则已调整为：
```go
Perms string `p:"perms" v:"length:1,100#权限标识长度为1-100位" dc:"权限标识（目录类型可选，菜单和按钮类型必填）"`
```

### 业务逻辑层验证
在Logic层实现了自定义验证方法 `validatePerms`：

```go
func (s SsysMenu) validatePerms(ctx context.Context, menuType packed.MenuType, perms string, excludeId int64) error {
    // 目录类型权限标识可选，菜单和按钮类型必填
    if menuType != packed.MENU_TYPE_DIR {
        if perms == "" {
            return gerror.New("菜单和按钮类型的权限标识不能为空")
        }
    }

    // 如果提供了权限标识，检查是否重复
    if perms != "" {
        if err := s.checkPerms(ctx, perms, excludeId); err != nil {
            return err
        }
    }

    return nil
}
```

## 使用示例

### 创建目录（权限标识可选）

```bash
# 不提供权限标识
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 0,
    "menu_type": "M",
    "menu_name": "系统管理",
    "menu_icon": "system",
    "perms": ""
  }'

# 提供权限标识
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 0,
    "menu_type": "M",
    "menu_name": "系统管理",
    "menu_icon": "system",
    "perms": "system:manage"
  }'
```

### 创建菜单（权限标识必填）

```bash
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 1,
    "menu_type": "C",
    "menu_name": "用户管理",
    "menu_icon": "user",
    "perms": "system:user:list"
  }'
```

### 创建按钮（权限标识必填）

```bash
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 2,
    "menu_type": "A",
    "menu_name": "新增用户",
    "perms": "system:user:create"
  }'
```

## 错误处理

### 菜单类型权限标识为空
```json
{
  "code": 1,
  "message": "菜单和按钮类型的权限标识不能为空",
  "data": null
}
```

### 按钮类型权限标识为空
```json
{
  "code": 1,
  "message": "菜单和按钮类型的权限标识不能为空",
  "data": null
}
```

### 权限标识重复
```json
{
  "code": 1,
  "message": "权限标识已存在",
  "data": null
}
```

## 权限标识命名规范

### 推荐格式
使用冒号分隔的层级结构：`模块:功能:操作`

### 目录类型示例
- `system` - 系统模块
- `system:manage` - 系统管理
- `business` - 业务模块
- `business:manage` - 业务管理

### 菜单类型示例
- `system:user:list` - 用户列表页面
- `system:role:list` - 角色列表页面
- `system:menu:list` - 菜单列表页面
- `business:order:list` - 订单列表页面

### 按钮类型示例
- `system:user:create` - 创建用户
- `system:user:update` - 更新用户
- `system:user:delete` - 删除用户
- `system:user:export` - 导出用户
- `system:user:import` - 导入用户

## 前端集成建议

### 1. 表单验证
根据菜单类型动态显示权限标识字段的必填状态：

```javascript
function validatePerms(menuType, perms) {
    if ((menuType === 'C' || menuType === 'A') && !perms.trim()) {
        return '菜单和按钮类型的权限标识不能为空';
    }
    return null;
}
```

### 2. 用户界面提示
在表单中显示不同菜单类型的权限标识要求：

```html
<input type="text" id="perms" placeholder="目录类型可选，菜单和按钮类型必填">
<small>目录类型可选，菜单和按钮类型必填</small>
```

### 3. 动态提示
根据选择的菜单类型动态更新提示信息：

```javascript
document.getElementById('menuType').addEventListener('change', function() {
    const menuType = this.value;
    const permsInput = document.getElementById('perms');
    
    if (menuType === 'M') {
        permsInput.placeholder = '目录类型可选，可以为空';
        permsInput.required = false;
    } else {
        permsInput.placeholder = '菜单和按钮类型必填';
        permsInput.required = true;
    }
});
```

## 注意事项

1. **向后兼容**: 现有的目录如果已经设置了权限标识，不会受到影响
2. **唯一性检查**: 如果提供了权限标识，仍然需要保证全局唯一
3. **空值处理**: 空字符串和null都被视为未提供权限标识
4. **更新操作**: 更新菜单时同样遵循此验证规则
5. **数据库存储**: 目录类型的权限标识可以存储为空字符串

## 业务价值

1. **灵活性**: 目录不需要权限标识，简化了菜单结构设计
2. **安全性**: 菜单和按钮必须有权限标识，确保权限控制的完整性
3. **易用性**: 减少了不必要的权限标识配置工作
4. **规范性**: 明确了不同菜单类型的权限管理要求
