// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"admin-server/internal/packed"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysAdmins is the golang structure for table sys_admins.
type SysAdmins struct {
	Id            int64       `json:"id"            orm:"id"              description:""`               //
	Username      string      `json:"username"      orm:"username"        description:"账号"`             // 账号
	Password      string      `json:"password"      orm:"password"        description:"密码"`             // 密码
	Nickname      string      `json:"nickname"      orm:"nickname"        description:"昵称"`             // 昵称
	IsSuper       packed.Super         `json:"isSuper"       orm:"is_super"        description:"是否是超级管理员1是0否"`   // 是否是超级管理员1是0否
	IsDisable     packed.Disable         `json:"isDisable"     orm:"is_disable"      description:"是否禁用: 0=否, 1=是"` // 是否禁用: 0=否, 1=是
	IsDelete      packed.IsDelete         `json:"isDelete"      orm:"is_delete"       description:"是否删除: 0=否, 1=是"` // 是否删除: 0=否, 1=是
	LastLoginIp   string      `json:"lastLoginIp"   orm:"last_login_ip"   description:"最后登录ip"`         // 最后登录ip
	LastLoginTime *gtime.Time `json:"lastLoginTime" orm:"last_login_time" description:"最后登录时间"`         // 最后登录时间
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"      description:"创建时间"`           // 创建时间
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"      description:"更新时间"`           // 更新时间
	DeletedAt     *gtime.Time `json:"deletedAt"     orm:"deleted_at"      description:"删除时间"`           // 删除时间
}
