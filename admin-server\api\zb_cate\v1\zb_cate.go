package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ZbCateCreateReq 创建招标类别请求
type ZbCateCreateReq struct {
	g.Meta    `path:"/zb_cate" method:"post" tags:"ZbCate" summary:"创建招标类别" permission:"system:zb_cate:add"`
	Name      string `p:"name" v:"required|length:1,255#类别名称不能为空|类别名称长度不能超过255个字符" dc:"类别名称"`
	Sort      int    `p:"sort" v:"min:0#排序值不能小于0" dc:"排序，数字越小越靠前"`
	IsDisable int    `p:"is_disable" v:"in:0,1#状态值必须是0或1" dc:"是否禁用：0=否，1=是"`
}

// ZbCateCreateRes 创建招标类别响应
type ZbCateCreateRes struct {
	Id int `json:"id" dc:"类别ID"`
}

// ZbCateUpdateReq 更新招标类别请求
type ZbCateUpdateReq struct {
	g.Meta    `path:"/zb_cate/{id}" method:"put" tags:"ZbCate" summary:"更新招标类别" permission:"system:zb_cate:edit"`
	Id        int    `p:"id" v:"required|min:1#类别ID不能为空|类别ID必须大于0" dc:"类别ID"`
	Name      string `p:"name" v:"required|length:1,255#类别名称不能为空|类别名称长度不能超过255个字符" dc:"类别名称"`
	Sort      int    `p:"sort" v:"min:0#排序值不能小于0" dc:"排序，数字越小越靠前"`
	IsDisable int    `p:"is_disable" v:"in:0,1#状态值必须是0或1" dc:"是否禁用：0=否，1=是"`
}

// ZbCateUpdateRes 更新招标类别响应
type ZbCateUpdateRes struct{}

// ZbCateDeleteReq 删除招标类别请求
type ZbCateDeleteReq struct {
	g.Meta `path:"/zb_cate/{id}" method:"delete" tags:"ZbCate" summary:"删除招标类别" permission:"system:zb_cate:del"`
	Id     int `p:"id" v:"required|min:1#类别ID不能为空|类别ID必须大于0" dc:"类别ID"`
}

// ZbCateDeleteRes 删除招标类别响应
type ZbCateDeleteRes struct{}

// ZbCateGetOneReq 获取单个招标类别请求
type ZbCateGetOneReq struct {
	g.Meta `path:"/zb_cate/{id}" method:"get" tags:"ZbCate" summary:"获取单个招标类别" permission:"system:zb_cate:info"`
	Id     int `p:"id" v:"required|min:1#类别ID不能为空|类别ID必须大于0" dc:"类别ID"`
}

// ZbCateGetOneRes 获取单个招标类别响应
type ZbCateGetOneRes struct {
	*ZbCateInfo `json:",inline"`
}

// ZbCateGetListReq 获取招标类别列表请求
type ZbCateGetListReq struct {
	g.Meta    `path:"/zb_cate/list" method:"get" tags:"ZbCate" summary:"获取招标类别列表" permission:"system:zb_cate:list"`
	Page      int    `p:"page" v:"min:1#页码必须大于0" dc:"页码，默认1"`
	PageSize  int    `p:"page_size" v:"min:1|max:100#每页数量必须大于0|每页数量不能超过100" dc:"每页数量，默认10"`
	Name      string `p:"name" dc:"类别名称，模糊搜索"`
	IsDisable *int   `p:"is_disable" v:"in:0,1#状态值必须是0或1" dc:"是否禁用：0=否，1=是"`
}

// ZbCateGetListRes 获取招标类别列表响应
type ZbCateGetListRes struct {
	List     []*ZbCateInfo `json:"list" dc:"类别列表"`
	Total    int           `json:"total" dc:"总数"`
	Page     int           `json:"page" dc:"当前页码"`
	PageSize int           `json:"page_size" dc:"每页数量"`
}

// ZbCateGetAllReq 获取所有招标类别请求
type ZbCateGetAllReq struct {
	g.Meta    `path:"/zb_cate/all" method:"get" tags:"ZbCate" summary:"获取所有招标类别"`
	IsDisable *int `p:"is_disable" v:"in:0,1#状态值必须是0或1" dc:"是否禁用：0=否，1=是"`
}

// ZbCateGetAllRes 获取所有招标类别响应
type ZbCateGetAllRes struct {
	List []*ZbCateInfo `json:"list" dc:"类别列表"`
}

// ZbCateUpdateSortReq 更新招标类别排序请求
type ZbCateUpdateSortReq struct {
	g.Meta `path:"/zb_cate/{id}/sort" method:"put" tags:"ZbCate" summary:"更新招标类别排序" permission:"system:zb_cate:sort"`
	Id     int `p:"id" v:"required|min:1#类别ID不能为空|类别ID必须大于0" dc:"类别ID"`
	Sort   int `p:"sort" v:"min:0#排序值不能小于0" dc:"排序值"`
}

// ZbCateUpdateSortRes 更新招标类别排序响应
type ZbCateUpdateSortRes struct{}

// ZbCateUpdateStatusReq 更新招标类别状态请求
type ZbCateUpdateStatusReq struct {
	g.Meta    `path:"/zb_cate/{id}/status" method:"put" tags:"ZbCate" summary:"更新招标类别状态" permission:"system:zb_cate:status"`
	Id        int `p:"id" v:"required|min:1#类别ID不能为空|类别ID必须大于0" dc:"类别ID"`
	IsDisable int `p:"is_disable" v:"required|in:0,1#状态不能为空|状态值必须是0或1" dc:"是否禁用：0=否，1=是"`
}

// ZbCateUpdateStatusRes 更新招标类别状态响应
type ZbCateUpdateStatusRes struct{}

// ZbCateBatchDeleteReq 批量删除招标类别请求
type ZbCateBatchDeleteReq struct {
	g.Meta `path:"/zb_cate/batch" method:"delete" tags:"ZbCate" summary:"批量删除招标类别" permission:"system:zb_cate:batchDel"`
	Ids    []int `p:"ids" v:"required|min-length:1#类别ID列表不能为空|至少选择一个类别" dc:"类别ID列表"`
}

// ZbCateBatchDeleteRes 批量删除招标类别响应
type ZbCateBatchDeleteRes struct {
	Count int `json:"count" dc:"删除数量"`
}

// ZbCateInfo 招标类别信息
type ZbCateInfo struct {
	Id        int         `json:"id" dc:"类别ID"`
	Name      string      `json:"name" dc:"类别名称"`
	Sort      int         `json:"sort" dc:"排序"`
	IsDisable int         `json:"is_disable" dc:"是否禁用"`
	IsDelete  int         `json:"is_delete" dc:"是否删除"`
	CreatedAt *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt *gtime.Time `json:"updated_at" dc:"更新时间"`
	DeletedAt *gtime.Time `json:"deleted_at" dc:"删除时间"`
}
