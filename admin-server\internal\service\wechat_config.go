package service

import (
	v1 "admin-server/api/wechat_config/v1"
	"context"

	"github.com/ArtisanCloud/PowerWeChat/v3/src/officialAccount"
)

// IWechatConfig 微信配置服务接口
type IWechatConfig interface {
	GetOfficialAccount(ctx context.Context) (*officialAccount.OfficialAccount, error)
	GetConfig(ctx context.Context) (res *v1.WechatConfigGetRes, err error)
	SaveConfig(ctx context.Context, req *v1.WechatConfigSaveReq) (success bool, message string, err error)
}

// 定义接口变量
var localWechatConfig IWechatConfig

// 获取接口实例的函数
func WechatConfig() IWechatConfig {
	if localWechatConfig == nil {
		panic("IWechatConfig接口未实现或未注册")
	}
	return localWechatConfig
}

// 接口实现的注册方法
func RegisterWechatConfig(i IWechatConfig) {
	localWechatConfig = i
}
