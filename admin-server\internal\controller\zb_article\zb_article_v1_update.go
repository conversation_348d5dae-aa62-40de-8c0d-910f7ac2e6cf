package zb_article

import (
	"context"
	"encoding/json"

	v1 "admin-server/api/zb_article/v1"
	"admin-server/internal/service"

	"github.com/gogf/gf/v2/errors/gerror"
)

// processJSONFieldForUpdate 处理JSON字段（用于更新）
func processJSONFieldForUpdate(data interface{}) (string, error) {
	if data == nil {
		return "", nil
	}

	// 如果已经是字符串，直接返回
	if str, ok := data.(string); ok {
		return str, nil
	}

	// 转换为JSON字符串
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return "", gerror.Wrap(err, "JSON序列化失败")
	}

	return string(jsonBytes), nil
}

func (c *ControllerV1) Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error) {
	// 处理JSON字段
	fullContentStr, err := processJSONFieldForUpdate(req.FullContent)
	if err != nil {
		return nil, err
	}

	shieidContentStr, err := processJSONFieldForUpdate(req.ShieidContent)
	if err != nil {
		return nil, err
	}

	// 构建更新字段映射，只包含业务字段
	updateFields := map[string]interface{}{
		"city_id":         req.CityId,
		"cate_id":         req.CateId,
		"title":           req.Title,
		"intro":           req.Intro,
		"full_content":    fullContentStr,
		"shieid_content":  shieidContentStr,
		"seo_title":       req.SeoTitle,
		"seo_keywords":    req.SeoKeywords,
		"seo_description": req.SeoDescription,
		"pic":             req.Pic,
		"author":          req.Author,
		"is_disable":      req.IsDisable,
	}

	// 更新信息
	err = service.ZbArticle().UpdateArticleFields(ctx, req.ID, updateFields)
	if err != nil {
		return nil, err
	}

	return &v1.UpdateRes{}, nil
}
