# 订单新增删除和我的订单接口说明

## 📋 新增接口概述

为订单模块新增两个重要接口：
1. **订单删除接口** - 只允许删除未支付的订单
2. **移动端我的订单列表** - 根据openid获取用户的订单列表

## 🔧 接口详情

### 1. 订单删除接口

#### API定义
```go
// DeleteReq 删除订单请求
type DeleteReq struct {
    g.Meta `path:"/zb_order/delete" tags:"Order" method:"delete" summary:"删除订单"`
    Id     int64 `json:"id" v:"required|min:1" dc:"订单ID"`
}

// DeleteRes 删除订单响应
type DeleteRes struct {
    Success bool `json:"success" dc:"是否成功"`
}
```

#### 业务逻辑
```go
func (s *sZbOrder) Delete(ctx context.Context, req *v1.DeleteReq) (*v1.DeleteRes, error) {
    // 1. 检查订单是否存在
    var order entity.ZbOrder
    err := dao.ZbOrder.Ctx(ctx).Where("id", req.Id).Scan(&order)
    
    // 2. 检查订单是否已支付
    if order.PayStatus == 1 {
        return nil, fmt.Errorf("已支付的订单不能删除")
    }
    
    // 3. 使用事务删除订单和关联的城市记录
    err = dao.ZbOrder.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
        // 删除订单城市关联记录
        dao.ZbOrderCity.Ctx(ctx).Where("order_id", req.Id).Delete()
        // 删除订单主记录
        dao.ZbOrder.Ctx(ctx).Where("id", req.Id).Delete()
        return nil
    })
    
    return &v1.DeleteRes{Success: true}, nil
}
```

#### 接口地址
- **URL**: `DELETE /api/zb_order/delete`
- **权限**: 需要后台管理员权限
- **参数**: `{"id": 订单ID}`

#### 删除规则
- ✅ **未支付订单** (`pay_status = 0`) - 可以删除
- ❌ **已支付订单** (`pay_status = 1`) - 不能删除
- 🔄 **事务保证** - 同时删除订单主表和城市关联表

### 2. 移动端我的订单列表接口

#### API定义
```go
// GetMyListReq 获取我的订单列表请求
type GetMyListReq struct {
    g.Meta   `path:"/zb_order/my-list" tags:"Order" method:"get" summary:"获取我的订单列表"`
    OpenId   string `json:"openid"    v:"required|length:1,100" dc:"用户OpenID"`
    Page     int    `json:"page"      v:"min:1" d:"1" dc:"页码"`
    PageSize int    `json:"page_size" v:"min:1,max:50" d:"10" dc:"每页数量"`
}

// GetMyListRes 获取我的订单列表响应
type GetMyListRes struct {
    List  []OrderInfo `json:"list"  dc:"订单列表"`
    Total int         `json:"total" dc:"总数"`
}
```

#### 业务逻辑
```go
func (s *sZbOrder) GetMyList(ctx context.Context, req *v1.GetMyListReq) (*v1.GetMyListRes, error) {
    // 1. 根据OpenID获取用户信息
    user, err := service.ZbUser().GetUserByOpenid(ctx, req.OpenId)
    if err != nil {
        return nil, fmt.Errorf("用户不存在或获取用户信息失败")
    }
    
    // 2. 根据用户ID查询订单列表
    model := dao.ZbOrder.Ctx(ctx).Where("user_id", user.Id)
    
    // 3. 分页查询
    var orders []entity.ZbOrder
    err = model.Page(req.Page, req.PageSize).
        Order("created_at DESC").
        Scan(&orders)
    
    // 4. 组装响应数据（包含城市信息）
    return &v1.GetMyListRes{List: list, Total: total}, nil
}
```

#### 接口地址
- **URL**: `GET /m/api/zb_order/my-list`
- **权限**: 无需token验证，通过openid获取用户
- **参数**: `openid`, `page`, `page_size`

#### 查询流程
1. **OpenID → 用户ID**: `service.ZbUser().GetUserByOpenid()`
2. **用户ID → 订单列表**: `WHERE user_id = ?`
3. **关联城市信息**: 查询 `zb_order_city` 表
4. **分页返回**: 按创建时间倒序

## 📊 API使用示例

### 1. 删除订单接口

#### 请求示例
```bash
curl -X DELETE "http://localhost:8000/api/zb_order/delete" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{"id": 123}'
```

#### 响应示例
```json
// 成功删除
{
    "code": 0,
    "message": "success",
    "data": {
        "success": true
    }
}

// 删除失败（已支付）
{
    "code": 1,
    "message": "已支付的订单不能删除"
}

// 删除失败（订单不存在）
{
    "code": 1,
    "message": "订单不存在"
}
```

### 2. 我的订单列表接口

#### 请求示例
```bash
curl "http://localhost:8000/m/api/zb_order/my-list?openid=oXXXXXXXXXXXXXXXXXXXXXXXXXXXX&page=1&page_size=10"
```

#### 响应示例
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "order_sn": "ZB20250123150405XXXX",
                "good_id": 1,
                "good_name": "VIP套餐",
                "good_price": 100.00,
                "city_count": 3,
                "user_id": 123,
                "user_nickname": "张三",
                "price": 300.00,
                "amount": 300.00,
                "pay_status": 1,
                "transaction_id": "wx_transaction_123",
                "trade_type": "JSAPI",
                "trade_state": "SUCCESS",
                "remark": "选择城市：北京、上海、深圳",
                "pay_at": "2025-01-23T15:30:00Z",
                "created_at": "2025-01-23T15:04:05Z",
                "updated_at": "2025-01-23T15:30:00Z",
                "cities": [
                    {"city_id": 1, "city_name": "北京"},
                    {"city_id": 2, "city_name": "上海"},
                    {"city_id": 3, "city_name": "深圳"}
                ]
            }
        ],
        "total": 1
    }
}
```

## 🛡️ 安全控制

### 1. 删除接口安全
- **权限验证**: 需要后台管理员权限
- **状态检查**: 只能删除未支付订单
- **事务保证**: 确保数据一致性
- **操作日志**: 记录删除操作日志

### 2. 我的订单接口安全
- **用户验证**: 通过OpenID验证用户身份
- **数据隔离**: 只能查看自己的订单
- **参数验证**: 严格的参数校验
- **分页限制**: 最大50条/页

## 🧪 测试用例

### 1. 删除订单测试

#### 测试场景1：删除未支付订单
```bash
# 1. 创建未支付订单
# 2. 调用删除接口
curl -X DELETE "http://localhost:8000/api/zb_order/delete" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{"id": 123}'
# 3. 验证订单已删除
```

#### 测试场景2：删除已支付订单
```bash
# 1. 创建已支付订单
# 2. 调用删除接口
# 3. 验证返回错误："已支付的订单不能删除"
```

#### 测试场景3：删除不存在的订单
```bash
# 1. 调用删除接口（不存在的ID）
# 2. 验证返回错误："订单不存在"
```

### 2. 我的订单列表测试

#### 测试场景1：正常查询
```bash
curl "http://localhost:8000/m/api/zb_order/my-list?openid=valid_openid&page=1&page_size=10"
# 验证返回用户的订单列表
```

#### 测试场景2：无效OpenID
```bash
curl "http://localhost:8000/m/api/zb_order/my-list?openid=invalid_openid&page=1&page_size=10"
# 验证返回错误："用户不存在或获取用户信息失败"
```

#### 测试场景3：分页测试
```bash
# 测试不同页码和页大小
curl "http://localhost:8000/m/api/zb_order/my-list?openid=valid_openid&page=2&page_size=5"
```

## 📁 文件结构

```
admin-server/
├── api/zb_order/
│   ├── zb_order.go                           # 新增Delete和GetMyList接口
│   └── v1/zb_order.go                        # 新增请求响应结构
├── internal/
│   ├── controller/zb_order/
│   │   ├── zb_order_v1_delete.go             # 删除订单控制器
│   │   └── zb_order_v1_get_my_list.go        # 我的订单列表控制器
│   ├── logic/zb_order/
│   │   └── zb_order.go                       # 新增Delete和GetMyList方法
│   ├── service/
│   │   └── zb_order.go                       # 新增接口定义
│   └── cmd/
│       └── cmd.go                            # 新增路由配置
└── docs/
    └── 订单新增删除和我的订单接口说明.md      # 本文档
```

## ⚠️ 注意事项

### 1. 删除接口注意事项
- **不可逆操作**: 删除后无法恢复，建议添加二次确认
- **关联数据**: 确保同时删除订单城市关联数据
- **业务规则**: 严格控制只能删除未支付订单
- **权限控制**: 只有管理员可以删除订单

### 2. 我的订单接口注意事项
- **性能考虑**: 大量订单时考虑添加索引优化
- **数据安全**: 确保用户只能查看自己的订单
- **OpenID验证**: 确保OpenID的有效性和安全性
- **分页限制**: 合理设置分页大小避免性能问题

## 📈 后续优化

### 1. 删除接口优化
- 添加软删除功能（标记删除而非物理删除）
- 添加删除原因记录
- 支持批量删除功能
- 添加删除权限细分

### 2. 我的订单接口优化
- 添加订单状态筛选
- 支持时间范围查询
- 添加订单搜索功能
- 优化查询性能

---

**开发状态**: ✅ 已完成  
**接口数量**: 2个新接口  
**安全级别**: 高  
**测试状态**: 待测试  
**文档版本**: v1.0  
**完成时间**: 2025-01-23
