// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysResources is the golang structure of table sys_resources for DAO operations like Where/Data.
type SysResources struct {
	g.Meta      `orm:"table:sys_resources, do:true"`
	Id          interface{} //
	GroupId     interface{} // 系统资源分组id
	StorageMode interface{} // 存储模式 (1 本地 2 阿里云 3 七牛云 4 腾讯云)
	OriginName  interface{} // 源文件名
	ObjectName  interface{} // 新文件名
	Hash        interface{} // 文件hash;用来去重
	MimeType    interface{} // 资源类型
	StoragePath interface{} // 存储目录
	Suffix      interface{} // 文件后缀
	SizeByte    interface{} // 字节数
	SizeInfo    interface{} // 文件大小
	Url         interface{} // url地址
	Remark      interface{} // 备注
	IsDelete    interface{} // 是否删除: 0=否, 1=是
	CreatedAt   *gtime.Time // 创建时间
	UpdatedAt   *gtime.Time // 更新时间
	DeletedAt   *gtime.Time // 删除时间
}
