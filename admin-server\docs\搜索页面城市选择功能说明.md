# 搜索页面城市选择功能说明

## 功能概述

为search.html搜索页面添加了城市下拉选择框功能，用户可以在搜索时选择特定城市，实现更精准的地域化搜索。

## 实现的功能

### 1. 城市下拉选择框
- 动态加载城市列表
- 支持城市筛选搜索
- 可选择特定城市或全部城市

### 2. 搜索功能增强
- 支持关键字+城市的组合搜索
- 城市选择变化时自动触发搜索
- 搜索历史记录功能

### 3. 用户体验优化
- 美观的下拉选择框样式
- 清晰的搜索条件显示
- 智能的搜索提示

## 代码实现

### 1. HTML结构
```html
<!-- 城市选择区域 -->
<div class="mb-3">
    <div class="relative">
        <select id="citySelect" class="w-full bg-white/90 backdrop-blur-sm rounded-full px-4 py-3 pl-10 pr-8 text-sm border-0 focus:outline-none focus:ring-2 focus:ring-white/50 appearance-none">
            <option value="">选择城市（可选）</option>
            <!-- 城市选项将通过JavaScript动态加载 -->
        </select>
        <i class="fas fa-map-marker-alt absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm"></i>
        <i class="fas fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-xs"></i>
    </div>
</div>
```

### 2. CSS样式优化
```css
/* 城市选择框样式 */
#citySelect {
    background-image: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

#citySelect:focus {
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
}

/* 自定义下拉箭头 */
.select-wrapper::after {
    content: '';
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #9ca3af;
    pointer-events: none;
}
```

### 3. JavaScript功能实现
```javascript
// 全局变量
let allCities = []; // 存储所有城市数据
let selectedCity = null; // 存储选中的城市

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    loadCities();
    initSearchEvents();
});

// 加载城市列表
async function loadCities() {
    try {
        const response = await fetch('/m/api/zb_city/tree');
        const result = await response.json();
        
        if (result.code === 0 && result.data && result.data.list) {
            allCities = result.data.list;
            renderCityOptions(allCities);
        } else {
            throw new Error(result.message || '城市数据加载失败');
        }
    } catch (error) {
        console.error('加载城市列表失败:', error);
        renderDefaultCities();
    }
}

// 渲染城市选项
function renderCityOptions(cities) {
    const citySelect = document.getElementById('citySelect');
    citySelect.innerHTML = '<option value="">选择城市（可选）</option>';
    
    cities.forEach(city => {
        const option = document.createElement('option');
        option.value = city.id;
        option.textContent = city.name;
        citySelect.appendChild(option);
    });
}
```

## 功能特性

### 1. 动态城市加载
- 从 `/m/api/zb_city/tree` 接口获取城市列表
- 支持API加载失败时的默认城市列表
- 实时渲染城市选项

### 2. 搜索功能增强
```javascript
// 执行搜索
function performSearch() {
    const keyword = document.getElementById('searchInput').value.trim();
    const cityId = selectedCity ? selectedCity.id : null;
    const cityName = selectedCity ? selectedCity.name : null;
    
    console.log('执行搜索:', {
        keyword: keyword,
        cityId: cityId,
        cityName: cityName
    });
    
    // 构建搜索参数
    const searchParams = new URLSearchParams();
    if (keyword) {
        searchParams.append('keyword', keyword);
    }
    if (cityId) {
        searchParams.append('city_id', cityId);
    }
    
    // 执行搜索逻辑
    executeSearch(keyword, cityId, cityName);
}
```

### 3. 搜索历史管理
```javascript
// 添加到搜索历史
function addToSearchHistory(keyword) {
    const historyContainer = document.querySelector('.flex.flex-wrap.gap-2');
    if (historyContainer && keyword) {
        const newTag = document.createElement('span');
        newTag.className = 'search-tag bg-gray-100 text-gray-700 px-3 py-2 rounded-full text-xs cursor-pointer';
        newTag.textContent = keyword;
        
        // 插入到第一个位置
        historyContainer.insertBefore(newTag, historyContainer.firstChild);
        
        // 限制历史记录数量
        if (historyContainer.children.length > 8) {
            historyContainer.removeChild(historyContainer.lastChild);
        }
    }
}
```

## 用户交互流程

### 1. 页面加载流程
1. 页面加载完成
2. 自动请求城市列表API
3. 渲染城市下拉选项
4. 初始化搜索事件监听

### 2. 搜索流程
1. 用户输入搜索关键字（可选）
2. 用户选择城市（可选）
3. 点击搜索按钮或按回车键
4. 执行搜索并显示结果
5. 添加到搜索历史

### 3. 城市选择流程
1. 用户点击城市下拉框
2. 选择特定城市
3. 如果有搜索关键字，自动执行搜索
4. 更新搜索结果标题

## 搜索参数说明

### 1. URL参数格式
```
/m/search?keyword=政府采购&city_id=1
```

### 2. 参数说明
- `keyword`: 搜索关键字
- `city_id`: 城市ID（可选）

### 3. 搜索条件组合
- **仅关键字**: `keyword=政府采购`
- **仅城市**: `city_id=1`
- **关键字+城市**: `keyword=政府采购&city_id=1`

## API接口

### 1. 城市列表接口
```
GET /m/api/zb_city/tree
```

**返回格式**:
```json
{
  "code": 0,
  "message": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "北京",
        "pid": 0,
        "sort": 1,
        "is_disable": 0,
        "is_delete": 0
      }
    ]
  }
}
```

### 2. 搜索接口（待实现）
```
GET /m/api/search?keyword=xxx&city_id=1
```

## 错误处理

### 1. 城市加载失败
- 显示默认城市列表
- 控制台输出错误信息
- 不影响搜索功能

### 2. 搜索参数验证
```javascript
if (!keyword && !cityId) {
    alert('请输入搜索关键字或选择城市');
    return;
}
```

### 3. 网络异常处理
- 捕获API请求异常
- 提供备用数据源
- 用户友好的错误提示

## 提供的API函数

### 1. 搜索相关
```javascript
// 执行搜索
performSearch()

// 清空搜索
clearSearch()

// 获取当前搜索条件
getCurrentSearchConditions()

// 设置搜索条件
setSearchConditions(keyword, cityId)
```

### 2. 城市相关
```javascript
// 加载城市列表
loadCities()

// 渲染城市选项
renderCityOptions(cities)

// 渲染默认城市
renderDefaultCities()
```

### 3. 历史记录
```javascript
// 添加搜索历史
addToSearchHistory(keyword)
```

## 样式特点

### 1. 一致的设计风格
- 与搜索框相同的圆角和背景
- 统一的图标和颜色方案
- 响应式的交互效果

### 2. 移动端优化
- 适合触摸操作的尺寸
- 清晰的视觉层次
- 流畅的动画效果

### 3. 可访问性
- 清晰的标签和提示
- 键盘导航支持
- 屏幕阅读器友好

## 测试验证

### 1. 功能测试
1. 访问搜索页面
2. 检查城市下拉框是否正确加载
3. 测试城市选择功能
4. 验证搜索功能是否正常

### 2. 交互测试
1. 测试搜索框回车事件
2. 测试城市选择变化事件
3. 测试搜索历史点击事件
4. 测试清空搜索功能

### 3. 边界测试
1. 测试API加载失败情况
2. 测试空搜索条件
3. 测试网络异常情况

## 后续优化建议

### 1. 搜索功能增强
- 实现实际的搜索API调用
- 添加搜索结果分页
- 支持高级搜索选项

### 2. 用户体验优化
- 添加搜索建议功能
- 支持搜索结果高亮
- 优化加载状态显示

### 3. 数据缓存
- 缓存城市列表数据
- 缓存搜索历史
- 优化API请求频率

### 4. 个性化功能
- 记住用户常用城市
- 智能推荐搜索词
- 个性化搜索结果排序
