# 搜索统计接口错误修复说明

## 问题描述

`/m/api/search/track-search` 接口在处理新关键词时出现错误：
```
sql: no rows in result set
```

## 错误原因

在事务中使用 `Scan()` 方法查询不存在的记录时，GoFrame会返回 `sql: no rows in result set` 错误，而不是返回空结果。

## 错误代码

```go
// 问题代码
var existing entity.ZbSearchKeywords
err := dao.ZbSearchKeywords.Ctx(ctx).Where("keyword", keyword).Scan(&existing)
if err != nil {
    return err  // 这里会因为记录不存在而返回错误
}

if existing.Id > 0 {
    // 更新逻辑
} else {
    // 插入逻辑
}
```

## 修复方案

使用 `Count()` 方法替代 `Scan()` 方法来检查记录是否存在：

```go
// 修复后的代码
count, err := dao.ZbSearchKeywords.Ctx(ctx).Where("keyword", keyword).Count()
if err != nil {
    return err
}

if count > 0 {
    // 更新现有记录
    _, err = dao.ZbSearchKeywords.Ctx(ctx).
        Where("keyword", keyword).
        Data(g.Map{
            "search_count": gdb.Raw("search_count + 1"),
            "updated_at":   gdb.Raw("NOW()"),
        }).
        Update()
} else {
    // 插入新记录
    _, err = dao.ZbSearchKeywords.Ctx(ctx).
        Data(g.Map{
            "keyword":      keyword,
            "search_count": 1,
            "click_count":  0,
            "trend":        "stable",
            "is_hot":       0,
            "is_new":       1,
        }).
        Insert()
}
```

## 修复优势

### 1. 避免错误
- `Count()` 方法不会因为记录不存在而返回错误
- 即使没有匹配的记录，也会返回 `count = 0`

### 2. 性能优化
- `Count()` 只返回数量，不需要加载完整的记录数据
- 减少内存使用和网络传输

### 3. 代码简洁
- 逻辑更清晰，直接通过数量判断是否存在
- 避免了复杂的错误处理

## 测试验证

### 1. 新关键词测试
```bash
curl -X POST "http://localhost:8000/m/api/search/track-search" \
     -H "Content-Type: application/json" \
     -d '{"keyword":"新测试关键词","city_id":1}'
```

预期结果：
- 成功插入新记录
- 返回 `{"success": true}`

### 2. 已存在关键词测试
```bash
curl -X POST "http://localhost:8000/m/api/search/track-search" \
     -H "Content-Type: application/json" \
     -d '{"keyword":"智慧城市建设","city_id":1}'
```

预期结果：
- 成功更新搜索次数
- 返回 `{"success": true}`

### 3. 数据库验证
```sql
-- 查看新插入的记录
SELECT * FROM zb_search_keywords WHERE keyword = '新测试关键词';

-- 查看更新的记录
SELECT keyword, search_count, updated_at 
FROM zb_search_keywords 
WHERE keyword = '智慧城市建设';
```

## 日志对比

### 修复前的错误日志
```
[DEBU] BEGIN (IosolationLevel: Default, ReadOnly: false)
[DEBU] SELECT `id`,`keyword`,`search_count`,... FROM `zb_search_keywords` WHERE `keyword`='中国银行' LIMIT 1
[DEBU] ROLLBACK
Stack: sql: no rows in result set
```

### 修复后的正常日志
```
[DEBU] BEGIN (IosolationLevel: Default, ReadOnly: false)
[DEBU] SELECT COUNT(1) FROM `zb_search_keywords` WHERE `keyword`='中国银行'
[DEBU] INSERT INTO `zb_search_keywords`(`keyword`,`search_count`,...) VALUES('中国银行',1,...)
[DEBU] COMMIT
```

## 相关最佳实践

### 1. 存在性检查
```go
// 推荐：使用Count()检查记录是否存在
count, err := dao.Table.Ctx(ctx).Where("field", value).Count()

// 不推荐：使用Scan()检查存在性
var entity Entity
err := dao.Table.Ctx(ctx).Where("field", value).Scan(&entity)
```

### 2. 错误处理
```go
// 正确的错误处理
if err != nil && err != sql.ErrNoRows {
    return err
}

// 或者使用Count()避免这个问题
count, err := dao.Table.Ctx(ctx).Where("field", value).Count()
if err != nil {
    return err
}
```

### 3. 事务中的查询
- 在事务中进行存在性检查时，优先使用 `Count()` 或 `Value()` 方法
- 避免使用 `Scan()` 方法查询可能不存在的记录
- 如果必须使用 `Scan()`，要正确处理 `sql.ErrNoRows` 错误

## 其他改进建议

### 1. 添加唯一索引
确保数据库表有唯一索引：
```sql
ALTER TABLE zb_search_keywords ADD UNIQUE KEY uk_keyword (keyword);
```

### 2. 使用UPSERT语法
如果数据库支持，可以使用UPSERT语法：
```go
_, err = dao.ZbSearchKeywords.Ctx(ctx).
    Data(g.Map{
        "keyword":      keyword,
        "search_count": gdb.Raw("search_count + 1"),
    }).
    OnDuplicate("search_count", gdb.Raw("search_count + 1")).
    Insert()
```

### 3. 批量处理
对于高频操作，可以考虑批量处理：
```go
// 先缓存到内存，定期批量更新数据库
```

---

**修复状态**: ✅ 已完成  
**问题类型**: 事务中查询不存在记录的错误处理  
**解决方案**: 使用Count()方法替代Scan()方法  
**文档版本**: v1.0  
**修复时间**: 2025-01-23
