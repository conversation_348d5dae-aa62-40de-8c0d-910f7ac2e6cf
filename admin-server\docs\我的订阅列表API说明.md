# 我的订阅列表API说明

## 📋 接口概述

新增手机端"我的订阅列表"API接口，用于获取用户已支付的订单列表，包含完整的订单信息和城市列表。

## 🔧 接口详情

### 基本信息
- **接口路径**: `GET /m/api/zb_order/my-subscriptions`
- **接口标签**: Order
- **接口描述**: 获取我的订阅列表
- **认证方式**: 通过OpenID验证用户身份

### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 验证规则 | 说明 |
|--------|------|------|--------|----------|------|
| `openid` | string | 是 | - | 长度1-100 | 用户OpenID |
| `page` | int | 否 | 1 | 最小值1 | 页码 |
| `page_size` | int | 否 | 10 | 1-50之间 | 每页数量 |

### 响应格式
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "order_sn": "ZB202507251114227737",
                "good_id": 2,
                "good_name": "特惠会员399元",
                "city_count": 2,
                "user_id": 1,
                "good_price": 298.00,
                "effective": 2,
                "price": 596.00,
                "amount": 596.00,
                "pay_status": 1,
                "remark": "选择城市：内蒙古、北京",
                "pay_at": "2025-07-25T15:37:35Z",
                "created_at": "2025-07-25T11:14:22Z",
                "updated_at": "2025-07-25T15:37:35Z",
                "cities": [
                    {"city_id": 29, "city_name": "内蒙古"},
                    {"city_id": 1, "city_name": "北京"}
                ]
            }
        ],
        "total": 1
    }
}
```

### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | int64 | 订单ID |
| `order_sn` | string | 订单编号 |
| `good_id` | int64 | 套餐ID |
| `good_name` | string | 套餐名称 |
| `city_count` | int | 选择的城市数量 |
| `user_id` | int64 | 用户ID |
| `good_price` | float64 | 套餐单价 |
| `effective` | int | 会员有效期（单位：月） |
| `price` | float64 | 需支付金额 |
| `amount` | float64 | 实际支付金额 |
| `pay_status` | int | 支付状态（1=已支付，0=未支付） |
| `remark` | string | 订单备注 |
| `pay_at` | datetime | 支付时间 |
| `created_at` | datetime | 创建时间 |
| `updated_at` | datetime | 更新时间 |
| `cities` | array | 选择的城市列表 |
| `cities[].city_id` | int | 城市ID |
| `cities[].city_name` | string | 城市名称 |

## 🎯 业务逻辑

### 1. 用户验证
```go
// 根据OpenID获取用户信息
user, err := service.ZbUser().GetUserByOpenid(ctx, req.OpenId)
if err != nil {
    return nil, fmt.Errorf("用户不存在或获取用户信息失败")
}
```

### 2. 查询条件
- ✅ **用户过滤**: 只查询当前用户的订单
- ✅ **状态过滤**: 只查询已支付的订单 (`pay_status = 1`)
- ✅ **排序规则**: 按支付时间倒序，创建时间倒序

### 3. 数据关联
```sql
-- 主查询：获取订单列表
SELECT * FROM zb_order 
WHERE user_id = ? AND pay_status = 1 
ORDER BY pay_at DESC, created_at DESC 
LIMIT ? OFFSET ?;

-- 关联查询：获取城市信息
SELECT city_id, city_name FROM zb_order_city 
WHERE order_id = ?;
```

### 4. 响应构建
- ✅ **订单信息**: 包含完整的订单详情
- ✅ **城市列表**: 每个订单包含选择的城市信息
- ✅ **分页支持**: 支持分页查询
- ✅ **错误处理**: 城市查询失败不影响订单数据

## 📊 数据表关系

### 主要表结构
```
zb_order (订单表)
├── id (主键)
├── user_id (用户ID)
├── pay_status (支付状态)
├── effective (有效期)
└── ... (其他字段)

zb_order_city (订单城市关联表)
├── id (主键)
├── order_id (订单ID) → zb_order.id
├── city_id (城市ID)
└── city_name (城市名称)
```

### 关联关系
- `zb_order.id` ← `zb_order_city.order_id` (一对多)
- 一个订单可以关联多个城市
- 城市信息直接存储在关联表中

## 🧪 测试用例

### 测试用例1：正常查询
```bash
curl "http://localhost:8000/m/api/zb_order/my-subscriptions?openid=test_openid&page=1&page_size=10"
```

**期望响应**:
- 返回用户的已支付订单列表
- 包含完整的订单信息和城市列表
- 按支付时间倒序排列

### 测试用例2：分页查询
```bash
curl "http://localhost:8000/m/api/zb_order/my-subscriptions?openid=test_openid&page=2&page_size=5"
```

**期望响应**:
- 返回第2页数据，每页5条
- 总数保持不变
- 数据连续性正确

### 测试用例3：用户不存在
```bash
curl "http://localhost:8000/m/api/zb_order/my-subscriptions?openid=invalid_openid&page=1&page_size=10"
```

**期望响应**:
- 返回错误信息
- 提示用户不存在

### 测试用例4：无订阅记录
```bash
curl "http://localhost:8000/m/api/zb_order/my-subscriptions?openid=new_user_openid&page=1&page_size=10"
```

**期望响应**:
- 返回空列表
- total为0

## ⚠️ 注意事项

### 1. 业务规则
- ✅ **只返回已支付订单**: `pay_status = 1`
- ✅ **用户数据隔离**: 只能查看自己的订单
- ✅ **时间排序**: 最新支付的订单在前
- ✅ **完整数据**: 包含所有必要的订单信息

### 2. 性能优化
- ✅ **分页查询**: 避免一次性加载大量数据
- ✅ **索引优化**: 在 `user_id` 和 `pay_status` 上建立索引
- ✅ **关联优化**: 批量查询城市信息减少数据库访问

### 3. 错误处理
- ✅ **用户验证**: OpenID无效时返回友好错误
- ✅ **数据容错**: 城市查询失败不影响订单数据
- ✅ **参数验证**: 严格验证请求参数

### 4. 前端计算字段
根据需求说明，以下字段由前端计算：
- `expire_date`: 到期日期 = `pay_at` + `effective`个月
- `days_remaining`: 剩余天数 = `expire_date` - 当前日期

## 📈 扩展功能

### 1. 状态筛选
可以扩展支持按状态筛选：
```go
// 支持查询不同状态的订单
if req.PayStatus != nil {
    model = model.Where("pay_status", *req.PayStatus)
}
```

### 2. 时间范围筛选
可以扩展支持时间范围查询：
```go
// 支持按支付时间筛选
if req.StartTime != nil {
    model = model.Where("pay_at >= ?", req.StartTime)
}
if req.EndTime != nil {
    model = model.Where("pay_at <= ?", req.EndTime)
}
```

### 3. 套餐筛选
可以扩展支持按套餐筛选：
```go
// 支持按套餐筛选
if req.GoodId != nil {
    model = model.Where("good_id", *req.GoodId)
}
```

---

**开发状态**: ✅ 已完成  
**接口路径**: `/m/api/zb_order/my-subscriptions`  
**支持功能**: 分页查询、用户验证、数据关联  
**数据安全**: 用户数据隔离  
**性能优化**: 分页查询、索引优化  
**文档版本**: v1.0  
**完成时间**: 2025-01-23
