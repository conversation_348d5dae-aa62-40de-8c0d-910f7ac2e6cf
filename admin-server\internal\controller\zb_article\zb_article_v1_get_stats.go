package zb_article

import (
	"context"

	v1 "admin-server/api/zb_article/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetStats(ctx context.Context, req *v1.GetStatsReq) (res *v1.GetStatsRes, err error) {
	totalArticles, publishedArticles, disabledArticles, todayArticles, err := service.ZbArticle().GetArticleStats(ctx, req.CateId, req.CityId)
	if err != nil {
		return nil, err
	}

	return &v1.GetStatsRes{
		TotalArticles:     totalArticles,
		PublishedArticles: publishedArticles,
		DisabledArticles:  disabledArticles,
		TodayArticles:     todayArticles,
	}, nil
}
