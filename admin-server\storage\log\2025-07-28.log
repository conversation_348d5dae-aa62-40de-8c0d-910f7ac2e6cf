2025-07-28T11:15:46 [INFO] 搜索趋势定时任务启动成功, CronID: {"Name":"cron-1","RegisterTime":"2025-07-28T11:15:46.8913591+08:00"}
2025-07-28T11:15:46 [INFO] 当前定时任务数量: 1
2025-07-28T11:15:46 [INFO] 定时任务 1 : {"Name":"cron-1","RegisterTime":"2025-07-28T11:15:46.8913591+08:00"}
2025-07-28T11:19:18 [INFO] {b84399ff144c5618248d4f6115e47bee} 记录登录成功日志: adminId: 1 username: admin ip: ::1
2025-07-28T11:19:18 [INFO] {7870a60a154c5618288d4f61c45aeed6} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:19:18 [INFO] {74c4bf0a154c5618298d4f619b3a1264} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:19:18 [INFO] {fc0bc80a154c56182a8d4f61468006da} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:19:18 [INFO] {7870a60a154c5618288d4f61c45aeed6} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:19:18 [INFO] {74c4bf0a154c5618298d4f619b3a1264} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:19:18 [INFO] {fc0bc80a154c56182a8d4f61468006da} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:19:18 [INFO] {7870a60a154c5618288d4f61c45aeed6} ValidateToken - Claims: 1 access admin
2025-07-28T11:19:18 [INFO] {74c4bf0a154c5618298d4f619b3a1264} ValidateToken - Claims: 1 access admin
2025-07-28T11:19:18 [INFO] {fc0bc80a154c56182a8d4f61468006da} ValidateToken - Claims: 1 access admin
2025-07-28T11:19:18 [INFO] {7870a60a154c5618288d4f61c45aeed6} Auth middleware - ValidateToken result: 1
2025-07-28T11:19:18 [INFO] {74c4bf0a154c5618298d4f619b3a1264} Auth middleware - ValidateToken result: 1
2025-07-28T11:19:18 [INFO] {fc0bc80a154c56182a8d4f61468006da} Auth middleware - ValidateToken result: 1
2025-07-28T11:19:18 [INFO] {7870a60a154c5618288d4f61c45aeed6} Auth middleware - set admin_id to context: 1
2025-07-28T11:19:18 [INFO] {74c4bf0a154c5618298d4f619b3a1264} Auth middleware - set admin_id to context: 1
2025-07-28T11:19:18 [INFO] {fc0bc80a154c56182a8d4f61468006da} Auth middleware - set admin_id to context: 1
2025-07-28T11:19:18 [WARN] {fc0bc80a154c56182a8d4f61468006da} 路由未配置权限标识: GET /auth/permissions
2025-07-28T11:19:18 [WARN] {7870a60a154c5618288d4f61c45aeed6} 路由未配置权限标识: GET /auth/userinfo
2025-07-28T11:19:18 [WARN] {74c4bf0a154c5618298d4f619b3a1264} 路由未配置权限标识: GET /auth/menus
2025-07-28T11:19:18 [INFO] {74c4bf0a154c5618298d4f619b3a1264} GetMenus - adminId from context: 1
2025-07-28T11:19:18 [INFO] {4434be26154c56182c8d4f61499f0100} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:19:18 [INFO] {4434be26154c56182c8d4f61499f0100} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:19:18 [INFO] {4434be26154c56182c8d4f61499f0100} ValidateToken - Claims: 1 access admin
2025-07-28T11:19:18 [INFO] {4434be26154c56182c8d4f61499f0100} Auth middleware - ValidateToken result: 1
2025-07-28T11:19:18 [INFO] {4434be26154c56182c8d4f61499f0100} Auth middleware - set admin_id to context: 1
2025-07-28T11:19:18 [INFO] {4434be26154c56182c8d4f61499f0100} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-28T11:19:18 [INFO] {4434be26154c56182c8d4f61499f0100} 超级管理员访问: system:zb_order:list
2025-07-28T11:19:18 [DEBU] {4434be26154c56182c8d4f61499f0100} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-28T11:19:24 [INFO] {54b24a7e164c5618308d4f61270fcaac} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:19:24 [INFO] {54b24a7e164c5618308d4f61270fcaac} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:19:24 [INFO] {b4f7597e164c5618318d4f617ecf7fd5} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:19:24 [INFO] {3c72667e164c5618328d4f6113f82a1a} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:19:24 [INFO] {54b24a7e164c5618308d4f61270fcaac} ValidateToken - Claims: 1 access admin
2025-07-28T11:19:24 [INFO] {b4f7597e164c5618318d4f617ecf7fd5} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:19:24 [INFO] {3c72667e164c5618328d4f6113f82a1a} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:19:24 [INFO] {54b24a7e164c5618308d4f61270fcaac} Auth middleware - ValidateToken result: 1
2025-07-28T11:19:24 [INFO] {b4f7597e164c5618318d4f617ecf7fd5} ValidateToken - Claims: 1 access admin
2025-07-28T11:19:24 [INFO] {3c72667e164c5618328d4f6113f82a1a} ValidateToken - Claims: 1 access admin
2025-07-28T11:19:24 [INFO] {54b24a7e164c5618308d4f61270fcaac} Auth middleware - set admin_id to context: 1
2025-07-28T11:19:24 [INFO] {b4f7597e164c5618318d4f617ecf7fd5} Auth middleware - ValidateToken result: 1
2025-07-28T11:19:24 [INFO] {b4f7597e164c5618318d4f617ecf7fd5} Auth middleware - set admin_id to context: 1
2025-07-28T11:19:24 [INFO] {3c72667e164c5618328d4f6113f82a1a} Auth middleware - ValidateToken result: 1
2025-07-28T11:19:24 [INFO] {3c72667e164c5618328d4f6113f82a1a} Auth middleware - set admin_id to context: 1
2025-07-28T11:19:24 [INFO] {54b24a7e164c5618308d4f61270fcaac} Required permission: system:member:list for GET /zb_user/list
2025-07-28T11:19:24 [INFO] {b4f7597e164c5618318d4f617ecf7fd5} Required permission: system:member:detail for GET /zb_user/stats
2025-07-28T11:19:24 [INFO] {3c72667e164c5618328d4f6113f82a1a} Required permission: system:member:vip for GET /zb_user/vip/expired
2025-07-28T11:19:24 [INFO] {54b24a7e164c5618308d4f61270fcaac} 超级管理员访问: system:member:list
2025-07-28T11:19:24 [DEBU] {54b24a7e164c5618308d4f61270fcaac} 权限验证通过: adminId: 1 permission: system:member:list path: /zb_user/list
2025-07-28T11:19:24 [INFO] {b4f7597e164c5618318d4f617ecf7fd5} 超级管理员访问: system:member:detail
2025-07-28T11:19:24 [INFO] {3c72667e164c5618328d4f6113f82a1a} 超级管理员访问: system:member:vip
2025-07-28T11:19:24 [DEBU] {b4f7597e164c5618318d4f617ecf7fd5} 权限验证通过: adminId: 1 permission: system:member:detail path: /zb_user/stats
2025-07-28T11:19:24 [DEBU] {3c72667e164c5618328d4f6113f82a1a} 权限验证通过: adminId: 1 permission: system:member:vip path: /zb_user/vip/expired
2025-07-28T11:19:24 [INFO] {54b24a7e164c5618308d4f61270fcaac} 在有效期内
2025-07-28T11:19:24 [INFO] {54b24a7e164c5618308d4f61270fcaac} 在有效期内
2025-07-28T11:19:27 [INFO] {540bf448174c5618348d4f61aebd9917} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:19:27 [INFO] {540bf448174c5618348d4f61aebd9917} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:19:27 [INFO] {540bf448174c5618348d4f61aebd9917} ValidateToken - Claims: 1 access admin
2025-07-28T11:19:27 [INFO] {540bf448174c5618348d4f61aebd9917} Auth middleware - ValidateToken result: 1
2025-07-28T11:19:27 [INFO] {540bf448174c5618348d4f61aebd9917} Auth middleware - set admin_id to context: 1
2025-07-28T11:19:27 [INFO] {540bf448174c5618348d4f61aebd9917} Required permission: system:member:list for GET /zb_user/list
2025-07-28T11:19:27 [INFO] {540bf448174c5618348d4f61aebd9917} 超级管理员访问: system:member:list
2025-07-28T11:19:27 [DEBU] {540bf448174c5618348d4f61aebd9917} 权限验证通过: adminId: 1 permission: system:member:list path: /zb_user/list
2025-07-28T11:19:27 [INFO] {540bf448174c5618348d4f61aebd9917} 在有效期内
2025-07-28T11:19:27 [INFO] {540bf448174c5618348d4f61aebd9917} 在有效期内
2025-07-28T11:19:37 [INFO] {8026b29b194c5618358d4f6149216031} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:19:37 [INFO] {8026b29b194c5618358d4f6149216031} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:19:37 [INFO] {8026b29b194c5618358d4f6149216031} ValidateToken - Claims: 1 access admin
2025-07-28T11:19:37 [INFO] {8026b29b194c5618358d4f6149216031} Auth middleware - ValidateToken result: 1
2025-07-28T11:19:37 [INFO] {8026b29b194c5618358d4f6149216031} Auth middleware - set admin_id to context: 1
2025-07-28T11:19:37 [INFO] {8026b29b194c5618358d4f6149216031} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-28T11:19:37 [INFO] {8026b29b194c5618358d4f6149216031} 超级管理员访问: system:zb_order:list
2025-07-28T11:19:37 [DEBU] {8026b29b194c5618358d4f6149216031} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-28T11:52:42 [INFO] 搜索趋势定时任务启动成功, CronID: {"Name":"cron-1","RegisterTime":"2025-07-28T11:52:42.8100991+08:00"}
2025-07-28T11:52:42 [INFO] 当前定时任务数量: 1
2025-07-28T11:52:42 [INFO] 定时任务 1 : {"Name":"cron-1","RegisterTime":"2025-07-28T11:52:42.8100991+08:00"}
2025-07-28T11:54:03 [INFO] {b45ff391fa4d56183fd9b51afd0dcc0e} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:54:03 [INFO] {b45ff391fa4d56183fd9b51afd0dcc0e} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:54:03 [INFO] {b45ff391fa4d56183fd9b51afd0dcc0e} ValidateToken - Claims: 1 access admin
2025-07-28T11:54:03 [INFO] {b45ff391fa4d56183fd9b51afd0dcc0e} Auth middleware - ValidateToken result: 1
2025-07-28T11:54:03 [INFO] {b45ff391fa4d56183fd9b51afd0dcc0e} Auth middleware - set admin_id to context: 1
2025-07-28T11:54:03 [INFO] {b45ff391fa4d56183fd9b51afd0dcc0e} Required permission: system:zb_order:pay for PUT /zb_order/pay-status
2025-07-28T11:54:03 [INFO] {b45ff391fa4d56183fd9b51afd0dcc0e} 超级管理员访问: system:zb_order:pay
2025-07-28T11:54:03 [DEBU] {b45ff391fa4d56183fd9b51afd0dcc0e} 权限验证通过: adminId: 1 permission: system:zb_order:pay path: /zb_order/pay-status
2025-07-28T11:54:03 [INFO] {b45ff391fa4d56183fd9b51afd0dcc0e} 用户VIP续期 {"new_effective_end":"2025-10-21 00:00:00","old_effective_end":"2025-08-21 00:00:00","user_id":1}
2025-07-28T11:54:03 [INFO] {b45ff391fa4d56183fd9b51afd0dcc0e} 后台代替支付成功: {"amount":9536,"effective":2,"order_id":4,"order_sn":"ZB202507261102111862","trade_type":"ADMIN","user_id":1}
2025-07-28T11:54:03 [INFO] {883ae195fa4d561842d9b51aec712944} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:54:03 [INFO] {883ae195fa4d561842d9b51aec712944} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:54:03 [INFO] {883ae195fa4d561842d9b51aec712944} ValidateToken - Claims: 1 access admin
2025-07-28T11:54:03 [INFO] {883ae195fa4d561842d9b51aec712944} Auth middleware - ValidateToken result: 1
2025-07-28T11:54:03 [INFO] {883ae195fa4d561842d9b51aec712944} Auth middleware - set admin_id to context: 1
2025-07-28T11:54:03 [INFO] {883ae195fa4d561842d9b51aec712944} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-28T11:54:03 [INFO] {883ae195fa4d561842d9b51aec712944} 超级管理员访问: system:zb_order:list
2025-07-28T11:54:03 [DEBU] {883ae195fa4d561842d9b51aec712944} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-28T11:54:46 [INFO] {e8c4c1ac044e561843d9b51ab51899c7} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:54:46 [INFO] {e8c4c1ac044e561843d9b51ab51899c7} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:54:46 [INFO] {e8c4c1ac044e561843d9b51ab51899c7} ValidateToken - Claims: 1 access admin
2025-07-28T11:54:46 [INFO] {e8c4c1ac044e561843d9b51ab51899c7} Auth middleware - ValidateToken result: 1
2025-07-28T11:54:46 [INFO] {e8c4c1ac044e561843d9b51ab51899c7} Auth middleware - set admin_id to context: 1
2025-07-28T11:54:46 [INFO] {e8c4c1ac044e561843d9b51ab51899c7} Required permission: system:zb_order:pay for PUT /zb_order/pay-status
2025-07-28T11:54:46 [INFO] {e8c4c1ac044e561843d9b51ab51899c7} 超级管理员访问: system:zb_order:pay
2025-07-28T11:54:46 [DEBU] {e8c4c1ac044e561843d9b51ab51899c7} 权限验证通过: adminId: 1 permission: system:zb_order:pay path: /zb_order/pay-status
2025-07-28T11:54:46 [INFO] {e8c4c1ac044e561843d9b51ab51899c7} 用户VIP续期 {"new_effective_end":"2025-12-21 00:00:00","old_effective_end":"2025-10-21 00:00:00","user_id":1}
2025-07-28T11:54:46 [INFO] {e8c4c1ac044e561843d9b51ab51899c7} 后台代替支付成功: {"amount":298,"effective":2,"order_id":5,"order_sn":"ZB202507261123415771","trade_type":"ADMIN","user_id":1}
2025-07-28T11:54:47 [INFO] {28f07cb3044e561845d9b51a69eba0ca} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:54:47 [INFO] {28f07cb3044e561845d9b51a69eba0ca} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:54:47 [INFO] {28f07cb3044e561845d9b51a69eba0ca} ValidateToken - Claims: 1 access admin
2025-07-28T11:54:47 [INFO] {28f07cb3044e561845d9b51a69eba0ca} Auth middleware - ValidateToken result: 1
2025-07-28T11:54:47 [INFO] {28f07cb3044e561845d9b51a69eba0ca} Auth middleware - set admin_id to context: 1
2025-07-28T11:54:47 [INFO] {28f07cb3044e561845d9b51a69eba0ca} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-28T11:54:47 [INFO] {28f07cb3044e561845d9b51a69eba0ca} 超级管理员访问: system:zb_order:list
2025-07-28T11:54:47 [DEBU] {28f07cb3044e561845d9b51a69eba0ca} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-28T11:55:26 [INFO] {608f40f60d4e561846d9b51a9d2d61d2} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:55:26 [INFO] {608f40f60d4e561846d9b51a9d2d61d2} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:55:26 [INFO] {608f40f60d4e561846d9b51a9d2d61d2} ValidateToken - Claims: 1 access admin
2025-07-28T11:55:26 [INFO] {608f40f60d4e561846d9b51a9d2d61d2} Auth middleware - ValidateToken result: 1
2025-07-28T11:55:26 [INFO] {608f40f60d4e561846d9b51a9d2d61d2} Auth middleware - set admin_id to context: 1
2025-07-28T11:55:26 [INFO] {5ccdb0f60d4e561847d9b51a0ac9a4fd} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:55:26 [INFO] {5ccdb0f60d4e561847d9b51a0ac9a4fd} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:55:26 [INFO] {5ccdb0f60d4e561847d9b51a0ac9a4fd} ValidateToken - Claims: 1 access admin
2025-07-28T11:55:26 [INFO] {608f40f60d4e561846d9b51a9d2d61d2} Required permission: system:member:list for GET /zb_user/list
2025-07-28T11:55:26 [INFO] {1c89cbf60d4e561848d9b51ab6ffe1fd} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:55:26 [INFO] {1c89cbf60d4e561848d9b51ab6ffe1fd} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:55:26 [INFO] {1c89cbf60d4e561848d9b51ab6ffe1fd} ValidateToken - Claims: 1 access admin
2025-07-28T11:55:26 [INFO] {1c89cbf60d4e561848d9b51ab6ffe1fd} Auth middleware - ValidateToken result: 1
2025-07-28T11:55:26 [INFO] {5ccdb0f60d4e561847d9b51a0ac9a4fd} Auth middleware - ValidateToken result: 1
2025-07-28T11:55:26 [INFO] {5ccdb0f60d4e561847d9b51a0ac9a4fd} Auth middleware - set admin_id to context: 1
2025-07-28T11:55:26 [INFO] {1c89cbf60d4e561848d9b51ab6ffe1fd} Auth middleware - set admin_id to context: 1
2025-07-28T11:55:26 [INFO] {1c89cbf60d4e561848d9b51ab6ffe1fd} Required permission: system:member:vip for GET /zb_user/vip/expired
2025-07-28T11:55:26 [INFO] {5ccdb0f60d4e561847d9b51a0ac9a4fd} Required permission: system:member:detail for GET /zb_user/stats
2025-07-28T11:55:26 [INFO] {608f40f60d4e561846d9b51a9d2d61d2} 超级管理员访问: system:member:list
2025-07-28T11:55:26 [DEBU] {608f40f60d4e561846d9b51a9d2d61d2} 权限验证通过: adminId: 1 permission: system:member:list path: /zb_user/list
2025-07-28T11:55:26 [INFO] {5ccdb0f60d4e561847d9b51a0ac9a4fd} 超级管理员访问: system:member:detail
2025-07-28T11:55:26 [DEBU] {5ccdb0f60d4e561847d9b51a0ac9a4fd} 权限验证通过: adminId: 1 permission: system:member:detail path: /zb_user/stats
2025-07-28T11:55:26 [INFO] {1c89cbf60d4e561848d9b51ab6ffe1fd} 超级管理员访问: system:member:vip
2025-07-28T11:55:26 [DEBU] {1c89cbf60d4e561848d9b51ab6ffe1fd} 权限验证通过: adminId: 1 permission: system:member:vip path: /zb_user/vip/expired
2025-07-28T11:55:26 [INFO] {608f40f60d4e561846d9b51a9d2d61d2} 在有效期内
2025-07-28T11:55:26 [INFO] {608f40f60d4e561846d9b51a9d2d61d2} 在有效期内
2025-07-28T11:55:29 [INFO] {1cde0ca60e4e561849d9b51a43480aa3} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:55:29 [INFO] {1cde0ca60e4e561849d9b51a43480aa3} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:55:29 [INFO] {1cde0ca60e4e561849d9b51a43480aa3} ValidateToken - Claims: 1 access admin
2025-07-28T11:55:29 [INFO] {1cde0ca60e4e561849d9b51a43480aa3} Auth middleware - ValidateToken result: 1
2025-07-28T11:55:29 [INFO] {1cde0ca60e4e561849d9b51a43480aa3} Auth middleware - set admin_id to context: 1
2025-07-28T11:55:29 [INFO] {1cde0ca60e4e561849d9b51a43480aa3} Required permission: system:member:list for GET /zb_user/list
2025-07-28T11:55:29 [INFO] {1cde0ca60e4e561849d9b51a43480aa3} 超级管理员访问: system:member:list
2025-07-28T11:55:29 [DEBU] {1cde0ca60e4e561849d9b51a43480aa3} 权限验证通过: adminId: 1 permission: system:member:list path: /zb_user/list
2025-07-28T11:55:29 [INFO] {1cde0ca60e4e561849d9b51a43480aa3} 在有效期内
2025-07-28T11:55:29 [INFO] {1cde0ca60e4e561849d9b51a43480aa3} 在有效期内
2025-07-28T11:56:45 [INFO] {30de4e66204e56184cd9b51a5c8f2e57} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:56:45 [INFO] {5cb05a66204e56184dd9b51ade903a89} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:56:45 [INFO] {30de4e66204e56184cd9b51a5c8f2e57} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:56:45 [INFO] {5cb05a66204e56184dd9b51ade903a89} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:56:45 [INFO] {30de4e66204e56184cd9b51a5c8f2e57} ValidateToken - Claims: 1 access admin
2025-07-28T11:56:45 [INFO] {5cb05a66204e56184dd9b51ade903a89} ValidateToken - Claims: 1 access admin
2025-07-28T11:56:45 [INFO] {30de4e66204e56184cd9b51a5c8f2e57} Auth middleware - ValidateToken result: 1
2025-07-28T11:56:45 [INFO] {5cb05a66204e56184dd9b51ade903a89} Auth middleware - ValidateToken result: 1
2025-07-28T11:56:45 [INFO] {30de4e66204e56184cd9b51a5c8f2e57} Auth middleware - set admin_id to context: 1
2025-07-28T11:56:45 [INFO] {5cb05a66204e56184dd9b51ade903a89} Auth middleware - set admin_id to context: 1
2025-07-28T11:56:45 [WARN] {30de4e66204e56184cd9b51a5c8f2e57} 路由未配置权限标识: GET /sys_config/key/web_name
2025-07-28T11:56:45 [INFO] {5cb05a66204e56184dd9b51ade903a89} Required permission: system:officialAccount:menu for GET /wechat/menu/tree
2025-07-28T11:56:45 [INFO] {5cb05a66204e56184dd9b51ade903a89} 超级管理员访问: system:officialAccount:menu
2025-07-28T11:56:45 [DEBU] {5cb05a66204e56184dd9b51ade903a89} 权限验证通过: adminId: 1 permission: system:officialAccount:menu path: /wechat/menu/tree
2025-07-28T11:57:01 [INFO] {94cda7ed234e56184fd9b51a0ebe7680} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:57:01 [INFO] {94cda7ed234e56184fd9b51a0ebe7680} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:57:01 [INFO] {94cda7ed234e56184fd9b51a0ebe7680} ValidateToken - Claims: 1 access admin
2025-07-28T11:57:01 [INFO] {94cda7ed234e56184fd9b51a0ebe7680} Auth middleware - ValidateToken result: 1
2025-07-28T11:57:01 [INFO] {94cda7ed234e56184fd9b51a0ebe7680} Auth middleware - set admin_id to context: 1
2025-07-28T11:57:01 [INFO] {94cda7ed234e56184fd9b51a0ebe7680} Required permission: system:officialAccount:menuEdit for PUT /wechat/menu/4
2025-07-28T11:57:01 [INFO] {94cda7ed234e56184fd9b51a0ebe7680} 超级管理员访问: system:officialAccount:menuEdit
2025-07-28T11:57:01 [DEBU] {94cda7ed234e56184fd9b51a0ebe7680} 权限验证通过: adminId: 1 permission: system:officialAccount:menuEdit path: /wechat/menu/4
2025-07-28T11:57:01 [INFO] {94cda7ed234e56184fd9b51a0ebe7680} 更新微信菜单成功: id: 4
2025-07-28T11:57:01 [INFO] {c8fcc7f1234e561851d9b51aa9ee7a54} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:57:01 [INFO] {c8fcc7f1234e561851d9b51aa9ee7a54} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:57:01 [INFO] {c8fcc7f1234e561851d9b51aa9ee7a54} ValidateToken - Claims: 1 access admin
2025-07-28T11:57:01 [INFO] {c8fcc7f1234e561851d9b51aa9ee7a54} Auth middleware - ValidateToken result: 1
2025-07-28T11:57:01 [INFO] {c8fcc7f1234e561851d9b51aa9ee7a54} Auth middleware - set admin_id to context: 1
2025-07-28T11:57:01 [INFO] {c8fcc7f1234e561851d9b51aa9ee7a54} Required permission: system:officialAccount:menu for GET /wechat/menu/tree
2025-07-28T11:57:01 [INFO] {c8fcc7f1234e561851d9b51aa9ee7a54} 超级管理员访问: system:officialAccount:menu
2025-07-28T11:57:01 [DEBU] {c8fcc7f1234e561851d9b51aa9ee7a54} 权限验证通过: adminId: 1 permission: system:officialAccount:menu path: /wechat/menu/tree
2025-07-28T11:57:05 [INFO] {4c85a5f2244e561853d9b51a0140b960} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:57:05 [INFO] {4c85a5f2244e561853d9b51a0140b960} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:57:05 [INFO] {4c85a5f2244e561853d9b51a0140b960} ValidateToken - Claims: 1 access admin
2025-07-28T11:57:05 [INFO] {4c85a5f2244e561853d9b51a0140b960} Auth middleware - ValidateToken result: 1
2025-07-28T11:57:05 [INFO] {4c85a5f2244e561853d9b51a0140b960} Auth middleware - set admin_id to context: 1
2025-07-28T11:57:05 [INFO] {4c85a5f2244e561853d9b51a0140b960} Required permission: system:officialAccount:menuEdit for PUT /wechat/menu/5
2025-07-28T11:57:05 [INFO] {4c85a5f2244e561853d9b51a0140b960} 超级管理员访问: system:officialAccount:menuEdit
2025-07-28T11:57:05 [DEBU] {4c85a5f2244e561853d9b51a0140b960} 权限验证通过: adminId: 1 permission: system:officialAccount:menuEdit path: /wechat/menu/5
2025-07-28T11:57:05 [INFO] {4c85a5f2244e561853d9b51a0140b960} 更新微信菜单成功: id: 5
2025-07-28T11:57:05 [INFO] {d4d516f4244e561855d9b51a7179922c} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:57:05 [INFO] {d4d516f4244e561855d9b51a7179922c} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:57:05 [INFO] {d4d516f4244e561855d9b51a7179922c} ValidateToken - Claims: 1 access admin
2025-07-28T11:57:05 [INFO] {d4d516f4244e561855d9b51a7179922c} Auth middleware - ValidateToken result: 1
2025-07-28T11:57:05 [INFO] {d4d516f4244e561855d9b51a7179922c} Auth middleware - set admin_id to context: 1
2025-07-28T11:57:05 [INFO] {d4d516f4244e561855d9b51a7179922c} Required permission: system:officialAccount:menu for GET /wechat/menu/tree
2025-07-28T11:57:05 [INFO] {d4d516f4244e561855d9b51a7179922c} 超级管理员访问: system:officialAccount:menu
2025-07-28T11:57:05 [DEBU] {d4d516f4244e561855d9b51a7179922c} 权限验证通过: adminId: 1 permission: system:officialAccount:menu path: /wechat/menu/tree
2025-07-28T11:57:10 [INFO] {e8783329264e561857d9b51aa5e73a02} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:57:10 [INFO] {e8783329264e561857d9b51aa5e73a02} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:57:10 [INFO] {e8783329264e561857d9b51aa5e73a02} ValidateToken - Claims: 1 access admin
2025-07-28T11:57:10 [INFO] {e8783329264e561857d9b51aa5e73a02} Auth middleware - ValidateToken result: 1
2025-07-28T11:57:10 [INFO] {e8783329264e561857d9b51aa5e73a02} Auth middleware - set admin_id to context: 1
2025-07-28T11:57:10 [INFO] {e8783329264e561857d9b51aa5e73a02} Required permission: system:officialAccount:menuEdit for PUT /wechat/menu/6
2025-07-28T11:57:10 [INFO] {e8783329264e561857d9b51aa5e73a02} 超级管理员访问: system:officialAccount:menuEdit
2025-07-28T11:57:10 [DEBU] {e8783329264e561857d9b51aa5e73a02} 权限验证通过: adminId: 1 permission: system:officialAccount:menuEdit path: /wechat/menu/6
2025-07-28T11:57:10 [INFO] {e8783329264e561857d9b51aa5e73a02} 更新微信菜单成功: id: 6
2025-07-28T11:57:10 [INFO] {04a33b2b264e561859d9b51abbf447cd} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:57:10 [INFO] {04a33b2b264e561859d9b51abbf447cd} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:57:10 [INFO] {04a33b2b264e561859d9b51abbf447cd} ValidateToken - Claims: 1 access admin
2025-07-28T11:57:10 [INFO] {04a33b2b264e561859d9b51abbf447cd} Auth middleware - ValidateToken result: 1
2025-07-28T11:57:10 [INFO] {04a33b2b264e561859d9b51abbf447cd} Auth middleware - set admin_id to context: 1
2025-07-28T11:57:10 [INFO] {04a33b2b264e561859d9b51abbf447cd} Required permission: system:officialAccount:menu for GET /wechat/menu/tree
2025-07-28T11:57:10 [INFO] {04a33b2b264e561859d9b51abbf447cd} 超级管理员访问: system:officialAccount:menu
2025-07-28T11:57:10 [DEBU] {04a33b2b264e561859d9b51abbf447cd} 权限验证通过: adminId: 1 permission: system:officialAccount:menu path: /wechat/menu/tree
2025-07-28T11:57:15 [INFO] {d03b6f5a274e56185bd9b51a52b582a2} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:57:15 [INFO] {d03b6f5a274e56185bd9b51a52b582a2} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:57:15 [INFO] {d03b6f5a274e56185bd9b51a52b582a2} ValidateToken - Claims: 1 access admin
2025-07-28T11:57:15 [INFO] {d03b6f5a274e56185bd9b51a52b582a2} Auth middleware - ValidateToken result: 1
2025-07-28T11:57:15 [INFO] {d03b6f5a274e56185bd9b51a52b582a2} Auth middleware - set admin_id to context: 1
2025-07-28T11:57:15 [INFO] {d03b6f5a274e56185bd9b51a52b582a2} Required permission: system:officialAccount:menuEdit for PUT /wechat/menu/7
2025-07-28T11:57:15 [INFO] {d03b6f5a274e56185bd9b51a52b582a2} 超级管理员访问: system:officialAccount:menuEdit
2025-07-28T11:57:15 [DEBU] {d03b6f5a274e56185bd9b51a52b582a2} 权限验证通过: adminId: 1 permission: system:officialAccount:menuEdit path: /wechat/menu/7
2025-07-28T11:57:15 [INFO] {d03b6f5a274e56185bd9b51a52b582a2} 更新微信菜单成功: id: 7
2025-07-28T11:57:15 [INFO] {b8b7045c274e56185dd9b51a49ea5b4d} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:57:15 [INFO] {b8b7045c274e56185dd9b51a49ea5b4d} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:57:15 [INFO] {b8b7045c274e56185dd9b51a49ea5b4d} ValidateToken - Claims: 1 access admin
2025-07-28T11:57:15 [INFO] {b8b7045c274e56185dd9b51a49ea5b4d} Auth middleware - ValidateToken result: 1
2025-07-28T11:57:15 [INFO] {b8b7045c274e56185dd9b51a49ea5b4d} Auth middleware - set admin_id to context: 1
2025-07-28T11:57:15 [INFO] {b8b7045c274e56185dd9b51a49ea5b4d} Required permission: system:officialAccount:menu for GET /wechat/menu/tree
2025-07-28T11:57:15 [INFO] {b8b7045c274e56185dd9b51a49ea5b4d} 超级管理员访问: system:officialAccount:menu
2025-07-28T11:57:15 [DEBU] {b8b7045c274e56185dd9b51a49ea5b4d} 权限验证通过: adminId: 1 permission: system:officialAccount:menu path: /wechat/menu/tree
2025-07-28T11:57:20 [INFO] {30a99d76284e56185fd9b51a7e98d123} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:57:20 [INFO] {30a99d76284e56185fd9b51a7e98d123} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:57:20 [INFO] {30a99d76284e56185fd9b51a7e98d123} ValidateToken - Claims: 1 access admin
2025-07-28T11:57:20 [INFO] {30a99d76284e56185fd9b51a7e98d123} Auth middleware - ValidateToken result: 1
2025-07-28T11:57:20 [INFO] {30a99d76284e56185fd9b51a7e98d123} Auth middleware - set admin_id to context: 1
2025-07-28T11:57:20 [INFO] {30a99d76284e56185fd9b51a7e98d123} Required permission: system:officialAccount:menuEdit for PUT /wechat/menu/2
2025-07-28T11:57:20 [INFO] {30a99d76284e56185fd9b51a7e98d123} 超级管理员访问: system:officialAccount:menuEdit
2025-07-28T11:57:20 [DEBU] {30a99d76284e56185fd9b51a7e98d123} 权限验证通过: adminId: 1 permission: system:officialAccount:menuEdit path: /wechat/menu/2
2025-07-28T11:57:20 [INFO] {30a99d76284e56185fd9b51a7e98d123} 更新微信菜单成功: id: 2
2025-07-28T11:57:20 [INFO] {989cb378284e561861d9b51a1d63a961} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:57:20 [INFO] {989cb378284e561861d9b51a1d63a961} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:57:20 [INFO] {989cb378284e561861d9b51a1d63a961} ValidateToken - Claims: 1 access admin
2025-07-28T11:57:20 [INFO] {989cb378284e561861d9b51a1d63a961} Auth middleware - ValidateToken result: 1
2025-07-28T11:57:20 [INFO] {989cb378284e561861d9b51a1d63a961} Auth middleware - set admin_id to context: 1
2025-07-28T11:57:20 [INFO] {989cb378284e561861d9b51a1d63a961} Required permission: system:officialAccount:menu for GET /wechat/menu/tree
2025-07-28T11:57:20 [INFO] {989cb378284e561861d9b51a1d63a961} 超级管理员访问: system:officialAccount:menu
2025-07-28T11:57:20 [DEBU] {989cb378284e561861d9b51a1d63a961} 权限验证通过: adminId: 1 permission: system:officialAccount:menu path: /wechat/menu/tree
2025-07-28T11:57:29 [INFO] {000dd47d2a4e561863d9b51a34fac099} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:57:29 [INFO] {000dd47d2a4e561863d9b51a34fac099} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:57:29 [INFO] {000dd47d2a4e561863d9b51a34fac099} ValidateToken - Claims: 1 access admin
2025-07-28T11:57:29 [INFO] {000dd47d2a4e561863d9b51a34fac099} Auth middleware - ValidateToken result: 1
2025-07-28T11:57:29 [INFO] {000dd47d2a4e561863d9b51a34fac099} Auth middleware - set admin_id to context: 1
2025-07-28T11:57:29 [INFO] {000dd47d2a4e561863d9b51a34fac099} Required permission: system:officialAccount:menuEdit for PUT /wechat/menu/8
2025-07-28T11:57:29 [INFO] {000dd47d2a4e561863d9b51a34fac099} 超级管理员访问: system:officialAccount:menuEdit
2025-07-28T11:57:29 [DEBU] {000dd47d2a4e561863d9b51a34fac099} 权限验证通过: adminId: 1 permission: system:officialAccount:menuEdit path: /wechat/menu/8
2025-07-28T11:57:29 [INFO] {000dd47d2a4e561863d9b51a34fac099} 更新微信菜单成功: id: 8
2025-07-28T11:57:29 [INFO] {9cc125812a4e561865d9b51ac3284ec7} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:57:29 [INFO] {9cc125812a4e561865d9b51ac3284ec7} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:57:29 [INFO] {9cc125812a4e561865d9b51ac3284ec7} ValidateToken - Claims: 1 access admin
2025-07-28T11:57:29 [INFO] {9cc125812a4e561865d9b51ac3284ec7} Auth middleware - ValidateToken result: 1
2025-07-28T11:57:29 [INFO] {9cc125812a4e561865d9b51ac3284ec7} Auth middleware - set admin_id to context: 1
2025-07-28T11:57:29 [INFO] {9cc125812a4e561865d9b51ac3284ec7} Required permission: system:officialAccount:menu for GET /wechat/menu/tree
2025-07-28T11:57:29 [INFO] {9cc125812a4e561865d9b51ac3284ec7} 超级管理员访问: system:officialAccount:menu
2025-07-28T11:57:29 [DEBU] {9cc125812a4e561865d9b51ac3284ec7} 权限验证通过: adminId: 1 permission: system:officialAccount:menu path: /wechat/menu/tree
2025-07-28T11:57:55 [INFO] {58da75a1304e561867d9b51a063ac917} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.o-Ne8zI2q7x5Pzi52bbd4MhF4btB1TRh7w41nxCpHHA
2025-07-28T11:57:55 [INFO] {58da75a1304e561867d9b51a063ac917} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-28T11:57:55 [INFO] {58da75a1304e561867d9b51a063ac917} ValidateToken - Claims: 1 access admin
2025-07-28T11:57:55 [INFO] {58da75a1304e561867d9b51a063ac917} Auth middleware - ValidateToken result: 1
2025-07-28T11:57:55 [INFO] {58da75a1304e561867d9b51a063ac917} Auth middleware - set admin_id to context: 1
2025-07-28T11:57:55 [INFO] {58da75a1304e561867d9b51a063ac917} Required permission: system:officialAccount:menuPublish for POST /wechat/menu/publish
2025-07-28T11:57:55 [INFO] {58da75a1304e561867d9b51a063ac917} 超级管理员访问: system:officialAccount:menuPublish
2025-07-28T11:57:55 [DEBU] {58da75a1304e561867d9b51a063ac917} 权限验证通过: adminId: 1 permission: system:officialAccount:menuPublish path: /wechat/menu/publish
2025-07-28T11:57:57 [INFO] {58da75a1304e561867d9b51a063ac917} 微信菜单发布成功: {"errmsg":"ok"}
2025-07-28T11:58:35 [INFO] {54c5b2ce394e561869d9b51a97db3772} wechat serve
2025-07-28T12:00:00 [INFO] 开始更新搜索趋势...
2025-07-28T12:00:00 [INFO] 标记热门关键词: 中关村
2025-07-28T12:00:00 [INFO] 标记热门关键词: 吉林工商学院
2025-07-28T12:00:00 [INFO] 标记热门关键词: 中国银行
2025-07-28T12:00:00 [INFO] 热门搜索趋势更新完成，共标记 3 个热门关键词
2025-07-28T12:00:00 [INFO] 搜索趋势更新完成
2025-07-28T12:02:17 [INFO] {44d032846d4e561870d9b51a821483a8} wechat serve
2025-07-28T12:02:17 [INFO] {44d032846d4e561870d9b51a821483a8} 收到微信事件: MsgType: event
2025-07-28T12:02:17 [INFO] {44d032846d4e561870d9b51a821483a8} 收到事件: Event: {"EventInterface":null,"XMLName":{"Space":"","Local":"xml"},"Text":"\n\n\n\n\n\n\n","ToUserName":"gh_423d13c078b0","FromUserName":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","CreateTime":1753675336,"MsgType":"event","Event":"VIEW","SessionFrom":"","ChangeType":"","EventKey":"http://t64f466a.natappfree.cc/m/orders","Content":"PHhtbD48VG9Vc2VyTmFtZT48IVtDREFUQVtnaF80MjNkMTNjMDc4YjBdXT48L1RvVXNlck5hbWU+CjxGcm9tVXNlck5hbWU+PCFbQ0RBVEFbbzNqNmV2cEtPQ0xBWGtZMXNIazU1TElPcjJnUV1dPjwvRnJvbVVzZXJOYW1lPgo8Q3JlYXRlVGltZT4xNzUzNjc1MzM2PC9DcmVhdGVUaW1lPgo8TXNnVHlwZT48IVtDREFUQVtldmVudF1dPjwvTXNnVHlwZT4KPEV2ZW50PjwhW0NEQVRBW1ZJRVddXT48L0V2ZW50Pgo8RXZlbnRLZXk+PCFbQ0RBVEFbaHR0cDovL3Q2NGY0NjZhLm5hdGFwcGZyZWUuY2MvbS9vcmRlcnNdXT48L0V2ZW50S2V5Pgo8TWVudUlkPjQyODg5OTIwNjwvTWVudUlkPgo8L3htbD4="}
2025-07-28T12:02:17 [INFO] {44d032846d4e561870d9b51a821483a8} 菜单跳转事件: EventKey: http://t64f466a.natappfree.cc/m/orders FromUser: o3j6evpKOCLAXkY1sHk55LIOr2gQ
2025-07-28T12:02:17 [INFO] {44d032846d4e561870d9b51a821483a8} 微信响应已发送 ResponseLength: 7
2025-07-28T12:02:17 [INFO] {603c40946d4e561871d9b51a802237f1} Detail页面Session ID: majltl01or6az5dbne0w7og8e83003im
2025-07-28T12:02:17 [INFO] {603c40946d4e561871d9b51a802237f1} Session中的wechat_user: {"is_nil":true,"raw_data":null}
2025-07-28T12:02:17 [INFO] {603c40946d4e561871d9b51a802237f1} GetCurrentWechatUser结果: {"is_nil":true,"user_data":null}
2025-07-28T12:02:22 [INFO] {747b8e7e6e4e561874d9b51a7dbbf27b} 用户登录: {"client_ip":"*************","login_at":"2025-07-28 12:02:21","user_id":1}
2025-07-28T12:02:22 [INFO] {747b8e7e6e4e561874d9b51a7dbbf27b} 用户信息已存储到Session: {"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","user_id":1}
2025-07-28T12:02:22 [INFO] {747b8e7e6e4e561874d9b51a7dbbf27b} Session存储验证成功
2025-07-28T12:02:22 [INFO] {747b8e7e6e4e561874d9b51a7dbbf27b} 准备重定向到: /m/orders
2025-07-28T12:02:22 [INFO] {c82befaa6e4e561875d9b51af89ee079} Detail页面Session ID: majltl01or6az5dbne0yb2yln440097l
2025-07-28T12:02:22 [INFO] {c82befaa6e4e561875d9b51af89ee079} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T12:02:22 [INFO] {c82befaa6e4e561875d9b51af89ee079} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T12:02:22 [INFO] {041939ba6e4e561877d9b51a2a6b1398} 用户订单统计查询成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","paid_order_count":0,"paid_total_amount":0,"total_order_count":4,"unpaid_order_count":4,"user_id":1}
2025-07-28T12:02:35 [INFO] {30e2ace4714e561878d9b51ac980d1b7} wechat serve
2025-07-28T12:02:35 [INFO] {30e2ace4714e561878d9b51ac980d1b7} 收到微信事件: MsgType: event
2025-07-28T12:02:35 [INFO] {30e2ace4714e561878d9b51ac980d1b7} 收到事件: Event: {"EventInterface":null,"XMLName":{"Space":"","Local":"xml"},"Text":"\n\n\n\n\n\n\n","ToUserName":"gh_423d13c078b0","FromUserName":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","CreateTime":1753675355,"MsgType":"event","Event":"VIEW","SessionFrom":"","ChangeType":"","EventKey":"http://t64f466a.natappfree.cc/m/search","Content":"PHhtbD48VG9Vc2VyTmFtZT48IVtDREFUQVtnaF80MjNkMTNjMDc4YjBdXT48L1RvVXNlck5hbWU+CjxGcm9tVXNlck5hbWU+PCFbQ0RBVEFbbzNqNmV2cEtPQ0xBWGtZMXNIazU1TElPcjJnUV1dPjwvRnJvbVVzZXJOYW1lPgo8Q3JlYXRlVGltZT4xNzUzNjc1MzU1PC9DcmVhdGVUaW1lPgo8TXNnVHlwZT48IVtDREFUQVtldmVudF1dPjwvTXNnVHlwZT4KPEV2ZW50PjwhW0NEQVRBW1ZJRVddXT48L0V2ZW50Pgo8RXZlbnRLZXk+PCFbQ0RBVEFbaHR0cDovL3Q2NGY0NjZhLm5hdGFwcGZyZWUuY2MvbS9zZWFyY2hdXT48L0V2ZW50S2V5Pgo8TWVudUlkPjQyODg5OTIwNjwvTWVudUlkPgo8L3htbD4="}
2025-07-28T12:02:35 [INFO] {30e2ace4714e561878d9b51ac980d1b7} 菜单跳转事件: EventKey: http://t64f466a.natappfree.cc/m/search FromUser: o3j6evpKOCLAXkY1sHk55LIOr2gQ
2025-07-28T12:02:35 [INFO] {30e2ace4714e561878d9b51ac980d1b7} 微信响应已发送 ResponseLength: 7
2025-07-28T12:03:53 [INFO] {241197ed834e561887d9b51ab984c967} Detail页面Session ID: majltl01or6az5dbne0yb2yln440097l
2025-07-28T12:03:53 [INFO] {241197ed834e561887d9b51ab984c967} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T12:03:53 [INFO] {241197ed834e561887d9b51ab984c967} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T12:03:53 [INFO] VIP状态验证详情: {"current_date":"2025-07-28","current_time":"2025-07-28 12:03:53","effective_end":"2025-12-21","effective_end_raw":"2025-12-21 00:00:00","effective_start":"2025-07-21","effective_start_raw":"2025-07-21 00:00:00","end_comparison":"2025-07-28 \u003c= 2025-12-21 = true","start_comparison":"2025-07-28 \u003e= 2025-07-21 = true"}
2025-07-28T12:03:53 [INFO] {241197ed834e561887d9b51ab984c967} 用户VIP状态验证完成: {"effective_end":"2025-12-21 00:00:00","effective_start":"2025-07-21 00:00:00","is_vip":1,"user_id":1}
2025-07-28T12:03:53 [INFO] {241197ed834e561887d9b51ab984c967} 用户已登录，传递用户信息到模板
2025-07-28T12:04:05 [INFO] {8cdd3db0864e56188ad9b51aba36d6bb} Detail页面Session ID: majltl01or6az5dbne0yb2yln440097l
2025-07-28T12:04:05 [INFO] {8cdd3db0864e56188ad9b51aba36d6bb} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T12:04:05 [INFO] {8cdd3db0864e56188ad9b51aba36d6bb} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T12:04:05 [INFO] VIP状态验证详情: {"current_date":"2025-07-28","current_time":"2025-07-28 12:04:05","effective_end":"2025-12-21","effective_end_raw":"2025-12-21 00:00:00","effective_start":"2025-07-21","effective_start_raw":"2025-07-21 00:00:00","end_comparison":"2025-07-28 \u003c= 2025-12-21 = true","start_comparison":"2025-07-28 \u003e= 2025-07-21 = true"}
2025-07-28T12:04:05 [INFO] {8cdd3db0864e56188ad9b51aba36d6bb} 用户VIP状态验证完成: {"effective_end":"2025-12-21 00:00:00","effective_start":"2025-07-21 00:00:00","is_vip":1,"user_id":1}
2025-07-28T12:04:05 [INFO] {8cdd3db0864e56188ad9b51aba36d6bb} 用户已登录，传递用户信息到模板
2025-07-28T12:04:08 [INFO] {5cb85556874e56188cd9b51aa982e308} Detail页面Session ID: majltl01or6az5dbne0yb2yln440097l
2025-07-28T12:04:08 [INFO] {5cb85556874e56188cd9b51aa982e308} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T12:04:08 [INFO] {5cb85556874e56188cd9b51aa982e308} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T12:04:08 [INFO] VIP状态验证详情: {"current_date":"2025-07-28","current_time":"2025-07-28 12:04:08","effective_end":"2025-12-21","effective_end_raw":"2025-12-21 00:00:00","effective_start":"2025-07-21","effective_start_raw":"2025-07-21 00:00:00","end_comparison":"2025-07-28 \u003c= 2025-12-21 = true","start_comparison":"2025-07-28 \u003e= 2025-07-21 = true"}
2025-07-28T12:04:08 [INFO] {5cb85556874e56188cd9b51aa982e308} 用户VIP状态验证完成: {"effective_end":"2025-12-21 00:00:00","effective_start":"2025-07-21 00:00:00","is_vip":1,"user_id":1}
2025-07-28T12:04:08 [INFO] {5cb85556874e56188cd9b51aa982e308} 用户已登录，传递用户信息到模板
2025-07-28T12:04:16 [INFO] {20284e55894e561891d9b51ab4bb61ec} wechat serve
2025-07-28T12:04:16 [INFO] {20284e55894e561891d9b51ab4bb61ec} 收到微信事件: MsgType: event
2025-07-28T12:04:16 [INFO] {20284e55894e561891d9b51ab4bb61ec} 收到事件: Event: {"EventInterface":null,"XMLName":{"Space":"","Local":"xml"},"Text":"\n\n\n\n\n\n\n","ToUserName":"gh_423d13c078b0","FromUserName":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","CreateTime":1753675456,"MsgType":"event","Event":"VIEW","SessionFrom":"","ChangeType":"","EventKey":"http://t64f466a.natappfree.cc/m/list?tableIndex=1","Content":"PHhtbD48VG9Vc2VyTmFtZT48IVtDREFUQVtnaF80MjNkMTNjMDc4YjBdXT48L1RvVXNlck5hbWU+CjxGcm9tVXNlck5hbWU+PCFbQ0RBVEFbbzNqNmV2cEtPQ0xBWGtZMXNIazU1TElPcjJnUV1dPjwvRnJvbVVzZXJOYW1lPgo8Q3JlYXRlVGltZT4xNzUzNjc1NDU2PC9DcmVhdGVUaW1lPgo8TXNnVHlwZT48IVtDREFUQVtldmVudF1dPjwvTXNnVHlwZT4KPEV2ZW50PjwhW0NEQVRBW1ZJRVddXT48L0V2ZW50Pgo8RXZlbnRLZXk+PCFbQ0RBVEFbaHR0cDovL3Q2NGY0NjZhLm5hdGFwcGZyZWUuY2MvbS9saXN0P3RhYmxlSW5kZXg9MV1dPjwvRXZlbnRLZXk+CjxNZW51SWQ+NDI4ODk5MjA2PC9NZW51SWQ+CjwveG1sPg=="}
2025-07-28T12:04:16 [INFO] {20284e55894e561891d9b51ab4bb61ec} 菜单跳转事件: EventKey: http://t64f466a.natappfree.cc/m/list?tableIndex=1 FromUser: o3j6evpKOCLAXkY1sHk55LIOr2gQ
2025-07-28T12:04:16 [INFO] {20284e55894e561891d9b51ab4bb61ec} 微信响应已发送 ResponseLength: 7
2025-07-28T12:04:24 [INFO] {58a9f2188b4e561896d9b51a0f8083be} Detail页面Session ID: majltl01or6az5dbne0yb2yln440097l
2025-07-28T12:04:24 [INFO] {58a9f2188b4e561896d9b51a0f8083be} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T12:04:24 [INFO] {58a9f2188b4e561896d9b51a0f8083be} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T12:04:24 [INFO] VIP状态验证详情: {"current_date":"2025-07-28","current_time":"2025-07-28 12:04:24","effective_end":"2025-12-21","effective_end_raw":"2025-12-21 00:00:00","effective_start":"2025-07-21","effective_start_raw":"2025-07-21 00:00:00","end_comparison":"2025-07-28 \u003c= 2025-12-21 = true","start_comparison":"2025-07-28 \u003e= 2025-07-21 = true"}
2025-07-28T12:04:24 [INFO] {58a9f2188b4e561896d9b51a0f8083be} 用户VIP状态验证完成: {"effective_end":"2025-12-21 00:00:00","effective_start":"2025-07-21 00:00:00","is_vip":1,"user_id":1}
2025-07-28T12:04:24 [INFO] {58a9f2188b4e561896d9b51a0f8083be} 用户已登录，传递用户信息到模板
2025-07-28T12:05:34 [INFO] {a0f79a889b4e561898d9b51ae2e365dc} wechat serve
2025-07-28T12:05:34 [INFO] {a0f79a889b4e561898d9b51ae2e365dc} 收到微信事件: MsgType: text
2025-07-28T12:05:34 [INFO] {a0f79a889b4e561898d9b51ae2e365dc} 收到文本消息: Content: http://t64f466a.natappfree.cc/m/detail?id=1 FromUser: o3j6evpKOCLAXkY1sHk55LIOr2gQ
2025-07-28T12:05:34 [INFO] {a0f79a889b4e561898d9b51ae2e365dc} 微信响应已发送 ResponseLength: 7
2025-07-28T12:12:35 [INFO] {d4dba387fd4e561899d9b51a43be6f60} wechat serve
2025-07-28T12:12:35 [INFO] {d4dba387fd4e561899d9b51a43be6f60} 收到微信事件: MsgType: event
2025-07-28T12:12:35 [INFO] {d4dba387fd4e561899d9b51a43be6f60} 收到事件: Event: {"EventInterface":null,"XMLName":{"Space":"","Local":"xml"},"Text":"\n\n\n\n\n\n\n","ToUserName":"gh_423d13c078b0","FromUserName":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","CreateTime":1753675955,"MsgType":"event","Event":"VIEW","SessionFrom":"","ChangeType":"","EventKey":"https://www.zszbcg.com/index.php?s=index/h5/my\u0026tabIndex=2","Content":"PHhtbD48VG9Vc2VyTmFtZT48IVtDREFUQVtnaF80MjNkMTNjMDc4YjBdXT48L1RvVXNlck5hbWU+CjxGcm9tVXNlck5hbWU+PCFbQ0RBVEFbbzNqNmV2cEtPQ0xBWGtZMXNIazU1TElPcjJnUV1dPjwvRnJvbVVzZXJOYW1lPgo8Q3JlYXRlVGltZT4xNzUzNjc1OTU1PC9DcmVhdGVUaW1lPgo8TXNnVHlwZT48IVtDREFUQVtldmVudF1dPjwvTXNnVHlwZT4KPEV2ZW50PjwhW0NEQVRBW1ZJRVddXT48L0V2ZW50Pgo8RXZlbnRLZXk+PCFbQ0RBVEFbaHR0cHM6Ly93d3cuenN6YmNnLmNvbS9pbmRleC5waHA/cz1pbmRleC9oNS9teSZ0YWJJbmRleD0yXV0+PC9FdmVudEtleT4KPE1lbnVJZD40Mjg4OTkyMDY8L01lbnVJZD4KPC94bWw+"}
2025-07-28T12:12:35 [INFO] {d4dba387fd4e561899d9b51a43be6f60} 菜单跳转事件: EventKey: https://www.zszbcg.com/index.php?s=index/h5/my&tabIndex=2 FromUser: o3j6evpKOCLAXkY1sHk55LIOr2gQ
2025-07-28T12:12:35 [INFO] {d4dba387fd4e561899d9b51a43be6f60} 微信响应已发送 ResponseLength: 7
2025-07-28T12:12:44 [INFO] {4405d687ff4e56189ad9b51a027eb994} wechat serve
2025-07-28T12:12:44 [INFO] {4405d687ff4e56189ad9b51a027eb994} 收到微信事件: MsgType: event
2025-07-28T12:12:44 [INFO] {4405d687ff4e56189ad9b51a027eb994} 收到事件: Event: {"EventInterface":null,"XMLName":{"Space":"","Local":"xml"},"Text":"\n\n\n\n\n\n\n","ToUserName":"gh_423d13c078b0","FromUserName":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","CreateTime":1753675964,"MsgType":"event","Event":"VIEW","SessionFrom":"","ChangeType":"","EventKey":"https://www.zszbcg.com/index.php?s=index/h5/my\u0026tabIndex=2","Content":"PHhtbD48VG9Vc2VyTmFtZT48IVtDREFUQVtnaF80MjNkMTNjMDc4YjBdXT48L1RvVXNlck5hbWU+CjxGcm9tVXNlck5hbWU+PCFbQ0RBVEFbbzNqNmV2cEtPQ0xBWGtZMXNIazU1TElPcjJnUV1dPjwvRnJvbVVzZXJOYW1lPgo8Q3JlYXRlVGltZT4xNzUzNjc1OTY0PC9DcmVhdGVUaW1lPgo8TXNnVHlwZT48IVtDREFUQVtldmVudF1dPjwvTXNnVHlwZT4KPEV2ZW50PjwhW0NEQVRBW1ZJRVddXT48L0V2ZW50Pgo8RXZlbnRLZXk+PCFbQ0RBVEFbaHR0cHM6Ly93d3cuenN6YmNnLmNvbS9pbmRleC5waHA/cz1pbmRleC9oNS9teSZ0YWJJbmRleD0yXV0+PC9FdmVudEtleT4KPE1lbnVJZD40Mjg4OTkyMDY8L01lbnVJZD4KPC94bWw+"}
2025-07-28T12:12:44 [INFO] {4405d687ff4e56189ad9b51a027eb994} 菜单跳转事件: EventKey: https://www.zszbcg.com/index.php?s=index/h5/my&tabIndex=2 FromUser: o3j6evpKOCLAXkY1sHk55LIOr2gQ
2025-07-28T12:12:44 [INFO] {4405d687ff4e56189ad9b51a027eb994} 微信响应已发送 ResponseLength: 7
2025-07-28T13:00:00 [INFO] 开始更新搜索趋势...
2025-07-28T13:00:00 [INFO] 标记热门关键词: 中关村
2025-07-28T13:00:00 [INFO] 标记热门关键词: 吉林工商学院
2025-07-28T13:00:00 [INFO] 标记热门关键词: 中国银行
2025-07-28T13:00:00 [INFO] 热门搜索趋势更新完成，共标记 3 个热门关键词
2025-07-28T13:00:00 [INFO] 搜索趋势更新完成
2025-07-28T14:00:00 [INFO] 开始更新搜索趋势...
2025-07-28T14:00:00 [INFO] 标记热门关键词: 中关村
2025-07-28T14:00:00 [INFO] 标记热门关键词: 吉林工商学院
2025-07-28T14:00:00 [INFO] 标记热门关键词: 中国银行
2025-07-28T14:00:00 [INFO] 热门搜索趋势更新完成，共标记 3 个热门关键词
2025-07-28T14:00:00 [INFO] 搜索趋势更新完成
2025-07-28T14:17:17 [INFO] 搜索趋势定时任务启动成功, CronID: {"Name":"cron-1","RegisterTime":"2025-07-28T14:17:17.1244438+08:00"}
2025-07-28T14:17:17 [INFO] 当前定时任务数量: 1
2025-07-28T14:17:17 [INFO] 定时任务 1 : {"Name":"cron-1","RegisterTime":"2025-07-28T14:17:17.1244438+08:00"}
2025-07-28T14:17:47 [INFO] {cc74ed7bd2555618074d697be5120e23} wechat serve
2025-07-28T14:17:47 [INFO] {cc74ed7bd2555618074d697be5120e23} 收到微信事件: MsgType: event
2025-07-28T14:17:47 [INFO] {cc74ed7bd2555618074d697be5120e23} 收到事件: Event: {"EventInterface":null,"XMLName":{"Space":"","Local":"xml"},"Text":"\n\n\n\n\n\n\n","ToUserName":"gh_423d13c078b0","FromUserName":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","CreateTime":1753683467,"MsgType":"event","Event":"VIEW","SessionFrom":"","ChangeType":"","EventKey":"http://t64f466a.natappfree.cc/m/list?tableIndex=1","Content":"PHhtbD48VG9Vc2VyTmFtZT48IVtDREFUQVtnaF80MjNkMTNjMDc4YjBdXT48L1RvVXNlck5hbWU+CjxGcm9tVXNlck5hbWU+PCFbQ0RBVEFbbzNqNmV2cEtPQ0xBWGtZMXNIazU1TElPcjJnUV1dPjwvRnJvbVVzZXJOYW1lPgo8Q3JlYXRlVGltZT4xNzUzNjgzNDY3PC9DcmVhdGVUaW1lPgo8TXNnVHlwZT48IVtDREFUQVtldmVudF1dPjwvTXNnVHlwZT4KPEV2ZW50PjwhW0NEQVRBW1ZJRVddXT48L0V2ZW50Pgo8RXZlbnRLZXk+PCFbQ0RBVEFbaHR0cDovL3Q2NGY0NjZhLm5hdGFwcGZyZWUuY2MvbS9saXN0P3RhYmxlSW5kZXg9MV1dPjwvRXZlbnRLZXk+CjxNZW51SWQ+NDI4ODk5MjA2PC9NZW51SWQ+CjwveG1sPg=="}
2025-07-28T14:17:47 [INFO] {cc74ed7bd2555618074d697be5120e23} 菜单跳转事件: EventKey: http://t64f466a.natappfree.cc/m/list?tableIndex=1 FromUser: o3j6evpKOCLAXkY1sHk55LIOr2gQ
2025-07-28T14:17:47 [INFO] {cc74ed7bd2555618074d697be5120e23} 微信响应已发送 ResponseLength: 7
2025-07-28T14:17:53 [INFO] {20c268ccd35556180d4d697b3dbc974e} Detail页面Session ID: majltl01or6az5dbne0yb2yln440097l
2025-07-28T14:17:53 [INFO] {20c268ccd35556180d4d697b3dbc974e} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:17:53 [INFO] {20c268ccd35556180d4d697b3dbc974e} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:18:33 [INFO] 搜索趋势定时任务启动成功, CronID: {"Name":"cron-1","RegisterTime":"2025-07-28T14:18:33.2913679+08:00"}
2025-07-28T14:18:33 [INFO] 当前定时任务数量: 1
2025-07-28T14:18:33 [INFO] 定时任务 1 : {"Name":"cron-1","RegisterTime":"2025-07-28T14:18:33.2913679+08:00"}
2025-07-28T14:18:36 [INFO] {d4fdb4c7dd55561827cd22115db1a2be} Detail页面Session ID: majltl01or6az5dbne0yb2yln440097l
2025-07-28T14:18:36 [INFO] {d4fdb4c7dd55561827cd22115db1a2be} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:18:36 [INFO] {d4fdb4c7dd55561827cd22115db1a2be} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:18:36 [INFO] {d4fdb4c7dd55561827cd22115db1a2be} 创建浏览记录成功: {"article_id":1,"user_id":1}
2025-07-28T14:18:36 [INFO] VIP状态验证详情: {"current_date":"2025-07-28","current_time":"2025-07-28 14:18:36","effective_end":"2025-12-21","effective_end_raw":"2025-12-21 00:00:00","effective_start":"2025-07-21","effective_start_raw":"2025-07-21 00:00:00","end_comparison":"2025-07-28 \u003c= 2025-12-21 = true","start_comparison":"2025-07-28 \u003e= 2025-07-21 = true"}
2025-07-28T14:18:36 [INFO] {d4fdb4c7dd55561827cd22115db1a2be} 用户VIP状态验证完成: {"effective_end":"2025-12-21 00:00:00","effective_start":"2025-07-21 00:00:00","is_vip":1,"user_id":1}
2025-07-28T14:18:36 [INFO] {d4fdb4c7dd55561827cd22115db1a2be} 用户已登录，传递用户信息到模板
2025-07-28T14:18:58 [INFO] {9445fe07e35556182bcd2211216a43de} Detail页面Session ID: majltl01or6az5dbne0yb2yln440097l
2025-07-28T14:18:58 [INFO] {9445fe07e35556182bcd2211216a43de} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:18:58 [INFO] {9445fe07e35556182bcd2211216a43de} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:18:58 [INFO] {9445fe07e35556182bcd2211216a43de} 创建浏览记录成功: {"article_id":3,"user_id":1}
2025-07-28T14:18:58 [INFO] VIP状态验证详情: {"current_date":"2025-07-28","current_time":"2025-07-28 14:18:58","effective_end":"2025-12-21","effective_end_raw":"2025-12-21 00:00:00","effective_start":"2025-07-21","effective_start_raw":"2025-07-21 00:00:00","end_comparison":"2025-07-28 \u003c= 2025-12-21 = true","start_comparison":"2025-07-28 \u003e= 2025-07-21 = true"}
2025-07-28T14:18:58 [INFO] {9445fe07e35556182bcd2211216a43de} 用户VIP状态验证完成: {"effective_end":"2025-12-21 00:00:00","effective_start":"2025-07-21 00:00:00","is_vip":1,"user_id":1}
2025-07-28T14:18:58 [INFO] {9445fe07e35556182bcd2211216a43de} 用户已登录，传递用户信息到模板
2025-07-28T14:19:08 [INFO] {ecab355be55556182dcd221189457478} Detail页面Session ID: majltl01or6az5dbne0yb2yln440097l
2025-07-28T14:19:08 [INFO] {ecab355be55556182dcd221189457478} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:19:08 [INFO] {ecab355be55556182dcd221189457478} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:19:08 [INFO] {ecab355be55556182dcd221189457478} 创建浏览记录成功: {"article_id":6,"user_id":1}
2025-07-28T14:19:08 [INFO] VIP状态验证详情: {"current_date":"2025-07-28","current_time":"2025-07-28 14:19:08","effective_end":"2025-12-21","effective_end_raw":"2025-12-21 00:00:00","effective_start":"2025-07-21","effective_start_raw":"2025-07-21 00:00:00","end_comparison":"2025-07-28 \u003c= 2025-12-21 = true","start_comparison":"2025-07-28 \u003e= 2025-07-21 = true"}
2025-07-28T14:19:08 [INFO] {ecab355be55556182dcd221189457478} 用户VIP状态验证完成: {"effective_end":"2025-12-21 00:00:00","effective_start":"2025-07-21 00:00:00","is_vip":1,"user_id":1}
2025-07-28T14:19:08 [INFO] {ecab355be55556182dcd221189457478} 用户已登录，传递用户信息到模板
2025-07-28T14:19:20 [INFO] {18923811e85556182fcd221176a67a21} Detail页面Session ID: majltl01or6az5dbne0yb2yln440097l
2025-07-28T14:19:20 [INFO] {18923811e85556182fcd221176a67a21} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:19:20 [INFO] {18923811e85556182fcd221176a67a21} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:19:20 [ERRO] {18923811e85556182fcd221176a67a21} 更新浏览记录失败: UPDATE `zb_user_browser` SET `updated_at`='NOW()' WHERE (`user_id`=1) AND (`article_id`=6): Error 1292 (22007): Incorrect datetime value: 'NOW()' for column 'updated_at' at row 3 
Stack:
1.  admin-server/internal/logic/zb_user_browser.(*sZbUserBrowser).CreateBrowserRecord
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/logic/zb_user_browser/zb_user_browser.go:43
2.  admin-server/internal/controller/mobile.(*ControllerMobile).Detail
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/controller/mobile/mobile.go:116
3.  admin-server/internal/cmd.init.func1.3.1
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/cmd/cmd.go:109

2025-07-28T14:19:20 [ERRO] {18923811e85556182fcd221176a67a21} 创建浏览记录失败: UPDATE `zb_user_browser` SET `updated_at`='NOW()' WHERE (`user_id`=1) AND (`article_id`=6): Error 1292 (22007): Incorrect datetime value: 'NOW()' for column 'updated_at' at row 3 
Stack:
1.  admin-server/internal/controller/mobile.(*ControllerMobile).Detail
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/controller/mobile/mobile.go:118
2.  admin-server/internal/cmd.init.func1.3.1
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/cmd/cmd.go:109

2025-07-28T14:19:20 [INFO] VIP状态验证详情: {"current_date":"2025-07-28","current_time":"2025-07-28 14:19:20","effective_end":"2025-12-21","effective_end_raw":"2025-12-21 00:00:00","effective_start":"2025-07-21","effective_start_raw":"2025-07-21 00:00:00","end_comparison":"2025-07-28 \u003c= 2025-12-21 = true","start_comparison":"2025-07-28 \u003e= 2025-07-21 = true"}
2025-07-28T14:19:20 [INFO] {18923811e85556182fcd221176a67a21} 用户VIP状态验证完成: {"effective_end":"2025-12-21 00:00:00","effective_start":"2025-07-21 00:00:00","is_vip":1,"user_id":1}
2025-07-28T14:19:20 [INFO] {18923811e85556182fcd221176a67a21} 用户已登录，传递用户信息到模板
2025-07-28T14:20:05 [INFO] {7ca8b393f255561831cd221198fdbd43} wechat serve
2025-07-28T14:20:05 [INFO] {7ca8b393f255561831cd221198fdbd43} 收到微信事件: MsgType: event
2025-07-28T14:20:05 [INFO] {7ca8b393f255561831cd221198fdbd43} 收到事件: Event: {"EventInterface":null,"XMLName":{"Space":"","Local":"xml"},"Text":"\n\n\n\n\n\n\n","ToUserName":"gh_423d13c078b0","FromUserName":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","CreateTime":1753683605,"MsgType":"event","Event":"VIEW","SessionFrom":"","ChangeType":"","EventKey":"https://www.zszbcg.com/index.php?s=index/h5/my\u0026tabIndex=2","Content":"PHhtbD48VG9Vc2VyTmFtZT48IVtDREFUQVtnaF80MjNkMTNjMDc4YjBdXT48L1RvVXNlck5hbWU+CjxGcm9tVXNlck5hbWU+PCFbQ0RBVEFbbzNqNmV2cEtPQ0xBWGtZMXNIazU1TElPcjJnUV1dPjwvRnJvbVVzZXJOYW1lPgo8Q3JlYXRlVGltZT4xNzUzNjgzNjA1PC9DcmVhdGVUaW1lPgo8TXNnVHlwZT48IVtDREFUQVtldmVudF1dPjwvTXNnVHlwZT4KPEV2ZW50PjwhW0NEQVRBW1ZJRVddXT48L0V2ZW50Pgo8RXZlbnRLZXk+PCFbQ0RBVEFbaHR0cHM6Ly93d3cuenN6YmNnLmNvbS9pbmRleC5waHA/cz1pbmRleC9oNS9teSZ0YWJJbmRleD0yXV0+PC9FdmVudEtleT4KPE1lbnVJZD40Mjg4OTkyMDY8L01lbnVJZD4KPC94bWw+"}
2025-07-28T14:20:05 [INFO] {7ca8b393f255561831cd221198fdbd43} 菜单跳转事件: EventKey: https://www.zszbcg.com/index.php?s=index/h5/my&tabIndex=2 FromUser: o3j6evpKOCLAXkY1sHk55LIOr2gQ
2025-07-28T14:20:05 [INFO] {7ca8b393f255561831cd221198fdbd43} 微信响应已发送 ResponseLength: 7
2025-07-28T14:20:11 [INFO] {0849f5fbf355561832cd22118d97201a} wechat serve
2025-07-28T14:20:11 [INFO] {0849f5fbf355561832cd22118d97201a} 收到微信事件: MsgType: event
2025-07-28T14:20:11 [INFO] {0849f5fbf355561832cd22118d97201a} 收到事件: Event: {"EventInterface":null,"XMLName":{"Space":"","Local":"xml"},"Text":"\n\n\n\n\n\n\n","ToUserName":"gh_423d13c078b0","FromUserName":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","CreateTime":1753683611,"MsgType":"event","Event":"VIEW","SessionFrom":"","ChangeType":"","EventKey":"https://www.zszbcg.com/index.php?s=index/h5/my\u0026tabIndex=2","Content":"PHhtbD48VG9Vc2VyTmFtZT48IVtDREFUQVtnaF80MjNkMTNjMDc4YjBdXT48L1RvVXNlck5hbWU+CjxGcm9tVXNlck5hbWU+PCFbQ0RBVEFbbzNqNmV2cEtPQ0xBWGtZMXNIazU1TElPcjJnUV1dPjwvRnJvbVVzZXJOYW1lPgo8Q3JlYXRlVGltZT4xNzUzNjgzNjExPC9DcmVhdGVUaW1lPgo8TXNnVHlwZT48IVtDREFUQVtldmVudF1dPjwvTXNnVHlwZT4KPEV2ZW50PjwhW0NEQVRBW1ZJRVddXT48L0V2ZW50Pgo8RXZlbnRLZXk+PCFbQ0RBVEFbaHR0cHM6Ly93d3cuenN6YmNnLmNvbS9pbmRleC5waHA/cz1pbmRleC9oNS9teSZ0YWJJbmRleD0yXV0+PC9FdmVudEtleT4KPE1lbnVJZD40Mjg4OTkyMDY8L01lbnVJZD4KPC94bWw+"}
2025-07-28T14:20:11 [INFO] {0849f5fbf355561832cd22118d97201a} 菜单跳转事件: EventKey: https://www.zszbcg.com/index.php?s=index/h5/my&tabIndex=2 FromUser: o3j6evpKOCLAXkY1sHk55LIOr2gQ
2025-07-28T14:20:11 [INFO] {0849f5fbf355561832cd22118d97201a} 微信响应已发送 ResponseLength: 7
2025-07-28T14:20:21 [INFO] {0c7a6171f655561833cd221100485a9a} wechat serve
2025-07-28T14:20:21 [INFO] {0c7a6171f655561833cd221100485a9a} 收到微信事件: MsgType: event
2025-07-28T14:20:21 [INFO] {0c7a6171f655561833cd221100485a9a} 收到事件: Event: {"EventInterface":null,"XMLName":{"Space":"","Local":"xml"},"Text":"\n\n\n\n\n\n\n","ToUserName":"gh_423d13c078b0","FromUserName":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","CreateTime":1753683621,"MsgType":"event","Event":"VIEW","SessionFrom":"","ChangeType":"","EventKey":"https://www.zszbcg.com/index.php?s=index/h5/my\u0026tabIndex=2","Content":"PHhtbD48VG9Vc2VyTmFtZT48IVtDREFUQVtnaF80MjNkMTNjMDc4YjBdXT48L1RvVXNlck5hbWU+CjxGcm9tVXNlck5hbWU+PCFbQ0RBVEFbbzNqNmV2cEtPQ0xBWGtZMXNIazU1TElPcjJnUV1dPjwvRnJvbVVzZXJOYW1lPgo8Q3JlYXRlVGltZT4xNzUzNjgzNjIxPC9DcmVhdGVUaW1lPgo8TXNnVHlwZT48IVtDREFUQVtldmVudF1dPjwvTXNnVHlwZT4KPEV2ZW50PjwhW0NEQVRBW1ZJRVddXT48L0V2ZW50Pgo8RXZlbnRLZXk+PCFbQ0RBVEFbaHR0cHM6Ly93d3cuenN6YmNnLmNvbS9pbmRleC5waHA/cz1pbmRleC9oNS9teSZ0YWJJbmRleD0yXV0+PC9FdmVudEtleT4KPE1lbnVJZD40Mjg4OTkyMDY8L01lbnVJZD4KPC94bWw+"}
2025-07-28T14:20:21 [INFO] {0c7a6171f655561833cd221100485a9a} 菜单跳转事件: EventKey: https://www.zszbcg.com/index.php?s=index/h5/my&tabIndex=2 FromUser: o3j6evpKOCLAXkY1sHk55LIOr2gQ
2025-07-28T14:20:21 [INFO] {0c7a6171f655561833cd221100485a9a} 微信响应已发送 ResponseLength: 7
2025-07-28T14:23:57 [INFO] {f49c75932856561834cd2211714df3a1} Detail页面Session ID: 1o4a3g910222h1dbnh1cxm3pbo1002pe
2025-07-28T14:23:57 [INFO] {f49c75932856561834cd2211714df3a1} Session中的wechat_user: {"is_nil":true,"raw_data":null}
2025-07-28T14:23:57 [INFO] {f49c75932856561834cd2211714df3a1} GetCurrentWechatUser结果: {"is_nil":true,"user_data":null}
2025-07-28T14:24:03 [INFO] {a48c4b032a56561836cd2211931ce001} Detail页面Session ID: 19rolh48927690dbnh1frob7382004ng
2025-07-28T14:24:03 [INFO] {a48c4b032a56561836cd2211931ce001} Session中的wechat_user: {"is_nil":true,"raw_data":null}
2025-07-28T14:24:03 [INFO] {a48c4b032a56561836cd2211931ce001} GetCurrentWechatUser结果: {"is_nil":true,"user_data":null}
2025-07-28T14:24:10 [INFO] {301fa6832b5656183acd22118d0d6218} 用户登录: {"client_ip":"*************","login_at":"2025-07-28 14:24:10","user_id":1}
2025-07-28T14:24:10 [INFO] {301fa6832b5656183acd22118d0d6218} 用户信息已存储到Session: {"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","user_id":1}
2025-07-28T14:24:10 [INFO] {301fa6832b5656183acd22118d0d6218} Session存储验证成功
2025-07-28T14:24:10 [INFO] {301fa6832b5656183acd22118d0d6218} 准备重定向到: /m/orders
2025-07-28T14:24:10 [INFO] {6474ccb02b5656183bcd2211991aca7c} Detail页面Session ID: g0a0xi08927690dbnh1j0oidyw3003r2
2025-07-28T14:24:10 [INFO] {6474ccb02b5656183bcd2211991aca7c} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:24:10 [INFO] {6474ccb02b5656183bcd2211991aca7c} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:24:11 [INFO] {c06206d22b5656183dcd221130010997} 用户订单统计查询成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","paid_order_count":0,"paid_total_amount":0,"total_order_count":4,"unpaid_order_count":4,"user_id":1}
2025-07-28T14:24:45 [INFO] 搜索趋势定时任务启动成功, CronID: {"Name":"cron-1","RegisterTime":"2025-07-28T14:24:45.2698998+08:00"}
2025-07-28T14:24:45 [INFO] 当前定时任务数量: 1
2025-07-28T14:24:45 [INFO] 定时任务 1 : {"Name":"cron-1","RegisterTime":"2025-07-28T14:24:45.2698998+08:00"}
2025-07-28T14:24:57 [INFO] {b883f08c365656188b7b26496da96b5c} Detail页面Session ID: g0a0xi08927690dbnh1j0oidyw3003r2
2025-07-28T14:24:57 [INFO] {b883f08c365656188b7b26496da96b5c} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:24:57 [INFO] {b883f08c365656188b7b26496da96b5c} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:25:28 [INFO] {cc3816ce3d5656188c7b2649bfbbd54e} Detail页面Session ID: g0a0xi08927690dbnh1j0oidyw3003r2
2025-07-28T14:25:28 [INFO] {cc3816ce3d5656188c7b2649bfbbd54e} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:25:28 [INFO] {cc3816ce3d5656188c7b2649bfbbd54e} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:25:36 [INFO] {84aa52ca3f5656188d7b2649127d2033} Detail页面Session ID: g0a0xi08927690dbnh1j0oidyw3003r2
2025-07-28T14:25:36 [INFO] {84aa52ca3f5656188d7b2649127d2033} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:25:36 [INFO] {84aa52ca3f5656188d7b2649127d2033} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:27:43 [INFO] {30f7212b5d5656188e7b26495d0ec90c} Detail页面Session ID: g0a0xi08927690dbnh1j0oidyw3003r2
2025-07-28T14:27:43 [INFO] {30f7212b5d5656188e7b26495d0ec90c} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:27:43 [INFO] {30f7212b5d5656188e7b26495d0ec90c} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:28:16 [INFO] {d01adee9645656188f7b26499821faca} Detail页面Session ID: g0a0xi08927690dbnh1j0oidyw3003r2
2025-07-28T14:28:16 [INFO] {d01adee9645656188f7b26499821faca} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:28:16 [INFO] {d01adee9645656188f7b26499821faca} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:28:47 [INFO] {789e2c1d6c565618907b264941a10c2a} Detail页面Session ID: g0a0xi08927690dbnh1j0oidyw3003r2
2025-07-28T14:28:47 [INFO] {789e2c1d6c565618907b264941a10c2a} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:28:47 [INFO] {789e2c1d6c565618907b264941a10c2a} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:29:00 [INFO] {9880611a6f565618917b264970e048ab} Detail页面Session ID: g0a0xi08927690dbnh1j0oidyw3003r2
2025-07-28T14:29:00 [INFO] {9880611a6f565618917b264970e048ab} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:29:00 [INFO] {9880611a6f565618917b264970e048ab} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:37:12 [INFO] {78bcb4a6e1565618937b2649a96b3b0e} Detail页面Session ID: g0a0xi08927690dbnh1j0oidyw3003r2
2025-07-28T14:37:12 [INFO] {78bcb4a6e1565618937b2649a96b3b0e} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:37:12 [INFO] {78bcb4a6e1565618937b2649a96b3b0e} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:38:33 [INFO] 搜索趋势定时任务启动成功, CronID: {"Name":"cron-1","RegisterTime":"2025-07-28T14:38:33.1933284+08:00"}
2025-07-28T14:38:33 [INFO] 当前定时任务数量: 1
2025-07-28T14:38:33 [INFO] 定时任务 1 : {"Name":"cron-1","RegisterTime":"2025-07-28T14:38:33.1933284+08:00"}
2025-07-28T14:38:36 [INFO] {d4cbca6bf5565618c988cb7d5c73ecf1} Detail页面Session ID: g0a0xi08927690dbnh1j0oidyw3003r2
2025-07-28T14:38:36 [INFO] {d4cbca6bf5565618c988cb7d5c73ecf1} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:38:36 [INFO] {d4cbca6bf5565618c988cb7d5c73ecf1} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:38:52 [INFO] {34476b19f9565618cb88cb7d91b1a808} Detail页面Session ID: g0a0xi08927690dbnh1j0oidyw3003r2
2025-07-28T14:38:52 [INFO] {34476b19f9565618cb88cb7d91b1a808} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:38:52 [INFO] {34476b19f9565618cb88cb7d91b1a808} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:43:24 [INFO] {bc9b6e7738575618cd88cb7d44088092} Detail页面Session ID: g0a0xi08927690dbnh1j0oidyw3003r2
2025-07-28T14:43:24 [INFO] {bc9b6e7738575618cd88cb7d44088092} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:43:24 [INFO] {bc9b6e7738575618cd88cb7d44088092} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:43:28 [INFO] {cc21b75139575618cf88cb7d557993b5} Detail页面Session ID: g0a0xi08927690dbnh1j0oidyw3003r2
2025-07-28T14:43:28 [INFO] {cc21b75139575618cf88cb7d557993b5} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-28T14:43:28 [INFO] {cc21b75139575618cf88cb7d557993b5} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
