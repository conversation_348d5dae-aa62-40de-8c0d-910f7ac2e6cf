# 文件上传功能使用示例

## 概述

素材中心的文件上传功能现在支持多种文件输入类型，包括：
- `*multipart.FileHeader` - HTTP表单上传的文件
- `[]byte` - 二进制数据
- `io.Reader` - 任何实现了Reader接口的对象
- `string` - 本地文件路径

## 使用示例

### 1. HTTP表单文件上传（推荐）

```go
// 在控制器中处理文件上传
func (c *ControllerV1) Upload(ctx context.Context, req *v1.UploadReq) (res *v1.UploadRes, err error) {
    // 获取上传的文件
    r := ghttp.RequestFromCtx(ctx)
    file := r.GetUploadFile("file")
    if file == nil {
        return nil, gerror.New("请选择要上传的文件")
    }

    // 调用上传服务
    resource, err := service.SysResources().UploadFile(ctx, req.GroupId, file)
    if err != nil {
        return nil, err
    }

    return &v1.UploadRes{
        ID:  resource.Id,
        Url: resource.Url,
    }, nil
}
```

### 2. 二进制数据上传

```go
// 上传二进制数据
func UploadBinaryData(ctx context.Context, groupId int64, data []byte) error {
    resource, err := service.SysResources().UploadFile(ctx, groupId, data)
    if err != nil {
        return err
    }
    
    g.Log().Info(ctx, "上传成功:", "url:", resource.Url)
    return nil
}

// 使用示例
func ExampleBinaryUpload() {
    ctx := context.Background()
    
    // 准备二进制数据
    imageData := []byte{0xFF, 0xD8, 0xFF, 0xE0} // JPEG文件头示例
    
    // 上传到分组ID为1的分组
    err := UploadBinaryData(ctx, 1, imageData)
    if err != nil {
        log.Printf("上传失败: %v", err)
    }
}
```

### 3. 从Reader上传

```go
// 从Reader上传文件
func UploadFromReader(ctx context.Context, groupId int64, reader io.Reader) error {
    resource, err := service.SysResources().UploadFile(ctx, groupId, reader)
    if err != nil {
        return err
    }
    
    g.Log().Info(ctx, "上传成功:", "url:", resource.Url)
    return nil
}

// 使用示例：从网络下载并上传
func ExampleReaderUpload() {
    ctx := context.Background()
    
    // 从网络获取文件
    resp, err := http.Get("https://example.com/image.jpg")
    if err != nil {
        log.Printf("下载失败: %v", err)
        return
    }
    defer resp.Body.Close()
    
    // 直接上传
    err = UploadFromReader(ctx, 1, resp.Body)
    if err != nil {
        log.Printf("上传失败: %v", err)
    }
}
```

### 4. 从本地文件路径上传

```go
// 从本地文件路径上传
func UploadFromPath(ctx context.Context, groupId int64, filePath string) error {
    resource, err := service.SysResources().UploadFile(ctx, groupId, filePath)
    if err != nil {
        return err
    }
    
    g.Log().Info(ctx, "上传成功:", "url:", resource.Url)
    return nil
}

// 使用示例
func ExamplePathUpload() {
    ctx := context.Background()
    
    // 上传本地文件
    err := UploadFromPath(ctx, 1, "/path/to/local/file.jpg")
    if err != nil {
        log.Printf("上传失败: %v", err)
    }
}
```

## 前端调用示例

### JavaScript/Ajax上传

```javascript
// 文件上传函数
function uploadFile(groupId, file) {
    const formData = new FormData();
    formData.append('group_id', groupId);
    formData.append('file', file);

    return fetch('/sys_resources/upload', {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + getToken()
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            console.log('上传成功:', data.data.url);
            return data.data;
        } else {
            throw new Error(data.message);
        }
    });
}

// 使用示例
document.getElementById('fileInput').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        uploadFile(1, file)
            .then(result => {
                console.log('文件上传成功:', result);
                // 显示上传的图片
                document.getElementById('preview').src = result.url;
            })
            .catch(error => {
                console.error('上传失败:', error);
            });
    }
});
```

### Vue.js上传组件

```vue
<template>
  <div>
    <input type="file" @change="handleFileChange" ref="fileInput" />
    <button @click="uploadFile" :disabled="!selectedFile">上传</button>
    <div v-if="uploading">上传中...</div>
    <img v-if="uploadedUrl" :src="uploadedUrl" alt="上传的图片" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      selectedFile: null,
      uploading: false,
      uploadedUrl: ''
    }
  },
  methods: {
    handleFileChange(event) {
      this.selectedFile = event.target.files[0];
    },
    
    async uploadFile() {
      if (!this.selectedFile) return;
      
      this.uploading = true;
      
      const formData = new FormData();
      formData.append('group_id', 1); // 分组ID
      formData.append('file', this.selectedFile);
      
      try {
        const response = await this.$http.post('/sys_resources/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
        
        if (response.data.code === 0) {
          this.uploadedUrl = response.data.data.url;
          this.$message.success('上传成功');
        } else {
          this.$message.error(response.data.message);
        }
      } catch (error) {
        this.$message.error('上传失败: ' + error.message);
      } finally {
        this.uploading = false;
      }
    }
  }
}
</script>
```

## 错误处理

### 常见错误及解决方案

1. **"资源分组不存在"**
   - 确保传入的groupId是有效的分组ID
   - 检查分组是否已被删除

2. **"文件已存在"**
   - 系统通过MD5 hash检测重复文件
   - 如需强制上传，可先删除已存在的文件

3. **"不支持的文件类型"**
   - 检查传入的file参数类型
   - 确保使用支持的类型：`*multipart.FileHeader`, `[]byte`, `io.Reader`, `string`

4. **"读取文件失败"**
   - 检查文件路径是否正确
   - 确保应用有读取文件的权限

## 注意事项

1. **文件大小限制**: 建议在前端和后端都设置合理的文件大小限制
2. **文件类型验证**: 可以在业务层添加文件类型验证
3. **存储空间**: 定期清理无用文件，避免存储空间不足
4. **权限控制**: 确保用户有上传权限
5. **安全性**: 对上传的文件进行安全检查，防止恶意文件

## 扩展功能

### 自定义文件名

```go
// 可以在上传前修改fileName
func UploadWithCustomName(ctx context.Context, groupId int64, data []byte, customName string) error {
    // 这里可以扩展UploadFile方法来支持自定义文件名
    // 或者在上传后更新数据库记录
    return nil
}
```

### 批量上传

```go
// 批量上传文件
func BatchUpload(ctx context.Context, groupId int64, files []interface{}) ([]string, error) {
    var urls []string
    
    for _, file := range files {
        resource, err := service.SysResources().UploadFile(ctx, groupId, file)
        if err != nil {
            return nil, err
        }
        urls = append(urls, resource.Url)
    }
    
    return urls, nil
}
```

这个文件上传功能现在支持多种输入类型，可以满足各种使用场景的需求！
