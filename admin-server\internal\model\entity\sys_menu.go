// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SysMenu is the golang structure for table sys_menu.
type SysMenu struct {
	Id        int64       `json:"id"        orm:"id"         description:""`                     //
	Pid       int64       `json:"pid"       orm:"pid"        description:"上级菜单id"`               // 上级菜单id
	MenuType  string      `json:"menuType"  orm:"menu_type"  description:"菜单类型: M=目录，C=菜单，A=按钮"` // 菜单类型: M=目录，C=菜单，A=按钮
	MenuName  string      `json:"menuName"  orm:"menu_name"  description:"菜单名称"`                 // 菜单名称
	MenuIcon  string      `json:"menuIcon"  orm:"menu_icon"  description:"菜单图表"`                 // 菜单图表
	MenuSort  int         `json:"menuSort"  orm:"menu_sort"  description:"菜单排序"`                 // 菜单排序
	Perms     string      `json:"perms"     orm:"perms"      description:"权限标识"`                 // 权限标识
	Paths     string      `json:"paths"     orm:"paths"      description:"路由地址"`                 // 路由地址
	Component string      `json:"component" orm:"component"  description:"前端组件"`                 // 前端组件
	Params    string      `json:"params"    orm:"params"     description:"路由参数"`                 // 路由参数
	IsCache   int         `json:"isCache"   orm:"is_cache"   description:"是否缓存: 0=否, 1=是"`       // 是否缓存: 0=否, 1=是
	IsShow    int         `json:"isShow"    orm:"is_show"    description:"是否显示: 0=否, 1=是"`       // 是否显示: 0=否, 1=是
	IsDisable int         `json:"isDisable" orm:"is_disable" description:"是否禁用: 0=否, 1=是"`       // 是否禁用: 0=否, 1=是
	IsDelete  int         `json:"isDelete"  orm:"is_delete"  description:"是否删除: 0=否, 1=是"`       // 是否删除: 0=否, 1=是
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`                 // 创建时间
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:"更新时间"`                 // 更新时间
	DeletedAt *gtime.Time `json:"deletedAt" orm:"deleted_at" description:"删除时间"`                 // 删除时间
}
