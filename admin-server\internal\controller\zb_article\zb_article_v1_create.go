package zb_article

import (
	"context"
	"encoding/json"

	v1 "admin-server/api/zb_article/v1"
	"admin-server/internal/model/entity"
	"admin-server/internal/service"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/net/ghttp"
)

// processJSONField 处理JSON字段
func processJSONField(data interface{}) (string, error) {
	if data == nil {
		return "", nil
	}

	// 如果已经是字符串，直接返回
	if str, ok := data.(string); ok {
		return str, nil
	}

	// 转换为JSON字符串
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return "", gerror.Wrap(err, "JSON序列化失败")
	}

	return string(jsonBytes), nil
}

func (c *ControllerV1) Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error) {
	// 获取客户端IP
	request := ghttp.RequestFromCtx(ctx)
	clientIP := request.GetClientIp()

	// 处理JSON字段
	fullContentStr, err := processJSONField(req.FullContent)
	if err != nil {
		return nil, err
	}

	shieidContentStr, err := processJSONField(req.ShieidContent)
	if err != nil {
		return nil, err
	}

	// 当前操作者id
	adminId := ctx.Value("admin_id").(int)
	// 构建信息数据
	article := &entity.ZbArticle{
		CityId:         req.CityId,
		CateId:         req.CateId,
		Title:          req.Title,
		Intro:          req.Intro,
		FullContent:    fullContentStr,
		ShieidContent:  shieidContentStr,
		SeoTitle:       req.SeoTitle,
		SeoKeywords:    req.SeoKeywords,
		SeoDescription: req.SeoDescription,
		Uid:            adminId,
		Pic:            req.Pic,
		Author:         req.Author,
		Ip:             clientIP,
		IsDisable:      req.IsDisable,
		ViewCount:      0,
	}

	// 创建信息
	id, err := service.ZbArticle().CreateArticle(ctx, article)
	if err != nil {
		return nil, err
	}

	return &v1.CreateRes{
		ID: id,
	}, nil
}
