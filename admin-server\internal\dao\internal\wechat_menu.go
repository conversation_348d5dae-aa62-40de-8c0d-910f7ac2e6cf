// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// WechatMenuDao is the data access object for the table wechat_menu.
type WechatMenuDao struct {
	table    string                // table is the underlying table name of the DAO.
	group    string                // group is the database configuration group name of the current DAO.
	columns  WechatMenuColumns     // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler    // handlers for customized model modification.
}

// WechatMenuColumns defines and stores column names for the table wechat_menu.
type WechatMenuColumns struct {
	Id        string //
	Pid       string // 父菜单ID，0表示一级菜单
	MenuName  string // 菜单名称
	MenuType  string // 菜单类型
	MenuKey   string // 菜单KEY值
	MenuUrl   string // 菜单链接
	Appid     string // 小程序AppID
	Pagepath  string // 小程序页面路径
	Sort      string // 排序
	Level     string // 菜单层级：1=一级菜单，2=二级菜单
	IsDisable string // 是否禁用: 0=否, 1=是
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
}

// wechatMenuColumns holds the columns for the table wechat_menu.
var wechatMenuColumns = WechatMenuColumns{
	Id:        "id",
	Pid:       "pid",
	MenuName:  "menu_name",
	MenuType:  "menu_type",
	MenuKey:   "menu_key",
	MenuUrl:   "menu_url",
	Appid:     "appid",
	Pagepath:  "pagepath",
	Sort:      "sort",
	Level:     "level",
	IsDisable: "is_disable",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// NewWechatMenuDao creates and returns a new DAO object for table data access.
func NewWechatMenuDao(handlers ...gdb.ModelHandler) *WechatMenuDao {
	return &WechatMenuDao{
		group:    "default",
		table:    "wechat_menu",
		columns:  wechatMenuColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *WechatMenuDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *WechatMenuDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *WechatMenuDao) Columns() WechatMenuColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *WechatMenuDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *WechatMenuDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *WechatMenuDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
