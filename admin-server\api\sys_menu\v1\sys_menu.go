package v1

import (
	"admin-server/internal/packed"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// CreateReq 创建菜单请求体
type CreateReq struct {
	g.Meta    `path:"/sys_menu/create" method:"post" tags:"SysMenu" summary:"创建菜单"`
	Pid       int64           `p:"pid" v:"required#上级菜单ID不能为空" dc:"上级菜单ID"`
	MenuType  packed.MenuType `p:"menu_type" v:"required|in:M,C,A#菜单类型不能为空|菜单类型必须是M,C,A" dc:"菜单类型"`
	MenuName  string          `p:"menu_name" v:"required|length:1,50#菜单名称不能为空|菜单名称长度为1-50位" dc:"菜单名称"`
	MenuIcon  string          `p:"menu_icon" dc:"菜单图标"`
	MenuSort  int             `p:"menu_sort" d:"1" v:"min:0#排序值不能小于0" dc:"菜单排序"`
	Perms     string          `p:"perms" v:"length:1,100#权限标识长度为1-100位" dc:"权限标识（目录类型可选，菜单和按钮类型必填）"`
	Paths     string          `p:"paths" dc:"路由地址"`
	Component string          `p:"component" dc:"前端组件"`
	Params    string          `p:"params" dc:"路由参数"`
	IsCache   *packed.IsCache `p:"is_cache" v:"in:0,1" dc:"是否缓存"`
	IsShow    *packed.IsShow  `p:"is_show" v:"in:0,1" dc:"是否显示"`
	IsDisable *packed.Disable `p:"is_disable" v:"in:0,1" dc:"是否禁用"`
}

type CreateRes struct {
	ID int64 `json:"id" dc:"菜单ID"`
}

// DeleteReq 删除菜单请求体
type DeleteReq struct {
	g.Meta `path:"/sys_menu/{id}" method:"delete" tags:"SysMenu" summary:"删除菜单"`
	ID     int64 `p:"id" v:"required#请选择需要删除的菜单" dc:"菜单ID"`
}
type DeleteRes struct{}

// UpdateReq 更新菜单请求体
type UpdateReq struct {
	g.Meta    `path:"/sys_menu/{id}" method:"put" tags:"SysMenu" summary:"更新菜单信息"`
	ID        int64           `p:"id" v:"required#请选择需要更新的菜单" dc:"菜单ID"`
	Pid       int64           `p:"pid" v:"required#上级菜单ID不能为空" dc:"上级菜单ID"`
	MenuType  packed.MenuType `p:"menu_type" v:"required|in:M,C,A#菜单类型不能为空|菜单类型必须是M,C,A" dc:"菜单类型"`
	MenuName  string          `p:"menu_name" v:"required|length:1,50#菜单名称不能为空|菜单名称长度为1-50位" dc:"菜单名称"`
	MenuIcon  string          `p:"menu_icon" dc:"菜单图标"`
	MenuSort  int             `p:"menu_sort" d:"1" v:"min:0#排序值不能小于0" dc:"菜单排序"`
	Perms     string          `p:"perms" v:"length:1,100#权限标识长度为1-100位" dc:"权限标识（目录类型可选，菜单和按钮类型必填）"`
	Paths     string          `p:"paths" dc:"路由地址"`
	Component string          `p:"component" dc:"前端组件"`
	Params    string          `p:"params" dc:"路由参数"`
	IsCache   *packed.IsCache `p:"is_cache" v:"in:0,1" dc:"是否缓存"`
	IsShow    *packed.IsShow  `p:"is_show" v:"in:0,1" dc:"是否显示"`
	IsDisable *packed.Disable `p:"is_disable" v:"in:0,1" dc:"是否禁用"`
}
type UpdateRes struct{}

// GetListReq 获取菜单列表请求体
type GetListReq struct {
	g.Meta    `path:"/sys_menu/list" tags:"SysMenu" method:"get" summary:"获取菜单列表"`
	Page      int             `p:"page" d:"1" v:"min:1#页码最小为1" dc:"页码"`
	PageSize  int             `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间" dc:"每页数量"`
	MenuName  string          `p:"menu_name" dc:"菜单名称（模糊搜索）"`
	MenuType  packed.MenuType `p:"menu_type" v:"in:M,C,A" dc:"菜单类型"`
	IsShow    *packed.IsShow  `p:"is_show" v:"in:0,1" dc:"是否显示"`
	IsDisable *packed.Disable `p:"is_disable" v:"in:0,1" dc:"是否禁用"`
}

// MenuInfo 菜单信息
type MenuInfo struct {
	ID        int64           `json:"id" dc:"菜单ID"`
	Pid       int64           `json:"pid" dc:"上级菜单ID"`
	MenuType  packed.MenuType `json:"menu_type" dc:"菜单类型"`
	MenuName  string          `json:"menu_name" dc:"菜单名称"`
	MenuIcon  string          `json:"menu_icon" dc:"菜单图标"`
	MenuSort  int             `json:"menu_sort" dc:"菜单排序"`
	Perms     string          `json:"perms" dc:"权限标识"`
	Paths     string          `json:"paths" dc:"路由地址"`
	Component string          `json:"component" dc:"前端组件"`
	Params    string          `json:"params" dc:"路由参数"`
	IsCache   packed.IsCache  `json:"is_cache" dc:"是否缓存"`
	IsShow    packed.IsShow   `json:"is_show" dc:"是否显示"`
	IsDisable packed.Disable  `json:"is_disable" dc:"是否禁用"`
	CreatedAt *gtime.Time     `json:"created_at" dc:"创建时间"`
	UpdatedAt *gtime.Time     `json:"updated_at" dc:"更新时间"`
}

type GetListRes struct {
	List     []*MenuInfo `json:"list" dc:"菜单列表"`
	Total    int         `json:"total" dc:"总数"`
	Page     int         `json:"page" dc:"当前页码"`
	PageSize int         `json:"page_size" dc:"每页数量"`
}

// GetTreeReq 获取菜单树请求体
type GetTreeReq struct {
	g.Meta    `path:"/sys_menu/tree" tags:"SysMenu" method:"get" summary:"获取菜单树"`
	IsShow    *packed.IsShow  `p:"is_show" v:"in:0,1" dc:"是否显示"`
	IsDisable *packed.Disable `p:"is_disable" v:"in:0,1" dc:"是否禁用"`
}

// MenuTree 菜单树节点
type MenuTree struct {
	*MenuInfo
	Children []*MenuTree `json:"children" dc:"子菜单"`
}

type GetTreeRes struct {
	Tree []*MenuTree `json:"tree" dc:"菜单树"`
}

// GetOneReq 获取单个菜单信息请求体
type GetOneReq struct {
	g.Meta `path:"/sys_menu/{id}" tags:"SysMenu" method:"get" summary:"获取单个菜单信息"`
	ID     int64 `p:"id" v:"required#请选择需要查询的菜单" dc:"菜单ID"`
}
type GetOneRes struct {
	*MenuInfo `dc:"菜单信息"`
}

// UpdateSortReq 更新字典项排序请求体
type UpdateSortReq struct {
	g.Meta `path:"/sys_menu/sort/{id}" method:"put" tags:"SysMenu" summary:"更新菜单排序"`
	ID     int64 `p:"id" v:"required#请选择需要更新的记录" dc:"菜单ID"`
	Sort   int   `p:"sort" v:"required#请输入排序值" dc:"排序"`
}

type UpdateSortRes struct{}
