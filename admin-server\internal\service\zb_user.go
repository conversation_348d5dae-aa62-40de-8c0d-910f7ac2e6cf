package service

import (
	"admin-server/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/os/gtime"
)

// IZbUser 会员服务接口
type IZbUser interface {
	// GetUserList 获取会员列表
	GetUserList(ctx context.Context, page, pageSize int, nickname, openid string, isDisable, vipStatus, hasVipPeriod int, startTime, endTime string) (list []entity.ZbUser, total int, err error)

	// GetUserDetail 获取会员详情
	GetUserDetail(ctx context.Context, id int64) (*entity.ZbUser, error)

	// UpdateUser 更新会员信息
	UpdateUser(ctx context.Context, id int64, nickname, avatar string, isDisable *int, effectiveStart, effectiveEnd string) error

	// DeleteUser 删除会员（软删除）
	DeleteUser(ctx context.Context, id int64) error

	// BatchDeleteUser 批量删除会员（软删除）
	BatchDeleteUser(ctx context.Context, ids []int64) error

	// SetUserStatus 设置会员状态
	SetUserStatus(ctx context.Context, id int64, isDisable int) error

	// UpdateVipPeriod 更新会员VIP有效期
	UpdateVipPeriod(ctx context.Context, id int64, effectiveStart, effectiveEnd string) error

	// CheckUserExists 检查会员是否存在
	CheckUserExists(ctx context.Context, id int64) (bool, error)

	// GetUserByOpenid 根据OpenID获取会员
	GetUserByOpenid(ctx context.Context, openid string) (*entity.ZbUser, error)

	// UpdateUserLoginTime 更新会员登录时间
	UpdateUserLoginTime(ctx context.Context, id int64) error

	// GetVipUsers 获取VIP会员列表
	GetVipUsers(ctx context.Context, page, pageSize int) (list []entity.ZbUser, total int, err error)

	// GetExpiredVipUsers 获取即将过期的VIP会员
	GetExpiredVipUsers(ctx context.Context, days int) ([]entity.ZbUser, error)

	// CreateFromWechat 从微信信息创建用户
	CreateFromWechat(ctx context.Context, openid, nickname, avatar, clientIP string) (*entity.ZbUser, error)

	// GetById 根据ID获取用户信息
	GetById(ctx context.Context, id int64) (*entity.ZbUser, error)

	// UpdateLastLogin 更新用户最后登录信息
	UpdateLastLogin(ctx context.Context, userId int64, clientIP string) error

	// CalculateVipStatus 计算VIP状态
	CalculateVipStatus(ctx context.Context, effectiveStart, effectiveEnd *gtime.Time) (vipStatus int, daysLeft int)

	// GetUserStats 获取会员统计信息
	GetUserStats(ctx context.Context) (totalUsers, vipUsers, disabledUsers int, err error)
}

var localZbUser IZbUser

func ZbUser() IZbUser {
	if localZbUser == nil {
		panic("IZbUser接口未实现或未注册")
	}
	return localZbUser
}

func RegisterZbUser(i IZbUser) {
	localZbUser = i
}
