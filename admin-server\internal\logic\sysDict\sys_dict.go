package sysDict

import (
	"admin-server/internal/dao"
	"admin-server/internal/model/do"
	"admin-server/internal/model/entity"
	"admin-server/internal/packed"
	"admin-server/internal/service"
	"context"

	"github.com/gogf/gf/v2/errors/gerror"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

type sSysDict struct{}

func init() {
	service.RegisterSysDict(&SsysDict{})
}

type SsysDict struct {
}

// GetDictList 获取字典项列表
func (s *SsysDict) GetDictList(ctx context.Context, page, pageSize int, groupId int64, name, code string, isDisable int) (list []entity.SysDict, total int, err error) {
	query := dao.SysDict.Ctx(ctx).Where("is_delete", packed.NO_DELETE)

	// 按分组筛选
	if groupId > 0 {
		query = query.Where("group_id", groupId)
	}

	// 按名称筛选
	if name != "" {
		query = query.WhereLike("name", "%"+name+"%")
	}

	// 按编码筛选
	if code != "" {
		query = query.WhereLike("code", "%"+code+"%")
	}

	// 按状态筛选
	if isDisable >= 0 {
		query = query.Where("is_disable", isDisable)
	}

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.OrderAsc("sort").OrderDesc("created_at").Limit(offset, pageSize).Scan(&list)
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

// GetDictDetail 获取字典项详情
func (s *SsysDict) GetDictDetail(ctx context.Context, id int64) (*entity.SysDict, error) {
	var dict entity.SysDict
	err := dao.SysDict.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Scan(&dict)
	if err != nil {
		return nil, err
	}

	if dict.Id == 0 {
		return nil, nil
	}

	return &dict, nil
}

// GetDictDetailByCode 根据字典编码获取字典项详情
func (s *SsysDict) GetDictDetailByCode(ctx context.Context, code string) (*entity.SysDict, error) {
	var dict entity.SysDict
	err := dao.SysDict.Ctx(ctx).Where("code", code).Where("is_delete", packed.NO_DELETE).Scan(&dict)
	if err != nil {
		return nil, err
	}

	if dict.Id == 0 {
		return nil, nil
	}

	return &dict, nil
}

// CreateDict 创建字典项
func (s *SsysDict) CreateDict(ctx context.Context, groupId int64, name, value, code, remark string, sort int, isDisable packed.Disable) error {
	// 检查分组是否存在
	dictGroup, err := service.SysDictGroup().GetDictGroupDetail(ctx, groupId)
	if err != nil {
		return err
	}
	if dictGroup == nil {
		return gerror.New("字典分组不存在")
	}

	// 检查编码是否已存在
	exists, err := s.CheckDictCodeExists(ctx, groupId, code, 0)
	if err != nil {
		return err
	}
	if exists {
		return gerror.New("字典项编码在该分组中已存在")
	}

	dict := &do.SysDict{
		GroupId:   groupId,
		Name:      name,
		Value:     value,
		Code:      code,
		Sort:      sort,
		Remark:    remark,
		IsDisable: isDisable,
		IsDelete:  packed.NO_DELETE,
		IsSystem:  packed.NO_SYSTEM,
		CreatedAt: gtime.Now(),
		UpdatedAt: gtime.Now(),
	}

	_, err = dao.SysDict.Ctx(ctx).Insert(dict)
	if err != nil {
		g.Log().Error(ctx, "创建字典项失败:", err)
		return err
	}

	g.Log().Info(ctx, "创建字典项成功:", "name:", name, "code:", code)
	return nil
}

// UpdateDict 更新字典项
func (s *SsysDict) UpdateDict(ctx context.Context, id int64, groupId int64, name, value, code, remark string, sort int, isDisable packed.Disable) error {
	// 检查字典项是否存在
	dict, err := s.GetDictDetail(ctx, id)
	if err != nil {
		return err
	}
	if dict == nil {
		return gerror.New("字典项不存在")
	}

	// 检查是否为系统保留
	if dict.IsSystem == packed.YES_SYSTEM {
		return gerror.New("系统保留字典项不允许修改")
	}

	// 检查分组是否存在
	dictGroup, err := service.SysDictGroup().GetDictGroupDetail(ctx, groupId)
	if err != nil {
		return err
	}
	if dictGroup == nil {
		return gerror.New("字典分组不存在")
	}

	// 检查编码是否已存在（排除自己）
	exists, err := s.CheckDictCodeExists(ctx, groupId, code, id)
	if err != nil {
		return err
	}
	if exists {
		return gerror.New("字典项编码在该分组中已存在")
	}

	updateData := &do.SysDict{
		GroupId:   groupId,
		Name:      name,
		Value:     value,
		Code:      code,
		Sort:      sort,
		Remark:    remark,
		IsDisable: isDisable,
		UpdatedAt: gtime.Now(),
	}

	_, err = dao.SysDict.Ctx(ctx).Where("id", id).Update(updateData)
	if err != nil {
		g.Log().Error(ctx, "更新字典项失败:", err)
		return err
	}

	g.Log().Info(ctx, "更新字典项成功:", "id:", id, "name:", name)
	return nil
}

// DeleteDict 删除字典项
func (s *SsysDict) DeleteDict(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	// 检查是否有系统保留的字典项
	var systemDicts []entity.SysDict
	err := dao.SysDict.Ctx(ctx).WhereIn("id", ids).Where("is_system", packed.YES_SYSTEM).Scan(&systemDicts)
	if err != nil {
		return err
	}
	if len(systemDicts) > 0 {
		return gerror.New("系统保留字典项不允许删除")
	}

	// 软删除
	_, err = dao.SysDict.Ctx(ctx).WhereIn("id", ids).Update(do.SysDict{
		IsDelete:  packed.IS_DELETE,
		DeletedAt: gtime.Now(),
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "删除字典项失败:", err)
		return err
	}

	g.Log().Info(ctx, "删除字典项成功:", "ids:", ids)
	return nil
}

// SetDictStatus 设置字典项状态
func (s *SsysDict) SetDictStatus(ctx context.Context, id int64, isDisable int) error {
	// 检查字典项是否存在
	dict, err := s.GetDictDetail(ctx, id)
	if err != nil {
		return err
	}
	if dict == nil {
		return gerror.New("字典项不存在")
	}

	// 检查是否为系统保留
	if dict.IsSystem == packed.YES_SYSTEM {
		return gerror.New("系统保留字典项不允许修改状态")
	}

	_, err = dao.SysDict.Ctx(ctx).Where("id", id).Update(do.SysDict{
		IsDisable: isDisable,
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "设置字典项状态失败:", err)
		return err
	}

	status := "启用"
	if isDisable == int(packed.DISABLE) {
		status = "禁用"
	}
	g.Log().Info(ctx, "设置字典项状态成功:", "id:", id, "status:", status)
	return nil
}

// GetDictsByGroupCode 根据分组编码获取字典项
func (s *SsysDict) GetDictsByGroupCode(ctx context.Context, groupCode string) ([]entity.SysDict, error) {
	var list []entity.SysDict
	err := dao.SysDict.Ctx(ctx).
		LeftJoin("sys_dict_group", "sys_dict.group_id = sys_dict_group.id").
		Where("sys_dict_group.code", groupCode).
		Where("sys_dict.is_delete", packed.NO_DELETE).
		Where("sys_dict.is_disable", packed.ENABLE).
		Where("sys_dict_group.is_delete", packed.NO_DELETE).
		Where("sys_dict_group.is_disable", packed.ENABLE).
		OrderAsc("sys_dict.sort").
		OrderDesc("sys_dict.created_at").
		Scan(&list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

// GetDictsByGroupId 根据分组ID获取字典项
func (s *SsysDict) GetDictsByGroupId(ctx context.Context, groupId int64) ([]entity.SysDict, error) {
	var list []entity.SysDict
	err := dao.SysDict.Ctx(ctx).
		Where("group_id", groupId).
		Where("is_delete", packed.NO_DELETE).
		Where("is_disable", packed.ENABLE).
		OrderAsc("sort").
		OrderDesc("created_at").
		Scan(&list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

// CheckDictCodeExists 检查字典项编码是否存在
func (s *SsysDict) CheckDictCodeExists(ctx context.Context, groupId int64, code string, excludeId int64) (bool, error) {
	query := dao.SysDict.Ctx(ctx).Where("group_id", groupId).Where("code", code).Where("is_delete", packed.NO_DELETE)
	if excludeId > 0 {
		query = query.WhereNot("id", excludeId)
	}

	count, err := query.Count()
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// UpdateDictSort 更新字典项排序
func (s *SsysDict) UpdateDictSort(ctx context.Context, id int64, sort int) error {
	_, err := dao.SysDict.Ctx(ctx).Where("id", id).Update(do.SysDict{
		Sort:      sort,
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "更新字典项排序失败:", err)
		return err
	}

	g.Log().Info(ctx, "更新字典项排序成功:", "id:", id, "sort:", sort)
	return nil
}
