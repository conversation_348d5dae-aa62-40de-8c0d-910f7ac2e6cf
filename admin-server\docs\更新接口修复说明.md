# 更新接口修复说明

## 问题描述

在使用信息更新接口 `PUT /zb_article/update/{id}` 时，发现创建时间（`created_at`）被更新为 `null`，这是因为原有的更新逻辑使用了整个 entity 对象进行更新，导致未设置的字段被更新为零值。

## 问题原因

### 原有的错误实现

```go
// 错误的更新方式
func (s *sZbArticle) UpdateArticle(ctx context.Context, id int64, data *entity.ZbArticle) error {
    // 设置更新时间
    data.UpdatedAt = gtime.Now()
    data.Id = int(id)

    // 直接使用整个entity对象更新，会导致未设置的字段被更新为零值
    _, err = dao.ZbArticle.Ctx(ctx).Where("id", id).Data(data).Update()
    return err
}
```

### 问题分析

1. **整体更新问题**: 使用整个 `entity.ZbArticle` 对象进行更新
2. **零值覆盖**: 未在请求中提供的字段（如 `created_at`、`uid`、`ip` 等）会被设置为零值
3. **数据丢失**: 系统字段被意外修改，导致数据完整性问题

## 修复方案

### 1. 新增灵活的更新方法

在 Service 接口中新增支持部分更新的方法：

```go
// UpdateArticleFields 更新信息字段（支持部分更新）
UpdateArticleFields(ctx context.Context, id int64, fields map[string]interface{}) error
```

### 2. Logic层实现

```go
// UpdateArticleFields 更新信息字段（支持部分更新）
func (s *sZbArticle) UpdateArticleFields(ctx context.Context, id int64, fields map[string]interface{}) error {
    // 检查信息是否存在
    exists, err := s.CheckArticleExists(ctx, id)
    if err != nil {
        return err
    }
    if !exists {
        return gerror.New("信息不存在")
    }

    // 添加更新时间
    fields["updated_at"] = gtime.Now()

    // 执行更新
    _, err = dao.ZbArticle.Ctx(ctx).Where("id", id).Data(fields).Update()
    if err != nil {
        return gerror.Wrap(err, "更新信息失败")
    }

    return nil
}
```

### 3. Controller层优化

```go
func (c *ControllerV1) Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error) {
    // 处理JSON字段
    fullContentStr, err := processJSONFieldForUpdate(req.FullContent)
    if err != nil {
        return nil, err
    }

    shieidContentStr, err := processJSONFieldForUpdate(req.ShieidContent)
    if err != nil {
        return nil, err
    }

    // 构建更新字段映射，只包含业务字段
    updateFields := map[string]interface{}{
        "city_id":         req.CityId,
        "cate_id":         req.CateId,
        "title":           req.Title,
        "intro":           req.Intro,
        "full_content":    fullContentStr,
        "shieid_content":  shieidContentStr,
        "seo_title":       req.SeoTitle,
        "seo_keywords":    req.SeoKeywords,
        "seo_description": req.SeoDescription,
        "pic":             req.Pic,
        "author":          req.Author,
        "is_disable":      req.IsDisable,
    }

    // 更新信息
    err = service.ZbArticle().UpdateArticleFields(ctx, req.ID, updateFields)
    if err != nil {
        return nil, err
    }

    return &v1.UpdateRes{}, nil
}
```

## 修复效果

### 修复前的问题

```sql
-- 错误的更新SQL（会更新所有字段）
UPDATE zb_article SET 
  city_id=1, cate_id=1, title='新标题', intro='新简介',
  full_content='新内容', shieid_content='', seo_title='',
  seo_keywords='', seo_description='', pic='', author='',
  is_disable=0, view_count=0, uid=0, ip='', 
  created_at=NULL, updated_at='2025-07-19 12:00:00',  -- ❌ created_at被设为NULL
  deleted_at=NULL, is_delete=0
WHERE id=1;
```

### 修复后的效果

```sql
-- 正确的更新SQL（只更新提交的字段）
UPDATE zb_article SET 
  city_id=1, cate_id=1, title='新标题', intro='新简介',
  full_content='新内容', shieid_content='新屏蔽内容',
  seo_title='新SEO标题', seo_keywords='新关键词',
  seo_description='新描述', pic='新图片', author='新作者',
  is_disable=0, updated_at='2025-07-19 12:00:00'  -- ✅ 只更新业务字段
WHERE id=1;
```

## 字段保护策略

### 受保护的系统字段

以下字段在更新时会被保护，不会被修改：

- `id`: 主键ID
- `created_at`: 创建时间
- `deleted_at`: 删除时间
- `is_delete`: 删除标记
- `uid`: 创建者ID
- `ip`: 创建者IP
- `view_count`: 浏览次数（通过专门接口更新）

### 可更新的业务字段

以下字段可以通过更新接口修改：

- `city_id`: 城市ID
- `cate_id`: 类别ID
- `title`: 标题
- `intro`: 简介
- `full_content`: 完整内容（JSON格式）
- `shieid_content`: 屏蔽内容（JSON格式）
- `seo_title`: SEO标题
- `seo_keywords`: SEO关键词
- `seo_description`: SEO描述
- `pic`: 缩略图
- `author`: 作者
- `is_disable`: 禁用状态
- `updated_at`: 更新时间（自动设置）

## 测试验证

### 测试用例

```bash
# 更新信息
curl -X PUT "http://localhost:8000/zb_article/update/1" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "city_id": 1,
    "cate_id": 1,
    "title": "更新后的标题",
    "intro": "更新后的简介",
    "full_content": {
      "type": "rich_text",
      "content": [{"type": "paragraph", "text": "更新后的内容"}]
    },
    "author": "更新者",
    "is_disable": 0
  }'
```

### 验证结果

更新后查询信息详情，应该看到：

- ✅ `created_at` 保持原值，不会变为 null
- ✅ `uid` 和 `ip` 保持原值
- ✅ `view_count` 保持原值
- ✅ `updated_at` 更新为当前时间
- ✅ 业务字段按请求参数更新

## 最佳实践

### 1. 字段分类管理

- **系统字段**: 由系统自动管理，不允许手动更新
- **业务字段**: 可以通过API更新
- **统计字段**: 通过专门的接口更新（如浏览次数）

### 2. 更新策略

- **部分更新**: 只更新提交的字段
- **字段验证**: 在Controller层进行参数验证
- **时间戳**: 自动更新 `updated_at` 字段

### 3. 安全考虑

- **权限验证**: 确保用户有更新权限
- **数据验证**: 验证字段格式和长度
- **审计日志**: 记录更新操作（如需要）

## 总结

通过这次修复：

1. **解决了数据丢失问题**: 系统字段不再被意外覆盖
2. **提高了更新精度**: 只更新提交的业务字段
3. **增强了数据安全性**: 保护重要的系统字段
4. **优化了性能**: 减少不必要的字段更新
5. **提升了可维护性**: 清晰的字段分类和更新策略

现在更新接口可以安全地使用，不会再出现创建时间被设为null的问题。
