package zb_cate

import (
	v1 "admin-server/api/zb_cate/v1"
	"context"
)

type IZbCateV1 interface {
	Create(ctx context.Context, req *v1.ZbCateCreateReq) (res *v1.ZbCateCreateRes, err error)
	Update(ctx context.Context, req *v1.ZbCateUpdateReq) (res *v1.ZbCateUpdateRes, err error)
	Delete(ctx context.Context, req *v1.ZbCateDeleteReq) (res *v1.ZbCateDeleteRes, err error)
	GetOne(ctx context.Context, req *v1.ZbCateGetOneReq) (res *v1.ZbCateGetOneRes, err error)
	GetList(ctx context.Context, req *v1.ZbCateGetListReq) (res *v1.ZbCateGetListRes, err error)
	GetAll(ctx context.Context, req *v1.ZbCateGetAllReq) (res *v1.ZbCateGetAllRes, err error)
	UpdateSort(ctx context.Context, req *v1.ZbCateUpdateSortReq) (res *v1.ZbCateUpdateSortRes, err error)
	UpdateStatus(ctx context.Context, req *v1.ZbCateUpdateStatusReq) (res *v1.ZbCateUpdateStatusRes, err error)
	BatchDelete(ctx context.Context, req *v1.ZbCateBatchDeleteReq) (res *v1.ZbCateBatchDeleteRes, err error)
}
