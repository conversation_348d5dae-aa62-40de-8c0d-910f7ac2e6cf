// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package sys_config

import (
	"context"

	"admin-server/api/sys_config/v1"
)

type ISysConfigV1 interface {
	Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error)
	Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error)
	Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error)
	GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error)
	GetOne(ctx context.Context, req *v1.GetOneReq) (res *v1.GetOneRes, err error)
	GetByGroup(ctx context.Context, req *v1.GetByGroupReq) (res *v1.GetByGroupRes, err error)
	UpdateValue(ctx context.Context, req *v1.UpdateValueReq) (res *v1.UpdateValueRes, err error)
	GetByKey(ctx context.Context, req *v1.GetByKeyReq) (res *v1.GetByKeyRes, err error)
}
