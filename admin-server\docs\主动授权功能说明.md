# 主动授权功能说明

## 功能概述

重新设计了微信授权流程，改为用户主动授权的方式。用户可以先浏览详情页面，然后根据需要选择是否进行微信授权登录。

## 实现的改进

### 1. 用户体验优化
- **无强制授权**: 用户可以直接访问详情页面，不会被强制跳转到授权页面
- **主动选择**: 用户可以根据需要主动选择是否登录
- **友好提示**: 页面顶部显示登录状态和登录提示
- **便捷登录**: 提供一键登录按钮和登录弹窗

### 2. 页面状态展示
- **未登录状态**: 显示蓝色提示条，提醒用户登录可查看完整信息
- **已登录状态**: 显示绿色欢迎信息，展示用户昵称和会员状态
- **VIP状态**: 区分普通用户和VIP用户，显示不同的标识

### 3. 登录流程优化
- **新增登录路由**: `/m/auth/login` - 主动授权登录
- **保持回调路由**: `/m/auth/callback` - 授权回调处理
- **智能回调**: 登录成功后自动返回原页面

## 路由配置

### 页面路由
```go
// 手机端页面路由（无需强制授权）
group.GET("/list", mobileController.List)      // 列表页
group.GET("/detail", mobileController.Detail)  // 详情页

// 微信授权相关路由
group.GET("/auth/login", mobileController.WechatLogin)      // 主动授权登录
group.GET("/auth/callback", mobileController.AuthCallback) // 授权回调
```

### API路由
```go
// 手机端API（无需token验证）
group.GET("/zb_cate/all", zb_cate.NewV1().GetAll) // 招标类别
```

## 页面功能

### 1. 详情页面状态展示

#### 未登录状态
```html
<div class="bg-gradient-to-r from-blue-50 to-purple-50 border-l-4 border-blue-500 p-4 mx-4 mt-4 rounded-lg">
    <div class="flex items-center justify-between">
        <div class="flex items-center">
            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
            <span class="text-sm text-gray-700">登录后可查看完整招标信息</span>
        </div>
        <button onclick="showLoginModal()" class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium">
            立即登录
        </button>
    </div>
</div>
```

#### 已登录状态
```html
<div class="bg-green-50 border-l-4 border-green-500 p-4 mx-4 mt-4 rounded-lg">
    <div class="flex items-center">
        <i class="fas fa-check-circle text-green-500 mr-2"></i>
        <span class="text-sm text-gray-700">欢迎回来，{{.user.nickname}}！</span>
        {{if eq .user.effective_status 1}}
        <span class="ml-2 bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">VIP会员</span>
        {{else}}
        <span class="ml-2 bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs font-medium">普通用户</span>
        {{end}}
    </div>
</div>
```

### 2. 登录弹窗
- **触发方式**: 点击"立即登录"按钮
- **弹窗内容**: 微信授权登录说明和按钮
- **操作选项**: "微信授权登录" 或 "稍后再说"
- **用户友好**: 用户可以选择关闭弹窗继续浏览

### 3. 登录流程

#### 用户操作流程
1. 访问 `/m/detail` 页面
2. 查看页面内容（无需登录）
3. 看到登录提示，点击"立即登录"
4. 弹出登录确认弹窗
5. 点击"微信授权登录"
6. 跳转到微信授权页面
7. 确认授权后返回原页面
8. 页面显示登录成功状态

#### 技术实现流程
1. 用户点击登录 → `/m/auth/login?callback=/m/detail`
2. 构建微信授权URL → 跳转到微信授权页面
3. 用户确认授权 → 微信回调到 `/m/auth/callback`
4. 处理用户信息 → 注册或登录用户
5. 存储到Session → 重定向回原页面
6. 页面检测登录状态 → 显示用户信息

## 控制器方法

### 1. WechatLogin 方法
```go
func (c *ControllerMobile) WechatLogin(r *ghttp.Request) {
    // 获取回调URL参数
    callbackURL := r.Get("callback").String()
    if callbackURL == "" {
        callbackURL = "/m/detail" // 默认回调到详情页
    }
    
    // 构建完整的回调URL
    fullCallbackURL := buildFullURL(r, callbackURL)
    
    // 构建微信授权URL并跳转
    authURL, err := buildWechatAuthURL(fullCallbackURL)
    if err != nil {
        // 错误处理
        return
    }
    
    r.Response.RedirectTo(authURL)
}
```

### 2. AuthCallback 方法
```go
func (c *ControllerMobile) AuthCallback(r *ghttp.Request) {
    // 获取授权码
    code := r.Get("code").String()
    
    // 通过PowerWeChat获取用户信息
    userInfo := getUserInfoFromWechat(code)
    
    // 处理用户注册或登录
    user := handleUserRegisterOrLogin(userInfo)
    
    // 存储到Session
    r.Session.Set("wechat_user", user)
    
    // 重定向回原页面
    r.Response.RedirectTo(callbackURL)
}
```

### 3. Detail 方法
```go
func (c *ControllerMobile) Detail(r *ghttp.Request) {
    // 获取用户信息（可能为空）
    wechatUser := middleware.GetCurrentWechatUser(r)
    
    // 构建模板数据
    templateData := g.Map{
        "title": "套餐详情",
        "id":    id,
    }
    
    // 添加用户状态
    if wechatUser != nil {
        templateData["user"] = wechatUser
        templateData["is_logged_in"] = true
    } else {
        templateData["is_logged_in"] = false
    }
    
    // 渲染页面
    r.Response.WriteTpl("mobile/detail.html", templateData)
}
```

## 用户注册和登录逻辑

### 1. 新用户注册
```go
// 用户不存在，创建新用户
newUser, err := service.ZbUser().CreateFromWechat(ctx, openid, nickname, avatar, clientIP)

// 返回用户信息
return map[string]interface{}{
    "id":               newUser.Id,
    "nickname":         newUser.Nickname,
    "avatar":           newUser.Avatar,
    "openid":           newUser.Openid,
    "effective_status": 0,  // 新用户默认为普通用户
    "is_new_user":      true,
}
```

### 2. 已注册用户登录
```go
// 用户已存在，检查状态
if existingUser.IsDisable == 1 {
    return nil, fmt.Errorf("用户已被禁用")
}

// 更新登录信息
service.ZbUser().UpdateLastLogin(ctx, existingUser.Id, clientIP)

// 返回用户信息
return map[string]interface{}{
    "id":               existingUser.Id,
    "nickname":         existingUser.Nickname,
    "avatar":           existingUser.Avatar,
    "effective_status": existingUser.EffectiveStatus,
    "is_new_user":      false,
}
```

## JavaScript 功能

### 1. 页面状态检查
```javascript
function checkUserStatusOnLoad() {
    {{if .is_logged_in}}
        const isLoggedIn = true;
        const isVipMember = {{if eq .user.effective_status 1}}true{{else}}false{{end}};
        console.log('用户已登录:', "{{.user.nickname}}");
    {{else}}
        const isLoggedIn = false;
        console.log('用户未登录');
    {{end}}
}
```

### 2. 登录相关函数
```javascript
// 显示登录弹窗
function showLoginModal() {
    document.getElementById('loginModal').style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

// 隐藏登录弹窗
function hideLoginModal() {
    document.getElementById('loginModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// 跳转到微信登录
function goToLogin() {
    const currentURL = window.location.pathname + window.location.search;
    window.location.href = '/m/auth/login?callback=' + encodeURIComponent(currentURL);
}
```

## 测试方法

### 1. 未登录状态测试
1. 清除浏览器Session/Cookie
2. 访问 `http://localhost:8000/m/detail`
3. 确认页面正常显示
4. 确认顶部显示蓝色登录提示
5. 点击"立即登录"按钮
6. 确认弹出登录弹窗

### 2. 登录流程测试
1. 在登录弹窗中点击"微信授权登录"
2. 确认跳转到微信授权页面（或显示配置错误）
3. 完成授权后确认返回原页面
4. 确认页面显示绿色欢迎信息
5. 确认用户信息正确显示

### 3. 已登录状态测试
1. 登录成功后刷新页面
2. 确认页面直接显示登录状态
3. 确认用户信息持久化
4. 确认VIP状态正确显示

## 优势特点

1. **用户体验友好**: 不强制授权，用户可以先浏览内容
2. **渐进式引导**: 通过提示引导用户主动登录
3. **状态清晰**: 明确显示登录状态和用户信息
4. **流程简化**: 减少不必要的跳转和等待
5. **兼容性好**: 支持微信环境和普通浏览器
6. **可扩展性**: 易于添加更多用户功能和权限控制

## 后续扩展

1. **权限控制**: 根据用户状态显示不同内容
2. **VIP功能**: 为VIP用户提供更多特权
3. **用户中心**: 添加用户信息管理页面
4. **消息通知**: 为登录用户提供个性化通知
5. **数据统计**: 统计用户行为和偏好
