// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"admin-server/internal/packed"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysResources is the golang structure for table sys_resources.
type SysResources struct {
	Id          int64       `json:"id"          orm:"id"           description:""`                              //
	GroupId     int64       `json:"groupId"     orm:"group_id"     description:"系统资源分组id"`                      // 系统资源分组id
	StorageMode int         `json:"storageMode" orm:"storage_mode" description:"存储模式 (1 本地 2 阿里云 3 七牛云 4 腾讯云)"` // 存储模式 (1 本地 2 阿里云 3 七牛云 4 腾讯云)
	OriginName  string      `json:"originName"  orm:"origin_name"  description:"源文件名"`                          // 源文件名
	ObjectName  string      `json:"objectName"  orm:"object_name"  description:"新文件名"`                          // 新文件名
	Hash        string      `json:"hash"        orm:"hash"         description:"文件hash;用来去重"`                   // 文件hash;用来去重
	MimeType    string      `json:"mimeType"    orm:"mime_type"    description:"资源类型"`                          // 资源类型
	StoragePath string      `json:"storagePath" orm:"storage_path" description:"存储目录"`                          // 存储目录
	Suffix      string      `json:"suffix"      orm:"suffix"       description:"文件后缀"`                          // 文件后缀
	SizeByte    string      `json:"sizeByte"    orm:"size_byte"    description:"字节数"`                           // 字节数
	SizeInfo    string      `json:"sizeInfo"    orm:"size_info"    description:"文件大小"`                          // 文件大小
	Url         string      `json:"url"         orm:"url"          description:"url地址"`                         // url地址
	Remark      string      `json:"remark"      orm:"remark"       description:"备注"`                            // 备注
	IsDelete    packed.IsDelete         `json:"isDelete"    orm:"is_delete"    description:"是否删除: 0=否, 1=是"`                // 是否删除: 0=否, 1=是
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"   description:"创建时间"`                          // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"   description:"更新时间"`                          // 更新时间
	DeletedAt   *gtime.Time `json:"deletedAt"   orm:"deleted_at"   description:"删除时间"`                          // 删除时间
}
