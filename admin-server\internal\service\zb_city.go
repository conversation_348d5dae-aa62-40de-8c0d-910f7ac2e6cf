package service

import (
	v1 "admin-server/api/zb_city/v1"
	"context"
)

// 1.定义接口
type IZbCity interface {
	Create(ctx context.Context, req *v1.ZbCityCreateReq) (res *v1.ZbCityCreateRes, err error)
	Update(ctx context.Context, req *v1.ZbCityUpdateReq) (res *v1.ZbCityUpdateRes, err error)
	Delete(ctx context.Context, req *v1.ZbCityDeleteReq) (res *v1.ZbCityDeleteRes, err error)
	GetOne(ctx context.Context, req *v1.ZbCityGetOneReq) (res *v1.ZbCityGetOneRes, err error)
	GetList(ctx context.Context, req *v1.ZbCityGetListReq) (res *v1.ZbCityGetListRes, err error)
	GetTree(ctx context.Context, req *v1.ZbCityGetTreeReq) (res *v1.ZbCityGetTreeRes, err error)
	UpdateSort(ctx context.Context, req *v1.ZbCityUpdateSortReq) (res *v1.ZbCityUpdateSortRes, err error)
	UpdateStatus(ctx context.Context, req *v1.ZbCityUpdateStatusReq) (res *v1.ZbCityUpdateStatusRes, err error)
	BuildTree(ctx context.Context, cities []*v1.ZbCityInfo, pid int) (tree []*v1.ZbCityTreeInfo, err error)
	GetChildren(ctx context.Context, pid int) (children []*v1.ZbCityInfo, err error)
}

// 2.定义接口变量
var localZbCity IZbCity

// 3.定义一个获取接口实例的函数
func ZbCity() IZbCity {
	if localZbCity == nil {
		panic("IZbCity接口未实现或未注册")
	}
	return localZbCity
}

// 4.定义一个接口实现的注册方法
func RegisterZbCity(i IZbCity) {
	localZbCity = i
}
