package sys_dict_group

import (
	"context"

	v1 "admin-server/api/sys_dict_group/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetOne(ctx context.Context, req *v1.GetOneReq) (res *v1.GetOneRes, err error) {
	group, err := service.SysDictGroup().GetDictGroupDetail(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	if group == nil {
		return &v1.GetOneRes{}, nil
	}

	// 转换为API响应格式
	detail := &v1.DictGroupDetail{
		ID:        group.Id,
		Name:      group.Name,
		Code:      group.Code,
		IsDisable: int(group.IsDisable),
		IsSystem:  int(group.IsSystem),
		Remark:    group.Remark,
		CreatedAt: group.CreatedAt,
		UpdatedAt: group.UpdatedAt,
	}

	return &v1.GetOneRes{
		DictGroup: detail,
	}, nil
}
