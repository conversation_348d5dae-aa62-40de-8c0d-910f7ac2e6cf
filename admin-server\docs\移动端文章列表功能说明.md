# 移动端文章列表功能说明

## 功能概述

已成功实现移动端文章列表页面的动态加载和分页功能，页面加载完毕后会自动调用 `/m/api/zb_article/mobileList` 接口获取文章数据并渲染列表。

## 实现的功能

### 1. 动态数据加载
- **自动加载**: 页面加载完成后自动调用API获取文章列表
- **分页支持**: 支持分页加载，默认每页20条数据
- **加载更多**: 点击"加载更多"按钮可以加载下一页数据
- **加载状态**: 显示加载中状态和加载完成提示

### 2. 分类筛选
- **分类标签**: 动态加载招标分类标签
- **分类筛选**: 点击分类标签可以按分类筛选文章
- **全部分类**: 支持查看所有分类的文章

### 3. 文章卡片展示
- **动态渲染**: 根据API返回的数据动态生成文章卡片
- **信息完整**: 显示标题、城市、类别、作者、浏览次数等信息
- **时间显示**: 智能显示发布时间（刚刚、几分钟前、几小时前等）
- **状态标签**: 根据发布时间显示不同的状态标签（最新、热门、推荐）

### 4. 用户体验优化
- **响应式设计**: 适配移动端屏幕
- **平滑滚动**: 支持触摸滚动
- **错误处理**: 网络错误和数据错误的友好提示
- **防重复加载**: 防止重复点击加载按钮

## 技术实现

### 1. 后端接口

#### API路径
```
GET /m/api/zb_article/mobileList
```

#### 请求参数
- `page`: 页码，默认1
- `page_size`: 每页数量，默认10，最大100
- `cate_id`: 分类ID，可选，用于分类筛选
- `city_id`: 城市ID，可选，用于城市筛选

#### 响应格式
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "title": "文章标题",
                "intro": "文章简介",
                "city_id": 1,
                "city_name": "北京市",
                "cate_id": 1,
                "cate_name": "工程建设",
                "author": "作者",
                "view_count": 100,
                "created_at": "2025-01-23T10:00:00Z",
                "updated_at": "2025-01-23T10:00:00Z"
            }
        ],
        "total": 100
    }
}
```

### 2. 前端实现

#### 核心功能
- **loadArticleList()**: 加载文章列表数据
- **renderArticleList()**: 渲染文章列表UI
- **createArticleCard()**: 创建单个文章卡片
- **filterByCategory()**: 按分类筛选文章
- **formatTimeAgo()**: 格式化时间显示

#### 全局变量
```javascript
let currentPage = 1;        // 当前页码
let pageSize = 20;          // 每页数量
let isLoading = false;      // 是否正在加载
let hasMoreData = true;     // 是否还有更多数据
let currentCategoryId = 0;  // 当前选中的分类ID
let articleList = [];       // 文章列表数据
```

### 3. 架构层次

#### 后端架构
```
API层 (zb_article/v1/zb_article.go)
    ↓
Controller层 (zb_article_v1_get_list_for_mobile.go)
    ↓
Logic层 (zbArticle/zb_article.go)
    ↓
Service层 (zb_article.go)
    ↓
DAO层 (自动生成)
```

#### 数据库查询
- 使用LEFT JOIN关联查询城市名称和分类名称
- 支持按分类和城市筛选
- 按ID倒序排列，显示最新文章
- 只显示未删除且未禁用的文章

## 使用说明

### 1. 访问页面
```
http://localhost:8000/m/list
```

### 2. 页面功能
1. **查看文章列表**: 页面自动加载最新的文章列表
2. **分类筛选**: 点击顶部的分类标签筛选不同类别的文章
3. **加载更多**: 滚动到底部点击"加载更多"按钮获取更多文章
4. **查看详情**: 点击文章卡片的"查看详情"按钮跳转到详情页

### 3. 测试步骤
1. 启动服务器: `gf run main.go`
2. 访问移动端列表页面
3. 检查文章列表是否正常加载
4. 测试分类筛选功能
5. 测试分页加载功能
6. 测试详情页跳转

## 配置说明

### 1. 路由配置
在 `internal/cmd/cmd.go` 中已配置移动端API路由：
```go
// 手机端api（无需token验证）
s.Group("/m/api", func(group *ghttp.RouterGroup) {
    // ...
    group.GET("/zb_article/mobileList", zb_article.NewV1().GetListForMobile)
})
```

### 2. 权限配置
移动端API无需token验证，可直接访问。

### 3. 数据库配置
确保以下数据表存在且有数据：
- `zb_article`: 文章表
- `zb_city`: 城市表
- `zb_cate`: 分类表

## 扩展功能

### 1. 搜索功能
可以添加搜索框，支持按标题搜索文章：
```javascript
// 在API请求中添加搜索参数
let url = `/m/api/zb_article/mobileList?page=${currentPage}&page_size=${pageSize}`;
if (searchKeyword) {
    url += `&keyword=${encodeURIComponent(searchKeyword)}`;
}
```

### 2. 城市筛选
可以添加城市选择功能：
```javascript
// 在API请求中添加城市参数
if (currentCityId > 0) {
    url += `&city_id=${currentCityId}`;
}
```

### 3. 排序功能
可以添加按时间、浏览量等排序：
```javascript
// 在API请求中添加排序参数
if (sortBy) {
    url += `&sort=${sortBy}`;
}
```

## 注意事项

1. **性能优化**: 大量数据时建议使用虚拟滚动或懒加载
2. **缓存策略**: 可以添加本地缓存减少API调用
3. **错误处理**: 完善网络错误和数据错误的处理
4. **用户体验**: 添加下拉刷新和上拉加载更多功能
5. **SEO优化**: 如需SEO，考虑服务端渲染

## 故障排除

### 1. 接口调用失败
- 检查服务器是否正常启动
- 检查路由配置是否正确
- 检查数据库连接是否正常

### 2. 数据显示异常
- 检查数据库中是否有测试数据
- 检查字段映射是否正确
- 检查前端数据处理逻辑

### 3. 分页功能异常
- 检查分页参数是否正确传递
- 检查后端分页逻辑是否正确
- 检查前端分页状态管理

---

**功能状态**: ✅ 已完成  
**测试状态**: 待测试  
**文档版本**: v1.0  
**最后更新**: 2025-01-23
