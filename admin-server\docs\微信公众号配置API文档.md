# 微信公众号配置API文档

## 概述

微信公众号配置模块提供了微信公众号基础配置的管理功能，包括AppID、AppSecret、服务器配置、消息加解密等设置。该模块采用单例模式，系统中只维护一套微信公众号配置。

## 基础信息

- **模块名称**: 微信公众号配置管理
- **基础路径**: `/wechat/config`
- **认证方式**: JWT Token
- **权限验证**: 需要相应的微信配置权限

## 配置字段说明

| 字段名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 微信公众号AppID，18位字符 | `wx1234567890abcdef` |
| app_secret | string | 是 | 微信公众号AppSecret，32位字符 | `abcdef1234567890abcdef1234567890` |
| auto_reply_enabled | int | 是 | 关注自动回复开关：0=关闭，1=开启 | `1` |
| server_url | string | 是 | 服务器地址URL | `https://your-domain.com/wechat/callback` |
| token | string | 是 | 微信Token，3-32位字符 | `your_wechat_token` |
| encoding_aes_key | string | 是 | 消息加解密密钥，43位字符 | `abcdefghijklmnopqrstuvwxyz1234567890ABCDEFG` |
| encrypt_mode | string | 是 | 消息加解密方式 | `safe` |

### 消息加解密方式说明

| 值 | 说明 |
|----|------|
| `plaintext` | 明文模式，不加密 |
| `compatible` | 兼容模式，支持明文和加密 |
| `safe` | 安全模式，只支持加密 |

## API接口列表

### 1. 获取微信公众号配置

**接口地址**: `GET /wechat/config`

**接口描述**: 获取当前的微信公众号配置信息

**请求参数**: 无

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| id | int | 配置ID |
| appid | string | 微信公众号AppID |
| app_secret | string | 微信公众号AppSecret |
| auto_reply_enabled | int | 关注自动回复开关 |
| server_url | string | 服务器地址URL |
| token | string | 微信Token |
| encoding_aes_key | string | 消息加解密密钥 |
| encrypt_mode | string | 消息加解密方式 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "appid": "wx1234567890abcdef",
    "app_secret": "abcdef1234567890abcdef1234567890",
    "auto_reply_enabled": 1,
    "server_url": "https://your-domain.com/wechat/callback",
    "token": "your_wechat_token",
    "encoding_aes_key": "abcdefghijklmnopqrstuvwxyz1234567890ABCDEFG",
    "encrypt_mode": "safe",
    "created_at": "2025-01-12 10:00:00",
    "updated_at": "2025-01-12 10:00:00"
  }
}
```

**首次访问响应**（无配置时）:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 0,
    "appid": "",
    "app_secret": "",
    "auto_reply_enabled": 0,
    "server_url": "",
    "token": "",
    "encoding_aes_key": "",
    "encrypt_mode": "safe",
    "created_at": null,
    "updated_at": null
  }
}
```

### 2. 保存微信公众号配置

**接口地址**: `POST /wechat/config`

**接口描述**: 保存微信公众号配置，如果配置不存在则创建，存在则更新

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| appid | string | 是 | 微信公众号AppID，必须为18位 |
| app_secret | string | 是 | 微信公众号AppSecret，必须为32位 |
| auto_reply_enabled | int | 是 | 关注自动回复开关：0=关闭，1=开启 |
| server_url | string | 是 | 服务器地址URL，必须为有效的URL格式 |
| token | string | 是 | 微信Token，长度3-32位 |
| encoding_aes_key | string | 是 | 消息加解密密钥，必须为43位 |
| encrypt_mode | string | 是 | 消息加解密方式：plaintext/compatible/safe |

**请求示例**:

```json
{
  "appid": "wx1234567890abcdef",
  "app_secret": "abcdef1234567890abcdef1234567890",
  "auto_reply_enabled": 1,
  "server_url": "https://your-domain.com/wechat/callback",
  "token": "your_wechat_token",
  "encoding_aes_key": "abcdefghijklmnopqrstuvwxyz1234567890ABCDEFG",
  "encrypt_mode": "safe"
}
```

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 保存是否成功 |
| message | string | 保存结果消息 |

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "success": true,
    "message": "微信公众号配置保存成功"
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 权限不足 |
| 500 | 服务器内部错误 |

## 常见错误示例

### 参数验证错误

```json
{
  "code": 400,
  "message": "AppID长度必须为18位",
  "data": null
}
```

### URL格式错误

```json
{
  "code": 400,
  "message": "服务器地址格式不正确",
  "data": null
}
```

### 权限不足

```json
{
  "code": 403,
  "message": "权限不足",
  "data": null
}
```

## 使用示例

### 获取配置

```bash
curl -X GET "http://localhost:8000/wechat/config" \
  -H "Authorization: Bearer your_token"
```

### 保存配置

```bash
curl -X POST "http://localhost:8000/wechat/config" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "wx1234567890abcdef",
    "app_secret": "abcdef1234567890abcdef1234567890",
    "auto_reply_enabled": 1,
    "server_url": "https://your-domain.com/wechat/callback",
    "token": "your_wechat_token",
    "encoding_aes_key": "abcdefghijklmnopqrstuvwxyz1234567890ABCDEFG",
    "encrypt_mode": "safe"
  }'
```

### 生成Token

```bash
curl -X POST "http://localhost:8000/wechat/config/generate-token" \
  -H "Authorization: Bearer your_token"
```

### 生成AES密钥

```bash
curl -X POST "http://localhost:8000/wechat/config/generate-aes-key" \
  -H "Authorization: Bearer your_token"
```

## 前端对接说明

### TypeScript类型定义

```typescript
// 微信配置信息
interface WechatConfigInfo {
  id: number;
  appid: string;
  app_secret: string;
  auto_reply_enabled: number;
  server_url: string;
  token: string;
  encoding_aes_key: string;
  encrypt_mode: string;
  created_at: string | null;
  updated_at: string | null;
}

// 保存配置请求
interface SaveConfigRequest {
  appid: string;
  app_secret: string;
  auto_reply_enabled: number;
  server_url: string;
  token: string;
  encoding_aes_key: string;
  encrypt_mode: string;
}

// 保存配置响应
interface SaveConfigResponse {
  success: boolean;
  message: string;
}
```

### Vue 3 使用示例

```typescript
import { ref } from 'vue'
import axios from 'axios'

export function useWechatConfig() {
  const config = ref<WechatConfigInfo | null>(null)
  const loading = ref(false)

  // 获取配置
  const getConfig = async () => {
    loading.value = true
    try {
      const response = await axios.get('/wechat/config')
      config.value = response.data.data
      return response.data.data
    } finally {
      loading.value = false
    }
  }

  // 保存配置
  const saveConfig = async (data: SaveConfigRequest) => {
    loading.value = true
    try {
      const response = await axios.post('/wechat/config', data)
      return response.data.data
    } finally {
      loading.value = false
    }
  }

  // 生成Token
  const generateToken = async () => {
    const response = await axios.post('/wechat/config/generate-token')
    return response.data.data.token
  }

  // 生成AES密钥
  const generateAESKey = async () => {
    const response = await axios.post('/wechat/config/generate-aes-key')
    return response.data.data.encoding_aes_key
  }

  return {
    config,
    loading,
    getConfig,
    saveConfig,
    generateToken,
    generateAESKey
  }
}
```

## 注意事项

1. **单例配置**: 系统中只维护一套微信公众号配置
2. **参数验证**: 严格的参数长度和格式验证
3. **权限控制**: 需要相应的微信配置管理权限
4. **安全性**: AppSecret等敏感信息需要妥善保管
5. **服务器配置**: server_url需要配置为可访问的公网地址
6. **加密模式**: 建议使用safe模式确保消息安全
