# 角色状态切换功能说明

## 功能概述

角色状态切换功能允许管理员快速切换角色的启用/禁用状态，无需进入编辑页面，提升操作效率。

## 功能特性

### 1. 一键切换
- 通过单个API调用即可切换角色状态
- 无需传递额外参数，系统自动判断当前状态
- 支持从启用切换到禁用，或从禁用切换到启用

### 2. 状态逻辑
- **启用状态** (`is_disable = 0`): 角色可以正常使用，用户可以被分配此角色
- **禁用状态** (`is_disable = 1`): 角色被禁用，不能分配给新用户，已分配的用户权限受限

### 3. 安全性
- 切换前验证角色是否存在
- 只能切换未删除的角色状态
- 操作记录可追溯

## API接口详情

### 接口信息
- **路径**: `PUT /sys_role/{id}/toggle`
- **方法**: PUT
- **认证**: 需要Bearer Token

### 请求参数
| 参数名 | 类型 | 位置 | 必填 | 说明 |
|--------|------|------|------|------|
| id | int64 | 路径参数 | 是 | 角色ID |

### 响应格式
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 错误响应
```json
{
  "code": 1,
  "message": "角色不存在",
  "data": null
}
```

## 实现细节

### 业务逻辑
```go
func (s SsysRole) ToggleStatus(ctx context.Context, id int64) (err error) {
    // 1. 检查角色是否存在
    var role entity.SysRole
    err = dao.SysRole.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Scan(&role)
    if err != nil {
        return err
    }
    if role.Id == 0 {
        return gerror.New("角色不存在")
    }

    // 2. 切换状态：0变1，1变0
    newStatus := packed.ENABLE
    if role.IsDisable == int(packed.ENABLE) {
        newStatus = packed.DISABLE
    }

    // 3. 更新状态
    _, err = dao.SysRole.Ctx(ctx).Where("id", id).Data(do.SysRole{
        IsDisable: newStatus,
    }).Update()

    return err
}
```

### 状态切换逻辑
1. **当前启用** (`is_disable = 0`) → **切换为禁用** (`is_disable = 1`)
2. **当前禁用** (`is_disable = 1`) → **切换为启用** (`is_disable = 0`)

### 数据验证
- 验证角色ID是否存在
- 验证角色是否已被删除
- 确保只操作有效的角色记录

## 使用场景

### 1. 临时禁用角色
当某个角色的权限配置需要调整时，可以先禁用该角色，避免影响正在使用的用户。

### 2. 角色管理
在角色列表页面，管理员可以快速启用或禁用角色，无需进入详情页面。

### 3. 批量操作
结合前端实现，可以支持批量切换多个角色的状态。

### 4. 应急处理
当发现某个角色权限配置有问题时，可以快速禁用该角色，防止安全风险。

## 前端集成建议

### 1. 按钮设计
```html
<!-- 启用状态显示禁用按钮 -->
<button v-if="role.is_disable === 0" @click="toggleStatus(role.id)" class="btn-disable">
    禁用
</button>

<!-- 禁用状态显示启用按钮 -->
<button v-if="role.is_disable === 1" @click="toggleStatus(role.id)" class="btn-enable">
    启用
</button>
```

### 2. JavaScript实现
```javascript
async function toggleStatus(roleId) {
    try {
        // 确认操作
        const confirmed = confirm('确定要切换角色状态吗？');
        if (!confirmed) return;

        // 调用API
        const response = await fetch(`/sys_role/${roleId}/toggle`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        
        if (result.code === 0) {
            // 成功后刷新列表或更新状态
            message.success('状态切换成功');
            refreshRoleList();
        } else {
            message.error(result.message || '操作失败');
        }
    } catch (error) {
        message.error('网络错误，请重试');
    }
}
```

### 3. 状态显示
```css
.role-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.role-status.enabled {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.role-status.disabled {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}
```

### 4. 卡片式列表集成
在角色卡片中添加状态切换按钮：

```html
<div class="role-card">
    <div class="card-header">
        <h3>{{ role.name }}</h3>
        <span class="role-status" :class="role.is_disable ? 'disabled' : 'enabled'">
            {{ role.is_disable ? '禁用' : '启用' }}
        </span>
    </div>
    
    <div class="card-footer">
        <button @click="toggleStatus(role.id)" class="toggle-btn">
            {{ role.is_disable ? '启用' : '禁用' }}
        </button>
        <button @click="editRole(role.id)">编辑</button>
        <button @click="deleteRole(role.id)">删除</button>
    </div>
</div>
```

## 测试用例

### 1. 正常切换测试
```bash
# 切换角色状态
curl -X PUT http://localhost:8000/sys_role/1/toggle \
  -H "Authorization: Bearer YOUR_TOKEN"

# 预期响应
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 2. 角色不存在测试
```bash
# 切换不存在的角色
curl -X PUT http://localhost:8000/sys_role/999/toggle \
  -H "Authorization: Bearer YOUR_TOKEN"

# 预期响应
{
  "code": 1,
  "message": "角色不存在",
  "data": null
}
```

### 3. 状态验证测试
```bash
# 1. 获取角色当前状态
curl -X GET http://localhost:8000/sys_role/1 \
  -H "Authorization: Bearer YOUR_TOKEN"

# 2. 切换状态
curl -X PUT http://localhost:8000/sys_role/1/toggle \
  -H "Authorization: Bearer YOUR_TOKEN"

# 3. 再次获取状态，验证是否切换成功
curl -X GET http://localhost:8000/sys_role/1 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 注意事项

### 1. 权限控制
- 只有具有角色管理权限的用户才能切换角色状态
- 建议在前端也进行权限验证

### 2. 用户影响
- 禁用角色不会立即影响已登录的用户
- 新的登录会受到角色状态的影响
- 建议在禁用重要角色前通知相关用户

### 3. 数据一致性
- 切换操作是原子性的，不会出现中间状态
- 建议记录操作日志，便于审计

### 4. 性能考虑
- 状态切换是轻量级操作，响应速度快
- 适合在列表页面进行批量操作

## 扩展功能

基于当前实现，可以扩展以下功能：

1. **批量状态切换**: 支持同时切换多个角色的状态
2. **状态变更日志**: 记录角色状态变更的操作日志
3. **定时任务**: 支持定时启用或禁用角色
4. **状态变更通知**: 角色状态变更时通知相关用户
5. **权限继承**: 禁用父角色时自动处理子角色状态

通过这个功能，管理员可以更加灵活和高效地管理角色状态，提升系统的可用性和安全性。
