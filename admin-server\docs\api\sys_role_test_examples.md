# 系统角色 API 测试示例

## 测试环境

- **服务器地址**: `http://localhost:8000`
- **认证方式**: <PERSON><PERSON>（需要先登录获取）

## 测试用例

### 1. 创建角色（不分配权限）

```bash
curl -X POST http://localhost:8000/sys_role/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "管理员",
    "sort": 1,
    "remark": "系统管理员角色",
    "is_disable": 0
  }'
```

**预期响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1
  }
}
```

### 1.1. 创建角色并同时分配权限

```bash
curl -X POST http://localhost:8000/sys_role/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "超级管理员",
    "sort": 1,
    "remark": "超级管理员角色，拥有所有权限",
    "is_disable": 0,
    "menu_ids": [1, 2, 3, 4, 5]
  }'
```

**预期响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 2
  }
}
```

### 2. 创建普通用户角色

```bash
curl -X POST http://localhost:8000/sys_role/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "普通用户",
    "sort": 2,
    "remark": "普通用户角色，权限有限",
    "is_disable": 0
  }'
```

### 3. 获取角色列表

```bash
curl -X GET "http://localhost:8000/sys_role/list?page=1&page_size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**预期响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "超级管理员",
        "sort": 1,
        "remark": "超级管理员角色，拥有所有权限",
        "is_disable": 0,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z",
        "menu_count": 8,
        "user_count": 1,
        "main_menus": ["系统管理", "用户管理", "角色管理", "菜单管理", "权限管理"]
      },
      {
        "id": 2,
        "name": "编辑员",
        "sort": 2,
        "remark": "内容编辑角色",
        "is_disable": 0,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z",
        "menu_count": 3,
        "user_count": 5,
        "main_menus": ["基本信息", "content:create", "content:update"]
      },
      {
        "id": 3,
        "name": "查看员",
        "sort": 3,
        "remark": "只读权限角色",
        "is_disable": 0,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z",
        "menu_count": 0,
        "user_count": 0,
        "main_menus": []
      }
    ],
    "total": 3,
    "page": 1,
    "page_size": 10
  }
}
```

### 4. 获取单个角色信息

```bash
curl -X GET http://localhost:8000/sys_role/1 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 5. 更新角色信息

```bash
curl -X PUT http://localhost:8000/sys_role/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "超级管理员",
    "sort": 1,
    "remark": "超级管理员角色，拥有所有权限",
    "is_disable": 0
  }'
```

### 6. 分配角色菜单权限

```bash
curl -X PUT http://localhost:8000/sys_role/1/menus \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "menu_ids": [1, 2, 3, 4, 5]
  }'
```

**预期响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 7. 获取角色菜单权限

```bash
curl -X GET http://localhost:8000/sys_role/1/menus \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**预期响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "role_id": 1,
    "role_name": "超级管理员",
    "menu_ids": [1, 2, 3, 4, 5]
  }
}
```

### 8. 清空角色菜单权限

```bash
curl -X PUT http://localhost:8000/sys_role/2/menus \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "menu_ids": []
  }'
```

### 9. 切换角色状态

```bash
curl -X PUT http://localhost:8000/sys_role/1/toggle \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**预期响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 10. 删除角色

```bash
curl -X DELETE http://localhost:8000/sys_role/2 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 搜索测试

### 按角色名称搜索

```bash
curl -X GET "http://localhost:8000/sys_role/list?name=管理" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 筛选禁用状态

```bash
curl -X GET "http://localhost:8000/sys_role/list?is_disable=0" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 分页测试

```bash
curl -X GET "http://localhost:8000/sys_role/list?page=1&page_size=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 错误测试

### 1. 创建重复角色名称

```bash
curl -X POST http://localhost:8000/sys_role/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "管理员",
    "sort": 1,
    "remark": "重复角色测试"
  }'
```

**预期响应**:
```json
{
  "code": 1,
  "message": "角色名称已存在",
  "data": null
}
```

### 2. 更新不存在的角色

```bash
curl -X PUT http://localhost:8000/sys_role/999 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "测试角色",
    "sort": 1,
    "remark": "测试"
  }'
```

**预期响应**:
```json
{
  "code": 1,
  "message": "角色不存在",
  "data": null
}
```

### 3. 创建角色时分配无效的菜单权限

```bash
curl -X POST http://localhost:8000/sys_role/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "测试角色",
    "sort": 1,
    "remark": "测试无效菜单ID",
    "menu_ids": [1, 2, 999, 1000]
  }'
```

**预期响应**:
```json
{
  "code": 1,
  "message": "存在无效的菜单ID",
  "data": null
}
```

### 4. 分配无效的菜单权限

```bash
curl -X PUT http://localhost:8000/sys_role/1/menus \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "menu_ids": [1, 2, 999, 1000]
  }'
```

**预期响应**:
```json
{
  "code": 1,
  "message": "存在无效的菜单ID",
  "data": null
}
```

### 4. 删除被使用的角色

```bash
# 首先需要有管理员使用此角色，然后尝试删除
curl -X DELETE http://localhost:8000/sys_role/1 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**预期响应**:
```json
{
  "code": 1,
  "message": "该角色已被管理员使用，无法删除",
  "data": null
}
```

## 参数验证测试

### 1. 角色名称长度验证

```bash
curl -X POST http://localhost:8000/sys_role/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "",
    "sort": 1,
    "remark": "空名称测试"
  }'
```

### 2. 角色名称过长验证

```bash
curl -X POST http://localhost:8000/sys_role/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "这是一个非常非常非常非常非常非常长的角色名称超过了30个字符的限制",
    "sort": 1,
    "remark": "长名称测试"
  }'
```

### 3. 排序值验证

```bash
curl -X POST http://localhost:8000/sys_role/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "测试角色",
    "sort": -1,
    "remark": "负数排序测试"
  }'
```

### 4. 备注长度验证

```bash
curl -X POST http://localhost:8000/sys_role/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "测试角色",
    "sort": 1,
    "remark": "这是一个超过255个字符的备注内容...(重复到超过255字符)"
  }'
```

## 完整测试流程

### 1. 创建测试数据

```bash
# 创建管理员角色
curl -X POST http://localhost:8000/sys_role/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "管理员",
    "sort": 1,
    "remark": "系统管理员角色"
  }'

# 创建编辑角色
curl -X POST http://localhost:8000/sys_role/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "编辑员",
    "sort": 2,
    "remark": "内容编辑角色"
  }'

# 创建查看角色
curl -X POST http://localhost:8000/sys_role/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "查看员",
    "sort": 3,
    "remark": "只读权限角色"
  }'
```

### 2. 分配权限

```bash
# 管理员分配所有权限
curl -X PUT http://localhost:8000/sys_role/1/menus \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "menu_ids": [1, 2, 3, 4, 5, 6, 7, 8]
  }'

# 编辑员分配部分权限
curl -X PUT http://localhost:8000/sys_role/2/menus \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "menu_ids": [2, 3, 4]
  }'

# 查看员只分配查看权限
curl -X PUT http://localhost:8000/sys_role/3/menus \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "menu_ids": [2]
  }'
```

### 3. 验证权限

```bash
# 验证各角色的权限
curl -X GET http://localhost:8000/sys_role/1/menus -H "Authorization: Bearer YOUR_TOKEN"
curl -X GET http://localhost:8000/sys_role/2/menus -H "Authorization: Bearer YOUR_TOKEN"
curl -X GET http://localhost:8000/sys_role/3/menus -H "Authorization: Bearer YOUR_TOKEN"
```

## 注意事项

1. 替换 `YOUR_TOKEN` 为实际的认证令牌
2. 确保服务器已启动并监听8000端口
3. 测试前请确保数据库连接正常
4. 建议按照顺序执行测试用例
5. 删除角色前确保没有管理员使用该角色
6. 菜单ID需要根据实际的菜单数据进行调整
