package sys_dict

import (
	"context"

	v1 "admin-server/api/sys_dict/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetByCode(ctx context.Context, req *v1.GetByCodeReq) (res *v1.GetByCodeRes, err error) {
	dict, err := service.SysDict().GetDictDetailByCode(ctx, req.Code)
	if err != nil {
		return nil, err
	}

	if dict == nil {
		return &v1.GetByCodeRes{}, nil
	}

	// 转换为API响应格式
	detail := &v1.DictDetail{
		ID:        dict.Id,
		GroupId:   dict.GroupId,
		Name:      dict.Name,
		Value:     dict.Value,
		Code:      dict.Code,
		Sort:      int(dict.Sort),
		IsDisable: int(dict.IsDisable),
		IsSystem:  int(dict.IsSystem),
		Remark:    dict.Remark,
		CreatedAt: dict.CreatedAt,
		UpdatedAt: dict.UpdatedAt,
	}

	return &v1.GetByCodeRes{
		Dict: detail,
	}, nil
}
