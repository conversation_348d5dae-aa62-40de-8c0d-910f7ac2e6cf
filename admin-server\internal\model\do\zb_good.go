// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ZbGood is the golang structure of table zb_good for DAO operations like Where/Data.
type ZbGood struct {
	g.Meta        `orm:"table:zb_good, do:true"`
	Id            interface{} //
	Name          interface{} // 名称
	Tag           interface{} // 标签
	OriginalPrice interface{} // 原始价格
	Price         interface{} // 现时价格
	Effective     interface{} // 会员有效期；单位月
	IsDisable     interface{} // 是否禁用: 0=否, 1=是
	IsDelete      interface{} // 是否删除: 0=否, 1=是
	CreatedAt     *gtime.Time // 创建时间
	UpdatedAt     *gtime.Time // 更新时间
	DeletedAt     *gtime.Time // 删除时间
}
