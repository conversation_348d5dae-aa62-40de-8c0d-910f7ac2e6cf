package test

import (
	"testing"

	v1 "admin-server/api/zb_user/v1"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestZbUserAPIStructure 测试会员API结构体
func TestZbUserAPIStructure(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 测试获取会员列表请求结构体
		req := &v1.GetListReq{
			Page:         1,
			PageSize:     10,
			VipStatus:    -1,
			HasVipPeriod: -1,
		}

		t.AssertEQ(req.Page, 1)
		t.AssertEQ(req.PageSize, 10)
		t.AssertEQ(req.VipStatus, -1)
		t.AssertEQ(req.HasVipPeriod, -1)
	})

	gtest.C(t, func(t *gtest.T) {
		// 测试UserInfo结构体
		userInfo := &v1.UserInfo{
			ID:          1,
			Nickname:    "测试用户",
			Avatar:      "http://example.com/avatar.jpg",
			Openid:      "wx_test_openid",
			IsDisable:   0,
			IsDelete:    0,
			VipStatus:   1,
			VipDaysLeft: 30,
		}

		t.<PERSON><PERSON>(userInfo.ID, int64(1))
		t.AssertEQ(userInfo.Nickname, "测试用户")
		t.AssertEQ(userInfo.IsDisable, 0)
		t.AssertEQ(userInfo.VipStatus, 1)
		t.AssertEQ(userInfo.VipDaysLeft, 30)
	})

	gtest.C(t, func(t *gtest.T) {
		// 测试UserDetail结构体
		userDetail := &v1.UserDetail{
			ID:          1,
			Nickname:    "测试用户详情",
			Openid:      "wx_test_detail_openid",
			VipStatus:   1,
			VipDaysLeft: 15,
		}

		t.AssertEQ(userDetail.ID, int64(1))
		t.AssertEQ(userDetail.Nickname, "测试用户详情")
		t.AssertEQ(userDetail.Openid, "wx_test_detail_openid")
		t.AssertEQ(userDetail.VipStatus, 1)
		t.AssertEQ(userDetail.VipDaysLeft, 15)
	})

	gtest.C(t, func(t *gtest.T) {
		// 测试GetStatsRes结构体
		statsRes := &v1.GetStatsRes{
			TotalUsers:    100,
			VipUsers:      25,
			DisabledUsers: 5,
			ActiveUsers:   95,
		}

		t.AssertEQ(statsRes.TotalUsers, 100)
		t.AssertEQ(statsRes.VipUsers, 25)
		t.AssertEQ(statsRes.DisabledUsers, 5)
		t.AssertEQ(statsRes.ActiveUsers, 95)
	})
}

// TestZbUserAPIDefaultValues 测试API默认值
func TestZbUserAPIDefaultValues(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 测试获取会员列表的默认值
		req := &v1.GetListReq{}

		// 检查默认值是否正确设置
		t.AssertEQ(req.Page, 0)         // 结构体默认值
		t.AssertEQ(req.PageSize, 0)     // 结构体默认值
		t.AssertEQ(req.IsDisable, 0)    // 结构体默认值
		t.AssertEQ(req.VipStatus, 0)    // 结构体默认值
		t.AssertEQ(req.HasVipPeriod, 0) // 结构体默认值
	})

	gtest.C(t, func(t *gtest.T) {
		// 测试获取过期VIP的默认值
		req := &v1.GetExpiredVipReq{}

		// 检查默认值
		t.AssertEQ(req.Days, 0) // 结构体默认值
	})
}

// TestZbUserAPIRequestTypes 测试请求类型
func TestZbUserAPIRequestTypes(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 测试更新请求
		updateReq := &v1.UpdateReq{
			ID:       1,
			Nickname: "新昵称",
			Avatar:   "http://example.com/new_avatar.jpg",
		}

		t.AssertEQ(updateReq.ID, int64(1))
		t.AssertEQ(updateReq.Nickname, "新昵称")
		t.AssertEQ(updateReq.Avatar, "http://example.com/new_avatar.jpg")
	})

	gtest.C(t, func(t *gtest.T) {
		// 测试设置状态请求
		statusReq := &v1.SetStatusReq{
			ID:        1,
			IsDisable: 1,
		}

		t.AssertEQ(statusReq.ID, int64(1))
		t.AssertEQ(statusReq.IsDisable, 1)
	})

	gtest.C(t, func(t *gtest.T) {
		// 测试更新VIP有效期请求
		vipReq := &v1.UpdateVipPeriodReq{
			ID:             1,
			EffectiveStart: "2025-01-01",
			EffectiveEnd:   "2025-12-31",
		}

		t.AssertEQ(vipReq.ID, int64(1))
		t.AssertEQ(vipReq.EffectiveStart, "2025-01-01")
		t.AssertEQ(vipReq.EffectiveEnd, "2025-12-31")
	})
}
