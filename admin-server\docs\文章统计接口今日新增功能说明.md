# 文章统计接口今日新增功能说明

## 功能概述

在 `/zb_article/stats` 接口中新增了今日新增条数的统计功能，现在接口会返回包含今日新增文章数量的完整统计信息。

## 接口信息

### 请求信息
- **接口路径**: `GET /zb_article/stats`
- **权限要求**: `system:article:stats`
- **请求参数**: 无

### 响应信息

#### 原有字段
- `total_articles`: 总信息数
- `published_articles`: 已发布信息数  
- `disabled_articles`: 禁用信息数

#### 新增字段
- `today_articles`: 今日新增信息数

#### 完整响应示例
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "total_articles": 1256,
        "published_articles": 1180,
        "disabled_articles": 76,
        "today_articles": 23
    }
}
```

## 技术实现

### 1. API层修改
**文件**: `api/zb_article/v1/zb_article.go`

```go
type GetStatsRes struct {
    TotalArticles     int `json:"total_articles" dc:"总信息数"`
    PublishedArticles int `json:"published_articles" dc:"已发布信息数"`
    DisabledArticles  int `json:"disabled_articles" dc:"禁用信息数"`
    TodayArticles     int `json:"today_articles" dc:"今日新增信息数"`
}
```

### 2. Service层修改
**文件**: `internal/service/zb_article.go`

```go
// GetArticleStats 获取信息统计
GetArticleStats(ctx context.Context) (totalArticles, publishedArticles, disabledArticles, todayArticles int, err error)
```

### 3. Logic层实现
**文件**: `internal/logic/zbArticle/zb_article.go`

```go
func (s *sZbArticle) GetArticleStats(ctx context.Context) (totalArticles, publishedArticles, disabledArticles, todayArticles int, err error) {
    // ... 原有统计逻辑 ...
    
    // 获取今日新增信息数
    today := gtime.Now().Format("Y-m-d")
    todayStart := today + " 00:00:00"
    todayEnd := today + " 23:59:59"
    
    todayArticles, err = dao.ZbArticle.Ctx(ctx).
        Where("is_delete", int(packed.NO_DELETE)).
        WhereBetween("created_at", todayStart, todayEnd).
        Count()
    if err != nil {
        return 0, 0, 0, 0, gerror.Wrap(err, "获取今日新增信息数失败")
    }

    return totalArticles, publishedArticles, disabledArticles, todayArticles, nil
}
```

### 4. Controller层修改
**文件**: `internal/controller/zb_article/zb_article_v1_get_stats.go`

```go
func (c *ControllerV1) GetStats(ctx context.Context, req *v1.GetStatsReq) (res *v1.GetStatsRes, err error) {
    totalArticles, publishedArticles, disabledArticles, todayArticles, err := service.ZbArticle().GetArticleStats(ctx)
    if err != nil {
        return nil, err
    }

    return &v1.GetStatsRes{
        TotalArticles:     totalArticles,
        PublishedArticles: publishedArticles,
        DisabledArticles:  disabledArticles,
        TodayArticles:     todayArticles,
    }, nil
}
```

## 统计逻辑说明

### 今日新增统计规则
- **时间范围**: 当天 00:00:00 到 23:59:59
- **统计条件**: 
  - `is_delete = 0` (未删除的文章)
  - `created_at` 在今日时间范围内
- **时区处理**: 使用服务器本地时区

### 查询优化
- 使用 `WhereBetween` 进行时间范围查询
- 利用 `created_at` 字段的索引提高查询性能
- 只统计未删除的文章，保证数据准确性

## 使用场景

### 1. 管理后台统计面板
```javascript
// 获取文章统计信息
fetch('/zb_article/stats', {
    headers: {
        'Authorization': 'Bearer ' + token
    }
})
.then(response => response.json())
.then(data => {
    if (data.code === 0) {
        const stats = data.data;
        document.getElementById('totalCount').textContent = stats.total_articles;
        document.getElementById('publishedCount').textContent = stats.published_articles;
        document.getElementById('disabledCount').textContent = stats.disabled_articles;
        document.getElementById('todayCount').textContent = stats.today_articles;
    }
});
```

### 2. 移动端统计显示
现在可以使用真实的今日新增数据替代之前的估算值：

```javascript
// 在移动端页面中使用真实的今日新增数据
async function loadRealStats() {
    try {
        const response = await fetch('/zb_article/stats');
        const result = await response.json();
        
        if (result.code === 0) {
            const todayCountElement = document.getElementById('todayCount');
            const totalCountElement = document.getElementById('totalCount');
            
            if (todayCountElement) {
                todayCountElement.textContent = result.data.today_articles;
            }
            if (totalCountElement) {
                totalCountElement.textContent = result.data.total_articles.toLocaleString();
            }
        }
    } catch (error) {
        console.error('获取统计数据失败:', error);
    }
}
```

### 3. 数据报表
可以用于生成日报、周报等数据报表：

```go
// 获取统计数据用于报表
stats, err := service.ZbArticle().GetArticleStats(ctx)
if err != nil {
    return err
}

report := map[string]interface{}{
    "date": gtime.Now().Format("Y-m-d"),
    "total_articles": stats.TotalArticles,
    "today_new": stats.TodayArticles,
    "published_rate": float64(stats.PublishedArticles) / float64(stats.TotalArticles) * 100,
}
```

## 测试说明

### 1. 功能测试
```bash
# 使用curl测试接口
curl -X GET "http://localhost:8000/zb_article/stats" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json"
```

### 2. 数据验证
- 检查今日新增数量是否正确
- 验证时间范围统计的准确性
- 确认删除的文章不被统计

### 3. 边界测试
- 测试跨日期时的统计准确性
- 测试没有新增文章时的返回值（应为0）
- 测试大量数据时的查询性能

## 性能考虑

### 1. 数据库索引
确保 `created_at` 字段有适当的索引：
```sql
-- 如果没有索引，建议添加
ALTER TABLE zb_article ADD INDEX idx_created_at (created_at);
```

### 2. 缓存策略
对于高频访问的统计数据，可以考虑添加缓存：
```go
// 示例：使用Redis缓存今日统计
cacheKey := "article_stats_today_" + gtime.Now().Format("Y-m-d")
// 缓存1小时，避免频繁查询数据库
```

### 3. 查询优化
- 使用覆盖索引减少回表查询
- 考虑在低峰期预计算统计数据

## 扩展功能

### 1. 更多时间维度统计
可以扩展支持更多时间维度：
- 本周新增
- 本月新增
- 本年新增

### 2. 分类统计
可以按分类统计今日新增：
```go
// 按分类统计今日新增
GetTodayStatsByCategory(ctx context.Context) (map[string]int, error)
```

### 3. 趋势分析
可以返回最近7天的新增趋势：
```go
// 最近7天新增趋势
GetRecentTrend(ctx context.Context, days int) ([]DailyStats, error)
```

## 注意事项

1. **时区问题**: 确保服务器时区设置正确
2. **数据一致性**: 统计数据与实际数据保持一致
3. **性能影响**: 大数据量时注意查询性能
4. **错误处理**: 完善异常情况的处理逻辑

---

**功能状态**: ✅ 已完成  
**测试状态**: 待测试  
**文档版本**: v1.0  
**最后更新**: 2025-01-23
