# 套餐管理API文档

## 概述

套餐管理模块提供了会员套餐的完整CRUD功能，支持价格管理、状态控制、折扣计算等功能。该模块用于管理用户购买的会员套餐信息。

## 基础信息

- **模块名称**: 套餐管理
- **基础路径**: `/zb_good`
- **认证方式**: JWT Token
- **权限验证**: 需要相应的套餐管理权限

## 数据字段说明

| 字段名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| id | int | 否 | 套餐ID，自增主键 | `1` |
| name | string | 是 | 套餐名称，1-255个字符 | `VIP会员套餐` |
| tag | string | 否 | 套餐标签，0-255个字符 | `热门` |
| original_price | float64 | 是 | 原始价格，不能小于0 | `199.00` |
| price | float64 | 是 | 现时价格，不能小于0且不能大于原始价格 | `99.00` |
| effective | int | 是 | 会员有效期（月），必须大于0 | `12` |
| is_disable | int | 否 | 是否禁用：0=否，1=是 | `0` |
| is_delete | int | 否 | 是否删除：0=否，1=是 | `0` |
| created_at | datetime | 否 | 创建时间 | `2025-01-15 10:00:00` |
| updated_at | datetime | 否 | 更新时间 | `2025-01-15 10:00:00` |
| deleted_at | datetime | 否 | 删除时间 | `null` |
| discount | float64 | 否 | 折扣率（计算字段） | `0.5` |
| discount_text | string | 否 | 折扣文本（计算字段） | `5折` |

## API接口列表

### 1. 创建套餐

**接口地址**: `POST /zb_good`

**接口描述**: 创建新的套餐

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 套餐名称，1-255个字符 |
| tag | string | 否 | 套餐标签，0-255个字符 |
| original_price | float64 | 是 | 原始价格，不能小于0 |
| price | float64 | 是 | 现时价格，不能小于0且不能大于原始价格 |
| effective | int | 是 | 会员有效期（月），必须大于0 |
| is_disable | int | 否 | 是否禁用：0=否，1=是，默认0 |

**请求示例**:
```json
{
  "name": "VIP会员套餐",
  "tag": "热门",
  "original_price": 199.00,
  "price": 99.00,
  "effective": 12,
  "is_disable": 0
}
```

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| id | int | 创建的套餐ID |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1
  }
}
```

### 2. 更新套餐

**接口地址**: `PUT /zb_good/{id}`

**接口描述**: 更新指定的套餐信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 套餐ID（路径参数） |
| name | string | 是 | 套餐名称，1-255个字符 |
| tag | string | 否 | 套餐标签，0-255个字符 |
| original_price | float64 | 是 | 原始价格，不能小于0 |
| price | float64 | 是 | 现时价格，不能小于0且不能大于原始价格 |
| effective | int | 是 | 会员有效期（月），必须大于0 |
| is_disable | int | 否 | 是否禁用：0=否，1=是 |

**请求示例**:
```json
{
  "name": "VIP会员套餐",
  "tag": "热门",
  "original_price": 199.00,
  "price": 99.00,
  "effective": 12,
  "is_disable": 0
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 3. 删除套餐

**接口地址**: `DELETE /zb_good/{id}`

**接口描述**: 删除指定的套餐（软删除）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 套餐ID（路径参数） |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 4. 获取单个套餐

**接口地址**: `GET /zb_good/{id}`

**接口描述**: 获取指定的套餐详细信息

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 套餐ID（路径参数） |

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| id | int | 套餐ID |
| name | string | 套餐名称 |
| tag | string | 套餐标签 |
| original_price | float64 | 原始价格 |
| price | float64 | 现时价格 |
| effective | int | 会员有效期（月） |
| is_disable | int | 是否禁用 |
| is_delete | int | 是否删除 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |
| deleted_at | string | 删除时间 |
| discount | float64 | 折扣率 |
| discount_text | string | 折扣文本 |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "VIP会员套餐",
    "tag": "热门",
    "original_price": 199.00,
    "price": 99.00,
    "effective": 12,
    "is_disable": 0,
    "is_delete": 0,
    "created_at": "2025-01-15 10:00:00",
    "updated_at": "2025-01-15 10:00:00",
    "deleted_at": null,
    "discount": 0.5,
    "discount_text": "5折"
  }
}
```

### 5. 获取套餐列表

**接口地址**: `GET /zb_good/list`

**接口描述**: 获取套餐分页列表

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认10，最大100 |
| name | string | 否 | 套餐名称，模糊搜索 |
| is_disable | int | 否 | 是否禁用：0=否，1=是 |
| min_price | float64 | 否 | 最低价格筛选 |
| max_price | float64 | 否 | 最高价格筛选 |

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| list | array | 套餐列表 |
| total | int | 总数 |
| page | int | 当前页码 |
| page_size | int | 每页数量 |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "VIP会员套餐",
        "tag": "热门",
        "original_price": 199.00,
        "price": 99.00,
        "effective": 12,
        "is_disable": 0,
        "is_delete": 0,
        "created_at": "2025-01-15 10:00:00",
        "updated_at": "2025-01-15 10:00:00",
        "deleted_at": null,
        "discount": 0.5,
        "discount_text": "5折"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10
  }
}
```

### 6. 获取所有套餐

**接口地址**: `GET /zb_good/all`

**接口描述**: 获取所有套餐（不分页）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| is_disable | int | 否 | 是否禁用：0=否，1=是 |

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| list | array | 套餐列表 |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "VIP会员套餐",
        "tag": "热门",
        "original_price": 199.00,
        "price": 99.00,
        "effective": 12,
        "is_disable": 0,
        "is_delete": 0,
        "created_at": "2025-01-15 10:00:00",
        "updated_at": "2025-01-15 10:00:00",
        "deleted_at": null,
        "discount": 0.5,
        "discount_text": "5折"
      }
    ]
  }
}
```

### 7. 更新套餐状态

**接口地址**: `PUT /zb_good/{id}/status`

**接口描述**: 更新指定套餐的启用/禁用状态

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 套餐ID（路径参数） |
| is_disable | int | 是 | 是否禁用：0=否，1=是 |

**请求示例**:
```json
{
  "is_disable": 1
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 8. 批量删除套餐

**接口地址**: `DELETE /zb_good/batch`

**接口描述**: 批量删除套餐（软删除）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | array | 是 | 套餐ID列表 |

**请求示例**:
```json
{
  "ids": [1, 2, 3]
}
```

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| count | int | 删除数量 |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "count": 3
  }
}
```

### 9. 获取可用套餐

**接口地址**: `GET /zb_good/active`

**接口描述**: 获取所有启用的套餐（用于前台展示）

**请求参数**: 无

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| list | array | 可用套餐列表 |

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "VIP会员套餐",
        "tag": "热门",
        "original_price": 199.00,
        "price": 99.00,
        "effective": 12,
        "is_disable": 0,
        "is_delete": 0,
        "created_at": "2025-01-15 10:00:00",
        "updated_at": "2025-01-15 10:00:00",
        "deleted_at": null,
        "discount": 0.5,
        "discount_text": "5折"
      }
    ]
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 常见错误示例

### 参数验证错误
```json
{
  "code": 400,
  "message": "套餐名称不能为空",
  "data": null
}
```

### 业务逻辑错误
```json
{
  "code": 400,
  "message": "现时价格不能大于原始价格",
  "data": null
}
```

### 资源不存在
```json
{
  "code": 404,
  "message": "套餐不存在",
  "data": null
}
```

## 业务规则

1. **名称唯一性**：
   - 套餐名称不能重复
   - 更新时排除自己进行重复检查

2. **价格验证**：
   - 原始价格和现时价格都不能小于0
   - 现时价格不能大于原始价格
   - 自动计算折扣率和折扣文本

3. **软删除机制**：
   - 删除采用软删除方式
   - 删除后的数据不会在列表中显示

4. **状态控制**：
   - 支持启用/禁用状态切换
   - 禁用的套餐不会在可用套餐接口中返回

5. **排序规则**：
   - 按价格升序排列
   - 价格相同时按ID升序排列

6. **折扣计算**：
   - 自动计算折扣率（现时价格/原始价格）
   - 自动生成折扣文本（如"5折"、"8折"等）
   - 无折扣时显示"无折扣"

## 使用示例

### 创建套餐
```bash
curl -X POST "http://localhost:8000/zb_good" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "VIP会员套餐",
    "tag": "热门",
    "original_price": 199.00,
    "price": 99.00,
    "effective": 12,
    "is_disable": 0
  }'
```

### 获取套餐列表
```bash
curl -X GET "http://localhost:8000/zb_good/list?page=1&page_size=10" \
  -H "Authorization: Bearer your_token"
```

### 获取可用套餐
```bash
curl -X GET "http://localhost:8000/zb_good/active" \
  -H "Authorization: Bearer your_token"
```

### 更新套餐状态
```bash
curl -X PUT "http://localhost:8000/zb_good/1/status" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "is_disable": 1
  }'
```

### 批量删除套餐
```bash
curl -X DELETE "http://localhost:8000/zb_good/batch" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [1, 2, 3]
  }'
```
