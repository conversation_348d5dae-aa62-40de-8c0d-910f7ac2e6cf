-- 数据库外键约束修复脚本
-- 问题：原SQL文件中的外键约束方向错误

-- 1. 删除错误的外键约束
-- 注意：需要先查看当前数据库中的外键约束名称，然后删除

-- 查看当前外键约束（执行此查询来获取约束名称）
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM 
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE 
    REFERENCED_TABLE_SCHEMA = 'diao_tea'
    AND TABLE_NAME IN ('sys_menu', 'sys_role_menu', 'sys_role');

-- 2. 删除错误的外键约束（请根据上面查询结果替换约束名称）
-- ALTER TABLE `sys_menu` DROP FOREIGN KEY `约束名称`;
-- ALTER TABLE `sys_role` DROP FOREIGN KEY `约束名称`;

-- 示例（约束名称可能不同，请根据实际情况修改）：
-- ALTER TABLE `sys_menu` DROP FOREIGN KEY `sys_menu_ibfk_1`;
-- ALTER TABLE `sys_role` DROP FOREIGN KEY `sys_role_ibfk_1`;

-- 3. 创建正确的外键约束

-- sys_role_menu表应该引用sys_menu和sys_role表
ALTER TABLE `sys_role_menu`
ADD CONSTRAINT `fk_role_menu_menu_id` 
FOREIGN KEY (`menu_id`) REFERENCES `sys_menu`(`id`)
ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE `sys_role_menu`
ADD CONSTRAINT `fk_role_menu_role_id` 
FOREIGN KEY (`role_id`) REFERENCES `sys_role`(`id`)
ON UPDATE CASCADE ON DELETE CASCADE;

-- 4. 其他表的外键约束也需要检查和修复

-- sys_admin_role表的外键约束
ALTER TABLE `sys_admin_role`
ADD CONSTRAINT `fk_admin_role_admin_id` 
FOREIGN KEY (`admin_id`) REFERENCES `sys_admins`(`id`)
ON UPDATE CASCADE ON DELETE CASCADE;

ALTER TABLE `sys_admin_role`
ADD CONSTRAINT `fk_admin_role_role_id` 
FOREIGN KEY (`role_id`) REFERENCES `sys_role`(`id`)
ON UPDATE CASCADE ON DELETE CASCADE;

-- sys_config表的外键约束
ALTER TABLE `sys_config`
ADD CONSTRAINT `fk_config_group_id` 
FOREIGN KEY (`group_id`) REFERENCES `sys_config_group`(`id`)
ON UPDATE CASCADE ON DELETE CASCADE;

-- sys_dict表的外键约束
ALTER TABLE `sys_dict`
ADD CONSTRAINT `fk_dict_group_id` 
FOREIGN KEY (`group_id`) REFERENCES `sys_dict_group`(`id`)
ON UPDATE CASCADE ON DELETE CASCADE;

-- sys_resources表的外键约束
ALTER TABLE `sys_resources`
ADD CONSTRAINT `fk_resources_group_id` 
FOREIGN KEY (`group_id`) REFERENCES `sys_resources_group`(`id`)
ON UPDATE CASCADE ON DELETE CASCADE;

-- sys_login_log表的外键约束
ALTER TABLE `sys_login_log`
ADD CONSTRAINT `fk_login_log_admin_id` 
FOREIGN KEY (`admin_id`) REFERENCES `sys_admins`(`id`)
ON UPDATE CASCADE ON DELETE CASCADE;

-- sys_operate_log表的外键约束
ALTER TABLE `sys_operate_log`
ADD CONSTRAINT `fk_operate_log_admin_id` 
FOREIGN KEY (`admin_id`) REFERENCES `sys_admins`(`id`)
ON UPDATE CASCADE ON DELETE CASCADE;
