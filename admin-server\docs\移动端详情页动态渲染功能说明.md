# 移动端详情页动态渲染功能说明

## 功能概述

移动端文章详情页面现在支持根据 `articleContent` 变量动态渲染页面内容，替代了原来的静态硬编码数据。页面会根据后端传递的文章数据自动填充各个区域的内容。

## 实现的功能

### 1. 动态数据渲染
- **标题区域**: 动态显示文章标题、分类标签、城市标签、浏览次数、作者信息
- **基本信息**: 根据文章数据生成基本信息列表
- **项目概况**: 显示文章简介信息
- **详细内容**: 渲染文章的完整内容，支持HTML和纯文本
- **发布时间**: 智能显示相对时间（刚刚、几分钟前等）

### 2. 数据适配
- **空数据处理**: 当某些字段为空时显示默认内容或隐藏相关区域
- **时间格式化**: 自动格式化时间显示
- **内容处理**: 支持HTML内容和纯文本内容的渲染
- **分享配置**: 根据文章数据更新微信分享信息

### 3. 用户体验优化
- **加载状态**: 页面加载时显示"加载中..."状态
- **错误处理**: 当数据为空或格式错误时的友好提示
- **响应式布局**: 保持原有的移动端适配效果

## 技术实现

### 1. 数据结构
页面期望的 `articleContent` 数据结构：
```javascript
{
    id: 1,                          // 文章ID
    title: "文章标题",               // 文章标题
    intro: "文章简介",               // 文章简介
    content: "文章详细内容",         // 文章内容（支持HTML）
    author: "作者名称",              // 作者
    view_count: 100,                // 浏览次数
    cate_name: "分类名称",           // 分类名称
    city_name: "城市名称",           // 城市名称
    created_at: "2025-01-23T10:00:00Z", // 创建时间
    updated_at: "2025-01-23T10:00:00Z"  // 更新时间
}
```

### 2. 核心渲染函数

#### renderArticleData()
主渲染函数，协调所有子渲染函数：
```javascript
function renderArticleData() {
    if (!articleContent) {
        console.error('文章数据为空');
        return;
    }
    
    renderTitleSection();      // 渲染标题区域
    renderBasicInfo();         // 渲染基本信息
    renderProjectOverview();   // 渲染项目概况
    renderDetailContent();     // 渲染详细内容
    updateShareConfig();       // 更新分享配置
}
```

#### renderTitleSection()
渲染页面顶部的标题区域：
```javascript
function renderTitleSection() {
    // 设置文章标题
    const titleElement = document.getElementById('articleTitle');
    if (titleElement && articleContent.title) {
        titleElement.textContent = articleContent.title;
    }
    
    // 设置浏览次数、作者、发布时间等
    // 渲染分类和城市标签
}
```

#### renderBasicInfo()
动态生成基本信息列表：
```javascript
function renderBasicInfo() {
    const infoItems = [
        { label: '文章ID', value: articleContent.id, type: 'text' },
        { label: '分类', value: articleContent.cate_name, type: 'tag' },
        // ... 其他信息项
    ];
    
    // 根据数据类型应用不同样式
}
```

#### renderDetailContent()
渲染文章详细内容：
```javascript
function renderDetailContent() {
    if (articleContent.content) {
        // 支持HTML内容
        if (articleContent.content.includes('<')) {
            contentElement.innerHTML = articleContent.content;
        } else {
            // 纯文本转换换行符
            const formattedContent = articleContent.content.replace(/\n/g, '<br>');
            contentElement.innerHTML = formattedContent;
        }
    }
}
```

### 3. 辅助函数

#### formatTimeAgo()
智能时间格式化：
```javascript
function formatTimeAgo(dateString) {
    const diffMins = Math.floor((now - date) / 60000);
    
    if (diffMins < 1) return '刚刚发布';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    // ...
}
```

#### formatDateTime()
标准日期时间格式化：
```javascript
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}
```

## 页面结构变化

### 1. HTML结构调整
将静态内容替换为动态容器：

**修改前**（静态内容）：
```html
<h2 class="text-lg font-bold text-gray-800 mb-3">中国银行周口分行办公楼装修改造工程</h2>
```

**修改后**（动态容器）：
```html
<h2 class="text-lg font-bold text-gray-800 mb-3" id="articleTitle">加载中...</h2>
```

### 2. 主要容器ID
- `articleTitle`: 文章标题
- `categoryTags`: 分类标签容器
- `publishTime`: 发布时间
- `viewCount`: 浏览次数
- `authorName`: 作者名称
- `basicInfo`: 基本信息容器
- `projectOverview`: 项目概况容器
- `articleContent`: 详细内容容器

## 数据流程

### 1. 页面加载流程
```
页面加载 → DOMContentLoaded事件 → renderArticleData() → 
各子渲染函数 → 页面内容更新完成 → 加载微信JSSDK
```

### 2. 数据渲染流程
```
检查articleContent → 渲染标题区域 → 渲染基本信息 → 
渲染项目概况 → 渲染详细内容 → 更新分享配置
```

### 3. 错误处理流程
```
数据检查 → 空数据处理 → 显示默认内容或错误提示 → 
记录错误日志 → 继续渲染其他部分
```

## 兼容性处理

### 1. 数据缺失处理
- 当某个字段为空时，显示默认值或隐藏相关元素
- 提供友好的"暂无信息"提示

### 2. 内容格式处理
- 自动检测HTML内容和纯文本内容
- 处理换行符和特殊字符
- 防止XSS攻击（使用textContent而非innerHTML处理用户输入）

### 3. 时间处理
- 兼容不同的时间格式
- 处理时区问题
- 提供备用的时间显示方案

## 使用示例

### 1. 后端数据传递
```go
// 在Controller中传递数据到模板
data := g.Map{
    "content": articleData,
    "is_vip": userVipStatus,
    "is_logged_in": isLoggedIn,
}
return gtpl.ParseContent(template, data)
```

### 2. 前端数据接收
```javascript
// 页面中的数据变量
var articleContent = {{.content}}

// 页面加载后自动渲染
document.addEventListener('DOMContentLoaded', function() {
    renderArticleData();
});
```

## 测试验证

### 1. 数据完整性测试
- 测试所有字段都有数据的情况
- 测试部分字段为空的情况
- 测试所有字段都为空的情况

### 2. 内容格式测试
- 测试纯文本内容
- 测试HTML格式内容
- 测试包含特殊字符的内容

### 3. 时间显示测试
- 测试刚发布的文章
- 测试几分钟前发布的文章
- 测试几天前发布的文章

## 注意事项

1. **数据安全**: 使用textContent处理用户输入，innerHTML仅用于可信的HTML内容
2. **性能考虑**: 避免频繁的DOM操作，批量更新元素
3. **错误处理**: 完善的错误处理和日志记录
4. **用户体验**: 提供加载状态和错误提示
5. **兼容性**: 确保在不同浏览器和设备上正常工作

---

**功能状态**: ✅ 已完成  
**测试状态**: 待测试  
**文档版本**: v1.0  
**最后更新**: 2025-01-23
