# RBAC权限系统

## 概述

本系统基于GoFrame框架实现了完整的RBAC（基于角色的访问控制）权限管理系统，参考了GoFrame官方文档的最佳实践。

## 系统架构

### 数据库设计

```
sys_admins (管理员表)
├── id (主键)
├── username (用户名)
├── password (密码)
├── is_super (是否超级管理员: 0-否, 1-是)
└── ...

sys_role (角色表)
├── id (主键)
├── name (角色名称)
├── remark (备注)
└── ...

sys_menu (菜单权限表)
├── id (主键)
├── pid (父级ID)
├── menu_type (菜单类型: M-目录, C-菜单, A-按钮)
├── menu_name (菜单名称)
├── perms (权限标识)
├── paths (路径)
└── ...

sys_admin_role (管理员角色关联表)
├── admin_id (管理员ID)
└── role_id (角色ID)

sys_role_menu (角色菜单关联表)
├── role_id (角色ID)
└── menu_id (菜单ID)
```

### 权限验证流程

```
用户请求 → JWT认证 → 权限验证 → 业务逻辑
```

1. **JWT认证中间件** - 验证token，设置admin_id到上下文
2. **权限验证中间件** - 检查用户是否有访问权限
3. **业务逻辑** - 执行具体的业务操作

## 权限验证逻辑

### 1. 超级管理员

- `is_super = 1` 的管理员拥有所有权限
- 跳过权限检查，直接允许访问

### 2. 普通管理员

权限验证步骤：

1. 查询管理员的角色关联 (`sys_admin_role`)
2. 查询角色的菜单权限 (`sys_role_menu`)
3. 查询菜单的权限信息 (`sys_menu`)
4. 进行权限匹配验证

### 3. 权限匹配规则

支持三种匹配方式：

#### 精确匹配
```go
// 权限标识: system:admin:list
// 请求: GET /sys_admin
// 匹配: ✅
```

#### 路径匹配
```go
// 菜单路径: /system/admin
// 请求路径: /system/admin/list
// 匹配: ✅ (前缀匹配)
```

#### 权限转换匹配
```go
// 权限格式: 模块:资源:操作
system:admin:list    -> GET /sys_admin
system:admin:add     -> POST /sys_admin
system:admin:edit    -> PUT /sys_admin/{id}
system:admin:delete  -> DELETE /sys_admin/{id}
system:admin:roles   -> PUT /sys_admin/{id}/roles
```

## 权限配置

### 1. 权限标识格式

```
模块:资源:操作
```

示例：
- `system:admin:list` - 系统管理员列表
- `system:role:add` - 系统角色添加
- `system:menu:edit` - 系统菜单编辑
- `system:config:group:list` - 系统配置分组列表

### 2. 菜单类型

- **M (Menu Directory)** - 目录，用于组织菜单结构
- **C (Menu Component)** - 菜单，对应具体的页面
- **A (Action/Permission)** - 按钮/权限，对应具体的操作

### 3. 权限初始化

运行SQL脚本初始化基础权限：

```bash
mysql -u root -p your_database < tools/permission_init.sql
```

## API接口权限

### 系统管理

| 接口 | 方法 | 路径 | 权限标识 |
|------|------|------|----------|
| 管理员列表 | GET | /sys_admin | system:admin:list |
| 管理员详情 | GET | /sys_admin/{id} | system:admin:view |
| 添加管理员 | POST | /sys_admin | system:admin:add |
| 编辑管理员 | PUT | /sys_admin/{id} | system:admin:edit |
| 删除管理员 | DELETE | /sys_admin/{id} | system:admin:delete |
| 分配角色 | PUT | /sys_admin/{id}/roles | system:admin:roles |

### 角色管理

| 接口 | 方法 | 路径 | 权限标识 |
|------|------|------|----------|
| 角色列表 | GET | /sys_role | system:role:list |
| 角色详情 | GET | /sys_role/{id} | system:role:view |
| 添加角色 | POST | /sys_role | system:role:add |
| 编辑角色 | PUT | /sys_role/{id} | system:role:edit |
| 删除角色 | DELETE | /sys_role/{id} | system:role:delete |
| 分配权限 | PUT | /sys_role/{id}/menus | system:role:menus |

### 菜单管理

| 接口 | 方法 | 路径 | 权限标识 |
|------|------|------|----------|
| 菜单列表 | GET | /sys_menu | system:menu:list |
| 菜单详情 | GET | /sys_menu/{id} | system:menu:view |
| 添加菜单 | POST | /sys_menu | system:menu:add |
| 编辑菜单 | PUT | /sys_menu/{id} | system:menu:edit |
| 删除菜单 | DELETE | /sys_menu/{id} | system:menu:delete |

### 配置管理

| 接口 | 方法 | 路径 | 权限标识 |
|------|------|------|----------|
| 配置列表 | GET | /sys_config | system:config:list |
| 配置分组列表 | GET | /sys_config_group | system:config:group:list |

## 排除路径

以下路径不需要权限验证：

- `/auth/login` - 登录
- `/auth/refresh` - 刷新token
- `/auth/permissions` - 获取权限
- `/auth/menus` - 获取菜单
- `/auth/userinfo` - 获取用户信息
- `/auth/logout` - 退出登录
- `/health` - 健康检查
- `/metrics` - 监控指标
- `/static/*` - 静态资源

## 使用示例

### 1. 创建角色

```go
// 创建角色
role := &entity.SysRole{
    Name:   "内容管理员",
    Remark: "负责内容管理",
}

// 分配权限
menuIds := []int64{1, 10, 11, 12} // 菜单ID列表
```

### 2. 分配角色给用户

```go
// 为用户分配角色
adminRole := &entity.SysAdminRole{
    AdminId: 1,
    RoleId:  2,
}
```

### 3. 检查权限

权限验证由中间件自动处理，无需手动调用。

## 测试工具

### 1. 权限匹配测试

```bash
go run tools/permission_test.go
```

### 2. 权限初始化

```bash
mysql -u root -p your_database < tools/permission_init.sql
```

## 注意事项

1. **超级管理员** - 至少保留一个超级管理员账号
2. **权限粒度** - 根据业务需要合理设计权限粒度
3. **性能优化** - 可以考虑添加权限缓存
4. **安全性** - 定期审查权限分配情况
5. **日志记录** - 重要操作需要记录审计日志

## 扩展功能

### 1. 数据权限

可以扩展实现数据权限控制：
- 部门数据权限
- 个人数据权限
- 自定义数据权限

### 2. 权限缓存

可以添加Redis缓存提高性能：
- 用户权限缓存
- 角色权限缓存
- 菜单权限缓存

### 3. 动态权限

可以实现动态权限配置：
- 运行时权限更新
- 权限热加载
- 权限版本控制

这个RBAC权限系统提供了完整的权限管理功能，支持灵活的权限配置和高效的权限验证。
