package service

import (
	"admin-server/internal/model/entity"
	"context"
)

// ISearch 搜索服务接口
type ISearch interface {
	GetHotKeywords(ctx context.Context, limit int) ([]entity.ZbSearchKeywords, error)
	TrackSearch(ctx context.Context, keyword string, cityId int64) error
	TrackClick(ctx context.Context, keyword string) error
	UpdateKeywordTrend(ctx context.Context) error
}

var (
	localSearch ISearch
)

func Search() ISearch {
	if localSearch == nil {
		panic("implement not found for interface ISearch, forgot register?")
	}
	return localSearch
}

func RegisterSearch(i ISearch) {
	localSearch = i
}
