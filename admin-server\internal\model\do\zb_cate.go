// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ZbCate is the golang structure of table zb_cate for DAO operations like Where/Data.
type ZbCate struct {
	g.Meta    `orm:"table:zb_cate, do:true"`
	Id        interface{} //
	Name      interface{} // 类别名称
	Sort      interface{} // 排序
	IsDisable interface{} // 是否禁用: 0=否, 1=是
	IsDelete  interface{} // 是否删除: 0=否, 1=是
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 更新时间
	DeletedAt *gtime.Time // 删除时间
}
