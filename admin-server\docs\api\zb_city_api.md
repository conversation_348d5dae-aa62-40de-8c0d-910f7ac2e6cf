# 开通城市管理API - 前端对接文档

## 接口概览

| 接口名称 | 方法 | 路径 | 说明 |
|---------|------|------|------|
| 创建城市 | POST | `/zb/city` | 创建新的开通城市 |
| 更新城市 | PUT | `/zb/city/{id}` | 更新城市信息 |
| 删除城市 | DELETE | `/zb/city/{id}` | 删除城市（软删除） |
| 获取城市详情 | GET | `/zb/city/{id}` | 获取单个城市信息 |
| 获取城市列表 | GET | `/zb/city/list` | 获取城市分页列表 |
| 获取城市树 | GET | `/zb/city/tree` | 获取城市树形结构 |
| 更新排序 | PUT | `/zb/city/{id}/sort` | 更新城市排序 |
| 更新状态 | PUT | `/zb/city/{id}/status` | 更新城市状态 |

## 数据模型

### ZbCityInfo

```typescript
interface ZbCityInfo {
  id: number;                    // 城市ID
  pid: number;                   // 父级ID，0表示顶级
  name: string;                  // 城市名称
  sort: number;                  // 排序值
  is_disable: number;            // 是否禁用：0=否，1=是
  is_delete: number;             // 是否删除：0=否，1=是
  created_at: string | null;     // 创建时间
  updated_at: string | null;     // 更新时间
  deleted_at: string | null;     // 删除时间
}
```

### ZbCityTreeInfo

```typescript
interface ZbCityTreeInfo extends ZbCityInfo {
  children?: ZbCityTreeInfo[];   // 子城市列表
}
```

### 状态枚举

```typescript
enum CityStatus {
  ENABLED = 0,    // 启用
  DISABLED = 1    // 禁用
}

enum DeleteStatus {
  NORMAL = 0,     // 正常
  DELETED = 1     // 已删除
}
```

## 接口详情

### 1. 创建城市

```typescript
// 请求
interface CreateCityRequest {
  pid: number;                   // 父级ID
  name: string;                  // 城市名称
  sort?: number;                 // 排序值，默认1
  is_disable?: number;           // 是否禁用，默认0
}

// 响应
interface CreateCityResponse {
  id: number;                    // 创建的城市ID
}
```

### 2. 更新城市

```typescript
// 请求
interface UpdateCityRequest {
  id: number;                    // 城市ID（路径参数）
  pid: number;                   // 父级ID
  name: string;                  // 城市名称
  sort: number;                  // 排序值
  is_disable: number;            // 是否禁用
}

// 响应
interface UpdateCityResponse {}
```

### 3. 删除城市

```typescript
// 请求
interface DeleteCityRequest {
  id: number;                    // 城市ID（路径参数）
}

// 响应
interface DeleteCityResponse {}
```

### 4. 获取城市详情

```typescript
// 请求
interface GetCityRequest {
  id: number;                    // 城市ID（路径参数）
}

// 响应
interface GetCityResponse extends ZbCityInfo {}
```

### 5. 获取城市列表

```typescript
// 请求
interface GetCityListRequest {
  page?: number;                 // 页码，默认1
  page_size?: number;            // 每页数量，默认10
  name?: string;                 // 城市名称，模糊搜索
  is_disable?: number;           // 是否禁用筛选
  pid?: number;                  // 父级ID筛选
}

// 响应
interface GetCityListResponse {
  list: ZbCityInfo[];            // 城市列表
  total: number;                 // 总数
  page: number;                  // 当前页码
  page_size: number;             // 每页数量
}
```

### 6. 获取城市树

```typescript
// 请求
interface GetCityTreeRequest {
  is_disable?: number;           // 是否禁用筛选
}

// 响应
interface GetCityTreeResponse {
  list: ZbCityTreeInfo[];        // 城市树形列表
}
```

### 7. 更新排序

```typescript
// 请求
interface UpdateCitySortRequest {
  id: number;                    // 城市ID（路径参数）
  sort: number;                  // 排序值
}

// 响应
interface UpdateCitySortResponse {}
```

### 8. 更新状态

```typescript
// 请求
interface UpdateCityStatusRequest {
  id: number;                    // 城市ID（路径参数）
  is_disable: number;            // 是否禁用：0=否，1=是
}

// 响应
interface UpdateCityStatusResponse {}
```

## Vue 3 + TypeScript 示例

```typescript
import { ref, reactive } from 'vue'
import axios from 'axios'

// 城市管理组合式函数
export function useZbCity() {
  const cities = ref<ZbCityInfo[]>([])
  const cityTree = ref<ZbCityTreeInfo[]>([])
  const loading = ref(false)
  const total = ref(0)

  // 获取城市列表
  const getCityList = async (params: GetCityListRequest) => {
    loading.value = true
    try {
      const response = await axios.get('/zb/city/list', { params })
      cities.value = response.data.data.list
      total.value = response.data.data.total
      return response.data.data
    } catch (error) {
      console.error('获取城市列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取城市树形结构
  const getCityTree = async (params?: GetCityTreeRequest) => {
    loading.value = true
    try {
      const response = await axios.get('/zb/city/tree', { params })
      cityTree.value = response.data.data.list
      return response.data.data.list
    } catch (error) {
      console.error('获取城市树失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建城市
  const createCity = async (data: CreateCityRequest) => {
    try {
      const response = await axios.post('/zb/city', data)
      return response.data.data
    } catch (error) {
      console.error('创建城市失败:', error)
      throw error
    }
  }

  // 更新城市
  const updateCity = async (id: number, data: Omit<UpdateCityRequest, 'id'>) => {
    try {
      const response = await axios.put(`/zb/city/${id}`, data)
      return response.data.data
    } catch (error) {
      console.error('更新城市失败:', error)
      throw error
    }
  }

  // 删除城市
  const deleteCity = async (id: number) => {
    try {
      const response = await axios.delete(`/zb/city/${id}`)
      return response.data.data
    } catch (error) {
      console.error('删除城市失败:', error)
      throw error
    }
  }

  // 获取城市详情
  const getCityDetail = async (id: number) => {
    try {
      const response = await axios.get(`/zb/city/${id}`)
      return response.data.data
    } catch (error) {
      console.error('获取城市详情失败:', error)
      throw error
    }
  }

  // 更新城市排序
  const updateCitySort = async (id: number, sort: number) => {
    try {
      const response = await axios.put(`/zb/city/${id}/sort`, { sort })
      return response.data.data
    } catch (error) {
      console.error('更新城市排序失败:', error)
      throw error
    }
  }

  // 更新城市状态
  const updateCityStatus = async (id: number, is_disable: number) => {
    try {
      const response = await axios.put(`/zb/city/${id}/status`, { is_disable })
      return response.data.data
    } catch (error) {
      console.error('更新城市状态失败:', error)
      throw error
    }
  }

  return {
    cities,
    cityTree,
    loading,
    total,
    getCityList,
    getCityTree,
    createCity,
    updateCity,
    deleteCity,
    getCityDetail,
    updateCitySort,
    updateCityStatus
  }
}
```

## 表单验证规则

```typescript
// 城市表单验证规则
export const cityFormRules = {
  name: [
    { required: true, message: '城市名称不能为空' },
    { min: 1, max: 255, message: '城市名称长度必须在1-255个字符之间' }
  ],
  pid: [
    { required: true, message: '请选择父级城市' },
    { type: 'number', min: 0, message: '父级ID不能小于0' }
  ],
  sort: [
    { type: 'number', min: 0, message: '排序值不能小于0' }
  ],
  is_disable: [
    { required: true, message: '请选择状态' },
    { type: 'enum', enum: [0, 1], message: '状态值必须是0或1' }
  ]
}
```

## 页面组件示例（Vue 3）

```vue
<template>
  <div class="city-management">
    <!-- 城市列表 -->
    <el-table :data="cities" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="城市名称" />
      <el-table-column prop="sort" label="排序" width="100" />
      <el-table-column prop="is_disable" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.is_disable === 0 ? 'success' : 'danger'">
            {{ row.is_disable === 0 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button 
            size="small" 
            :type="row.is_disable === 0 ? 'warning' : 'success'"
            @click="handleToggleStatus(row)"
          >
            {{ row.is_disable === 0 ? '禁用' : '启用' }}
          </el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.pageSize"
      :total="total"
      @current-change="handlePageChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useZbCity } from './composables/useZbCity'

const { cities, loading, total, getCityList, updateCityStatus, deleteCity } = useZbCity()

const pagination = ref({
  page: 1,
  pageSize: 10
})

// 获取列表数据
const fetchData = async () => {
  await getCityList({
    page: pagination.value.page,
    page_size: pagination.value.pageSize
  })
}

// 切换状态
const handleToggleStatus = async (row: ZbCityInfo) => {
  const newStatus = row.is_disable === 0 ? 1 : 0
  await updateCityStatus(row.id, newStatus)
  await fetchData()
}

// 删除城市
const handleDelete = async (row: ZbCityInfo) => {
  await deleteCity(row.id)
  await fetchData()
}

// 页码变化
const handlePageChange = (page: number) => {
  pagination.value.page = page
  fetchData()
}

// 页大小变化
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.page = 1
  fetchData()
}

onMounted(() => {
  fetchData()
})
</script>
```

## 注意事项

1. **层级关系**：创建或更新城市时需要正确设置父级ID
2. **名称唯一性**：同级城市名称不能重复
3. **删除限制**：有子城市的城市不能删除
4. **状态管理**：禁用状态不影响子城市
5. **排序功能**：支持拖拽排序或手动输入排序值
6. **树形展示**：使用树形组件展示层级关系
7. **权限控制**：根据用户权限显示相应操作按钮
