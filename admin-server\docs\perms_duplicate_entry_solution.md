# 权限标识重复错误解决方案

## 问题描述

在添加第二个目录类型菜单时出现以下错误：

```
Error 1062 (23000): Duplicate entry '' for key 'perms'
```

## 问题原因

数据库中 `sys_menu` 表的 `perms` 字段设置为 `NOT NULL UNIQUE`：

```sql
`perms` VARCHAR(255) NOT NULL UNIQUE COMMENT '权限标识'
```

当多个目录都使用空字符串作为权限标识时，违反了唯一性约束。

## 解决方案

### 方案一：修改数据库结构（推荐）

**步骤1：执行数据库修改脚本**

```sql
-- 1. 删除现有的唯一索引
ALTER TABLE `sys_menu` DROP INDEX `perms`;

-- 2. 修改字段允许NULL
ALTER TABLE `sys_menu` MODIFY COLUMN `perms` VARCHAR(255) NULL COMMENT '权限标识';

-- 3. 将现有的空字符串更新为NULL
UPDATE `sys_menu` SET `perms` = NULL WHERE `perms` = '';

-- 4. 创建新的唯一索引，只对非NULL值生效
CREATE UNIQUE INDEX `idx_perms_unique` ON `sys_menu` (`perms`);
```

**步骤2：验证修改结果**

```sql
-- 查看表结构
DESCRIBE `sys_menu`;

-- 查看索引
SHOW INDEX FROM `sys_menu` WHERE Key_name = 'idx_perms_unique';
```

### 方案二：仅修改业务逻辑（当前实现）

如果不想修改数据库结构，已经在业务逻辑中实现了以下处理：

1. **Create方法**：目录类型的空权限标识转换为NULL
2. **Update方法**：同样的转换逻辑
3. **checkPerms方法**：跳过空字符串的重复检查

```go
// 处理权限标识：目录类型如果为空则设为NULL
var perms interface{}
if req.MenuType == packed.MENU_TYPE_DIR && req.Perms == "" {
    perms = nil
} else {
    perms = req.Perms
}
```

## 立即解决方法

### 快速修复（临时方案）

如果需要立即解决问题，可以执行以下SQL：

```sql
-- 临时删除唯一索引
ALTER TABLE `sys_menu` DROP INDEX `perms`;

-- 重新创建允许NULL的唯一索引
CREATE UNIQUE INDEX `idx_perms_unique` ON `sys_menu` (`perms`);
```

### 完整修复（推荐方案）

执行完整的数据库修改脚本：

```sql
-- 完整修复脚本
ALTER TABLE `sys_menu` DROP INDEX `perms`;
ALTER TABLE `sys_menu` MODIFY COLUMN `perms` VARCHAR(255) NULL COMMENT '权限标识';
UPDATE `sys_menu` SET `perms` = NULL WHERE `perms` = '';
CREATE UNIQUE INDEX `idx_perms_unique` ON `sys_menu` (`perms`);
```

## 验证解决方案

### 测试用例1：创建多个目录

```bash
# 第一个目录
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 0,
    "menu_type": "M",
    "menu_name": "系统管理",
    "perms": ""
  }'

# 第二个目录
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 0,
    "menu_type": "M", 
    "menu_name": "业务管理",
    "perms": ""
  }'
```

### 测试用例2：权限标识唯一性

```bash
# 创建带权限标识的菜单
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 1,
    "menu_type": "C",
    "menu_name": "用户管理",
    "perms": "system:user:list"
  }'

# 尝试创建重复权限标识（应该失败）
curl -X POST http://localhost:8000/sys_menu/create \
  -H "Content-Type: application/json" \
  -d '{
    "pid": 1,
    "menu_type": "C",
    "menu_name": "角色管理",
    "perms": "system:user:list"
  }'
```

## 数据库状态检查

### 检查当前权限标识

```sql
-- 查看所有菜单的权限标识
SELECT id, menu_name, menu_type, perms FROM sys_menu WHERE is_delete = 0;

-- 查看NULL权限标识的菜单
SELECT id, menu_name, menu_type, perms FROM sys_menu WHERE perms IS NULL;

-- 查看空字符串权限标识的菜单
SELECT id, menu_name, menu_type, perms FROM sys_menu WHERE perms = '';
```

### 检查索引状态

```sql
-- 查看表的所有索引
SHOW INDEX FROM sys_menu;

-- 查看权限标识相关的索引
SHOW INDEX FROM sys_menu WHERE Column_name = 'perms';
```

## 注意事项

1. **数据备份**：在修改数据库结构前，请务必备份数据
2. **生产环境**：在生产环境中执行修改前，请先在测试环境验证
3. **应用重启**：修改数据库结构后，建议重启应用程序
4. **索引性能**：新的唯一索引只对非NULL值生效，不会影响查询性能

## 预防措施

1. **设计阶段**：在设计数据库时，考虑字段的NULL值处理
2. **业务逻辑**：在业务逻辑中明确处理可选字段的存储方式
3. **测试覆盖**：增加边界情况的测试用例
4. **文档说明**：在API文档中明确说明字段的可选性

## 后续优化

1. **数据迁移**：将现有的空字符串权限标识迁移为NULL
2. **查询优化**：更新查询逻辑，正确处理NULL值
3. **前端适配**：前端显示时将NULL值显示为空字符串
4. **API文档**：更新文档说明权限标识的存储方式

通过以上解决方案，可以彻底解决权限标识重复的问题，同时保持业务逻辑的正确性。
