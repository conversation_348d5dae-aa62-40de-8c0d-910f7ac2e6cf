package wechat_menu

import (
	"context"

	"admin-server/api/wechat_menu/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) UpdateStatus(ctx context.Context, req *v1.WechatMenuUpdateStatusReq) (res *v1.WechatMenuUpdateStatusRes, err error) {
	err = service.WechatMenu().UpdateStatus(ctx, req.Id, req.IsDisable)
	if err != nil {
		return nil, err
	}

	res = &v1.WechatMenuUpdateStatusRes{}
	return res, nil
}
