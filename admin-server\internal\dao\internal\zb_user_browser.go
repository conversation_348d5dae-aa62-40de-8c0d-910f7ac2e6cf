// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ZbUserBrowserDao is the data access object for table zb_user_browser.
type ZbUserBrowserDao struct {
	table   string               // table is the underlying table name of the DAO.
	group   string               // group is the database configuration group name of current DAO.
	columns ZbUserBrowserColumns // columns contains all the column names of Table for convenient usage.
}

// ZbUserBrowserColumns defines and stores column names for table zb_user_browser.
type ZbUserBrowserColumns struct {
	Id        string // 主键ID
	UserId    string // 会员id
	ArticleId string // 招标信息id
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
}

// zbUserBrowserColumns holds the columns for table zb_user_browser.
var zbUserBrowserColumns = ZbUserBrowserColumns{
	Id:        "id",
	UserId:    "user_id",
	ArticleId: "article_id",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// NewZbUserBrowserDao creates and returns a new DAO object for table data access.
func NewZbUserBrowserDao() *ZbUserBrowserDao {
	return &ZbUserBrowserDao{
		group:   "default",
		table:   "zb_user_browser",
		columns: zbUserBrowserColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *ZbUserBrowserDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *ZbUserBrowserDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *ZbUserBrowserDao) Columns() ZbUserBrowserColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *ZbUserBrowserDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *ZbUserBrowserDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *ZbUserBrowserDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.DB().Transaction(ctx, f)
}
