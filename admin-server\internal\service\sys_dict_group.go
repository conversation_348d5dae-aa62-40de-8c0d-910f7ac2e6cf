package service

import (
	"admin-server/internal/model/entity"
	"context"
)

// ISysDictGroup 字典分组服务接口
type ISysDictGroup interface {
	GetDictGroupList(ctx context.Context, page, pageSize int, name, code string, isDisable int) (list []entity.SysDictGroup, total int, err error)
	GetDictGroupDetail(ctx context.Context, id int64) (*entity.SysDictGroup, error)
	CreateDictGroup(ctx context.Context, name, code, remark string) error
	UpdateDictGroup(ctx context.Context, id int64, name, code, remark string) error
	DeleteDictGroup(ctx context.Context, ids []int64) error
	SetDictGroupStatus(ctx context.Context, id int64, isDisable int) error
	GetAllDictGroups(ctx context.Context) ([]entity.SysDictGroup, error)
	CheckDictGroupCodeExists(ctx context.Context, code string, excludeId int64) (bool, error)
}

var localSysDictGroup ISysDictGroup

func SysDictGroup() ISysDictGroup {
	if localSysDictGroup == nil {
		panic("ISysDictGroup接口未实现或未注册")
	}
	return localSysDictGroup
}

func RegisterSysDictGroup(i ISysDictGroup) {
	localSysDictGroup = i
}
