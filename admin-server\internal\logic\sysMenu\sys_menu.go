package sysMenu

import (
	v1 "admin-server/api/sys_menu/v1"
	"admin-server/internal/dao"
	"admin-server/internal/model/do"
	"admin-server/internal/model/entity"
	"admin-server/internal/packed"
	"admin-server/internal/service"
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"github.com/gogf/gf/v2/errors/gerror"
)

func init() {
	service.RegisterSysMenu(&SsysMenu{})
}

type SsysMenu struct {
}

func (s SsysMenu) UpdateSort(ctx context.Context, id int64, sort int) error {
	_, err := dao.SysMenu.Ctx(ctx).Where("id", id).Update(do.SysMenu{
		MenuSort:  sort,
		UpdatedAt: gtime.Now(),
	})
	if err != nil {
		g.Log().Error(ctx, "更新菜单排序失败:", err)
		return err
	}

	g.Log().Info(ctx, "更新菜单排序成功:", "id:", id, "sort:", sort)
	return nil
}

func (s SsysMenu) Create(ctx context.Context, req *v1.CreateReq) (insertId int64, err error) {
	// 验证权限标识：目录类型可选，菜单和按钮类型必填
	if err = s.validatePerms(ctx, req.MenuType, req.Perms, 0); err != nil {
		return 0, err
	}

	// 如果不是顶级菜单，检查父菜单是否存在
	if req.Pid != 0 {
		if err = s.checkParentExists(ctx, req.Pid); err != nil {
			return 0, err
		}
	}

	// 处理权限标识：目录类型如果为空则设为NULL
	var perms interface{}
	if req.MenuType == packed.MENU_TYPE_DIR && req.Perms == "" {
		perms = nil
	} else {
		perms = req.Perms
	}

	insertId, err = dao.SysMenu.Ctx(ctx).Data(do.SysMenu{
		Pid:       req.Pid,
		MenuType:  req.MenuType,
		MenuName:  req.MenuName,
		MenuIcon:  req.MenuIcon,
		MenuSort:  req.MenuSort,
		Perms:     perms,
		Paths:     req.Paths,
		Component: req.Component,
		Params:    req.Params,
		IsCache:   req.IsCache,
		IsShow:    req.IsShow,
		IsDisable: req.IsDisable,
	}).InsertAndGetId()

	return insertId, err
}

func (s SsysMenu) Update(ctx context.Context, req *v1.UpdateReq) (err error) {
	// 检查菜单是否存在
	count, err := dao.SysMenu.Ctx(ctx).Where("id", req.ID).Where("is_delete", packed.NO_DELETE).Count()
	if err != nil {
		return err
	}
	if count == 0 {
		return gerror.New("菜单不存在")
	}

	// 验证权限标识：目录类型可选，菜单和按钮类型必填
	if err = s.validatePerms(ctx, req.MenuType, req.Perms, req.ID); err != nil {
		return err
	}

	// 如果不是顶级菜单，检查父菜单是否存在
	if req.Pid != 0 {
		if err = s.checkParentExists(ctx, req.Pid); err != nil {
			return err
		}

		// 检查是否设置自己为父菜单
		if req.Pid == req.ID {
			return gerror.New("不能设置自己为父菜单")
		}
	}

	// 处理权限标识：目录类型如果为空则设为NULL
	var perms interface{}
	if req.MenuType == packed.MENU_TYPE_DIR && req.Perms == "" {
		perms = nil
	} else {
		perms = req.Perms
	}

	// 更新菜单信息
	_, err = dao.SysMenu.Ctx(ctx).Where("id", req.ID).Data(do.SysMenu{
		Pid:       req.Pid,
		MenuType:  req.MenuType,
		MenuName:  req.MenuName,
		MenuIcon:  req.MenuIcon,
		MenuSort:  req.MenuSort,
		Perms:     perms,
		Paths:     req.Paths,
		Component: req.Component,
		Params:    req.Params,
		IsCache:   req.IsCache,
		IsShow:    req.IsShow,
		IsDisable: req.IsDisable,
	}).Update()

	return err
}

func (s SsysMenu) GetList(ctx context.Context, req *v1.GetListReq) (list []*v1.MenuInfo, total int, err error) {
	// 构建查询条件
	m := dao.SysMenu.Ctx(ctx).Where("is_delete", packed.NO_DELETE)

	// 添加搜索条件
	if req.MenuName != "" {
		m = m.WhereLike("menu_name", "%"+req.MenuName+"%")
	}
	if req.MenuType != "" {
		m = m.Where("menu_type", req.MenuType)
	}
	if req.IsShow != nil {
		m = m.Where("is_show", *req.IsShow)
	}
	if req.IsDisable != nil {
		m = m.Where("is_disable", *req.IsDisable)
	}

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	var menus []entity.SysMenu
	err = m.Page(req.Page, req.PageSize).OrderAsc("menu_sort").OrderAsc("id").Scan(&menus)
	if err != nil {
		return nil, 0, err
	}

	// 转换为MenuInfo格式
	list = make([]*v1.MenuInfo, len(menus))
	for i, menu := range menus {
		list[i] = s.entityToMenuInfo(&menu)
	}

	return list, total, nil
}

func (s SsysMenu) GetTree(ctx context.Context, req *v1.GetTreeReq) (tree []*v1.MenuTree, err error) {
	// 构建查询条件
	m := dao.SysMenu.Ctx(ctx).Where("is_delete", packed.NO_DELETE)

	// 添加筛选条件
	if req.IsShow != nil {
		m = m.Where("is_show", *req.IsShow)
	}
	if req.IsDisable != nil {
		m = m.Where("is_disable", *req.IsDisable)
	}

	// 查询所有菜单
	var menus []entity.SysMenu
	err = m.OrderAsc("menu_sort").OrderAsc("id").Scan(&menus)
	if err != nil {
		return nil, err
	}

	// 转换为MenuInfo格式
	menuInfos := make([]*v1.MenuInfo, len(menus))
	for i, menu := range menus {
		menuInfos[i] = s.entityToMenuInfo(&menu)
	}

	// 构建树形结构
	tree, err = s.BuildTree(ctx, menuInfos, 0)
	return tree, err
}

func (s SsysMenu) GetOne(ctx context.Context, id int64) (menu *v1.MenuInfo, err error) {
	var sysMenu entity.SysMenu
	err = dao.SysMenu.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Scan(&sysMenu)
	if err != nil {
		return nil, err
	}

	if sysMenu.Id == 0 {
		return nil, gerror.New("菜单不存在")
	}

	menu = s.entityToMenuInfo(&sysMenu)
	return menu, nil
}

func (s SsysMenu) Delete(ctx context.Context, id int64) (err error) {
	// 检查菜单是否存在
	count, err := dao.SysMenu.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Count()
	if err != nil {
		return err
	}
	if count == 0 {
		return gerror.New("菜单不存在")
	}

	// 检查是否有子菜单
	childCount, err := dao.SysMenu.Ctx(ctx).Where("pid", id).Where("is_delete", packed.NO_DELETE).Count()
	if err != nil {
		return err
	}
	if childCount > 0 {
		return gerror.New("存在子菜单，无法删除")
	}

	// 软删除菜单
	_, err = dao.SysMenu.Ctx(ctx).Where("id", id).Data(do.SysMenu{
		IsDelete: packed.IS_DELETE,
	}).Update()

	return err
}

func (s SsysMenu) GetChildren(ctx context.Context, pid int64) (children []*v1.MenuInfo, err error) {
	var menus []entity.SysMenu
	err = dao.SysMenu.Ctx(ctx).Where("pid", pid).Where("is_delete", packed.NO_DELETE).OrderAsc("menu_sort").OrderAsc("id").Scan(&menus)
	if err != nil {
		return nil, err
	}

	children = make([]*v1.MenuInfo, len(menus))
	for i, menu := range menus {
		children[i] = s.entityToMenuInfo(&menu)
	}

	return children, nil
}

func (s SsysMenu) BuildTree(ctx context.Context, menus []*v1.MenuInfo, pid int64) (tree []*v1.MenuTree, err error) {
	tree = make([]*v1.MenuTree, 0)

	for _, menu := range menus {
		if menu.Pid == pid {
			node := &v1.MenuTree{
				MenuInfo: menu,
				Children: make([]*v1.MenuTree, 0),
			}

			// 递归构建子树
			children, err := s.BuildTree(ctx, menus, menu.ID)
			if err != nil {
				return nil, err
			}
			node.Children = children

			tree = append(tree, node)
		}
	}

	return tree, nil
}

// 辅助方法：验证权限标识（根据菜单类型决定是否必填）
func (s SsysMenu) validatePerms(ctx context.Context, menuType packed.MenuType, perms string, excludeId int64) error {
	// 目录类型权限标识可选，菜单和按钮类型必填
	if menuType != packed.MENU_TYPE_DIR {
		if perms == "" {
			return gerror.New("菜单和按钮类型的权限标识不能为空")
		}
	}

	// 如果提供了权限标识，检查是否重复
	if perms != "" {
		if err := s.checkPerms(ctx, perms, excludeId); err != nil {
			return err
		}
	}

	return nil
}

// 辅助方法：检查权限标识是否重复
func (s SsysMenu) checkPerms(ctx context.Context, perms string, excludeId int64) error {
	// 空字符串不需要检查重复（会被转换为NULL）
	if perms == "" {
		return nil
	}

	m := dao.SysMenu.Ctx(ctx).Where("perms", perms).Where("is_delete", packed.NO_DELETE)
	if excludeId > 0 {
		m = m.Where("id !=", excludeId)
	}

	count, err := m.Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return gerror.New("权限标识已存在")
	}
	return nil
}

// 辅助方法：检查父菜单是否存在
func (s SsysMenu) checkParentExists(ctx context.Context, pid int64) error {
	count, err := dao.SysMenu.Ctx(ctx).Where("id", pid).Where("is_delete", packed.NO_DELETE).Count()
	if err != nil {
		return err
	}
	if count == 0 {
		return gerror.New("父菜单不存在")
	}
	return nil
}

// 辅助方法：将entity转换为MenuInfo
func (s SsysMenu) entityToMenuInfo(menu *entity.SysMenu) *v1.MenuInfo {
	return &v1.MenuInfo{
		ID:        menu.Id,
		Pid:       menu.Pid,
		MenuType:  packed.MenuType(menu.MenuType),
		MenuName:  menu.MenuName,
		MenuIcon:  menu.MenuIcon,
		MenuSort:  menu.MenuSort,
		Perms:     menu.Perms,
		Paths:     menu.Paths,
		Component: menu.Component,
		Params:    menu.Params,
		IsCache:   packed.IsCache(menu.IsCache),
		IsShow:    packed.IsShow(menu.IsShow),
		IsDisable: packed.Disable(menu.IsDisable),
		CreatedAt: menu.CreatedAt,
		UpdatedAt: menu.UpdatedAt,
	}
}
