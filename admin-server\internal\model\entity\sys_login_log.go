// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SysLoginLog is the golang structure for table sys_login_log.
type SysLoginLog struct {
	Id        int64         `json:"id"        orm:"id"         description:""`      //
	AdminId   int64       `json:"adminId"   orm:"admin_id"   description:"管理员id"` // 管理员id
	Username  string      `json:"username"  orm:"username"   description:"登录账号"`  // 登录账号
	Ip        string      `json:"ip"        orm:"ip"         description:"登录ip"`  // 登录ip
	Os        string      `json:"os"        orm:"os"         description:"操作系统"`  // 操作系统
	Browser   string      `json:"browser"   orm:"browser"    description:"浏览器"`   // 浏览器
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`  // 创建时间
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:"更新时间"`  // 更新时间
}
