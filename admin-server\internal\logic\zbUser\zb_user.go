package zbUser

import (
	"admin-server/internal/dao"
	"admin-server/internal/model/do"
	"admin-server/internal/model/entity"
	"admin-server/internal/packed"
	"admin-server/internal/service"
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

type sZbUser struct{}

func init() {
	service.RegisterZbUser(&sZbUser{})
}

// GetUserList 获取会员列表
func (s *sZbUser) GetUserList(ctx context.Context, page, pageSize int, nickname, openid string, isDisable, vipStatus, hasVipPeriod int, startTime, endTime string) (list []entity.ZbUser, total int, err error) {
	query := dao.ZbUser.Ctx(ctx).Where("is_delete", packed.NO_DELETE)

	// 按昵称筛选
	if nickname != "" {
		query = query.WhereLike("nickname", "%"+nickname+"%")
	}

	// 按OpenID筛选
	if openid != "" {
		query = query.WhereLike("openid", "%"+openid+"%")
	}

	// 按禁用状态筛选
	if isDisable >= 0 {
		query = query.Where("is_disable", isDisable)
	}

	// 按VIP状态筛选
	if vipStatus >= 0 {
		now := gtime.Now()
		if vipStatus == 1 {
			// 查询VIP用户：有有效期且当前时间在有效期内
			query = query.Where("effective_start IS NOT NULL AND effective_end IS NOT NULL").
				Where("effective_start <= ?", now.Format("2006-01-02")).
				Where("effective_end >= ?", now.Format("2006-01-02"))
		} else {
			// 查询非VIP用户：没有有效期或当前时间不在有效期内
			query = query.Where("(effective_start IS NULL OR effective_end IS NULL OR effective_start > ? OR effective_end < ?)",
				now.Format("2006-01-02"), now.Format("2006-01-02"))
		}
	}

	// 按是否设置VIP期限筛选
	if hasVipPeriod >= 0 {
		if hasVipPeriod == 1 {
			query = query.Where("effective_start IS NOT NULL AND effective_end IS NOT NULL")
		} else {
			query = query.Where("effective_start IS NULL OR effective_end IS NULL")
		}
	}

	// 按时间范围筛选
	if startTime != "" && endTime != "" {
		query = query.WhereBetween("created_at", startTime, endTime)
	}

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取会员总数失败")
	}

	// 获取列表数据
	err = query.Page(page, pageSize).OrderDesc("id").Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取会员列表失败")
	}

	return list, total, nil
}

// GetUserDetail 获取会员详情
func (s *sZbUser) GetUserDetail(ctx context.Context, id int64) (*entity.ZbUser, error) {
	var user *entity.ZbUser
	err := dao.ZbUser.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Scan(&user)
	if err != nil {
		return nil, gerror.Wrap(err, "获取会员详情失败")
	}
	if user == nil {
		return nil, gerror.New("会员不存在")
	}
	return user, nil
}

// UpdateUser 更新会员信息
func (s *sZbUser) UpdateUser(ctx context.Context, id int64, nickname, avatar string, isDisable *int, effectiveStart, effectiveEnd string) error {
	// 检查会员是否存在
	exists, err := s.CheckUserExists(ctx, id)
	if err != nil {
		return err
	}
	if !exists {
		return gerror.New("会员不存在")
	}

	// 构建更新数据
	updateData := g.Map{
		"updated_at": gtime.Now(),
	}

	if nickname != "" {
		updateData["nickname"] = nickname
	}
	if avatar != "" {
		updateData["avatar"] = avatar
	}
	if isDisable != nil {
		updateData["is_disable"] = *isDisable
	}
	if effectiveStart != "" {
		updateData["effective_start"] = effectiveStart
	}
	if effectiveEnd != "" {
		updateData["effective_end"] = effectiveEnd
	}

	// 执行更新
	_, err = dao.ZbUser.Ctx(ctx).Where("id", id).Data(updateData).Update()
	if err != nil {
		return gerror.Wrap(err, "更新会员信息失败")
	}

	return nil
}

// DeleteUser 删除会员（软删除）
func (s *sZbUser) DeleteUser(ctx context.Context, id int64) error {
	// 检查会员是否存在
	exists, err := s.CheckUserExists(ctx, id)
	if err != nil {
		return err
	}
	if !exists {
		return gerror.New("会员不存在")
	}

	// 软删除
	_, err = dao.ZbUser.Ctx(ctx).Where("id", id).Data(g.Map{
		"is_delete":  packed.IS_DELETE,
		"deleted_at": gtime.Now(),
		"updated_at": gtime.Now(),
	}).Update()
	if err != nil {
		return gerror.Wrap(err, "删除会员失败")
	}

	return nil
}

// BatchDeleteUser 批量删除会员（软删除）
func (s *sZbUser) BatchDeleteUser(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return gerror.New("请选择要删除的会员")
	}

	// 批量软删除
	_, err := dao.ZbUser.Ctx(ctx).WhereIn("id", ids).Where("is_delete", packed.NO_DELETE).Data(g.Map{
		"is_delete":  packed.IS_DELETE,
		"deleted_at": gtime.Now(),
		"updated_at": gtime.Now(),
	}).Update()
	if err != nil {
		return gerror.Wrap(err, "批量删除会员失败")
	}

	return nil
}

// SetUserStatus 设置会员状态
func (s *sZbUser) SetUserStatus(ctx context.Context, id int64, isDisable int) error {
	// 检查会员是否存在
	exists, err := s.CheckUserExists(ctx, id)
	if err != nil {
		return err
	}
	if !exists {
		return gerror.New("会员不存在")
	}

	// 更新状态
	_, err = dao.ZbUser.Ctx(ctx).Where("id", id).Data(g.Map{
		"is_disable": isDisable,
		"updated_at": gtime.Now(),
	}).Update()
	if err != nil {
		return gerror.Wrap(err, "设置会员状态失败")
	}

	return nil
}

// UpdateVipPeriod 更新会员VIP有效期
func (s *sZbUser) UpdateVipPeriod(ctx context.Context, id int64, effectiveStart, effectiveEnd string) error {
	// 检查会员是否存在
	exists, err := s.CheckUserExists(ctx, id)
	if err != nil {
		return err
	}
	if !exists {
		return gerror.New("会员不存在")
	}

	// 解析日期
	startTime, err := gtime.StrToTime(effectiveStart)
	if err != nil {
		return gerror.New("开始日期格式错误")
	}
	endTime, err := gtime.StrToTime(effectiveEnd)
	if err != nil {
		return gerror.New("结束日期格式错误")
	}

	// 检查日期逻辑
	if endTime.Before(startTime) {
		return gerror.New("结束日期不能早于开始日期")
	}

	// 注释：VIP状态现在通过动态计算，不再存储在数据库中

	// 更新VIP有效期
	_, err = dao.ZbUser.Ctx(ctx).Where("id", id).Data(g.Map{
		"effective_start": startTime,
		"effective_end":   endTime,
		"updated_at":      gtime.Now(),
	}).Update()
	if err != nil {
		return gerror.Wrap(err, "更新VIP有效期失败")
	}

	return nil
}

// CheckUserExists 检查会员是否存在
func (s *sZbUser) CheckUserExists(ctx context.Context, id int64) (bool, error) {
	count, err := dao.ZbUser.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Count()
	if err != nil {
		return false, gerror.Wrap(err, "检查会员是否存在失败")
	}
	return count > 0, nil
}

// GetUserByOpenid 根据OpenID获取会员
func (s *sZbUser) GetUserByOpenid(ctx context.Context, openid string) (*entity.ZbUser, error) {
	var user *entity.ZbUser
	err := dao.ZbUser.Ctx(ctx).Where("openid", openid).Where("is_delete", packed.NO_DELETE).Scan(&user)
	if err != nil {
		return nil, gerror.Wrap(err, "根据OpenID获取会员失败")
	}
	return user, nil
}

// UpdateUserLoginTime 更新会员登录时间
func (s *sZbUser) UpdateUserLoginTime(ctx context.Context, id int64) error {
	_, err := dao.ZbUser.Ctx(ctx).Where("id", id).Data(g.Map{
		"updated_at": gtime.Now(),
	}).Update()
	if err != nil {
		return gerror.Wrap(err, "更新会员登录时间失败")
	}
	return nil
}

// GetVipUsers 获取VIP会员列表
func (s *sZbUser) GetVipUsers(ctx context.Context, page, pageSize int) (list []entity.ZbUser, total int, err error) {
	// 查询VIP用户：有有效期且当前时间在有效期内
	now := gtime.Now()
	query := dao.ZbUser.Ctx(ctx).Where("is_delete", packed.NO_DELETE).
		Where("effective_start IS NOT NULL AND effective_end IS NOT NULL").
		Where("effective_start <= ?", now.Format("2006-01-02")).
		Where("effective_end >= ?", now.Format("2006-01-02"))

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取VIP会员总数失败")
	}

	// 获取列表数据
	err = query.Page(page, pageSize).OrderDesc("id").Scan(&list)
	if err != nil {
		return nil, 0, gerror.Wrap(err, "获取VIP会员列表失败")
	}

	return list, total, nil
}

// GetExpiredVipUsers 获取即将过期的VIP会员
func (s *sZbUser) GetExpiredVipUsers(ctx context.Context, days int) ([]entity.ZbUser, error) {
	var list []entity.ZbUser

	// 计算目标日期
	now := gtime.New(time.Now())
	targetDate := now.AddDate(0, 0, days)

	err := dao.ZbUser.Ctx(ctx).
		Where("is_delete", packed.NO_DELETE).
		Where("effective_start IS NOT NULL AND effective_end IS NOT NULL").
		Where("effective_start <= ?", gtime.Date()).
		Where("effective_end >= ?", gtime.Date()).
		Where("effective_end <=", targetDate.Format("Y-m-d")).
		OrderDesc("effective_end").
		Scan(&list)
	if err != nil {
		return nil, gerror.Wrap(err, fmt.Sprintf("获取%d天内过期的VIP会员失败", days))
	}

	return list, nil
}

// CalculateVipStatus 计算VIP状态
func (s *sZbUser) CalculateVipStatus(ctx context.Context, effectiveStart, effectiveEnd *gtime.Time) (vipStatus int, daysLeft int) {
	// 如果没有设置有效期，则不是VIP
	if effectiveStart == nil || effectiveEnd == nil {
		return 0, 0
	}

	now := gtime.Now()
	nowTime := now.Time
	startTime := effectiveStart.Time
	endTime := effectiveEnd.Time

	// 检查是否在有效期内
	if nowTime.After(startTime) && nowTime.Before(endTime.AddDate(0, 0, 1)) {
		g.Log().Info(ctx, "在有效期内")
		// 计算剩余天数
		duration := endTime.Sub(nowTime)
		daysLeft = int(duration.Hours() / 24)
		if daysLeft < 0 {
			daysLeft = 0
		}
		return 1, daysLeft
	}

	// 不在有效期内
	if nowTime.Before(startTime) {
		// 还未开始，返回距离开始的天数（负数表示未来）
		duration := startTime.Sub(nowTime)
		return 0, -int(duration.Hours() / 24)
	}

	// 已过期
	return 0, 0
}

// GetUserStats 获取会员统计信息
func (s *sZbUser) GetUserStats(ctx context.Context) (totalUsers, vipUsers, disabledUsers int, err error) {
	// 获取总用户数
	totalUsers, err = dao.ZbUser.Ctx(ctx).Where("is_delete", packed.NO_DELETE).Count()
	if err != nil {
		return 0, 0, 0, gerror.Wrap(err, "获取总用户数失败")
	}

	// 获取禁用用户数
	disabledUsers, err = dao.ZbUser.Ctx(ctx).Where("is_delete", packed.NO_DELETE).Where("is_disable", 1).Count()
	if err != nil {
		return 0, 0, 0, gerror.Wrap(err, "获取禁用用户数失败")
	}

	// 获取VIP用户数（有有效期且当前时间在有效期内）
	currentDate := gtime.Date()
	vipUsers, err = dao.ZbUser.Ctx(ctx).
		Where("is_delete", packed.NO_DELETE).
		Where("effective_start IS NOT NULL AND effective_end IS NOT NULL").
		Where("effective_start <= ?", currentDate).
		Where("effective_end >= ?", currentDate).
		Count()
	// 打印vipUsers的执行sql语句

	if err != nil {
		return 0, 0, 0, gerror.Wrap(err, "获取VIP用户数失败")
	}

	return totalUsers, vipUsers, disabledUsers, nil
}

func (s *sZbUser) CreateFromWechat(ctx context.Context, openid, nickname, avatar, clientIP string) (*entity.ZbUser, error) {
	now := gtime.Now()

	// 创建用户数据
	userData := &do.ZbUser{
		Openid:          openid,
		Nickname:        nickname,
		Avatar:          avatar,
		IsDisable:       0,
		IsDelete:        0,
		EffectiveStatus: 0, // 新用户默认无效，需要购买套餐
		CreatedAt:       now,
		UpdatedAt:       now,
	}

	// 插入数据库
	result, err := dao.ZbUser.Ctx(ctx).Data(userData).Insert()
	if err != nil {
		return nil, err
	}

	// 获取插入的ID
	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	// 记录用户注册日志
	g.Log().Info(ctx, "新用户注册:", g.Map{
		"user_id":   id,
		"openid":    openid,
		"nickname":  nickname,
		"client_ip": clientIP,
	})

	// 返回创建的用户信息
	return s.GetById(ctx, id)
}

// GetById 根据ID获取用户信息
func (s *sZbUser) GetById(ctx context.Context, id int64) (*entity.ZbUser, error) {
	var user *entity.ZbUser
	err := dao.ZbUser.Ctx(ctx).Where("id", id).Where("is_delete", 0).Scan(&user)
	if err != nil {
		return nil, err
	}
	return user, nil
}

// UpdateLastLogin 更新用户最后登录信息
func (s *sZbUser) UpdateLastLogin(ctx context.Context, userId int64, clientIP string) error {
	now := gtime.Now()

	_, err := dao.ZbUser.Ctx(ctx).
		Where("id", userId).
		Data(g.Map{
			"updated_at": now,
			// 注意：原表结构中没有last_login_time和last_login_ip字段
			// 如果需要记录登录信息，可以考虑添加这些字段或创建登录日志表
		}).
		Update()

	if err != nil {
		return err
	}

	// 记录登录日志
	g.Log().Info(ctx, "用户登录:", g.Map{
		"user_id":   userId,
		"client_ip": clientIP,
		"login_at":  now,
	})

	return nil
}
