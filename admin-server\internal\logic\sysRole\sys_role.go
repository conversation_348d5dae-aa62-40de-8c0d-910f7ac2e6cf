package sysRole

import (
	v1 "admin-server/api/sys_role/v1"
	"admin-server/internal/dao"
	"admin-server/internal/model/do"
	"admin-server/internal/model/entity"
	"admin-server/internal/packed"
	"admin-server/internal/service"
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
)

func init() {
	service.RegisterSysRole(&SsysRole{})
}

type SsysRole struct {
}

func (s SsysRole) Create(ctx context.Context, req *v1.CreateReq) (insertId int64, err error) {
	// 检查角色名称是否已存在
	exists, err := s.CheckRoleNameExists(ctx, req.Name, 0)
	if err != nil {
		return 0, err
	}
	if exists {
		return 0, gerror.New("角色名称已存在")
	}

	// 如果提供了菜单权限ID，验证其有效性
	if len(req.MenuIds) > 0 {
		validMenuCount, err := dao.SysMenu.Ctx(ctx).Where("id", req.MenuIds).Where("is_delete", packed.NO_DELETE).Count()
		if err != nil {
			return 0, err
		}
		if validMenuCount != len(req.MenuIds) {
			return 0, gerror.New("存在无效的菜单ID")
		}
	}

	// 使用事务创建角色和分配权限
	err = dao.SysRole.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 创建角色
		insertId, err = dao.SysRole.Ctx(ctx).TX(tx).Data(do.SysRole{
			Name:      req.Name,
			Sort:      req.Sort,
			Remark:    req.Remark,
			IsDisable: req.IsDisable,
		}).InsertAndGetId()
		if err != nil {
			return err
		}

		// 如果提供了菜单权限ID，则分配权限
		if len(req.MenuIds) > 0 {
			var roleMenus []do.SysRoleMenu
			for _, menuId := range req.MenuIds {
				roleMenus = append(roleMenus, do.SysRoleMenu{
					RoleId: insertId,
					MenuId: menuId,
				})
			}
			_, err = dao.SysRoleMenu.Ctx(ctx).TX(tx).Data(roleMenus).Insert()
			if err != nil {
				return err
			}
		}

		return nil
	})

	return insertId, err
}

func (s SsysRole) Update(ctx context.Context, req *v1.UpdateReq) (err error) {
	// 检查角色是否存在
	exists, err := s.CheckRoleExists(ctx, req.ID)
	if err != nil {
		return err
	}
	if !exists {
		return gerror.New("角色不存在")
	}

	// 检查角色名称是否被其他角色使用
	nameExists, err := s.CheckRoleNameExists(ctx, req.Name, req.ID)
	if err != nil {
		return err
	}
	if nameExists {
		return gerror.New("角色名称已被使用")
	}

	// 更新角色信息
	_, err = dao.SysRole.Ctx(ctx).Where("id", req.ID).Data(do.SysRole{
		Name:      req.Name,
		Sort:      req.Sort,
		Remark:    req.Remark,
		IsDisable: req.IsDisable,
	}).Update()

	return err
}

func (s SsysRole) GetList(ctx context.Context, req *v1.GetListReq) (list []*v1.RoleInfo, total int, err error) {
	// 构建查询条件
	m := dao.SysRole.Ctx(ctx).Where("is_delete", packed.NO_DELETE)

	// 添加搜索条件
	if req.Name != "" {
		m = m.WhereLike("name", "%"+req.Name+"%")
	}
	if req.IsDisable != nil {
		m = m.Where("is_disable", *req.IsDisable)
	}

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	var roles []entity.SysRole
	err = m.Page(req.Page, req.PageSize).OrderAsc("sort").OrderAsc("id").Scan(&roles)
	if err != nil {
		return nil, 0, err
	}

	// 转换为RoleInfo格式，包含统计信息
	list = make([]*v1.RoleInfo, len(roles))
	for i, role := range roles {
		roleInfo := s.entityToRoleInfo(&role)

		// 获取角色统计信息
		menuCount, userCount, mainMenus, err := s.GetRoleStatistics(ctx, role.Id)
		if err != nil {
			// 如果获取统计信息失败，使用默认值
			menuCount, userCount, mainMenus = 0, 0, []string{}
		}

		roleInfo.MenuCount = menuCount
		roleInfo.UserCount = userCount
		roleInfo.MainMenus = mainMenus

		list[i] = roleInfo
	}

	return list, total, nil
}

func (s SsysRole) GetOne(ctx context.Context, id int64) (role *v1.RoleInfo, err error) {
	var sysRole entity.SysRole
	err = dao.SysRole.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Scan(&sysRole)
	if err != nil {
		return nil, err
	}

	if sysRole.Id == 0 {
		return nil, gerror.New("角色不存在")
	}

	role = s.entityToRoleInfo(&sysRole)
	return role, nil
}

func (s SsysRole) Delete(ctx context.Context, id int64) (err error) {
	// 检查角色是否存在
	exists, err := s.CheckRoleExists(ctx, id)
	if err != nil {
		return err
	}
	if !exists {
		return gerror.New("角色不存在")
	}

	// 检查是否有管理员使用此角色
	adminCount, err := dao.SysAdminRole.Ctx(ctx).Where("role_id", id).Count()
	if err != nil {
		return err
	}
	if adminCount > 0 {
		return gerror.New("该角色已被管理员使用，无法删除")
	}

	// 开启事务
	return dao.SysRole.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 软删除角色
		_, err = dao.SysRole.Ctx(ctx).TX(tx).Where("id", id).Data(do.SysRole{
			IsDelete: packed.IS_DELETE,
		}).Update()
		if err != nil {
			return err
		}

		// 删除角色菜单关联
		_, err = dao.SysRoleMenu.Ctx(ctx).TX(tx).Where("role_id", id).Delete()
		if err != nil {
			return err
		}

		return nil
	})
}

func (s SsysRole) AssignMenus(ctx context.Context, roleId int64, menuIds []int64) (err error) {
	// 检查角色是否存在
	exists, err := s.CheckRoleExists(ctx, roleId)
	if err != nil {
		return err
	}
	if !exists {
		return gerror.New("角色不存在")
	}

	// 验证菜单ID是否有效
	if len(menuIds) > 0 {
		validMenuCount, err := dao.SysMenu.Ctx(ctx).Where("id", menuIds).Where("is_delete", packed.NO_DELETE).Count()
		if err != nil {
			return err
		}
		if validMenuCount != len(menuIds) {
			return gerror.New("存在无效的菜单ID")
		}
	}

	// 开启事务
	return dao.SysRole.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 删除原有的角色菜单关联
		_, err = dao.SysRoleMenu.Ctx(ctx).TX(tx).Where("role_id", roleId).Delete()
		if err != nil {
			return err
		}

		// 插入新的角色菜单关联
		if len(menuIds) > 0 {
			var roleMenus []do.SysRoleMenu
			for _, menuId := range menuIds {
				roleMenus = append(roleMenus, do.SysRoleMenu{
					RoleId: roleId,
					MenuId: menuId,
				})
			}
			_, err = dao.SysRoleMenu.Ctx(ctx).TX(tx).Data(roleMenus).Insert()
			if err != nil {
				return err
			}
		}

		return nil
	})
}

func (s SsysRole) GetRoleMenus(ctx context.Context, roleId int64) (roleMenuInfo *v1.RoleMenuInfo, err error) {
	// 检查角色是否存在
	var role entity.SysRole
	err = dao.SysRole.Ctx(ctx).Where("id", roleId).Where("is_delete", packed.NO_DELETE).Scan(&role)
	if err != nil {
		return nil, err
	}
	if role.Id == 0 {
		return nil, gerror.New("角色不存在")
	}

	// 获取角色关联的菜单ID
	var roleMenus []entity.SysRoleMenu
	err = dao.SysRoleMenu.Ctx(ctx).Where("role_id", roleId).Scan(&roleMenus)
	if err != nil {
		return nil, err
	}

	// 提取菜单ID列表
	menuIds := make([]int64, len(roleMenus))
	for i, rm := range roleMenus {
		menuIds[i] = rm.MenuId
	}

	roleMenuInfo = &v1.RoleMenuInfo{
		RoleID:   role.Id,
		RoleName: role.Name,
		MenuIds:  menuIds,
	}

	return roleMenuInfo, nil
}

func (s SsysRole) CheckRoleExists(ctx context.Context, id int64) (exists bool, err error) {
	count, err := dao.SysRole.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s SsysRole) CheckRoleNameExists(ctx context.Context, name string, excludeId int64) (exists bool, err error) {
	m := dao.SysRole.Ctx(ctx).Where("name", name).Where("is_delete", packed.NO_DELETE)
	if excludeId > 0 {
		m = m.Where("id !=", excludeId)
	}

	count, err := m.Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s SsysRole) GetRoleStatistics(ctx context.Context, roleId int64) (menuCount int, userCount int, mainMenus []string, err error) {
	// 获取角色的菜单数量
	menuCount, err = dao.SysRoleMenu.Ctx(ctx).Where("role_id", roleId).Count()
	if err != nil {
		return 0, 0, nil, err
	}

	// 获取使用此角色的用户数量
	userCount, err = dao.SysAdminRole.Ctx(ctx).Where("role_id", roleId).Count()
	if err != nil {
		return 0, 0, nil, err
	}

	// 获取主要菜单权限（所有）
	var roleMenus []entity.SysRoleMenu
	err = dao.SysRoleMenu.Ctx(ctx).Where("role_id", roleId).Scan(&roleMenus)
	if err != nil {
		return 0, 0, nil, err
	}

	// 获取菜单名称
	if len(roleMenus) > 0 {
		menuIds := make([]int64, len(roleMenus))
		for i, rm := range roleMenus {
			menuIds[i] = rm.MenuId
		}

		var menus []entity.SysMenu
		err = dao.SysMenu.Ctx(ctx).Where("id", menuIds).Where("is_delete", packed.NO_DELETE).Scan(&menus)
		if err != nil {
			return 0, 0, nil, err
		}

		mainMenus = make([]string, len(menus))
		for i, menu := range menus {
			mainMenus[i] = menu.MenuName
		}
	} else {
		mainMenus = []string{}
	}

	return menuCount, userCount, mainMenus, nil
}

func (s SsysRole) ToggleStatus(ctx context.Context, id int64) (err error) {
	// 检查角色是否存在
	var role entity.SysRole
	err = dao.SysRole.Ctx(ctx).Where("id", id).Where("is_delete", packed.NO_DELETE).Scan(&role)
	if err != nil {
		return err
	}
	if role.Id == 0 {
		return gerror.New("角色不存在")
	}

	// 切换状态：0变1，1变0
	newStatus := packed.ENABLE
	if role.IsDisable == int(packed.ENABLE) {
		newStatus = packed.DISABLE
	}

	// 更新状态
	_, err = dao.SysRole.Ctx(ctx).Where("id", id).Data(do.SysRole{
		IsDisable: newStatus,
	}).Update()

	return err
}

// 辅助方法：将entity转换为RoleInfo
func (s SsysRole) entityToRoleInfo(role *entity.SysRole) *v1.RoleInfo {
	return &v1.RoleInfo{
		ID:        role.Id,
		Name:      role.Name,
		Sort:      role.Sort,
		Remark:    role.Remark,
		IsDisable: packed.Disable(role.IsDisable),
		CreatedAt: role.CreatedAt,
		UpdatedAt: role.UpdatedAt,
		MenuCount: 0,          // 将在GetList中填充
		UserCount: 0,          // 将在GetList中填充
		MainMenus: []string{}, // 将在GetList中填充
	}
}
