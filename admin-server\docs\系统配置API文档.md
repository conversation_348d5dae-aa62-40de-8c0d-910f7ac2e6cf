# 系统配置API文档

## 概述

系统配置模块提供了完整的配置管理功能，包括配置分组和配置项的CRUD操作。配置分组用于组织和管理相关的配置项，配置项支持多种输入类型（input、textarea、select、radio、switch、image）。

## 数据结构

### 配置分组 (sys_config_group)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 配置分组ID |
| name | string | 分组名称 |
| code | string | 分组编码 |
| sort | int | 排序 |
| remark | string | 备注 |
| is_system | int | 是否系统保留 (0=否, 1=是) |
| is_disable | int | 是否禁用 (0=否, 1=是) |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |
| config_count | int | 配置项数量 |

### 配置项 (sys_config)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 配置项ID |
| group_id | int64 | 配置分组ID |
| group_name | string | 配置分组名称 |
| key | string | 配置键名 |
| value | string | 配置键值 |
| name | string | 配置名称 |
| sort | int | 排序 |
| input_type | string | 数据输入类型 |
| config_select_data | string | 配置项数据(JSON格式) |
| is_system | int | 是否系统保留 (0=否, 1=是) |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

### 输入类型说明

- `input`: 单行文本输入框
- `textarea`: 多行文本输入框
- `select`: 下拉选择框
- `radio`: 单选按钮
- `switch`: 开关按钮
- `image`: 图片上传

## 配置分组API

### 1. 创建配置分组

**接口地址：** `POST /sys_config_group/create`

**请求参数：**
```json
{
  "name": "基础配置",
  "code": "basic_config",
  "sort": 1,
  "remark": "系统基础配置",
  "is_system": 0,
  "is_disable": 0
}
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1
  }
}
```

### 2. 更新配置分组

**接口地址：** `PUT /sys_config_group/{id}`

**请求参数：**
```json
{
  "name": "基础配置",
  "code": "basic_config",
  "sort": 1,
  "remark": "系统基础配置",
  "is_system": 0,
  "is_disable": 0
}
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 3. 删除配置分组

**接口地址：** `DELETE /sys_config_group/{id}`

**说明：**
- 系统保留分组（is_system=1）不可删除
- 分组下有配置项时不可删除

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

**错误响应示例：**
```json
{
  "code": 400,
  "message": "系统保留分组不可删除",
  "data": null
}
```

### 4. 获取配置分组列表

**接口地址：** `GET /sys_config_group/list`

**请求参数：**
- `page`: 页码 (默认: 1)
- `page_size`: 每页数量 (默认: 10)
- `name`: 分组名称 (模糊搜索)
- `code`: 分组编码 (模糊搜索)
- `is_system`: 是否系统保留 (0/1)
- `is_disable`: 是否禁用 (0/1)

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "基础配置",
        "code": "basic_config",
        "sort": 1,
        "remark": "系统基础配置",
        "is_system": 0,
        "is_disable": 0,
        "created_at": "2024-01-01 12:00:00",
        "updated_at": "2024-01-01 12:00:00",
        "config_count": 5
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10
  }
}
```

### 5. 获取单个配置分组

**接口地址：** `GET /sys_config_group/{id}`

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "基础配置",
    "code": "basic_config",
    "sort": 1,
    "remark": "系统基础配置",
    "is_system": 0,
    "is_disable": 0,
    "created_at": "2024-01-01 12:00:00",
    "updated_at": "2024-01-01 12:00:00",
    "config_count": 5
  }
}
```

### 6. 切换配置分组状态

**接口地址：** `PUT /sys_config_group/{id}/toggle`

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

## 配置项API

### 1. 创建配置项

**接口地址：** `POST /sys_config/create`

**请求参数：**
```json
{
  "group_id": 1,
  "key": "site_name",
  "value": "我的网站",
  "name": "网站名称",
  "sort": 1,
  "input_type": "input",
  "config_select_data": "",
  "is_system": 0
}
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1
  }
}
```

### 2. 更新配置项

**接口地址：** `PUT /sys_config/{id}`

**请求参数：**
```json
{
  "group_id": 1,
  "key": "site_name",
  "value": "我的网站",
  "name": "网站名称",
  "sort": 1,
  "input_type": "input",
  "config_select_data": "",
  "is_system": 0
}
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 3. 删除配置项

**接口地址：** `DELETE /sys_config/{id}`

**说明：**
- 系统保留配置项（is_system=1）不可删除

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

**错误响应示例：**
```json
{
  "code": 400,
  "message": "系统保留配置项不可删除",
  "data": null
}
```

### 4. 获取配置项列表

**接口地址：** `GET /sys_config/list`

**请求参数：**
- `group_id`: 配置分组ID
- `key`: 配置键名 (模糊搜索)
- `name`: 配置名称 (模糊搜索)
- `input_type`: 数据输入类型
- `is_system`: 是否系统保留 (0/1)

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "group_id": 1,
        "group_name": "基础配置",
        "key": "site_name",
        "value": "我的网站",
        "name": "网站名称",
        "sort": 1,
        "input_type": "input",
        "config_select_data": "",
        "is_system": 0,
        "created_at": "2024-01-01 12:00:00",
        "updated_at": "2024-01-01 12:00:00"
      }
    ],
    "total": 1
  }
}
```

### 5. 获取单个配置项

**接口地址：** `GET /sys_config/{id}`

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "group_id": 1,
    "group_name": "基础配置",
    "key": "site_name",
    "value": "我的网站",
    "name": "网站名称",
    "sort": 1,
    "input_type": "input",
    "config_select_data": "",
    "is_system": 0,
    "created_at": "2024-01-01 12:00:00",
    "updated_at": "2024-01-01 12:00:00"
  }
}
```

### 6. 按分组获取配置项

**接口地址：** `GET /sys_config/group/{group_id}`

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "group_id": 1,
        "group_name": "基础配置",
        "key": "site_name",
        "value": "我的网站",
        "name": "网站名称",
        "sort": 1,
        "input_type": "input",
        "config_select_data": "",
        "is_system": 0,
        "created_at": "2024-01-01 12:00:00",
        "updated_at": "2024-01-01 12:00:00"
      }
    ]
  }
}
```

### 7. 更新配置项值

**接口地址：** `PUT /sys_config/{id}/value`

**请求参数：**
```json
{
  "value": "新的配置值"
}
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

### 8. 根据键名获取配置项

**接口地址：** `GET /sys_config/key/{key}`

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "group_id": 1,
    "group_name": "基础配置",
    "key": "site_name",
    "value": "我的网站",
    "name": "网站名称",
    "sort": 1,
    "input_type": "input",
    "config_select_data": "",
    "is_system": 0,
    "created_at": "2024-01-01 12:00:00",
    "updated_at": "2024-01-01 12:00:00"
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 使用示例

### 前端集成示例

```javascript
// 获取配置分组列表
const getConfigGroups = async () => {
  const response = await fetch('/sys_config_group/list?page=1&page_size=10');
  const data = await response.json();
  return data.data;
};

// 获取指定分组的配置项
const getConfigsByGroup = async (groupId) => {
  const response = await fetch(`/sys_config/group/${groupId}`);
  const data = await response.json();
  return data.data.list;
};

// 更新配置项值
const updateConfigValue = async (configId, value) => {
  const response = await fetch(`/sys_config/${configId}/value`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ value }),
  });
  return response.json();
};
```

## 注意事项

1. **删除限制**：
   - 删除配置分组时，需要确保该分组下没有配置项
   - 系统保留的配置分组（is_system=1）不能删除
   - 系统保留的配置项（is_system=1）不能删除
2. **数据唯一性**：配置键名在全局范围内必须唯一
3. **JSON字段处理**：
   - 配置项的`config_select_data`字段用于存储select、radio、switch类型的选项数据，格式为JSON
   - 当`config_select_data`为空时，系统会自动处理为NULL值，避免JSON格式错误
4. **软删除机制**：所有删除操作都是软删除，不会真正删除数据
5. **系统保留标识**：is_system字段用于标识系统保留项，1=系统保留，0=用户创建
