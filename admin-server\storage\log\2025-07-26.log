2025-07-26T09:56:39 [INFO] 搜索趋势定时任务启动成功, CronID: {"Name":"cron-1","RegisterTime":"2025-07-26T09:56:39.1124671+08:00"}
2025-07-26T09:56:39 [INFO] 当前定时任务数量: 1
2025-07-26T09:56:39 [INFO] 定时任务 1 : {"Name":"cron-1","RegisterTime":"2025-07-26T09:56:39.1124671+08:00"}
2025-07-26T09:58:38 [INFO] {8c90db1985aa551861131350f962f15f} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbl9pZCI6MSwidXNlcm5hbWUiOiJhZG1pbiIsInR5cGUiOiJhY2Nlc3MiLCJleHAiOjE3NTM0MzU0MzcsImlhdCI6MTc1MzQyODIzN30.0WZzzoccTvUlqRG6LUYVkg9k3dmA0ozj1ya7XkvevqU
2025-07-26T09:58:38 [INFO] {f07a001a85aa5518621313507dd25ee8} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbl9pZCI6MSwidXNlcm5hbWUiOiJhZG1pbiIsInR5cGUiOiJhY2Nlc3MiLCJleHAiOjE3NTM0MzU0MzcsImlhdCI6MTc1MzQyODIzN30.0WZzzoccTvUlqRG6LUYVkg9k3dmA0ozj1ya7XkvevqU
2025-07-26T09:58:38 [INFO] {3c601a1a85aa551863131350743dea10} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbl9pZCI6MSwidXNlcm5hbWUiOiJhZG1pbiIsInR5cGUiOiJhY2Nlc3MiLCJleHAiOjE3NTM0MzU0MzcsImlhdCI6MTc1MzQyODIzN30.0WZzzoccTvUlqRG6LUYVkg9k3dmA0ozj1ya7XkvevqU
2025-07-26T09:58:38 [INFO] {8c90db1985aa551861131350f962f15f} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T09:58:38 [INFO] {f07a001a85aa5518621313507dd25ee8} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T09:58:38 [INFO] {3c601a1a85aa551863131350743dea10} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T09:58:38 [ERRO] {f07a001a85aa5518621313507dd25ee8} ValidateToken - JWT parse error: token has invalid claims: token is expired 
Stack:
1.  admin-server/internal/logic/sysAuth.SsysAuth.ValidateToken
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/logic/sysAuth/sys_auth.go:369
2.  admin-server/internal/middleware.Auth
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/middleware/auth.go:50
3.  admin-server/internal/cmd.init.func1.2.1
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/cmd/cmd.go:64

2025-07-26T09:58:38 [ERRO] {3c601a1a85aa551863131350743dea10} ValidateToken - JWT parse error: token has invalid claims: token is expired 
Stack:
1.  admin-server/internal/logic/sysAuth.SsysAuth.ValidateToken
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/logic/sysAuth/sys_auth.go:369
2.  admin-server/internal/middleware.Auth
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/middleware/auth.go:50
3.  admin-server/internal/cmd.init.func1.2.1
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/cmd/cmd.go:64

2025-07-26T09:58:38 [INFO] {3c601a1a85aa551863131350743dea10} Auth middleware - ValidateToken result: 0 令牌无效
2025-07-26T09:58:38 [ERRO] {8c90db1985aa551861131350f962f15f} ValidateToken - JWT parse error: token has invalid claims: token is expired 
Stack:
1.  admin-server/internal/logic/sysAuth.SsysAuth.ValidateToken
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/logic/sysAuth/sys_auth.go:369
2.  admin-server/internal/middleware.Auth
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/middleware/auth.go:50
3.  admin-server/internal/cmd.init.func1.2.1
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/cmd/cmd.go:64

2025-07-26T09:58:38 [INFO] {f07a001a85aa5518621313507dd25ee8} Auth middleware - ValidateToken result: 0 令牌无效
2025-07-26T09:58:38 [INFO] {8c90db1985aa551861131350f962f15f} Auth middleware - ValidateToken result: 0 令牌无效
2025-07-26T09:58:38 [INFO] {348e7f2085aa551866131350825e2ca3} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T09:58:38 [INFO] {348e7f2085aa55186713135048f1ca44} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T09:58:38 [INFO] {348e7f2085aa551866131350825e2ca3} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T09:58:38 [INFO] {348e7f2085aa55186713135048f1ca44} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T09:58:38 [INFO] {30e4872085aa551868131350993225e0} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T09:58:38 [INFO] {348e7f2085aa551866131350825e2ca3} ValidateToken - Claims: 1 access admin
2025-07-26T09:58:38 [INFO] {348e7f2085aa55186713135048f1ca44} ValidateToken - Claims: 1 access admin
2025-07-26T09:58:38 [INFO] {30e4872085aa551868131350993225e0} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T09:58:38 [INFO] {348e7f2085aa551866131350825e2ca3} Auth middleware - ValidateToken result: 1
2025-07-26T09:58:38 [INFO] {348e7f2085aa55186713135048f1ca44} Auth middleware - ValidateToken result: 1
2025-07-26T09:58:38 [INFO] {30e4872085aa551868131350993225e0} ValidateToken - Claims: 1 access admin
2025-07-26T09:58:38 [INFO] {348e7f2085aa551866131350825e2ca3} Auth middleware - set admin_id to context: 1
2025-07-26T09:58:38 [INFO] {348e7f2085aa55186713135048f1ca44} Auth middleware - set admin_id to context: 1
2025-07-26T09:58:38 [INFO] {30e4872085aa551868131350993225e0} Auth middleware - ValidateToken result: 1
2025-07-26T09:58:38 [INFO] {30e4872085aa551868131350993225e0} Auth middleware - set admin_id to context: 1
2025-07-26T09:58:38 [WARN] {348e7f2085aa55186713135048f1ca44} 路由未配置权限标识: GET /auth/permissions
2025-07-26T09:58:38 [WARN] {348e7f2085aa551866131350825e2ca3} 路由未配置权限标识: GET /auth/menus
2025-07-26T09:58:38 [INFO] {348e7f2085aa551866131350825e2ca3} GetMenus - adminId from context: 1
2025-07-26T09:58:38 [WARN] {30e4872085aa551868131350993225e0} 路由未配置权限标识: GET /auth/userinfo
2025-07-26T09:58:41 [INFO] {84f645b585aa5518691313501fb3a2d9} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T09:58:41 [INFO] {84f645b585aa5518691313501fb3a2d9} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T09:58:41 [INFO] {84f645b585aa5518691313501fb3a2d9} ValidateToken - Claims: 1 access admin
2025-07-26T09:58:41 [INFO] {84f645b585aa5518691313501fb3a2d9} Auth middleware - ValidateToken result: 1
2025-07-26T09:58:41 [INFO] {84f645b585aa5518691313501fb3a2d9} Auth middleware - set admin_id to context: 1
2025-07-26T09:58:41 [INFO] {4c1267b585aa55186a131350d2e56f95} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T09:58:41 [INFO] {4c1267b585aa55186a131350d2e56f95} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T09:58:41 [INFO] {4c1267b585aa55186a131350d2e56f95} ValidateToken - Claims: 1 access admin
2025-07-26T09:58:41 [WARN] {84f645b585aa5518691313501fb3a2d9} 路由未配置权限标识: GET /auth/userinfo
2025-07-26T09:58:41 [INFO] {4c1267b585aa55186a131350d2e56f95} Auth middleware - ValidateToken result: 1
2025-07-26T09:58:41 [INFO] {4c1267b585aa55186a131350d2e56f95} Auth middleware - set admin_id to context: 1
2025-07-26T09:58:41 [INFO] {44ae82b585aa55186b13135054546b0d} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T09:58:41 [INFO] {44ae82b585aa55186b13135054546b0d} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T09:58:41 [INFO] {44ae82b585aa55186b13135054546b0d} ValidateToken - Claims: 1 access admin
2025-07-26T09:58:41 [INFO] {44ae82b585aa55186b13135054546b0d} Auth middleware - ValidateToken result: 1
2025-07-26T09:58:41 [INFO] {44ae82b585aa55186b13135054546b0d} Auth middleware - set admin_id to context: 1
2025-07-26T09:58:41 [WARN] {4c1267b585aa55186a131350d2e56f95} 路由未配置权限标识: GET /auth/permissions
2025-07-26T09:58:41 [WARN] {44ae82b585aa55186b13135054546b0d} 路由未配置权限标识: GET /auth/menus
2025-07-26T09:58:41 [INFO] {44ae82b585aa55186b13135054546b0d} GetMenus - adminId from context: 1
2025-07-26T09:58:41 [INFO] {3c82e7ca85aa55186d1313508889e43d} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T09:58:41 [INFO] {3c82e7ca85aa55186d1313508889e43d} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T09:58:41 [INFO] {3c82e7ca85aa55186d1313508889e43d} ValidateToken - Claims: 1 access admin
2025-07-26T09:58:41 [INFO] {3c82e7ca85aa55186d1313508889e43d} Auth middleware - ValidateToken result: 1
2025-07-26T09:58:41 [INFO] {3c82e7ca85aa55186d1313508889e43d} Auth middleware - set admin_id to context: 1
2025-07-26T09:58:41 [INFO] {3c82e7ca85aa55186d1313508889e43d} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T09:58:41 [INFO] {3c82e7ca85aa55186d1313508889e43d} 超级管理员访问: system:zb_order:list
2025-07-26T09:58:41 [DEBU] {3c82e7ca85aa55186d1313508889e43d} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T09:58:44 [INFO] {5801428e86aa55186e13135025b5677d} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T09:58:44 [INFO] {5801428e86aa55186e13135025b5677d} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T09:58:44 [INFO] {5801428e86aa55186e13135025b5677d} ValidateToken - Claims: 1 access admin
2025-07-26T09:58:44 [INFO] {5801428e86aa55186e13135025b5677d} Auth middleware - ValidateToken result: 1
2025-07-26T09:58:44 [INFO] {5801428e86aa55186e13135025b5677d} Auth middleware - set admin_id to context: 1
2025-07-26T09:58:44 [INFO] {30b6698e86aa55186f1313502f7e910f} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T09:58:44 [INFO] {30b6698e86aa55186f1313502f7e910f} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T09:58:44 [INFO] {30b6698e86aa55186f1313502f7e910f} ValidateToken - Claims: 1 access admin
2025-07-26T09:58:44 [INFO] {30b6698e86aa55186f1313502f7e910f} Auth middleware - ValidateToken result: 1
2025-07-26T09:58:44 [INFO] {30b6698e86aa55186f1313502f7e910f} Auth middleware - set admin_id to context: 1
2025-07-26T09:58:44 [INFO] {f820b48e86aa551870131350651f645a} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T09:58:44 [INFO] {f820b48e86aa551870131350651f645a} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T09:58:44 [INFO] {f820b48e86aa551870131350651f645a} ValidateToken - Claims: 1 access admin
2025-07-26T09:58:44 [INFO] {f820b48e86aa551870131350651f645a} Auth middleware - ValidateToken result: 1
2025-07-26T09:58:44 [INFO] {f820b48e86aa551870131350651f645a} Auth middleware - set admin_id to context: 1
2025-07-26T09:58:44 [WARN] {5801428e86aa55186e13135025b5677d} 路由未配置权限标识: GET /auth/userinfo
2025-07-26T09:58:44 [WARN] {30b6698e86aa55186f1313502f7e910f} 路由未配置权限标识: GET /auth/permissions
2025-07-26T09:58:44 [WARN] {f820b48e86aa551870131350651f645a} 路由未配置权限标识: GET /auth/menus
2025-07-26T09:58:44 [INFO] {f820b48e86aa551870131350651f645a} GetMenus - adminId from context: 1
2025-07-26T09:58:44 [INFO] {2030739886aa551871131350b27f6eb0} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T09:58:44 [INFO] {2030739886aa551871131350b27f6eb0} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T09:58:44 [INFO] {2030739886aa551871131350b27f6eb0} ValidateToken - Claims: 1 access admin
2025-07-26T09:58:44 [INFO] {2030739886aa551871131350b27f6eb0} Auth middleware - ValidateToken result: 1
2025-07-26T09:58:44 [INFO] {2030739886aa551871131350b27f6eb0} Auth middleware - set admin_id to context: 1
2025-07-26T09:58:44 [INFO] {2030739886aa551871131350b27f6eb0} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T09:58:44 [INFO] {2030739886aa551871131350b27f6eb0} 超级管理员访问: system:zb_order:list
2025-07-26T09:58:44 [DEBU] {2030739886aa551871131350b27f6eb0} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T10:00:00 [INFO] 开始更新搜索趋势...
2025-07-26T10:00:00 [INFO] 标记热门关键词: 中关村
2025-07-26T10:00:00 [INFO] 标记热门关键词: 吉林工商学院
2025-07-26T10:00:00 [INFO] 标记热门关键词: 中国银行
2025-07-26T10:00:00 [INFO] 热门搜索趋势更新完成，共标记 3 个热门关键词
2025-07-26T10:00:00 [INFO] 搜索趋势更新完成
2025-07-26T10:03:02 [INFO] {5407ffa1c2aa5518791313500bc75e7b} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:03:02 [INFO] {5407ffa1c2aa5518791313500bc75e7b} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:03:02 [INFO] {5407ffa1c2aa5518791313500bc75e7b} ValidateToken - Claims: 1 access admin
2025-07-26T10:03:02 [INFO] {5407ffa1c2aa5518791313500bc75e7b} Auth middleware - ValidateToken result: 1
2025-07-26T10:03:02 [INFO] {5407ffa1c2aa5518791313500bc75e7b} Auth middleware - set admin_id to context: 1
2025-07-26T10:03:02 [INFO] {5407ffa1c2aa5518791313500bc75e7b} Required permission: system:zb_order:detail for GET /zb_order/detail
2025-07-26T10:03:02 [INFO] {5407ffa1c2aa5518791313500bc75e7b} 超级管理员访问: system:zb_order:detail
2025-07-26T10:03:02 [DEBU] {5407ffa1c2aa5518791313500bc75e7b} 权限验证通过: adminId: 1 permission: system:zb_order:detail path: /zb_order/detail
2025-07-26T10:03:20 [INFO] {c0595ab1c6aa55187b1313505dd5e4c1} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:03:20 [INFO] {c0595ab1c6aa55187b1313505dd5e4c1} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:03:20 [INFO] {c0595ab1c6aa55187b1313505dd5e4c1} ValidateToken - Claims: 1 access admin
2025-07-26T10:03:20 [INFO] {c0595ab1c6aa55187b1313505dd5e4c1} Auth middleware - ValidateToken result: 1
2025-07-26T10:03:20 [INFO] {c0595ab1c6aa55187b1313505dd5e4c1} Auth middleware - set admin_id to context: 1
2025-07-26T10:03:20 [INFO] {c0595ab1c6aa55187b1313505dd5e4c1} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T10:03:20 [INFO] {c0595ab1c6aa55187b1313505dd5e4c1} 超级管理员访问: system:zb_order:list
2025-07-26T10:03:20 [DEBU] {c0595ab1c6aa55187b1313505dd5e4c1} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T10:03:27 [INFO] {5c04104fc8aa55187d131350f0ab1c25} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:03:27 [INFO] {5c04104fc8aa55187d131350f0ab1c25} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:03:27 [INFO] {5c04104fc8aa55187d131350f0ab1c25} ValidateToken - Claims: 1 access admin
2025-07-26T10:03:27 [INFO] {5c04104fc8aa55187d131350f0ab1c25} Auth middleware - ValidateToken result: 1
2025-07-26T10:03:27 [INFO] {5c04104fc8aa55187d131350f0ab1c25} Auth middleware - set admin_id to context: 1
2025-07-26T10:03:27 [INFO] {5c04104fc8aa55187d131350f0ab1c25} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T10:03:27 [INFO] {5c04104fc8aa55187d131350f0ab1c25} 超级管理员访问: system:zb_order:list
2025-07-26T10:03:27 [DEBU] {5c04104fc8aa55187d131350f0ab1c25} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T10:03:34 [INFO] {54f9e115caaa55187f131350b5f60c14} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:03:34 [INFO] {54f9e115caaa55187f131350b5f60c14} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:03:34 [INFO] {54f9e115caaa55187f131350b5f60c14} ValidateToken - Claims: 1 access admin
2025-07-26T10:03:34 [INFO] {54f9e115caaa55187f131350b5f60c14} Auth middleware - ValidateToken result: 1
2025-07-26T10:03:34 [INFO] {54f9e115caaa55187f131350b5f60c14} Auth middleware - set admin_id to context: 1
2025-07-26T10:03:34 [INFO] {54f9e115caaa55187f131350b5f60c14} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T10:03:34 [INFO] {54f9e115caaa55187f131350b5f60c14} 超级管理员访问: system:zb_order:list
2025-07-26T10:03:34 [DEBU] {54f9e115caaa55187f131350b5f60c14} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T10:03:37 [INFO] {f46a479ecaaa5518811313505acf22a9} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:03:37 [INFO] {f46a479ecaaa5518811313505acf22a9} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:03:37 [INFO] {f46a479ecaaa5518811313505acf22a9} ValidateToken - Claims: 1 access admin
2025-07-26T10:03:37 [INFO] {f46a479ecaaa5518811313505acf22a9} Auth middleware - ValidateToken result: 1
2025-07-26T10:03:37 [INFO] {f46a479ecaaa5518811313505acf22a9} Auth middleware - set admin_id to context: 1
2025-07-26T10:03:37 [INFO] {f46a479ecaaa5518811313505acf22a9} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T10:03:37 [INFO] {f46a479ecaaa5518811313505acf22a9} 超级管理员访问: system:zb_order:list
2025-07-26T10:03:37 [DEBU] {f46a479ecaaa5518811313505acf22a9} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T10:03:44 [INFO] {2cd1775accaa551883131350c8eb3437} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:03:44 [INFO] {2cd1775accaa551883131350c8eb3437} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:03:44 [INFO] {2cd1775accaa551883131350c8eb3437} ValidateToken - Claims: 1 access admin
2025-07-26T10:03:44 [INFO] {2cd1775accaa551883131350c8eb3437} Auth middleware - ValidateToken result: 1
2025-07-26T10:03:44 [INFO] {2cd1775accaa551883131350c8eb3437} Auth middleware - set admin_id to context: 1
2025-07-26T10:03:44 [INFO] {2cd1775accaa551883131350c8eb3437} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T10:03:44 [INFO] {2cd1775accaa551883131350c8eb3437} 超级管理员访问: system:zb_order:list
2025-07-26T10:03:44 [DEBU] {2cd1775accaa551883131350c8eb3437} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T10:03:47 [INFO] {4ce06924cdaa5518851313501d9b1cbf} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:03:47 [INFO] {4ce06924cdaa5518851313501d9b1cbf} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:03:47 [INFO] {4ce06924cdaa5518851313501d9b1cbf} ValidateToken - Claims: 1 access admin
2025-07-26T10:03:47 [INFO] {4ce06924cdaa5518851313501d9b1cbf} Auth middleware - ValidateToken result: 1
2025-07-26T10:03:47 [INFO] {4ce06924cdaa5518851313501d9b1cbf} Auth middleware - set admin_id to context: 1
2025-07-26T10:03:47 [INFO] {4ce06924cdaa5518851313501d9b1cbf} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T10:03:47 [INFO] {4ce06924cdaa5518851313501d9b1cbf} 超级管理员访问: system:zb_order:list
2025-07-26T10:03:47 [DEBU] {4ce06924cdaa5518851313501d9b1cbf} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T10:05:52 [INFO] {e80b0a18eaaa5518871313509fb38f8d} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:05:52 [INFO] {e80b0a18eaaa5518871313509fb38f8d} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:05:52 [INFO] {e80b0a18eaaa5518871313509fb38f8d} ValidateToken - Claims: 1 access admin
2025-07-26T10:05:52 [INFO] {e80b0a18eaaa5518871313509fb38f8d} Auth middleware - ValidateToken result: 1
2025-07-26T10:05:52 [INFO] {e80b0a18eaaa5518871313509fb38f8d} Auth middleware - set admin_id to context: 1
2025-07-26T10:05:52 [INFO] {e80b0a18eaaa5518871313509fb38f8d} Required permission: system:officialAccount:config for GET /wechat/config
2025-07-26T10:05:52 [INFO] {e80b0a18eaaa5518871313509fb38f8d} 超级管理员访问: system:officialAccount:config
2025-07-26T10:05:52 [DEBU] {e80b0a18eaaa5518871313509fb38f8d} 权限验证通过: adminId: 1 permission: system:officialAccount:config path: /wechat/config
2025-07-26T10:05:53 [INFO] {08153c4eeaaa55188a13135021ab8607} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:05:53 [INFO] {08153c4eeaaa55188a13135021ab8607} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:05:53 [INFO] {08153c4eeaaa55188a13135021ab8607} ValidateToken - Claims: 1 access admin
2025-07-26T10:05:53 [INFO] {08153c4eeaaa55188a13135021ab8607} Auth middleware - ValidateToken result: 1
2025-07-26T10:05:53 [INFO] {08153c4eeaaa55188a13135021ab8607} Auth middleware - set admin_id to context: 1
2025-07-26T10:05:53 [WARN] {08153c4eeaaa55188a13135021ab8607} 路由未配置权限标识: GET /sys_config/key/web_name
2025-07-26T10:05:53 [INFO] {6ca9984eeaaa55188b131350581ed779} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:05:53 [INFO] {6ca9984eeaaa55188b131350581ed779} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:05:53 [INFO] {6ca9984eeaaa55188b131350581ed779} ValidateToken - Claims: 1 access admin
2025-07-26T10:05:53 [INFO] {6ca9984eeaaa55188b131350581ed779} Auth middleware - ValidateToken result: 1
2025-07-26T10:05:53 [INFO] {6ca9984eeaaa55188b131350581ed779} Auth middleware - set admin_id to context: 1
2025-07-26T10:05:53 [INFO] {6ca9984eeaaa55188b131350581ed779} Required permission: system:officialAccount:menu for GET /wechat/menu/tree
2025-07-26T10:05:53 [INFO] {6ca9984eeaaa55188b131350581ed779} 超级管理员访问: system:officialAccount:menu
2025-07-26T10:05:53 [DEBU] {6ca9984eeaaa55188b131350581ed779} 权限验证通过: adminId: 1 permission: system:officialAccount:menu path: /wechat/menu/tree
2025-07-26T10:09:10 [INFO] 搜索趋势定时任务启动成功, CronID: {"Name":"cron-1","RegisterTime":"2025-07-26T10:09:10.1688219+08:00"}
2025-07-26T10:09:10 [INFO] 当前定时任务数量: 1
2025-07-26T10:09:10 [INFO] 定时任务 1 : {"Name":"cron-1","RegisterTime":"2025-07-26T10:09:10.1688219+08:00"}
2025-07-26T10:09:28 [INFO] {c4f4d06d1cab5518dc3682704539ac67} Detail页面Session ID: e59xvh06wlklo0dbkt4gnr5bg02001zu
2025-07-26T10:09:28 [INFO] {c4f4d06d1cab5518dc3682704539ac67} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:09:28 [INFO] {c4f4d06d1cab5518dc3682704539ac67} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:09:33 [INFO] {a0e2c07b1dab5518e1368270a03e9f05} Detail页面Session ID: e59xvh06wlklo0dbkt4gnr5bg02001zu
2025-07-26T10:09:33 [INFO] {a0e2c07b1dab5518e1368270a03e9f05} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:09:33 [INFO] {a0e2c07b1dab5518e1368270a03e9f05} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:09:59 [INFO] {cc143f9e23ab5518e23682706a22a672} Detail页面Session ID: e59xvh06wlklo0dbkt4gnr5bg02001zu
2025-07-26T10:09:59 [INFO] {cc143f9e23ab5518e23682706a22a672} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:09:59 [INFO] {cc143f9e23ab5518e23682706a22a672} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:10:10 [INFO] {bca7154726ab5518e336827007dce6c6} Detail页面Session ID: e59xvh06wlklo0dbkt4gnr5bg02001zu
2025-07-26T10:10:10 [INFO] {bca7154726ab5518e336827007dce6c6} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:10:10 [INFO] {bca7154726ab5518e336827007dce6c6} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:10:53 [INFO] {20fae72730ab5518e43682703b7aaa8c} Detail页面Session ID: e59xvh06wlklo0dbkt4gnr5bg02001zu
2025-07-26T10:10:53 [INFO] {20fae72730ab5518e43682703b7aaa8c} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:10:53 [INFO] {20fae72730ab5518e43682703b7aaa8c} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:11:00 [INFO] {dceabecf31ab5518e8368270a08e6cab} Detail页面Session ID: 1b5piw7hpc3ui0dblmelknktss100lwz
2025-07-26T10:11:00 [INFO] {dceabecf31ab5518e8368270a08e6cab} Session中的wechat_user: {"is_nil":true,"raw_data":null}
2025-07-26T10:11:00 [INFO] {dceabecf31ab5518e8368270a08e6cab} GetCurrentWechatUser结果: {"is_nil":true,"user_data":null}
2025-07-26T10:12:04 [INFO] {485b4db540ab5518ec3682709ca9694d} Detail页面Session ID: 1bl7n50hpc3ui0dblmfeys4oy0200qc9
2025-07-26T10:12:04 [INFO] {485b4db540ab5518ec3682709ca9694d} Session中的wechat_user: {"is_nil":true,"raw_data":null}
2025-07-26T10:12:04 [INFO] {485b4db540ab5518ec3682709ca9694d} GetCurrentWechatUser结果: {"is_nil":true,"user_data":null}
2025-07-26T10:12:56 [INFO] {f4e99edb4cab5518ed368270d3a2f376} Detail页面Session ID: 1bl7n50hpc3ui0dblmg2xs8hdg300xsd
2025-07-26T10:12:56 [INFO] {f4e99edb4cab5518ed368270d3a2f376} Session中的wechat_user: {"is_nil":true,"raw_data":null}
2025-07-26T10:12:56 [INFO] {f4e99edb4cab5518ed368270d3a2f376} GetCurrentWechatUser结果: {"is_nil":true,"user_data":null}
2025-07-26T10:14:09 [INFO] {382610d45dab5518ee368270bd912e0d} Detail页面Session ID: wxk8560hpc3ui0dblmh0f7npyw400ho0
2025-07-26T10:14:09 [INFO] {382610d45dab5518ee368270bd912e0d} Session中的wechat_user: {"is_nil":true,"raw_data":null}
2025-07-26T10:14:09 [INFO] {382610d45dab5518ee368270bd912e0d} GetCurrentWechatUser结果: {"is_nil":true,"user_data":null}
2025-07-26T10:14:20 [INFO] {90f2415360ab5518f1368270ccc9f93f} 用户登录: {"client_ip":"*************","login_at":"2025-07-26 10:14:20","user_id":1}
2025-07-26T10:14:20 [INFO] {90f2415360ab5518f1368270ccc9f93f} 用户信息已存储到Session: {"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","user_id":1}
2025-07-26T10:14:20 [INFO] {90f2415360ab5518f1368270ccc9f93f} Session存储验证成功
2025-07-26T10:14:20 [INFO] {90f2415360ab5518f1368270ccc9f93f} 准备重定向到: /m/orders
2025-07-26T10:14:20 [INFO] {d02cd87e60ab5518f2368270a1f4f894} Detail页面Session ID: wo1la801sw7hv0dblmh5muid08500jid
2025-07-26T10:14:20 [INFO] {d02cd87e60ab5518f2368270a1f4f894} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:14:20 [INFO] {d02cd87e60ab5518f2368270a1f4f894} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:16:49 [INFO] {c095323083ab5518f43682701a3e09f2} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:16:49 [INFO] {c095323083ab5518f43682701a3e09f2} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:16:49 [INFO] {c095323083ab5518f43682701a3e09f2} ValidateToken - Claims: 1 access admin
2025-07-26T10:16:49 [INFO] {c095323083ab5518f43682701a3e09f2} Auth middleware - ValidateToken result: 1
2025-07-26T10:16:49 [INFO] {c095323083ab5518f43682701a3e09f2} Auth middleware - set admin_id to context: 1
2025-07-26T10:16:49 [INFO] {c095323083ab5518f43682701a3e09f2} Required permission: system:officialAccount:menuEdit for PUT /wechat/menu/8
2025-07-26T10:16:49 [INFO] {c095323083ab5518f43682701a3e09f2} 超级管理员访问: system:officialAccount:menuEdit
2025-07-26T10:16:49 [DEBU] {c095323083ab5518f43682701a3e09f2} 权限验证通过: adminId: 1 permission: system:officialAccount:menuEdit path: /wechat/menu/8
2025-07-26T10:16:49 [INFO] {c095323083ab5518f43682701a3e09f2} 更新微信菜单成功: id: 8
2025-07-26T10:16:49 [INFO] {f469383683ab5518f73682708545d537} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:16:49 [INFO] {f469383683ab5518f73682708545d537} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:16:49 [INFO] {f469383683ab5518f73682708545d537} ValidateToken - Claims: 1 access admin
2025-07-26T10:16:49 [INFO] {f469383683ab5518f73682708545d537} Auth middleware - ValidateToken result: 1
2025-07-26T10:16:49 [INFO] {f469383683ab5518f73682708545d537} Auth middleware - set admin_id to context: 1
2025-07-26T10:16:49 [INFO] {f469383683ab5518f73682708545d537} Required permission: system:officialAccount:menu for GET /wechat/menu/tree
2025-07-26T10:16:49 [INFO] {f469383683ab5518f73682708545d537} 超级管理员访问: system:officialAccount:menu
2025-07-26T10:16:49 [DEBU] {f469383683ab5518f73682708545d537} 权限验证通过: adminId: 1 permission: system:officialAccount:menu path: /wechat/menu/tree
2025-07-26T10:16:54 [INFO] {14c1f82f84ab5518f93682701b8beffd} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:16:54 [INFO] {14c1f82f84ab5518f93682701b8beffd} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:16:54 [INFO] {14c1f82f84ab5518f93682701b8beffd} ValidateToken - Claims: 1 access admin
2025-07-26T10:16:54 [INFO] {14c1f82f84ab5518f93682701b8beffd} Auth middleware - ValidateToken result: 1
2025-07-26T10:16:54 [INFO] {14c1f82f84ab5518f93682701b8beffd} Auth middleware - set admin_id to context: 1
2025-07-26T10:16:54 [INFO] {14c1f82f84ab5518f93682701b8beffd} Required permission: system:officialAccount:menuPublish for POST /wechat/menu/publish
2025-07-26T10:16:54 [INFO] {14c1f82f84ab5518f93682701b8beffd} 超级管理员访问: system:officialAccount:menuPublish
2025-07-26T10:16:54 [DEBU] {14c1f82f84ab5518f93682701b8beffd} 权限验证通过: adminId: 1 permission: system:officialAccount:menuPublish path: /wechat/menu/publish
2025-07-26T10:16:55 [INFO] {14c1f82f84ab5518f93682701b8beffd} 微信菜单发布成功: {"errmsg":"ok"}
2025-07-26T10:17:08 [INFO] {f0a98b8487ab5518fb3682704205e2c9} Detail页面Session ID: wo1la801sw7hv0dblmh5muid08500jid
2025-07-26T10:17:08 [INFO] {f0a98b8487ab5518fb3682704205e2c9} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:17:08 [INFO] {f0a98b8487ab5518fb3682704205e2c9} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:19:34 [INFO] {bc16229da9ab5518fe36827010445d4c} Detail页面Session ID: wo1la801sw7hv0dblmh5muid08500jid
2025-07-26T10:19:34 [INFO] {bc16229da9ab5518fe36827010445d4c} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:19:34 [INFO] {bc16229da9ab5518fe36827010445d4c} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:19:46 [INFO] {e8a98446acab5518ff3682701916ec8f} Detail页面Session ID: vfrxq00hpc3ui0dblmlb7e0emo600fng
2025-07-26T10:19:46 [INFO] {e8a98446acab5518ff3682701916ec8f} Session中的wechat_user: {"is_nil":true,"raw_data":null}
2025-07-26T10:19:46 [INFO] {e8a98446acab5518ff3682701916ec8f} GetCurrentWechatUser结果: {"is_nil":true,"user_data":null}
2025-07-26T10:19:58 [INFO] {bcf79ae8aeab55180237827048520985} 用户登录: {"client_ip":"*************","login_at":"2025-07-26 10:19:58","user_id":1}
2025-07-26T10:19:58 [INFO] {bcf79ae8aeab55180237827048520985} 用户信息已存储到Session: {"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","user_id":1}
2025-07-26T10:19:58 [INFO] {bcf79ae8aeab55180237827048520985} Session存储验证成功
2025-07-26T10:19:58 [INFO] {bcf79ae8aeab55180237827048520985} 准备重定向到: /m/orders
2025-07-26T10:19:58 [INFO] {cc74e411afab5518033782705f0d544c} Detail页面Session ID: 1n18l4h1sw7hv0dblmlgo3rq1k700xbn
2025-07-26T10:19:58 [INFO] {cc74e411afab5518033782705f0d544c} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:19:58 [INFO] {cc74e411afab5518033782705f0d544c} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:20:21 [INFO] {5493ad5bb4ab5518043782708da39ef4} Detail页面Session ID: 1n18l4h1sw7hv0dblmlgo3rq1k700xbn
2025-07-26T10:20:21 [INFO] {5493ad5bb4ab5518043782708da39ef4} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:20:21 [INFO] {5493ad5bb4ab5518043782708da39ef4} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:21:10 [INFO] {847f59e7bfab551805378270d766e6ab} Auth middleware - Authorization header:
2025-07-26T10:22:58 [INFO] 搜索趋势定时任务启动成功, CronID: {"Name":"cron-1","RegisterTime":"2025-07-26T10:22:58.975759+08:00"}
2025-07-26T10:22:58 [INFO] 当前定时任务数量: 1
2025-07-26T10:22:58 [INFO] 定时任务 1 : {"Name":"cron-1","RegisterTime":"2025-07-26T10:22:58.975759+08:00"}
2025-07-26T10:26:06 [INFO] {c82aefe504ac5518951c300243544592} Auth middleware - Authorization header:
2025-07-26T10:26:22 [INFO] {f029758f08ac5518961c300206655ba2} 用户订单统计查询成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","paid_order_count":1,"paid_total_amount":596,"total_order_count":1,"unpaid_order_count":0,"user_id":1}
2025-07-26T10:30:35 [INFO] 搜索趋势定时任务启动成功, CronID: {"Name":"cron-1","RegisterTime":"2025-07-26T10:30:35.5930438+08:00"}
2025-07-26T10:30:35 [INFO] 当前定时任务数量: 1
2025-07-26T10:30:35 [INFO] 定时任务 1 : {"Name":"cron-1","RegisterTime":"2025-07-26T10:30:35.5930438+08:00"}
2025-07-26T10:35:03 [INFO] {88c48cc681ac55180af21c4c7695488f} Detail页面Session ID: 1n18l4h1sw7hv0dblmlgo3rq1k700xbn
2025-07-26T10:35:03 [INFO] {88c48cc681ac55180af21c4c7695488f} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:35:03 [INFO] {88c48cc681ac55180af21c4c7695488f} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:35:03 [INFO] {b8520ce881ac55180cf21c4c459c6051} 用户订单统计查询成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","paid_order_count":0,"paid_total_amount":0,"total_order_count":1,"unpaid_order_count":1,"user_id":1}
2025-07-26T10:36:59 [INFO] {cc4676d89cac55180df21c4c1c3c787a} Detail页面Session ID: 1n18l4h1sw7hv0dblmlgo3rq1k700xbn
2025-07-26T10:36:59 [INFO] {cc4676d89cac55180df21c4c1c3c787a} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:36:59 [INFO] {cc4676d89cac55180df21c4c1c3c787a} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:37:06 [INFO] {ec319e7b9eac551811f21c4cfcfb3390} 订单创建成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","order_id":3,"order_sn":"ZB202507261037064862","price":298,"user_id":1}
2025-07-26T10:37:14 [INFO] {c492fe6ca0ac551812f21c4c6cd54ad9} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:37:14 [INFO] {c492fe6ca0ac551812f21c4c6cd54ad9} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:37:14 [INFO] {c492fe6ca0ac551812f21c4c6cd54ad9} ValidateToken - Claims: 1 access admin
2025-07-26T10:37:14 [INFO] {c492fe6ca0ac551812f21c4c6cd54ad9} Auth middleware - ValidateToken result: 1
2025-07-26T10:37:14 [INFO] {c492fe6ca0ac551812f21c4c6cd54ad9} Auth middleware - set admin_id to context: 1
2025-07-26T10:37:14 [INFO] {c492fe6ca0ac551812f21c4c6cd54ad9} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T10:37:14 [INFO] {c492fe6ca0ac551812f21c4c6cd54ad9} 超级管理员访问: system:zb_order:list
2025-07-26T10:37:14 [DEBU] {c492fe6ca0ac551812f21c4c6cd54ad9} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T10:37:32 [INFO] {184f2087a4ac551814f21c4cb303e8c1} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:37:32 [INFO] {184f2087a4ac551814f21c4cb303e8c1} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:37:32 [INFO] {184f2087a4ac551814f21c4cb303e8c1} ValidateToken - Claims: 1 access admin
2025-07-26T10:37:32 [INFO] {184f2087a4ac551814f21c4cb303e8c1} Auth middleware - ValidateToken result: 1
2025-07-26T10:37:32 [INFO] {184f2087a4ac551814f21c4cb303e8c1} Auth middleware - set admin_id to context: 1
2025-07-26T10:37:32 [INFO] {184f2087a4ac551814f21c4cb303e8c1} Required permission: system:zb_order:pay for PUT /zb_order/pay-status
2025-07-26T10:37:32 [INFO] {184f2087a4ac551814f21c4cb303e8c1} 超级管理员访问: system:zb_order:pay
2025-07-26T10:37:32 [DEBU] {184f2087a4ac551814f21c4cb303e8c1} 权限验证通过: adminId: 1 permission: system:zb_order:pay path: /zb_order/pay-status
2025-07-26T10:37:32 [INFO] {184f2087a4ac551814f21c4cb303e8c1} 后台代替支付成功: {"amount":298,"order_id":3,"order_sn":"ZB202507261037064862","trade_type":"ADMIN","user_id":1}
2025-07-26T10:37:32 [INFO] {3011538ea4ac551817f21c4cacf50e30} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:37:32 [INFO] {3011538ea4ac551817f21c4cacf50e30} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:37:32 [INFO] {3011538ea4ac551817f21c4cacf50e30} ValidateToken - Claims: 1 access admin
2025-07-26T10:37:32 [INFO] {3011538ea4ac551817f21c4cacf50e30} Auth middleware - ValidateToken result: 1
2025-07-26T10:37:32 [INFO] {3011538ea4ac551817f21c4cacf50e30} Auth middleware - set admin_id to context: 1
2025-07-26T10:37:32 [INFO] {3011538ea4ac551817f21c4cacf50e30} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T10:37:32 [INFO] {3011538ea4ac551817f21c4cacf50e30} 超级管理员访问: system:zb_order:list
2025-07-26T10:37:32 [DEBU] {3011538ea4ac551817f21c4cacf50e30} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T10:37:43 [INFO] {74a0780ba7ac551818f21c4ce3cf24ae} Detail页面Session ID: 1n18l4h1sw7hv0dblmlgo3rq1k700xbn
2025-07-26T10:37:43 [INFO] {74a0780ba7ac551818f21c4ce3cf24ae} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:37:43 [INFO] {74a0780ba7ac551818f21c4ce3cf24ae} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:37:43 [INFO] {8c45ad28a7ac55181af21c4c9f9be295} 用户订单统计查询成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","paid_order_count":0,"paid_total_amount":0,"total_order_count":2,"unpaid_order_count":2,"user_id":1}
2025-07-26T10:38:01 [INFO] {b000f147abac55181bf21c4c654e7176} Detail页面Session ID: 1n18l4h1sw7hv0dblmlgo3rq1k700xbn
2025-07-26T10:38:01 [INFO] {b000f147abac55181bf21c4c654e7176} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:38:01 [INFO] {b000f147abac55181bf21c4c654e7176} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:38:02 [INFO] {70b42a63abac55181cf21c4c318fbc5f} 用户订单统计查询成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","paid_order_count":0,"paid_total_amount":0,"total_order_count":2,"unpaid_order_count":2,"user_id":1}
2025-07-26T10:39:17 [INFO] {a4ccdd01bdac55181ef21c4cba16daf5} Detail页面Session ID: 1n18l4h1sw7hv0dblmlgo3rq1k700xbn
2025-07-26T10:39:17 [INFO] {a4ccdd01bdac55181ef21c4cba16daf5} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:39:17 [INFO] {a4ccdd01bdac55181ef21c4cba16daf5} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:39:18 [INFO] {2458e822bdac551820f21c4c310f1478} 用户订单统计查询成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","paid_order_count":0,"paid_total_amount":0,"total_order_count":2,"unpaid_order_count":2,"user_id":1}
2025-07-26T10:39:26 [INFO] {780f9c0cbfac551822f21c4cde0478bb} Detail页面Session ID: 1n18l4h1sw7hv0dblmlgo3rq1k700xbn
2025-07-26T10:39:26 [INFO] {780f9c0cbfac551822f21c4cde0478bb} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:39:26 [INFO] {780f9c0cbfac551822f21c4cde0478bb} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:39:26 [INFO] {c08cdf26bfac551824f21c4cbb62c384} 用户订单统计查询成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","paid_order_count":0,"paid_total_amount":0,"total_order_count":2,"unpaid_order_count":2,"user_id":1}
2025-07-26T10:39:43 [INFO] {a07fbdf4c2ac551826f21c4c4262caa3} Detail页面Session ID: 1n18l4h1sw7hv0dblmlgo3rq1k700xbn
2025-07-26T10:39:43 [INFO] {a07fbdf4c2ac551826f21c4c4262caa3} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:39:43 [INFO] {a07fbdf4c2ac551826f21c4c4262caa3} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:39:43 [INFO] {d03e500ec3ac551827f21c4cf82b2cfd} 用户订单统计查询成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","paid_order_count":0,"paid_total_amount":0,"total_order_count":2,"unpaid_order_count":2,"user_id":1}
2025-07-26T10:44:27 [INFO] {0ccf541405ad551829f21c4ca01ea302} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:44:27 [INFO] {0c68611405ad55182af21c4ca9e71b97} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:44:27 [INFO] {0c68611405ad55182af21c4ca9e71b97} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:44:27 [INFO] {0ccf541405ad551829f21c4ca01ea302} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:44:27 [INFO] {0c68611405ad55182af21c4ca9e71b97} ValidateToken - Claims: 1 access admin
2025-07-26T10:44:27 [INFO] {0c68611405ad55182af21c4ca9e71b97} Auth middleware - ValidateToken result: 1
2025-07-26T10:44:27 [INFO] {0ccf541405ad551829f21c4ca01ea302} ValidateToken - Claims: 1 access admin
2025-07-26T10:44:27 [INFO] {84367f1405ad55182bf21c4c0819850f} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:44:27 [INFO] {0c68611405ad55182af21c4ca9e71b97} Auth middleware - set admin_id to context: 1
2025-07-26T10:44:27 [INFO] {0ccf541405ad551829f21c4ca01ea302} Auth middleware - ValidateToken result: 1
2025-07-26T10:44:27 [INFO] {84367f1405ad55182bf21c4c0819850f} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:44:27 [INFO] {0ccf541405ad551829f21c4ca01ea302} Auth middleware - set admin_id to context: 1
2025-07-26T10:44:27 [INFO] {84367f1405ad55182bf21c4c0819850f} ValidateToken - Claims: 1 access admin
2025-07-26T10:44:27 [INFO] {84367f1405ad55182bf21c4c0819850f} Auth middleware - ValidateToken result: 1
2025-07-26T10:44:27 [INFO] {84367f1405ad55182bf21c4c0819850f} Auth middleware - set admin_id to context: 1
2025-07-26T10:44:27 [WARN] {0c68611405ad55182af21c4ca9e71b97} 路由未配置权限标识: GET /auth/permissions
2025-07-26T10:44:27 [WARN] {0ccf541405ad551829f21c4ca01ea302} 路由未配置权限标识: GET /auth/userinfo
2025-07-26T10:44:27 [WARN] {84367f1405ad55182bf21c4c0819850f} 路由未配置权限标识: GET /auth/menus
2025-07-26T10:44:27 [INFO] {84367f1405ad55182bf21c4c0819850f} GetMenus - adminId from context: 1
2025-07-26T10:44:27 [INFO] {ccbc3a2105ad55182cf21c4c819dfdb8} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:44:27 [INFO] {ccbc3a2105ad55182cf21c4c819dfdb8} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:44:27 [INFO] {ccbc3a2105ad55182cf21c4c819dfdb8} ValidateToken - Claims: 1 access admin
2025-07-26T10:44:27 [INFO] {ccbc3a2105ad55182cf21c4c819dfdb8} Auth middleware - ValidateToken result: 1
2025-07-26T10:44:27 [INFO] {ccbc3a2105ad55182cf21c4c819dfdb8} Auth middleware - set admin_id to context: 1
2025-07-26T10:44:27 [INFO] {ccbc3a2105ad55182cf21c4c819dfdb8} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T10:44:27 [INFO] {ccbc3a2105ad55182cf21c4c819dfdb8} 超级管理员访问: system:zb_order:list
2025-07-26T10:44:27 [DEBU] {ccbc3a2105ad55182cf21c4c819dfdb8} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T10:44:41 [INFO] {90ed955008ad55182df21c4c96e73783} Detail页面Session ID: 1n18l4h1sw7hv0dblmlgo3rq1k700xbn
2025-07-26T10:44:41 [INFO] {90ed955008ad55182df21c4c96e73783} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:44:41 [INFO] {90ed955008ad55182df21c4c96e73783} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:44:41 [INFO] {c8ddac7008ad55182ff21c4c90c942a9} 用户订单统计查询成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","paid_order_count":0,"paid_total_amount":0,"total_order_count":2,"unpaid_order_count":2,"user_id":1}
2025-07-26T10:44:56 [INFO] {d46263d50bad551832f21c4c1349763a} Detail页面Session ID: 1n18l4h1sw7hv0dblmlgo3rq1k700xbn
2025-07-26T10:44:56 [INFO] {d46263d50bad551832f21c4c1349763a} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:44:56 [INFO] {d46263d50bad551832f21c4c1349763a} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:44:56 [INFO] {2c4abaf30bad551833f21c4c02566e1f} 用户订单统计查询成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","paid_order_count":0,"paid_total_amount":0,"total_order_count":2,"unpaid_order_count":2,"user_id":1}
2025-07-26T10:55:15 [INFO] {18237bf69bad551835f21c4c03266656} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:55:15 [INFO] {18237bf69bad551835f21c4c03266656} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:55:15 [INFO] {18237bf69bad551835f21c4c03266656} ValidateToken - Claims: 1 access admin
2025-07-26T10:55:15 [INFO] {18237bf69bad551835f21c4c03266656} Auth middleware - ValidateToken result: 1
2025-07-26T10:55:15 [INFO] {18237bf69bad551836f21c4c08578ac9} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:55:15 [INFO] {b86683f69bad551837f21c4c0ac9d8cb} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:55:15 [INFO] {18237bf69bad551835f21c4c03266656} Auth middleware - set admin_id to context: 1
2025-07-26T10:55:15 [INFO] {18237bf69bad551836f21c4c08578ac9} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:55:15 [INFO] {b86683f69bad551837f21c4c0ac9d8cb} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:55:15 [INFO] {18237bf69bad551836f21c4c08578ac9} ValidateToken - Claims: 1 access admin
2025-07-26T10:55:15 [INFO] {18237bf69bad551836f21c4c08578ac9} Auth middleware - ValidateToken result: 1
2025-07-26T10:55:15 [INFO] {18237bf69bad551836f21c4c08578ac9} Auth middleware - set admin_id to context: 1
2025-07-26T10:55:15 [INFO] {b86683f69bad551837f21c4c0ac9d8cb} ValidateToken - Claims: 1 access admin
2025-07-26T10:55:15 [INFO] {b86683f69bad551837f21c4c0ac9d8cb} Auth middleware - ValidateToken result: 1
2025-07-26T10:55:15 [INFO] {b86683f69bad551837f21c4c0ac9d8cb} Auth middleware - set admin_id to context: 1
2025-07-26T10:55:15 [WARN] {18237bf69bad551835f21c4c03266656} 路由未配置权限标识: GET /auth/userinfo
2025-07-26T10:55:15 [WARN] {b86683f69bad551837f21c4c0ac9d8cb} 路由未配置权限标识: GET /auth/menus
2025-07-26T10:55:15 [INFO] {b86683f69bad551837f21c4c0ac9d8cb} GetMenus - adminId from context: 1
2025-07-26T10:55:15 [WARN] {18237bf69bad551836f21c4c08578ac9} 路由未配置权限标识: GET /auth/permissions
2025-07-26T10:55:15 [INFO] {488ea7019cad551838f21c4cf5a7b4a6} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:55:15 [INFO] {488ea7019cad551838f21c4cf5a7b4a6} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:55:15 [INFO] {488ea7019cad551838f21c4cf5a7b4a6} ValidateToken - Claims: 1 access admin
2025-07-26T10:55:15 [INFO] {488ea7019cad551838f21c4cf5a7b4a6} Auth middleware - ValidateToken result: 1
2025-07-26T10:55:15 [INFO] {488ea7019cad551838f21c4cf5a7b4a6} Auth middleware - set admin_id to context: 1
2025-07-26T10:55:15 [INFO] {488ea7019cad551838f21c4cf5a7b4a6} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T10:55:15 [INFO] {488ea7019cad551838f21c4cf5a7b4a6} 超级管理员访问: system:zb_order:list
2025-07-26T10:55:15 [DEBU] {488ea7019cad551838f21c4cf5a7b4a6} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T10:55:24 [INFO] {e07fec219ead551839f21c4c5cab6aeb} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:55:24 [INFO] {e07fec219ead551839f21c4c5cab6aeb} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:55:24 [INFO] {e07fec219ead551839f21c4c5cab6aeb} ValidateToken - Claims: 1 access admin
2025-07-26T10:55:24 [INFO] {e07fec219ead551839f21c4c5cab6aeb} Auth middleware - ValidateToken result: 1
2025-07-26T10:55:24 [INFO] {e07fec219ead551839f21c4c5cab6aeb} Auth middleware - set admin_id to context: 1
2025-07-26T10:55:24 [INFO] {94260f229ead55183bf21c4c603416e4} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:55:24 [INFO] {94260f229ead55183bf21c4c603416e4} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:55:24 [INFO] {94260f229ead55183bf21c4c603416e4} ValidateToken - Claims: 1 access admin
2025-07-26T10:55:24 [INFO] {94260f229ead55183bf21c4c603416e4} Auth middleware - ValidateToken result: 1
2025-07-26T10:55:24 [INFO] {94260f229ead55183bf21c4c603416e4} Auth middleware - set admin_id to context: 1
2025-07-26T10:55:24 [WARN] {e07fec219ead551839f21c4c5cab6aeb} 路由未配置权限标识: GET /auth/userinfo
2025-07-26T10:55:24 [INFO] {94260f229ead55183af21c4c1383460c} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:55:24 [INFO] {94260f229ead55183af21c4c1383460c} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:55:24 [INFO] {94260f229ead55183af21c4c1383460c} ValidateToken - Claims: 1 access admin
2025-07-26T10:55:24 [INFO] {94260f229ead55183af21c4c1383460c} Auth middleware - ValidateToken result: 1
2025-07-26T10:55:24 [WARN] {94260f229ead55183bf21c4c603416e4} 路由未配置权限标识: GET /auth/menus
2025-07-26T10:55:24 [INFO] {94260f229ead55183af21c4c1383460c} Auth middleware - set admin_id to context: 1
2025-07-26T10:55:24 [INFO] {94260f229ead55183bf21c4c603416e4} GetMenus - adminId from context: 1
2025-07-26T10:55:24 [WARN] {94260f229ead55183af21c4c1383460c} 路由未配置权限标识: GET /auth/permissions
2025-07-26T10:55:25 [INFO] {6441c7429ead55183cf21c4cf4894996} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:55:25 [INFO] {6441c7429ead55183cf21c4cf4894996} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:55:25 [INFO] {6441c7429ead55183cf21c4cf4894996} ValidateToken - Claims: 1 access admin
2025-07-26T10:55:25 [INFO] {6441c7429ead55183cf21c4cf4894996} Auth middleware - ValidateToken result: 1
2025-07-26T10:55:25 [INFO] {6441c7429ead55183cf21c4cf4894996} Auth middleware - set admin_id to context: 1
2025-07-26T10:55:25 [INFO] {6441c7429ead55183cf21c4cf4894996} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T10:55:25 [INFO] {6441c7429ead55183cf21c4cf4894996} 超级管理员访问: system:zb_order:list
2025-07-26T10:55:25 [DEBU] {6441c7429ead55183cf21c4cf4894996} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T10:55:56 [INFO] {1cd6e872a5ad55183df21c4c21f08fbe} Detail页面Session ID: 1n18l4h1sw7hv0dblmlgo3rq1k700xbn
2025-07-26T10:55:56 [INFO] {1cd6e872a5ad55183df21c4c21f08fbe} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:55:56 [INFO] {1cd6e872a5ad55183df21c4c21f08fbe} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T10:55:56 [INFO] {8481ba92a5ad55183ff21c4c9ff6188d} 用户订单统计查询成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","paid_order_count":0,"paid_total_amount":0,"total_order_count":2,"unpaid_order_count":2,"user_id":1}
2025-07-26T10:56:59 [INFO] {3c9d4435b4ad551841f21c4c136a1d80} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T10:56:59 [INFO] {3c9d4435b4ad551841f21c4c136a1d80} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T10:56:59 [INFO] {3c9d4435b4ad551841f21c4c136a1d80} ValidateToken - Claims: 1 access admin
2025-07-26T10:56:59 [INFO] {3c9d4435b4ad551841f21c4c136a1d80} Auth middleware - ValidateToken result: 1
2025-07-26T10:56:59 [INFO] {3c9d4435b4ad551841f21c4c136a1d80} Auth middleware - set admin_id to context: 1
2025-07-26T10:56:59 [INFO] {3c9d4435b4ad551841f21c4c136a1d80} Required permission: system:zb_order:detail for GET /zb_order/detail
2025-07-26T10:56:59 [INFO] {3c9d4435b4ad551841f21c4c136a1d80} 超级管理员访问: system:zb_order:detail
2025-07-26T10:56:59 [DEBU] {3c9d4435b4ad551841f21c4c136a1d80} 权限验证通过: adminId: 1 permission: system:zb_order:detail path: /zb_order/detail
2025-07-26T10:57:29 [INFO] 搜索趋势定时任务启动成功, CronID: {"Name":"cron-1","RegisterTime":"2025-07-26T10:57:29.3645048+08:00"}
2025-07-26T10:57:29 [INFO] 当前定时任务数量: 1
2025-07-26T10:57:29 [INFO] 定时任务 1 : {"Name":"cron-1","RegisterTime":"2025-07-26T10:57:29.3645048+08:00"}
2025-07-26T11:00:00 [INFO] 开始更新搜索趋势...
2025-07-26T11:00:00 [INFO] 标记热门关键词: 中关村
2025-07-26T11:00:00 [INFO] 标记热门关键词: 吉林工商学院
2025-07-26T11:00:00 [INFO] 标记热门关键词: 中国银行
2025-07-26T11:00:00 [INFO] 热门搜索趋势更新完成，共标记 3 个热门关键词
2025-07-26T11:00:00 [INFO] 搜索趋势更新完成
2025-07-26T11:00:11 [INFO] {684af9cfe0ad55180989542755842c64} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:00:11 [INFO] {684af9cfe0ad55180989542755842c64} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:00:11 [INFO] {684af9cfe0ad55180989542755842c64} ValidateToken - Claims: 1 access admin
2025-07-26T11:00:11 [INFO] {684af9cfe0ad55180989542755842c64} Auth middleware - ValidateToken result: 1
2025-07-26T11:00:11 [INFO] {684af9cfe0ad55180989542755842c64} Auth middleware - set admin_id to context: 1
2025-07-26T11:00:11 [INFO] {684af9cfe0ad55180989542755842c64} Required permission: system:zb_order:detail for GET /zb_order/detail
2025-07-26T11:00:11 [INFO] {684af9cfe0ad55180989542755842c64} 超级管理员访问: system:zb_order:detail
2025-07-26T11:00:11 [DEBU] {684af9cfe0ad55180989542755842c64} 权限验证通过: adminId: 1 permission: system:zb_order:detail path: /zb_order/detail
2025-07-26T11:00:44 [INFO] {2066ccade8ad55180b895427ba00c3b7} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:00:44 [INFO] {2066ccade8ad55180b895427ba00c3b7} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:00:44 [INFO] {2066ccade8ad55180b895427ba00c3b7} ValidateToken - Claims: 1 access admin
2025-07-26T11:00:44 [INFO] {2066ccade8ad55180b895427ba00c3b7} Auth middleware - ValidateToken result: 1
2025-07-26T11:00:44 [INFO] {2066ccade8ad55180b895427ba00c3b7} Auth middleware - set admin_id to context: 1
2025-07-26T11:00:44 [INFO] {2066ccade8ad55180b895427ba00c3b7} Required permission: system:zb_good:list for GET /zb_good/list
2025-07-26T11:00:44 [INFO] {2066ccade8ad55180b895427ba00c3b7} 超级管理员访问: system:zb_good:list
2025-07-26T11:00:44 [DEBU] {2066ccade8ad55180b895427ba00c3b7} 权限验证通过: adminId: 1 permission: system:zb_good:list path: /zb_good/list
2025-07-26T11:00:48 [INFO] {481ee770e9ad55180c895427ae19b233} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:00:48 [INFO] {481ee770e9ad55180c895427ae19b233} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:00:48 [INFO] {481ee770e9ad55180c895427ae19b233} ValidateToken - Claims: 1 access admin
2025-07-26T11:00:48 [INFO] {481ee770e9ad55180c895427ae19b233} Auth middleware - ValidateToken result: 1
2025-07-26T11:00:48 [INFO] {481ee770e9ad55180c895427ae19b233} Auth middleware - set admin_id to context: 1
2025-07-26T11:00:48 [INFO] {481ee770e9ad55180c895427ae19b233} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T11:00:48 [INFO] {481ee770e9ad55180c895427ae19b233} 超级管理员访问: system:zb_order:list
2025-07-26T11:00:48 [DEBU] {481ee770e9ad55180c895427ae19b233} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T11:00:49 [INFO] {500c1eb7e9ad55180d89542707e8e769} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:00:49 [INFO] {500c1eb7e9ad55180d89542707e8e769} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:00:49 [INFO] {500c1eb7e9ad55180d89542707e8e769} ValidateToken - Claims: 1 access admin
2025-07-26T11:00:49 [INFO] {500c1eb7e9ad55180d89542707e8e769} Auth middleware - ValidateToken result: 1
2025-07-26T11:00:49 [INFO] {500c1eb7e9ad55180d89542707e8e769} Auth middleware - set admin_id to context: 1
2025-07-26T11:00:49 [INFO] {500c1eb7e9ad55180d89542707e8e769} Required permission: system:zb_order:detail for GET /zb_order/detail
2025-07-26T11:00:49 [INFO] {500c1eb7e9ad55180d89542707e8e769} 超级管理员访问: system:zb_order:detail
2025-07-26T11:00:49 [DEBU] {500c1eb7e9ad55180d89542707e8e769} 权限验证通过: adminId: 1 permission: system:zb_order:detail path: /zb_order/detail
2025-07-26T11:01:16 [INFO] {d0fedd00f0ad55180e8954271aba979e} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:01:16 [INFO] {d0fedd00f0ad55180e8954271aba979e} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:01:16 [INFO] {d0fedd00f0ad55180e8954271aba979e} ValidateToken - Claims: 1 access admin
2025-07-26T11:01:16 [INFO] {d0fedd00f0ad55180e8954271aba979e} Auth middleware - ValidateToken result: 1
2025-07-26T11:01:16 [INFO] {d0fedd00f0ad55180e8954271aba979e} Auth middleware - set admin_id to context: 1
2025-07-26T11:01:16 [INFO] {d0fedd00f0ad55180e8954271aba979e} Required permission: system:zb_good:list for GET /zb_good/list
2025-07-26T11:01:16 [INFO] {d0fedd00f0ad55180e8954271aba979e} 超级管理员访问: system:zb_good:list
2025-07-26T11:01:16 [DEBU] {d0fedd00f0ad55180e8954271aba979e} 权限验证通过: adminId: 1 permission: system:zb_good:list path: /zb_good/list
2025-07-26T11:01:24 [INFO] {9097c0e6f1ad55180f8954271f5c5e78} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:01:24 [INFO] {9097c0e6f1ad55180f8954271f5c5e78} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:01:24 [INFO] {9097c0e6f1ad55180f8954271f5c5e78} ValidateToken - Claims: 1 access admin
2025-07-26T11:01:24 [INFO] {9097c0e6f1ad55180f8954271f5c5e78} Auth middleware - ValidateToken result: 1
2025-07-26T11:01:24 [INFO] {9097c0e6f1ad55180f8954271f5c5e78} Auth middleware - set admin_id to context: 1
2025-07-26T11:01:24 [INFO] {9097c0e6f1ad55180f8954271f5c5e78} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T11:01:24 [INFO] {9097c0e6f1ad55180f8954271f5c5e78} 超级管理员访问: system:zb_order:list
2025-07-26T11:01:24 [DEBU] {9097c0e6f1ad55180f8954271f5c5e78} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T11:01:25 [INFO] {1cc5e02af2ad551810895427e913a0c5} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:01:25 [INFO] {1cc5e02af2ad551810895427e913a0c5} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:01:25 [INFO] {1cc5e02af2ad551810895427e913a0c5} ValidateToken - Claims: 1 access admin
2025-07-26T11:01:25 [INFO] {1cc5e02af2ad551810895427e913a0c5} Auth middleware - ValidateToken result: 1
2025-07-26T11:01:25 [INFO] {1cc5e02af2ad551810895427e913a0c5} Auth middleware - set admin_id to context: 1
2025-07-26T11:01:25 [INFO] {1cc5e02af2ad551810895427e913a0c5} Required permission: system:zb_order:detail for GET /zb_order/detail
2025-07-26T11:01:25 [INFO] {1cc5e02af2ad551810895427e913a0c5} 超级管理员访问: system:zb_order:detail
2025-07-26T11:01:25 [DEBU] {1cc5e02af2ad551810895427e913a0c5} 权限验证通过: adminId: 1 permission: system:zb_order:detail path: /zb_order/detail
2025-07-26T11:02:05 [INFO] {e820a594fbad551811895427fca3f544} Detail页面Session ID: 1n18l4h1sw7hv0dblmlgo3rq1k700xbn
2025-07-26T11:02:05 [INFO] {e820a594fbad551811895427fca3f544} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T11:02:05 [INFO] {e820a594fbad551811895427fca3f544} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T11:02:11 [INFO] {7053bec4fcad551815895427bd14f34a} 订单创建成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","order_id":4,"order_sn":"ZB202507261102111862","price":9536,"user_id":1}
2025-07-26T11:02:15 [INFO] {f4e752bcfdad551816895427a8696a1e} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:02:15 [INFO] {f4e752bcfdad551816895427a8696a1e} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:02:15 [INFO] {f4e752bcfdad551816895427a8696a1e} ValidateToken - Claims: 1 access admin
2025-07-26T11:02:15 [INFO] {f4e752bcfdad551816895427a8696a1e} Auth middleware - ValidateToken result: 1
2025-07-26T11:02:15 [INFO] {f4e752bcfdad551816895427a8696a1e} Auth middleware - set admin_id to context: 1
2025-07-26T11:02:15 [WARN] {f4e752bcfdad551816895427a8696a1e} 路由未配置权限标识: GET /auth/userinfo
2025-07-26T11:02:15 [INFO] {f8ae8cbcfdad55181789542749144986} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:02:15 [INFO] {f8ae8cbcfdad55181789542749144986} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:02:15 [INFO] {f8ae8cbcfdad55181789542749144986} ValidateToken - Claims: 1 access admin
2025-07-26T11:02:15 [INFO] {f8ae8cbcfdad55181789542749144986} Auth middleware - ValidateToken result: 1
2025-07-26T11:02:15 [INFO] {0cb3a1bcfdad551818895427b3a89810} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:02:15 [INFO] {f8ae8cbcfdad55181789542749144986} Auth middleware - set admin_id to context: 1
2025-07-26T11:02:15 [INFO] {0cb3a1bcfdad551818895427b3a89810} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:02:15 [INFO] {0cb3a1bcfdad551818895427b3a89810} ValidateToken - Claims: 1 access admin
2025-07-26T11:02:15 [INFO] {0cb3a1bcfdad551818895427b3a89810} Auth middleware - ValidateToken result: 1
2025-07-26T11:02:15 [INFO] {0cb3a1bcfdad551818895427b3a89810} Auth middleware - set admin_id to context: 1
2025-07-26T11:02:15 [WARN] {f8ae8cbcfdad55181789542749144986} 路由未配置权限标识: GET /auth/permissions
2025-07-26T11:02:15 [WARN] {0cb3a1bcfdad551818895427b3a89810} 路由未配置权限标识: GET /auth/menus
2025-07-26T11:02:15 [INFO] {0cb3a1bcfdad551818895427b3a89810} GetMenus - adminId from context: 1
2025-07-26T11:02:15 [INFO] {084bb4defdad551819895427762ed2f3} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:02:15 [INFO] {084bb4defdad551819895427762ed2f3} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:02:15 [INFO] {084bb4defdad551819895427762ed2f3} ValidateToken - Claims: 1 access admin
2025-07-26T11:02:15 [INFO] {084bb4defdad551819895427762ed2f3} Auth middleware - ValidateToken result: 1
2025-07-26T11:02:15 [INFO] {084bb4defdad551819895427762ed2f3} Auth middleware - set admin_id to context: 1
2025-07-26T11:02:15 [INFO] {084bb4defdad551819895427762ed2f3} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T11:02:15 [INFO] {084bb4defdad551819895427762ed2f3} 超级管理员访问: system:zb_order:list
2025-07-26T11:02:15 [DEBU] {084bb4defdad551819895427762ed2f3} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T11:03:10 [INFO] {ace0ee8a0aae55181a895427a8324393} Detail页面Session ID: 1n18l4h1sw7hv0dblmlgo3rq1k700xbn
2025-07-26T11:03:10 [INFO] {ace0ee8a0aae55181a895427a8324393} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T11:03:10 [INFO] {ace0ee8a0aae55181a895427a8324393} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T11:03:10 [INFO] {b0f6f7b20aae55181c895427924a6167} 用户订单统计查询成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","paid_order_count":0,"paid_total_amount":0,"total_order_count":3,"unpaid_order_count":3,"user_id":1}
2025-07-26T11:22:15 [INFO] {b8346b3915af55181e895427fdb6f059} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:22:15 [INFO] {b8346b3915af55181e895427fdb6f059} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:22:15 [INFO] {b8346b3915af55181e895427fdb6f059} ValidateToken - Claims: 1 access admin
2025-07-26T11:22:15 [INFO] {b8346b3915af55181e895427fdb6f059} Auth middleware - ValidateToken result: 1
2025-07-26T11:22:15 [INFO] {b8346b3915af55181e895427fdb6f059} Auth middleware - set admin_id to context: 1
2025-07-26T11:22:15 [INFO] {b8346b3915af55181e895427fdb6f059} Required permission: system:config_group:list for GET /sys_config_group/list
2025-07-26T11:22:15 [INFO] {b8346b3915af55181e895427fdb6f059} 超级管理员访问: system:config_group:list
2025-07-26T11:22:15 [DEBU] {b8346b3915af55181e895427fdb6f059} 权限验证通过: adminId: 1 permission: system:config_group:list path: /sys_config_group/list
2025-07-26T11:22:19 [INFO] {1889c52416af551821895427fb842d14} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:22:19 [INFO] {1889c52416af551821895427fb842d14} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:22:19 [INFO] {1889c52416af551821895427fb842d14} ValidateToken - Claims: 1 access admin
2025-07-26T11:22:19 [INFO] {1889c52416af551821895427fb842d14} Auth middleware - ValidateToken result: 1
2025-07-26T11:22:19 [INFO] {1889c52416af551821895427fb842d14} Auth middleware - set admin_id to context: 1
2025-07-26T11:22:19 [INFO] {a008ff2416af5518228954272194c8bd} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:22:19 [INFO] {1889c52416af551821895427fb842d14} Required permission: system:config_group:list for GET /sys_config_group/list
2025-07-26T11:22:19 [INFO] {a008ff2416af5518228954272194c8bd} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:22:19 [INFO] {a008ff2416af5518228954272194c8bd} ValidateToken - Claims: 1 access admin
2025-07-26T11:22:19 [INFO] {a008ff2416af5518228954272194c8bd} Auth middleware - ValidateToken result: 1
2025-07-26T11:22:19 [INFO] {a008ff2416af5518228954272194c8bd} Auth middleware - set admin_id to context: 1
2025-07-26T11:22:19 [INFO] {1889c52416af551821895427fb842d14} 超级管理员访问: system:config_group:list
2025-07-26T11:22:19 [DEBU] {1889c52416af551821895427fb842d14} 权限验证通过: adminId: 1 permission: system:config_group:list path: /sys_config_group/list
2025-07-26T11:22:19 [INFO] {a008ff2416af5518228954272194c8bd} Required permission: system:config:list for GET /sys_config/list
2025-07-26T11:22:19 [INFO] {a008ff2416af5518228954272194c8bd} 超级管理员访问: system:config:list
2025-07-26T11:22:19 [DEBU] {a008ff2416af5518228954272194c8bd} 权限验证通过: adminId: 1 permission: system:config:list path: /sys_config/list
2025-07-26T11:22:28 [INFO] {34fc322618af5518238954273e46ba5f} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:22:28 [INFO] {34fc322618af5518238954273e46ba5f} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:22:28 [INFO] {34fc322618af5518238954273e46ba5f} ValidateToken - Claims: 1 access admin
2025-07-26T11:22:28 [INFO] {34fc322618af5518238954273e46ba5f} Auth middleware - ValidateToken result: 1
2025-07-26T11:22:28 [INFO] {34fc322618af5518238954273e46ba5f} Auth middleware - set admin_id to context: 1
2025-07-26T11:22:28 [INFO] {34fc322618af5518238954273e46ba5f} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T11:22:28 [INFO] {34fc322618af5518238954273e46ba5f} 超级管理员访问: system:zb_order:list
2025-07-26T11:22:28 [DEBU] {34fc322618af5518238954273e46ba5f} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T11:23:25 [INFO] {88f42b6f25af551824895427d88b0fcc} Detail页面Session ID: 1n18l4h1sw7hv0dblmlgo3rq1k700xbn
2025-07-26T11:23:25 [INFO] {88f42b6f25af551824895427d88b0fcc} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T11:23:25 [INFO] {88f42b6f25af551824895427d88b0fcc} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T11:23:25 [INFO] {08ce0a9125af5518268954275cbc2bec} 用户订单统计查询成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","paid_order_count":0,"paid_total_amount":0,"total_order_count":3,"unpaid_order_count":3,"user_id":1}
2025-07-26T11:23:34 [INFO] {64b6e39427af551827895427e742bdba} Detail页面Session ID: 1n18l4h1sw7hv0dblmlgo3rq1k700xbn
2025-07-26T11:23:34 [INFO] {64b6e39427af551827895427e742bdba} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T11:23:34 [INFO] {64b6e39427af551827895427e742bdba} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T11:23:41 [INFO] {18a7942e29af55182b89542725feb582} 订单创建成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","order_id":5,"order_sn":"ZB202507261123415771","price":298,"user_id":1}
2025-07-26T11:23:48 [INFO] {f482e4cf2aaf55182c89542798341c7b} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:23:48 [INFO] {f482e4cf2aaf55182c89542798341c7b} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:23:48 [INFO] {f482e4cf2aaf55182c89542798341c7b} ValidateToken - Claims: 1 access admin
2025-07-26T11:23:48 [INFO] {f482e4cf2aaf55182c89542798341c7b} Auth middleware - ValidateToken result: 1
2025-07-26T11:23:48 [INFO] {f482e4cf2aaf55182c89542798341c7b} Auth middleware - set admin_id to context: 1
2025-07-26T11:23:48 [WARN] {f482e4cf2aaf55182c89542798341c7b} 路由未配置权限标识: GET /auth/userinfo
2025-07-26T11:23:48 [INFO] {080139d02aaf55182d8954270f04a11f} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:23:48 [INFO] {080139d02aaf55182d8954270f04a11f} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:23:48 [INFO] {080139d02aaf55182d8954270f04a11f} ValidateToken - Claims: 1 access admin
2025-07-26T11:23:48 [INFO] {080139d02aaf55182d8954270f04a11f} Auth middleware - ValidateToken result: 1
2025-07-26T11:23:48 [INFO] {080139d02aaf55182d8954270f04a11f} Auth middleware - set admin_id to context: 1
2025-07-26T11:23:48 [WARN] {080139d02aaf55182d8954270f04a11f} 路由未配置权限标识: GET /auth/permissions
2025-07-26T11:23:48 [INFO] {608ea3d02aaf55182e895427b402372d} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:23:48 [INFO] {608ea3d02aaf55182e895427b402372d} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:23:48 [INFO] {608ea3d02aaf55182e895427b402372d} ValidateToken - Claims: 1 access admin
2025-07-26T11:23:48 [INFO] {608ea3d02aaf55182e895427b402372d} Auth middleware - ValidateToken result: 1
2025-07-26T11:23:48 [INFO] {608ea3d02aaf55182e895427b402372d} Auth middleware - set admin_id to context: 1
2025-07-26T11:23:48 [WARN] {608ea3d02aaf55182e895427b402372d} 路由未配置权限标识: GET /auth/menus
2025-07-26T11:23:48 [INFO] {608ea3d02aaf55182e895427b402372d} GetMenus - adminId from context: 1
2025-07-26T11:23:48 [INFO] {98902dee2aaf55182f895427f674818f} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:23:48 [INFO] {98902dee2aaf55182f895427f674818f} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:23:48 [INFO] {98902dee2aaf55182f895427f674818f} ValidateToken - Claims: 1 access admin
2025-07-26T11:23:48 [INFO] {98902dee2aaf55182f895427f674818f} Auth middleware - ValidateToken result: 1
2025-07-26T11:23:48 [INFO] {98902dee2aaf55182f895427f674818f} Auth middleware - set admin_id to context: 1
2025-07-26T11:23:48 [INFO] {98902dee2aaf55182f895427f674818f} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T11:23:48 [INFO] {98902dee2aaf55182f895427f674818f} 超级管理员访问: system:zb_order:list
2025-07-26T11:23:48 [DEBU] {98902dee2aaf55182f895427f674818f} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T11:27:06 [INFO] {d457dfe258af551830895427f005481d} Detail页面Session ID: 1n18l4h1sw7hv0dblmlgo3rq1k700xbn
2025-07-26T11:27:06 [INFO] {d457dfe258af551830895427f005481d} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T11:27:06 [INFO] {d457dfe258af551830895427f005481d} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T11:27:06 [INFO] {50a3100359af55183289542704e004cd} 用户订单统计查询成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","paid_order_count":0,"paid_total_amount":0,"total_order_count":4,"unpaid_order_count":4,"user_id":1}
2025-07-26T11:57:58 [INFO] {88b2422408b1551833895427c19b6096} Detail页面Session ID: kfuldj010222h1dblooi0a1sw8300ab3
2025-07-26T11:57:58 [INFO] {88b2422408b1551833895427c19b6096} Session中的wechat_user: {"is_nil":true,"raw_data":null}
2025-07-26T11:57:58 [INFO] {88b2422408b1551833895427c19b6096} GetCurrentWechatUser结果: {"is_nil":true,"user_data":null}
2025-07-26T11:58:24 [INFO] {8c7c3d190eb15518378954279a576d10} 用户登录: {"client_ip":"*************","login_at":"2025-07-26 11:58:24","user_id":1}
2025-07-26T11:58:24 [INFO] {8c7c3d190eb15518378954279a576d10} 用户信息已存储到Session: {"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","user_id":1}
2025-07-26T11:58:24 [INFO] {8c7c3d190eb15518378954279a576d10} Session存储验证成功
2025-07-26T11:58:24 [INFO] {8c7c3d190eb15518378954279a576d10} 准备重定向到: /m/orders
2025-07-26T11:58:24 [INFO] {c453f4480eb15518388954279c64579a} Detail页面Session ID: kfuldj010222h1dbloou02kwmg400a8e
2025-07-26T11:58:24 [INFO] {c453f4480eb15518388954279c64579a} Session中的wechat_user: {"is_nil":false,"raw_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T11:58:24 [INFO] {c453f4480eb15518388954279c64579a} GetCurrentWechatUser结果: {"is_nil":false,"user_data":{"avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLsyFc1MLCFAC8c1TdVeNxeWadcfibu9jmh7DQ6CrP7OouZamYI9okHb19hypDYyHJl0CLJRs5q16A/132","id":1,"nickname":"　　　　　　　　","openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ"}}
2025-07-26T11:58:25 [INFO] {a8c2be580eb155183a8954270320e5b0} 用户订单统计查询成功: {"openid":"o3j6evpKOCLAXkY1sHk55LIOr2gQ","paid_order_count":0,"paid_total_amount":0,"total_order_count":4,"unpaid_order_count":4,"user_id":1}
2025-07-26T11:58:38 [INFO] {a4b1687511b155183b89542705e5b7f3} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:58:38 [INFO] {a4b1687511b155183b89542705e5b7f3} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:58:38 [ERRO] {a4b1687511b155183b89542705e5b7f3} ValidateToken - JWT parse error: token has invalid claims: token is expired 
Stack:
1.  admin-server/internal/logic/sysAuth.SsysAuth.ValidateToken
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/logic/sysAuth/sys_auth.go:369
2.  admin-server/internal/middleware.Auth
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/middleware/auth.go:50
3.  admin-server/internal/cmd.init.func1.2.1
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/cmd/cmd.go:64

2025-07-26T11:58:38 [INFO] {a4b1687511b155183b89542705e5b7f3} Auth middleware - ValidateToken result: 0 令牌无效
2025-07-26T11:58:38 [INFO] {a820d27511b155183c89542790b0045e} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:58:38 [INFO] {a820d27511b155183c89542790b0045e} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:58:38 [ERRO] {a820d27511b155183c89542790b0045e} ValidateToken - JWT parse error: token has invalid claims: token is expired 
Stack:
1.  admin-server/internal/logic/sysAuth.SsysAuth.ValidateToken
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/logic/sysAuth/sys_auth.go:369
2.  admin-server/internal/middleware.Auth
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/middleware/auth.go:50
3.  admin-server/internal/cmd.init.func1.2.1
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/cmd/cmd.go:64

2025-07-26T11:58:38 [INFO] {a820d27511b155183c89542790b0045e} Auth middleware - ValidateToken result: 0 令牌无效
2025-07-26T11:58:38 [INFO] {0c3bef7511b155183d895427de0a9bc0} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************.XprRjJ0d18eqEo_vblpkpsLWrl8O78TSVJ0Xwca1ib0
2025-07-26T11:58:38 [INFO] {0c3bef7511b155183d895427de0a9bc0} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:58:38 [ERRO] {0c3bef7511b155183d895427de0a9bc0} ValidateToken - JWT parse error: token has invalid claims: token is expired 
Stack:
1.  admin-server/internal/logic/sysAuth.SsysAuth.ValidateToken
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/logic/sysAuth/sys_auth.go:369
2.  admin-server/internal/middleware.Auth
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/middleware/auth.go:50
3.  admin-server/internal/cmd.init.func1.2.1
    F:/aiProject/diaoTea/go-backend-management/admin-server/internal/cmd/cmd.go:64

2025-07-26T11:58:38 [INFO] {0c3bef7511b155183d895427de0a9bc0} Auth middleware - ValidateToken result: 0 令牌无效
2025-07-26T11:58:38 [INFO] {801d357f11b155184289542740a206c0} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbl9pZCI6MSwidXNlcm5hbWUiOiJhZG1pbiIsInR5cGUiOiJhY2Nlc3MiLCJleHAiOjE3NTM1MDk1MTgsImlhdCI6MTc1MzUwMjMxOH0.go7T_E1I59T1CllxfyUuknx52p43JlB62c6HCrjQ86o
2025-07-26T11:58:38 [INFO] {801d357f11b155184289542740a206c0} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:58:38 [INFO] {a4133e7f11b15518438954276ff89b8d} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbl9pZCI6MSwidXNlcm5hbWUiOiJhZG1pbiIsInR5cGUiOiJhY2Nlc3MiLCJleHAiOjE3NTM1MDk1MTgsImlhdCI6MTc1MzUwMjMxOH0.go7T_E1I59T1CllxfyUuknx52p43JlB62c6HCrjQ86o
2025-07-26T11:58:38 [INFO] {801d357f11b155184289542740a206c0} ValidateToken - Claims: 1 access admin
2025-07-26T11:58:38 [INFO] {a4133e7f11b15518438954276ff89b8d} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:58:38 [INFO] {a4133e7f11b15518438954276ff89b8d} ValidateToken - Claims: 1 access admin
2025-07-26T11:58:38 [INFO] {4cbe4f7f11b15518448954278bdbc8cd} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbl9pZCI6MSwidXNlcm5hbWUiOiJhZG1pbiIsInR5cGUiOiJhY2Nlc3MiLCJleHAiOjE3NTM1MDk1MTgsImlhdCI6MTc1MzUwMjMxOH0.go7T_E1I59T1CllxfyUuknx52p43JlB62c6HCrjQ86o
2025-07-26T11:58:38 [INFO] {4cbe4f7f11b15518448954278bdbc8cd} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:58:38 [INFO] {801d357f11b155184289542740a206c0} Auth middleware - ValidateToken result: 1
2025-07-26T11:58:38 [INFO] {a4133e7f11b15518438954276ff89b8d} Auth middleware - ValidateToken result: 1
2025-07-26T11:58:38 [INFO] {4cbe4f7f11b15518448954278bdbc8cd} ValidateToken - Claims: 1 access admin
2025-07-26T11:58:38 [INFO] {4cbe4f7f11b15518448954278bdbc8cd} Auth middleware - ValidateToken result: 1
2025-07-26T11:58:38 [INFO] {801d357f11b155184289542740a206c0} Auth middleware - set admin_id to context: 1
2025-07-26T11:58:38 [INFO] {a4133e7f11b15518438954276ff89b8d} Auth middleware - set admin_id to context: 1
2025-07-26T11:58:38 [INFO] {4cbe4f7f11b15518448954278bdbc8cd} Auth middleware - set admin_id to context: 1
2025-07-26T11:58:38 [WARN] {801d357f11b155184289542740a206c0} 路由未配置权限标识: GET /auth/userinfo
2025-07-26T11:58:38 [WARN] {4cbe4f7f11b15518448954278bdbc8cd} 路由未配置权限标识: GET /auth/menus
2025-07-26T11:58:38 [INFO] {4cbe4f7f11b15518448954278bdbc8cd} GetMenus - adminId from context: 1
2025-07-26T11:58:38 [WARN] {a4133e7f11b15518438954276ff89b8d} 路由未配置权限标识: GET /auth/permissions
2025-07-26T11:58:38 [INFO] {688f398511b1551845895427d4af5eed} Auth middleware - Authorization header: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbl9pZCI6MSwidXNlcm5hbWUiOiJhZG1pbiIsInR5cGUiOiJhY2Nlc3MiLCJleHAiOjE3NTM1MDk1MTgsImlhdCI6MTc1MzUwMjMxOH0.go7T_E1I59T1CllxfyUuknx52p43JlB62c6HCrjQ86o
2025-07-26T11:58:38 [INFO] {688f398511b1551845895427d4af5eed} Auth middleware - extracted token: eyJhbGciOiJIUzI1NiIs...
2025-07-26T11:58:38 [INFO] {688f398511b1551845895427d4af5eed} ValidateToken - Claims: 1 access admin
2025-07-26T11:58:38 [INFO] {688f398511b1551845895427d4af5eed} Auth middleware - ValidateToken result: 1
2025-07-26T11:58:38 [INFO] {688f398511b1551845895427d4af5eed} Auth middleware - set admin_id to context: 1
2025-07-26T11:58:38 [INFO] {688f398511b1551845895427d4af5eed} Required permission: system:zb_order:list for GET /zb_order/list
2025-07-26T11:58:38 [INFO] {688f398511b1551845895427d4af5eed} 超级管理员访问: system:zb_order:list
2025-07-26T11:58:38 [DEBU] {688f398511b1551845895427d4af5eed} 权限验证通过: adminId: 1 permission: system:zb_order:list path: /zb_order/list
2025-07-26T12:00:00 [INFO] 开始更新搜索趋势...
2025-07-26T12:00:00 [INFO] 标记热门关键词: 中关村
2025-07-26T12:00:00 [INFO] 标记热门关键词: 吉林工商学院
2025-07-26T12:00:00 [INFO] 标记热门关键词: 中国银行
2025-07-26T12:00:00 [INFO] 热门搜索趋势更新完成，共标记 3 个热门关键词
2025-07-26T12:00:00 [INFO] 搜索趋势更新完成
2025-07-26T13:00:00 [INFO] 开始更新搜索趋势...
2025-07-26T13:00:00 [INFO] 标记热门关键词: 中关村
2025-07-26T13:00:00 [INFO] 标记热门关键词: 吉林工商学院
2025-07-26T13:00:00 [INFO] 标记热门关键词: 中国银行
2025-07-26T13:00:00 [INFO] 热门搜索趋势更新完成，共标记 3 个热门关键词
2025-07-26T13:00:00 [INFO] 搜索趋势更新完成
2025-07-26T14:00:00 [INFO] 开始更新搜索趋势...
2025-07-26T14:00:00 [INFO] 标记热门关键词: 中关村
2025-07-26T14:00:00 [INFO] 标记热门关键词: 吉林工商学院
2025-07-26T14:00:00 [INFO] 标记热门关键词: 中国银行
2025-07-26T14:00:00 [INFO] 热门搜索趋势更新完成，共标记 3 个热门关键词
2025-07-26T14:00:00 [INFO] 搜索趋势更新完成
2025-07-26T15:00:00 [INFO] 开始更新搜索趋势...
2025-07-26T15:00:00 [INFO] 标记热门关键词: 中关村
2025-07-26T15:00:00 [INFO] 标记热门关键词: 吉林工商学院
2025-07-26T15:00:00 [INFO] 标记热门关键词: 中国银行
2025-07-26T15:00:00 [INFO] 热门搜索趋势更新完成，共标记 3 个热门关键词
2025-07-26T15:00:00 [INFO] 搜索趋势更新完成
2025-07-26T16:00:00 [INFO] 开始更新搜索趋势...
2025-07-26T16:00:00 [INFO] 标记热门关键词: 中关村
2025-07-26T16:00:00 [INFO] 标记热门关键词: 吉林工商学院
2025-07-26T16:00:00 [INFO] 标记热门关键词: 中国银行
2025-07-26T16:00:00 [INFO] 热门搜索趋势更新完成，共标记 3 个热门关键词
2025-07-26T16:00:00 [INFO] 搜索趋势更新完成
2025-07-26T17:00:00 [INFO] 开始更新搜索趋势...
2025-07-26T17:00:00 [INFO] 标记热门关键词: 中关村
2025-07-26T17:00:00 [INFO] 标记热门关键词: 吉林工商学院
2025-07-26T17:00:00 [INFO] 标记热门关键词: 中国银行
2025-07-26T17:00:00 [INFO] 热门搜索趋势更新完成，共标记 3 个热门关键词
2025-07-26T17:00:00 [INFO] 搜索趋势更新完成
2025-07-26T18:00:00 [INFO] 开始更新搜索趋势...
2025-07-26T18:00:00 [INFO] 标记热门关键词: 中关村
2025-07-26T18:00:00 [INFO] 标记热门关键词: 吉林工商学院
2025-07-26T18:00:00 [INFO] 标记热门关键词: 中国银行
2025-07-26T18:00:00 [INFO] 热门搜索趋势更新完成，共标记 3 个热门关键词
2025-07-26T18:00:00 [INFO] 搜索趋势更新完成
