# 系统管理员 API 测试示例

## 测试环境

- **服务器地址**: `http://localhost:8000`
- **认证方式**: <PERSON><PERSON>（需要先登录获取）

## 测试用例

### 1. 创建管理员

```bash
curl -X POST http://localhost:8000/sys_admin/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "username": "testadmin",
    "password": "123456",
    "nickname": "测试管理员",
    "is_super": 0,
    "is_disable": 0
  }'
```

**预期响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 2
  }
}
```

### 2. 获取管理员列表

```bash
curl -X GET "http://localhost:8000/sys_admin/list?page=1&page_size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**预期响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 2,
        "username": "testadmin",
        "nickname": "测试管理员",
        "is_super": 0,
        "is_disable": 0,
        "last_login_ip": "",
        "last_login_time": null,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10
  }
}
```

### 3. 获取单个管理员信息

```bash
curl -X GET http://localhost:8000/sys_admin/2 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. 更新管理员信息（不修改密码）

```bash
curl -X PUT http://localhost:8000/sys_admin/2 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "username": "testadmin",
    "nickname": "更新后的管理员",
    "is_super": 0,
    "is_disable": 0
  }'
```

### 4.1. 更新管理员信息（同时修改密码）

```bash
curl -X PUT http://localhost:8000/sys_admin/2 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "username": "testadmin",
    "password": "newpassword123",
    "nickname": "更新后的管理员",
    "is_super": 0,
    "is_disable": 0
  }'
```

### 5. 修改管理员密码

```bash
curl -X PUT http://localhost:8000/sys_admin/2/password \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "old_password": "123456",
    "new_password": "654321"
  }'
```

### 6. 禁用管理员

```bash
curl -X PUT http://localhost:8000/sys_admin/2/disable \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 7. 启用管理员

```bash
curl -X PUT http://localhost:8000/sys_admin/2/enable \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 8. 删除管理员

```bash
curl -X DELETE http://localhost:8000/sys_admin/2 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 搜索测试

### 按用户名搜索

```bash
curl -X GET "http://localhost:8000/sys_admin/list?username=test" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 按昵称搜索

```bash
curl -X GET "http://localhost:8000/sys_admin/list?nickname=管理员" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 筛选超级管理员

```bash
curl -X GET "http://localhost:8000/sys_admin/list?is_super=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 筛选禁用状态

```bash
curl -X GET "http://localhost:8000/sys_admin/list?is_disable=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 错误测试

### 1. 创建重复用户名

```bash
curl -X POST http://localhost:8000/sys_admin/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "username": "testadmin",
    "password": "123456",
    "nickname": "重复用户名测试"
  }'
```

**预期响应**:
```json
{
  "code": 1,
  "message": "账号已存在",
  "data": null
}
```

### 2. 获取不存在的管理员

```bash
curl -X GET http://localhost:8000/sys_admin/999 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**预期响应**:
```json
{
  "code": 1,
  "message": "管理员不存在",
  "data": null
}
```

### 3. 修改密码时原密码错误

```bash
curl -X PUT http://localhost:8000/sys_admin/2/password \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "old_password": "wrongpassword",
    "new_password": "654321"
  }'
```

**预期响应**:
```json
{
  "code": 1,
  "message": "原密码错误",
  "data": null
}
```

## 参数验证测试

### 1. 用户名长度验证

```bash
curl -X POST http://localhost:8000/sys_admin/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "username": "ab",
    "password": "123456",
    "nickname": "测试"
  }'
```

### 2. 密码长度验证

```bash
curl -X POST http://localhost:8000/sys_admin/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "username": "testuser",
    "password": "123",
    "nickname": "测试"
  }'
```

## 注意事项

1. 替换 `YOUR_TOKEN` 为实际的认证令牌
2. 确保服务器已启动并监听8000端口
3. 测试前请确保数据库连接正常
4. 建议在测试环境中进行测试，避免影响生产数据
