// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-server/internal/dao/internal"
)

// zbUserDao is the data access object for the table zb_user.
// You can define custom methods on it to extend its functionality as needed.
type zbUserDao struct {
	*internal.ZbUserDao
}

var (
	// ZbUser is a globally accessible object for table zb_user operations.
	ZbUser = zbUserDao{internal.NewZbUserDao()}
)

// Add your custom methods and functionality below.
