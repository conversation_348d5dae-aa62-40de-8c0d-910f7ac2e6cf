// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ZbUserDao is the data access object for the table zb_user.
type ZbUserDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  ZbUserColumns      // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// ZbUserColumns defines and stores column names for the table zb_user.
type ZbUserColumns struct {
	Id              string //
	Nickname        string // 昵称
	Avatar          string // 头像
	Openid          string // 会员微信openid
	IsDisable       string // 是否禁用: 0=否, 1=是
	IsDelete        string // 是否删除: 0=否, 1=是
	EffectiveStart  string // 有效开始日期
	EffectiveEnd    string // 有效结束日期
	CreatedAt       string // 注册时间
	UpdatedAt       string // 更新时间
	DeletedAt       string // 删除时间
	EffectiveStatus string // 是否有效 1有效 0无效  根据会员购买的套餐先计算有效开始日期和结束日期，然后在计算有效状态
}

// zbUserColumns holds the columns for the table zb_user.
var zbUserColumns = ZbUserColumns{
	Id:              "id",
	Nickname:        "nickname",
	Avatar:          "avatar",
	Openid:          "openid",
	IsDisable:       "is_disable",
	IsDelete:        "is_delete",
	EffectiveStart:  "effective_start",
	EffectiveEnd:    "effective_end",
	CreatedAt:       "created_at",
	UpdatedAt:       "updated_at",
	DeletedAt:       "deleted_at",
	EffectiveStatus: "effective_status",
}

// NewZbUserDao creates and returns a new DAO object for table data access.
func NewZbUserDao(handlers ...gdb.ModelHandler) *ZbUserDao {
	return &ZbUserDao{
		group:    "default",
		table:    "zb_user",
		columns:  zbUserColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ZbUserDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ZbUserDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ZbUserDao) Columns() ZbUserColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ZbUserDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ZbUserDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ZbUserDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
