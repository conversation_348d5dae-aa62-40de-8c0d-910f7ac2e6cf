package sys_resources

import (
	"context"

	v1 "admin-server/api/sys_resources/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error) {
	err = service.SysResources().CreateResources(
		ctx,
		req.GroupId,
		req.StorageMode,
		req.OriginName,
		req.ObjectName,
		req.Hash,
		req.Mime<PERSON>ype,
		req.StoragePath,
		req.Suffix,
		req.SizeByte,
		req.SizeInfo,
		req.Url,
		req.Remark,
	)
	if err != nil {
		return nil, err
	}

	return &v1.CreateRes{}, nil
}
