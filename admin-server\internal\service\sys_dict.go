package service

import (
	"admin-server/internal/model/entity"
	"admin-server/internal/packed"
	"context"
)

// ISysDict 字典项服务接口
type ISysDict interface {
	GetDictList(ctx context.Context, page, pageSize int, groupId int64, name, code string, isDisable int) (list []entity.SysDict, total int, err error)
	GetDictDetail(ctx context.Context, id int64) (*entity.SysDict, error)
	GetDictDetailByCode(ctx context.Context, code string) (*entity.SysDict, error)
	CreateDict(ctx context.Context, groupId int64, name, value, code, remark string, sort int, isDisable packed.Disable) error
	UpdateDict(ctx context.Context, id int64, groupId int64, name, value, code, remark string, sort int, isDisable packed.Disable) error
	DeleteDict(ctx context.Context, ids []int64) error
	SetDictStatus(ctx context.Context, id int64, isDisable int) error
	GetDictsByGroupCode(ctx context.Context, groupCode string) ([]entity.SysDict, error)
	GetDictsByGroupId(ctx context.Context, groupId int64) ([]entity.SysDict, error)
	CheckDictCodeExists(ctx context.Context, groupId int64, code string, excludeId int64) (bool, error)
	UpdateDictSort(ctx context.Context, id int64, sort int) error
}

var localSysDict ISysDict

func SysDict() ISysDict {
	if localSysDict == nil {
		panic("ISysDict接口未实现或未注册")
	}
	return localSysDict
}

func RegisterSysDict(i ISysDict) {
	localSysDict = i
}
