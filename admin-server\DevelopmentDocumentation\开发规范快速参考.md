# 开发规范快速参考

## 🚀 快速开始

### 项目启动
```bash
cd admin-server
go mod tidy
gf run main.go
```

### 生成代码
```bash
# 生成 DAO 和 Model
gf gen dao

# 生成 Controller
gf gen ctrl
```

## 📁 目录结构速查

```
api/{module}/
├── {module}.go          # 接口定义
└── v1/{module}.go       # 结构体定义

internal/
├── controller/{module}/ # 控制器实现
├── logic/{module}/      # 业务逻辑
├── service/            # 服务接口
├── dao/               # 数据访问
└── model/             # 数据模型
```

## 🏷️ 命名规范速查

| 类型 | 规范 | 示例 |
|------|------|------|
| 模块名 | 下划线分隔 | `sys_admin`, `zb_user` |
| 控制器文件 | `{module}_v1_{action}.go` | `sys_admin_v1_get_list.go` |
| API结构体 | `{Action}Req/Res` | `GetListReq`, `CreateRes` |
| 权限标识 | `{module}:{resource}:{action}` | `system:admin:list` |

## 🔧 代码模板

### API接口定义
```go
type I{Module}V1 interface {
    GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error)
    Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error)
}
```

### 请求结构体
```go
type GetListReq struct {
    g.Meta   `path:"/{module}" method:"get" tags:"{模块}管理" summary:"获取{模块}列表" perms:"{module}:list"`
    Page     int    `p:"page" d:"1" v:"min:1#页码最小为1"`
    PageSize int    `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间"`
}
```

### 控制器方法
```go
func (c *ControllerV1) GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error) {
    return service.{Module}().GetList(ctx, req)
}
```

### Logic实现
```go
func (s *s{Module}) GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error) {
    // 业务逻辑实现
    return
}
```

## 🔐 权限配置

### Meta标签权限
```go
g.Meta `path:"/sys_admin" method:"post" perms:"system:admin:create"`
```

### 常用权限标识
- `system:admin:list` - 查看管理员列表
- `system:admin:create` - 创建管理员
- `system:admin:update` - 更新管理员
- `system:admin:delete` - 删除管理员

## 📊 数据库规范

### 表命名前缀
- `sys_` - 系统表
- `zb_` - 业务表  
- `wechat_` - 微信表

### 必备字段
```sql
id bigint PRIMARY KEY AUTO_INCREMENT,
is_delete tinyint DEFAULT 0 COMMENT '是否删除: 0=否, 1=是',
created_at datetime DEFAULT CURRENT_TIMESTAMP,
updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
```

## 🌐 API规范

### RESTful路由
- `GET /{module}` - 列表
- `GET /{module}/{id}` - 详情
- `POST /{module}` - 创建
- `PUT /{module}/{id}` - 更新
- `DELETE /{module}/{id}` - 删除

### 响应格式
```json
{
    "code": 0,
    "message": "success", 
    "data": {}
}
```

## 🔍 验证规则

### 常用验证
```go
// 必填
`v:"required#请输入用户名"`

// 长度限制
`v:"max-length:50#用户名长度不能超过50个字符"`

// 数值范围
`v:"between:1,100#每页数量必须在1-100之间"`

// 枚举值
`v:"in:0,1#状态值必须为0或1"`
```

## 🚨 错误处理

### 错误返回
```go
if err != nil {
    g.Log().Error(ctx, "操作失败:", err)
    return nil, gerror.New("操作失败")
}
```

### 日志记录
```go
g.Log().Info(ctx, "操作成功")
g.Log().Warning(ctx, "警告信息")
g.Log().Error(ctx, "错误信息:", err)
```

## 📱 移动端

### 路由规范
- `/m/list` - 移动端列表页
- `/m/detail` - 移动端详情页
- `/m/api/` - 移动端API

### 微信集成
```go
// 微信授权中间件
group.Middleware(middleware.WechatAuth())

// JSSDK配置
group.POST("/get_wechat_jssdk", mobile.NewMobile().GetWechatJssdk)
```

## 🧪 测试规范

### 单元测试
```go
func TestGetList(t *testing.T) {
    // 测试实现
}
```

### API测试
```bash
# 获取列表
curl -X GET "http://localhost:8000/sys_admin?page=1&page_size=10" \
     -H "Authorization: Bearer {token}"

# 创建记录
curl -X POST "http://localhost:8000/sys_admin" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer {token}" \
     -d '{"username":"test","password":"123456"}'
```

## 🛠️ 常用命令

### 开发命令
```bash
# 启动服务
gf run main.go

# 代码生成
gf gen dao
gf gen ctrl

# 代码格式化
gofmt -w .
go vet ./...
```

### 数据库命令
```bash
# 执行SQL
mysql -u root -p diao_tea < file.sql

# 权限初始化
mysql -u root -p diao_tea < tools/resources_permission_init.sql
```

## ✅ 开发检查清单

### 代码提交前
- [ ] 代码格式化完成
- [ ] 单元测试通过
- [ ] API测试通过
- [ ] 权限配置正确
- [ ] 文档更新完成

### 功能开发完成
- [ ] 数据库表设计合理
- [ ] API接口完整
- [ ] 业务逻辑正确
- [ ] 错误处理完善
- [ ] 日志记录充分

## 📚 相关文档

- [完整开发规范文档](./开发规范文档.md)
- [API接口文档](./api/)
- [数据库设计文档](./database_design.md)
- [部署文档](./deployment.md)

---

**快速参考版本**: v1.0  
**最后更新**: 2025-01-23
