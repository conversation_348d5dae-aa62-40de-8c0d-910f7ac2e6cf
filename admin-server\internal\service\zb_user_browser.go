package service

import (
	"context"
	v1 "admin-server/api/zb_user_browser/v1"
)

// IZbUserBrowser 用户浏览记录服务接口
type IZbUserBrowser interface {
	CreateBrowserRecord(ctx context.Context, userId int64, articleId int64) error
	GetMyBrowserList(ctx context.Context, req *v1.GetMyBrowserListReq) (*v1.GetMyBrowserListRes, error)
}

var localZbUserBrowser IZbUserBrowser

func ZbUserBrowser() IZbUserBrowser {
	if localZbUserBrowser == nil {
		panic("implement not found for interface IZbUserBrowser, forgot register?")
	}
	return localZbUserBrowser
}

func RegisterZbUserBrowser(i IZbUserBrowser) {
	localZbUserBrowser = i
}
