package sys_dict

import (
	"context"

	v1 "admin-server/api/sys_dict/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error) {
	list, total, err := service.SysDict().GetDictList(
		ctx,
		req.Page,
		req.PageSize,
		req.GroupId,
		req.Name,
		req.Code,
		req.IsDisable,
	)
	if err != nil {
		return nil, err
	}

	// 转换为API响应格式
	dicts := make([]v1.DictInfo, len(list))
	for i, dict := range list {
		dicts[i] = v1.DictInfo{
			ID:        dict.Id,
			GroupId:   dict.GroupId,
			Name:      dict.Name,
			Value:     dict.Value,
			Code:      dict.Code,
			Sort:      int(dict.Sort),
			IsDisable: int(dict.IsDisable),
			IsSystem:  int(dict.IsSystem),
			Remark:    dict.Remark,
			CreatedAt: dict.CreatedAt,
			UpdatedAt: dict.UpdatedAt,
		}
	}

	return &v1.GetListRes{
		List:  dicts,
		Total: total,
	}, nil
}
