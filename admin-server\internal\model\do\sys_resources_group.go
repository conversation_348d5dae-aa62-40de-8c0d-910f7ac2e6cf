// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysResourcesGroup is the golang structure of table sys_resources_group for DAO operations like Where/Data.
type SysResourcesGroup struct {
	g.Meta    `orm:"table:sys_resources_group, do:true"`
	Id        interface{} //
	Name      interface{} // 资源名称
	Type      interface{} // 资源类型，图片:PIC；视频:VIDEO
	IsDelete  interface{} // 是否删除: 0=否, 1=是
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 更新时间
	DeletedAt *gtime.Time // 删除时间
}
