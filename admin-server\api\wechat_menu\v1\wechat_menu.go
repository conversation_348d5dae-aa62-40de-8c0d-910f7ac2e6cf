package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// WechatMenuCreateReq 创建微信菜单请求
type WechatMenuCreateReq struct {
	g.Meta   `path:"/wechat/menu" method:"post" tags:"WechatMenu" summary:"创建微信菜单" permission:"system:officialAccount:menuCreate"`
	Pid      int    `p:"pid" v:"min:0#父菜单ID不能小于0" dc:"父菜单ID，0表示一级菜单"`
	MenuName string `p:"menu_name" v:"required|length:1,255#菜单名称不能为空|菜单名称长度不能超过255个字符" dc:"菜单名称"`
	MenuType string `p:"menu_type" v:"required|in:click,view,miniprogram#菜单类型不能为空|菜单类型必须是click,view,miniprogram之一" dc:"菜单类型：click=点击推事件，view=跳转URL，miniprogram=跳转小程序"`
	MenuKey  string `p:"menu_key" v:"length:0,255#菜单KEY值长度不能超过255个字符" dc:"菜单KEY值，click类型必填"`
	MenuUrl  string `p:"menu_url" v:"length:0,255#菜单链接长度不能超过255个字符" dc:"菜单链接，view类型必填"`
	Appid    string `p:"appid" v:"length:0,255#小程序AppID长度不能超过255个字符" dc:"小程序AppID，miniprogram类型必填"`
	Pagepath string `p:"pagepath" v:"length:0,255#小程序页面路径长度不能超过255个字符" dc:"小程序页面路径，miniprogram类型必填"`
	Sort     int    `p:"sort" v:"min:0#排序值不能小于0" dc:"排序，数字越小越靠前"`
}

// WechatMenuCreateRes 创建微信菜单响应
type WechatMenuCreateRes struct {
	Id int `json:"id" dc:"菜单ID"`
}

// WechatMenuUpdateReq 更新微信菜单请求
type WechatMenuUpdateReq struct {
	g.Meta   `path:"/wechat/menu/{id}" method:"put" tags:"WechatMenu" summary:"更新微信菜单" permission:"system:officialAccount:menuEdit"`
	Id       int    `p:"id" v:"required|min:1#菜单ID不能为空|菜单ID必须大于0" dc:"菜单ID"`
	Pid      int    `p:"pid" v:"min:0#父菜单ID不能小于0" dc:"父菜单ID，0表示一级菜单"`
	MenuName string `p:"menu_name" v:"required|length:1,255#菜单名称不能为空|菜单名称长度不能超过255个字符" dc:"菜单名称"`
	MenuType string `p:"menu_type" v:"required|in:click,view,miniprogram#菜单类型不能为空|菜单类型必须是click,view,miniprogram之一" dc:"菜单类型：click=点击推事件，view=跳转URL，miniprogram=跳转小程序"`
	MenuKey  string `p:"menu_key" v:"length:0,255#菜单KEY值长度不能超过255个字符" dc:"菜单KEY值，click类型必填"`
	MenuUrl  string `p:"menu_url" v:"length:0,255#菜单链接长度不能超过255个字符" dc:"菜单链接，view类型必填"`
	Appid    string `p:"appid" v:"length:0,255#小程序AppID长度不能超过255个字符" dc:"小程序AppID，miniprogram类型必填"`
	Pagepath string `p:"pagepath" v:"length:0,255#小程序页面路径长度不能超过255个字符" dc:"小程序页面路径，miniprogram类型必填"`
	Sort     int    `p:"sort" v:"min:0#排序值不能小于0" dc:"排序，数字越小越靠前"`
}

// WechatMenuUpdateRes 更新微信菜单响应
type WechatMenuUpdateRes struct{}

// WechatMenuDeleteReq 删除微信菜单请求
type WechatMenuDeleteReq struct {
	g.Meta `path:"/wechat/menu/{id}" method:"delete" tags:"WechatMenu" summary:"删除微信菜单" permission:"system:officialAccount:menuDel"`
	Id     int `p:"id" v:"required|min:1#菜单ID不能为空|菜单ID必须大于0" dc:"菜单ID"`
}

// WechatMenuDeleteRes 删除微信菜单响应
type WechatMenuDeleteRes struct{}

// WechatMenuGetOneReq 获取单个微信菜单请求
type WechatMenuGetOneReq struct {
	g.Meta `path:"/wechat/menu/{id}" method:"get" tags:"WechatMenu" summary:"获取单个微信菜单"`
	Id     int `p:"id" v:"required|min:1#菜单ID不能为空|菜单ID必须大于0" dc:"菜单ID"`
}

// WechatMenuGetOneRes 获取单个微信菜单响应
type WechatMenuGetOneRes struct {
	*WechatMenuInfo `json:",inline"`
}

// WechatMenuGetListReq 获取微信菜单列表请求
type WechatMenuGetListReq struct {
	g.Meta    `path:"/wechat/menu/list" method:"get" tags:"WechatMenu" summary:"获取微信菜单列表" permission:"system:officialAccount:menu"`
	Page      int    `p:"page" v:"min:1#页码必须大于0" dc:"页码，默认1"`
	PageSize  int    `p:"page_size" v:"min:1|max:100#每页数量必须大于0|每页数量不能超过100" dc:"每页数量，默认10"`
	MenuName  string `p:"menu_name" dc:"菜单名称，模糊搜索"`
	MenuType  string `p:"menu_type" dc:"菜单类型筛选"`
	IsDisable *int   `p:"is_disable" v:"in:0,1#状态值必须是0或1" dc:"是否禁用：0=否，1=是"`
	Pid       *int   `p:"pid" v:"min:0#父菜单ID不能小于0" dc:"父菜单ID筛选"`
}

// WechatMenuGetListRes 获取微信菜单列表响应
type WechatMenuGetListRes struct {
	List     []*WechatMenuInfo `json:"list" dc:"菜单列表"`
	Total    int               `json:"total" dc:"总数"`
	Page     int               `json:"page" dc:"当前页码"`
	PageSize int               `json:"page_size" dc:"每页数量"`
}

// WechatMenuGetTreeReq 获取微信菜单树形结构请求
type WechatMenuGetTreeReq struct {
	g.Meta    `path:"/wechat/menu/tree" method:"get" tags:"WechatMenu" summary:"获取微信菜单树形结构" permission:"system:officialAccount:menu"`
	IsDisable *int `p:"is_disable" v:"in:0,1#状态值必须是0或1" dc:"是否禁用：0=否，1=是"`
}

// WechatMenuGetTreeRes 获取微信菜单树形结构响应
type WechatMenuGetTreeRes struct {
	List []*WechatMenuTreeInfo `json:"list" dc:"菜单树形列表"`
}

// WechatMenuUpdateSortReq 更新微信菜单排序请求
type WechatMenuUpdateSortReq struct {
	g.Meta `path:"/wechat/menu/{id}/sort" method:"put" tags:"WechatMenu" summary:"更新微信菜单排序" permission:"system:officialAccount:menuSort"`
	Id     int `p:"id" v:"required|min:1#菜单ID不能为空|菜单ID必须大于0" dc:"菜单ID"`
	Sort   int `p:"sort" v:"min:0#排序值不能小于0" dc:"排序值"`
}

// WechatMenuUpdateSortRes 更新微信菜单排序响应
type WechatMenuUpdateSortRes struct{}

// WechatMenuUpdateStatusReq 更新微信菜单状态请求
type WechatMenuUpdateStatusReq struct {
	g.Meta    `path:"/wechat/menu/{id}/status" method:"put" tags:"WechatMenu" summary:"更新微信菜单状态"`
	Id        int `p:"id" v:"required|min:1#菜单ID不能为空|菜单ID必须大于0" dc:"菜单ID"`
	IsDisable int `p:"is_disable" v:"required|in:0,1#状态不能为空|状态值必须是0或1" dc:"是否禁用：0=否，1=是"`
}

// WechatMenuUpdateStatusRes 更新微信菜单状态响应
type WechatMenuUpdateStatusRes struct{}

// WechatMenuPublishReq 发布微信菜单请求
type WechatMenuPublishReq struct {
	g.Meta `path:"/wechat/menu/publish" method:"post" tags:"WechatMenu" summary:"发布微信菜单到微信服务器" permission:"system:officialAccount:menuPublish"`
}

// WechatMenuPublishRes 发布微信菜单响应
type WechatMenuPublishRes struct {
	Success bool   `json:"success" dc:"发布是否成功"`
	Message string `json:"message" dc:"发布结果消息"`
}

// WechatMenuInfo 微信菜单信息
type WechatMenuInfo struct {
	Id        int         `json:"id" dc:"菜单ID"`
	Pid       int         `json:"pid" dc:"父菜单ID"`
	MenuName  string      `json:"menu_name" dc:"菜单名称"`
	MenuType  string      `json:"menu_type" dc:"菜单类型"`
	MenuKey   string      `json:"menu_key" dc:"菜单KEY值"`
	MenuUrl   string      `json:"menu_url" dc:"菜单链接"`
	Appid     string      `json:"appid" dc:"小程序AppID"`
	Pagepath  string      `json:"pagepath" dc:"小程序页面路径"`
	Sort      int         `json:"sort" dc:"排序"`
	Level     int         `json:"level" dc:"菜单层级"`
	IsDisable int         `json:"is_disable" dc:"是否禁用"`
	CreatedAt *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt *gtime.Time `json:"updated_at" dc:"更新时间"`
}

// WechatMenuTreeInfo 微信菜单树形信息
type WechatMenuTreeInfo struct {
	*WechatMenuInfo
	Children []*WechatMenuTreeInfo `json:"children,omitempty" dc:"子菜单"`
}
