// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-server/internal/dao/internal"
)

// sysAdminsDao is the data access object for the table sys_admins.
// You can define custom methods on it to extend its functionality as needed.
type sysAdminsDao struct {
	*internal.SysAdminsDao
}

var (
	// SysAdmins is a globally accessible object for table sys_admins operations.
	SysAdmins = sysAdminsDao{internal.NewSysAdminsDao()}
)

// Add your custom methods and functionality below.
