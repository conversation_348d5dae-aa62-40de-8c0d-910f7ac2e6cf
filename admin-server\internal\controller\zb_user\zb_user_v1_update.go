package zb_user

import (
	"context"

	v1 "admin-server/api/zb_user/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error) {
	err = service.ZbUser().UpdateUser(
		ctx,
		req.ID,
		req.Nickname,
		req.Avatar,
		req.IsDisable,
		req.EffectiveStart,
		req.EffectiveEnd,
	)
	if err != nil {
		return nil, err
	}

	return &v1.UpdateRes{}, nil
}
