package middleware

import (
	"github.com/gogf/gf/v2/net/ghttp"
)

// CORS 跨域中间件
func CORS(r *ghttp.Request) {
	r.Response.CORSDefault()
	r.Middleware.Next()
}

// CORSCustom 自定义CORS中间件
func CORSCustom(r *ghttp.Request) {
	corsOptions := r.Response.DefaultCORSOptions()
	corsOptions.AllowDomain = []string{"*"}
	corsOptions.AllowOrigin = "*"
	corsOptions.AllowCredentials = "true"
	corsOptions.AllowMethods = "GET,POST,PUT,DELETE,OPTIONS,HEAD,PATCH"
	corsOptions.AllowHeaders = "Origin,Content-Type,Accept,Authorization,X-Requested-With,X-CSRF-Token"
	corsOptions.ExposeHeaders = "Content-Length,Access-Control-Allow-Origin,Access-Control-Allow-Headers,Content-Type"
	corsOptions.MaxAge = 3600

	if r.Method == "OPTIONS" {
		r.Response.CORS(corsOptions)
		r.Response.WriteStatus(200)
		return
	}

	r.Response.CORS(corsOptions)
	r.Middleware.Next()
}
