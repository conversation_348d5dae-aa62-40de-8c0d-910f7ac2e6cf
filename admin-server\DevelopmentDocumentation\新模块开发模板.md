# 新模块开发模板

## 📋 开发步骤

### 1. 数据库设计
首先设计数据库表结构，遵循项目命名规范：

```sql
-- 示例：创建示例模块表
CREATE TABLE `example_module` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '名称',
  `description` text COMMENT '描述',
  `status` tinyint DEFAULT '1' COMMENT '状态: 0=禁用, 1=启用',
  `sort` int DEFAULT '0' COMMENT '排序',
  `is_disable` tinyint DEFAULT '0' COMMENT '是否禁用: 0=否, 1=是',
  `is_delete` tinyint DEFAULT '0' COMMENT '是否删除: 0=否, 1=是',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_is_delete` (`is_delete`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例模块表';
```

### 2. 生成基础代码
```bash
# 生成 DAO 和 Model
gf gen dao
```

### 3. 创建API接口定义

#### 文件：`api/example_module/example_module.go`
```go
package example_module

import (
	"context"
	v1 "admin-server/api/example_module/v1"
)

type IExampleModuleV1 interface {
	GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error)
	GetOne(ctx context.Context, req *v1.GetOneReq) (res *v1.GetOneRes, err error)
	Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error)
	Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error)
	Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error)
	SetStatus(ctx context.Context, req *v1.SetStatusReq) (res *v1.SetStatusRes, err error)
}
```

#### 文件：`api/example_module/v1/example_module.go`
```go
package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ExampleModuleInfo 示例模块信息
type ExampleModuleInfo struct {
	Id          int64       `json:"id" dc:"ID"`
	Name        string      `json:"name" dc:"名称"`
	Description string      `json:"description" dc:"描述"`
	Status      int         `json:"status" dc:"状态"`
	Sort        int         `json:"sort" dc:"排序"`
	IsDisable   int         `json:"is_disable" dc:"是否禁用"`
	CreatedAt   *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt   *gtime.Time `json:"updated_at" dc:"更新时间"`
}

// GetListReq 获取列表请求
type GetListReq struct {
	g.Meta   `path:"/example_module" method:"get" tags:"示例模块管理" summary:"获取示例模块列表" dc:"获取示例模块列表" perms:"system:example:list"`
	Page     int    `p:"page" d:"1" v:"min:1#页码最小为1"`
	PageSize int    `p:"page_size" d:"10" v:"between:1,100#每页数量必须在1-100之间"`
	Name     string `p:"name" d:"" dc:"名称"`
	Status   int    `p:"status" d:"-1" v:"in:-1,0,1#状态值错误" dc:"状态: -1=全部, 0=禁用, 1=启用"`
}

type GetListRes struct {
	List  []ExampleModuleInfo `json:"list" dc:"示例模块列表"`
	Total int                 `json:"total" dc:"总数"`
}

// GetOneReq 获取详情请求
type GetOneReq struct {
	g.Meta `path:"/example_module/{id}" method:"get" tags:"示例模块管理" summary:"获取示例模块详情" dc:"获取示例模块详情" perms:"system:example:detail"`
	Id     int64 `p:"id" v:"required|min:1#请选择记录|ID必须大于0"`
}

type GetOneRes struct {
	Info ExampleModuleInfo `json:"info" dc:"示例模块信息"`
}

// CreateReq 创建请求
type CreateReq struct {
	g.Meta      `path:"/example_module" method:"post" tags:"示例模块管理" summary:"创建示例模块" dc:"创建示例模块" perms:"system:example:create"`
	Name        string `p:"name" v:"required|max-length:100#请输入名称|名称长度不能超过100个字符"`
	Description string `p:"description" v:"max-length:500#描述长度不能超过500个字符"`
	Status      int    `p:"status" d:"1" v:"in:0,1#状态值必须为0或1"`
	Sort        int    `p:"sort" d:"0" v:"min:0#排序值不能小于0"`
}

type CreateRes struct {
	Id int64 `json:"id" dc:"创建的记录ID"`
}

// UpdateReq 更新请求
type UpdateReq struct {
	g.Meta      `path:"/example_module/{id}" method:"put" tags:"示例模块管理" summary:"更新示例模块" dc:"更新示例模块" perms:"system:example:update"`
	Id          int64  `p:"id" v:"required|min:1#请选择记录|ID必须大于0"`
	Name        string `p:"name" v:"required|max-length:100#请输入名称|名称长度不能超过100个字符"`
	Description string `p:"description" v:"max-length:500#描述长度不能超过500个字符"`
	Status      int    `p:"status" v:"in:0,1#状态值必须为0或1"`
	Sort        int    `p:"sort" v:"min:0#排序值不能小于0"`
}

type UpdateRes struct{}

// DeleteReq 删除请求
type DeleteReq struct {
	g.Meta `path:"/example_module/{id}" method:"delete" tags:"示例模块管理" summary:"删除示例模块" dc:"删除示例模块" perms:"system:example:delete"`
	Id     int64 `p:"id" v:"required|min:1#请选择记录|ID必须大于0"`
}

type DeleteRes struct{}

// SetStatusReq 设置状态请求
type SetStatusReq struct {
	g.Meta `path:"/example_module/{id}/status" method:"put" tags:"示例模块管理" summary:"设置示例模块状态" dc:"设置示例模块状态" perms:"system:example:status"`
	Id     int64 `p:"id" v:"required|min:1#请选择记录|ID必须大于0"`
	Status int   `p:"status" v:"required|in:0,1#请选择状态|状态值必须为0或1"`
}

type SetStatusRes struct{}
```

### 4. 创建Service接口

#### 文件：`internal/service/example_module.go`
```go
package service

import (
	"context"
	v1 "admin-server/api/example_module/v1"
)

type IExampleModule interface {
	GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error)
	GetOne(ctx context.Context, req *v1.GetOneReq) (res *v1.GetOneRes, err error)
	Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error)
	Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error)
	Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error)
	SetStatus(ctx context.Context, req *v1.SetStatusReq) (res *v1.SetStatusRes, err error)
}

var localExampleModule IExampleModule

func ExampleModule() IExampleModule {
	if localExampleModule == nil {
		panic("implement not found for interface IExampleModule, forgot register?")
	}
	return localExampleModule
}

func RegisterExampleModule(i IExampleModule) {
	localExampleModule = i
}
```

### 5. 实现Logic业务逻辑

#### 文件：`internal/logic/exampleModule/example_module.go`
```go
package exampleModule

import (
	"context"
	"admin-server/internal/dao"
	"admin-server/internal/model/do"
	"admin-server/internal/model/entity"
	"admin-server/internal/packed"
	"admin-server/internal/service"
	v1 "admin-server/api/example_module/v1"
	
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
)

type sExampleModule struct{}

func New() *sExampleModule {
	return &sExampleModule{}
}

func init() {
	service.RegisterExampleModule(New())
}

// GetList 获取列表
func (s *sExampleModule) GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error) {
	// 构建查询条件
	query := dao.ExampleModule.Ctx(ctx).Where("is_delete", packed.NO_DELETE)
	
	// 名称搜索
	if req.Name != "" {
		query = query.WhereLike("name", "%"+req.Name+"%")
	}
	
	// 状态筛选
	if req.Status >= 0 {
		query = query.Where("status", req.Status)
	}
	
	// 获取总数
	total, err := query.Count()
	if err != nil {
		g.Log().Error(ctx, "获取示例模块总数失败:", err)
		return nil, gerror.New("获取数据失败")
	}
	
	// 分页查询
	var list []entity.ExampleModule
	err = query.Order("sort ASC, id DESC").
		Limit((req.Page-1)*req.PageSize, req.PageSize).
		Scan(&list)
	if err != nil {
		g.Log().Error(ctx, "获取示例模块列表失败:", err)
		return nil, gerror.New("获取数据失败")
	}
	
	// 转换数据格式
	var infoList []v1.ExampleModuleInfo
	err = gconv.Scan(list, &infoList)
	if err != nil {
		g.Log().Error(ctx, "数据转换失败:", err)
		return nil, gerror.New("数据处理失败")
	}
	
	return &v1.GetListRes{
		List:  infoList,
		Total: total,
	}, nil
}

// GetOne 获取详情
func (s *sExampleModule) GetOne(ctx context.Context, req *v1.GetOneReq) (res *v1.GetOneRes, err error) {
	var info entity.ExampleModule
	err = dao.ExampleModule.Ctx(ctx).
		Where("id", req.Id).
		Where("is_delete", packed.NO_DELETE).
		Scan(&info)
	if err != nil {
		g.Log().Error(ctx, "获取示例模块详情失败:", err)
		return nil, gerror.New("获取数据失败")
	}
	
	if info.Id == 0 {
		return nil, gerror.New("记录不存在")
	}
	
	var moduleInfo v1.ExampleModuleInfo
	err = gconv.Scan(info, &moduleInfo)
	if err != nil {
		g.Log().Error(ctx, "数据转换失败:", err)
		return nil, gerror.New("数据处理失败")
	}
	
	return &v1.GetOneRes{Info: moduleInfo}, nil
}

// Create 创建
func (s *sExampleModule) Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error) {
	// 检查名称是否重复
	count, err := dao.ExampleModule.Ctx(ctx).
		Where("name", req.Name).
		Where("is_delete", packed.NO_DELETE).
		Count()
	if err != nil {
		g.Log().Error(ctx, "检查名称重复失败:", err)
		return nil, gerror.New("操作失败")
	}
	if count > 0 {
		return nil, gerror.New("名称已存在")
	}
	
	// 插入数据
	result, err := dao.ExampleModule.Ctx(ctx).Data(do.ExampleModule{
		Name:        req.Name,
		Description: req.Description,
		Status:      req.Status,
		Sort:        req.Sort,
	}).Insert()
	if err != nil {
		g.Log().Error(ctx, "创建示例模块失败:", err)
		return nil, gerror.New("创建失败")
	}
	
	id, err := result.LastInsertId()
	if err != nil {
		g.Log().Error(ctx, "获取插入ID失败:", err)
		return nil, gerror.New("创建失败")
	}
	
	return &v1.CreateRes{Id: id}, nil
}

// Update 更新
func (s *sExampleModule) Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error) {
	// 检查记录是否存在
	count, err := dao.ExampleModule.Ctx(ctx).
		Where("id", req.Id).
		Where("is_delete", packed.NO_DELETE).
		Count()
	if err != nil {
		g.Log().Error(ctx, "检查记录存在失败:", err)
		return nil, gerror.New("操作失败")
	}
	if count == 0 {
		return nil, gerror.New("记录不存在")
	}
	
	// 检查名称是否重复（排除自己）
	count, err = dao.ExampleModule.Ctx(ctx).
		Where("name", req.Name).
		Where("id !=", req.Id).
		Where("is_delete", packed.NO_DELETE).
		Count()
	if err != nil {
		g.Log().Error(ctx, "检查名称重复失败:", err)
		return nil, gerror.New("操作失败")
	}
	if count > 0 {
		return nil, gerror.New("名称已存在")
	}
	
	// 更新数据
	_, err = dao.ExampleModule.Ctx(ctx).
		Where("id", req.Id).
		Data(do.ExampleModule{
			Name:        req.Name,
			Description: req.Description,
			Status:      req.Status,
			Sort:        req.Sort,
		}).Update()
	if err != nil {
		g.Log().Error(ctx, "更新示例模块失败:", err)
		return nil, gerror.New("更新失败")
	}
	
	return &v1.UpdateRes{}, nil
}

// Delete 删除
func (s *sExampleModule) Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error) {
	// 检查记录是否存在
	count, err := dao.ExampleModule.Ctx(ctx).
		Where("id", req.Id).
		Where("is_delete", packed.NO_DELETE).
		Count()
	if err != nil {
		g.Log().Error(ctx, "检查记录存在失败:", err)
		return nil, gerror.New("操作失败")
	}
	if count == 0 {
		return nil, gerror.New("记录不存在")
	}
	
	// 软删除
	_, err = dao.ExampleModule.Ctx(ctx).
		Where("id", req.Id).
		Data(do.ExampleModule{
			IsDelete: packed.IS_DELETE,
		}).Update()
	if err != nil {
		g.Log().Error(ctx, "删除示例模块失败:", err)
		return nil, gerror.New("删除失败")
	}
	
	return &v1.DeleteRes{}, nil
}

// SetStatus 设置状态
func (s *sExampleModule) SetStatus(ctx context.Context, req *v1.SetStatusReq) (res *v1.SetStatusRes, err error) {
	// 检查记录是否存在
	count, err := dao.ExampleModule.Ctx(ctx).
		Where("id", req.Id).
		Where("is_delete", packed.NO_DELETE).
		Count()
	if err != nil {
		g.Log().Error(ctx, "检查记录存在失败:", err)
		return nil, gerror.New("操作失败")
	}
	if count == 0 {
		return nil, gerror.New("记录不存在")
	}
	
	// 更新状态
	_, err = dao.ExampleModule.Ctx(ctx).
		Where("id", req.Id).
		Data(do.ExampleModule{
			Status: req.Status,
		}).Update()
	if err != nil {
		g.Log().Error(ctx, "设置状态失败:", err)
		return nil, gerror.New("设置状态失败")
	}
	
	return &v1.SetStatusRes{}, nil
}
```

### 6. 生成Controller
```bash
gf gen ctrl
```

### 7. 注册路由
在 `internal/cmd/cmd.go` 中添加路由注册：

```go
// 在需要认证的接口组中添加
group.Bind(
    // ... 其他控制器
    example_module.NewV1(),
)
```

### 8. 更新Logic注册
在 `internal/logic/logic.go` 中添加：

```go
import (
    // ... 其他导入
    _ "admin-server/internal/logic/exampleModule"
)
```

### 9. 权限初始化SQL
创建权限初始化SQL文件：

```sql
-- 示例模块权限初始化
INSERT INTO `sys_menu` (`pid`, `menu_type`, `menu_name`, `menu_icon`, `menu_sort`, `perms`, `paths`, `component`, `is_cache`, `is_show`, `is_disable`, `is_delete`) VALUES
(0, 'M', '示例模块管理', 'example', 100, '', '/example', '', 0, 1, 0, 0),
(LAST_INSERT_ID(), 'A', '查看示例模块', '', 1, 'system:example:list', '', '', 0, 1, 0, 0),
(LAST_INSERT_ID()-1, 'A', '查看示例模块详情', '', 2, 'system:example:detail', '', '', 0, 1, 0, 0),
(LAST_INSERT_ID()-2, 'A', '创建示例模块', '', 3, 'system:example:create', '', '', 0, 1, 0, 0),
(LAST_INSERT_ID()-3, 'A', '更新示例模块', '', 4, 'system:example:update', '', '', 0, 1, 0, 0),
(LAST_INSERT_ID()-4, 'A', '删除示例模块', '', 5, 'system:example:delete', '', '', 0, 1, 0, 0),
(LAST_INSERT_ID()-5, 'A', '设置示例模块状态', '', 6, 'system:example:status', '', '', 0, 1, 0, 0);
```

### 10. 测试验证
```bash
# 启动服务
gf run main.go

# 测试API
curl -X GET "http://localhost:8000/example_module?page=1&page_size=10" \
     -H "Authorization: Bearer {token}"
```

## ✅ 完成检查清单

- [ ] 数据库表创建完成
- [ ] DAO和Model生成完成
- [ ] API接口定义完成
- [ ] Service接口定义完成
- [ ] Logic业务逻辑实现完成
- [ ] Controller生成完成
- [ ] 路由注册完成
- [ ] Logic注册完成
- [ ] 权限初始化完成
- [ ] API测试通过
- [ ] 文档更新完成

---

按照此模板开发新模块，可以确保代码结构统一，功能完整。
