// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// WechatMenu is the golang structure for table wechat_menu.
type WechatMenu struct {
	Id        int         `json:"id"        orm:"id"         description:""`                     //
	Pid       int         `json:"pid"       orm:"pid"        description:"父菜单ID，0表示一级菜单"`       // 父菜单ID，0表示一级菜单
	MenuName  string      `json:"menuName"  orm:"menu_name"  description:"菜单名称"`                 // 菜单名称
	MenuType  string      `json:"menuType"  orm:"menu_type"  description:"菜单类型"`                 // 菜单类型
	MenuKey   string      `json:"menuKey"   orm:"menu_key"   description:"菜单KEY值"`               // 菜单KEY值
	MenuUrl   string      `json:"menuUrl"   orm:"menu_url"   description:"菜单链接"`                 // 菜单链接
	Appid     string      `json:"appid"     orm:"appid"      description:"小程序AppID"`             // 小程序AppID
	Pagepath  string      `json:"pagepath"  orm:"pagepath"   description:"小程序页面路径"`              // 小程序页面路径
	Sort      int         `json:"sort"      orm:"sort"       description:"排序"`                   // 排序
	Level     int         `json:"level"     orm:"level"      description:"菜单层级：1=一级菜单，2=二级菜单"`   // 菜单层级：1=一级菜单，2=二级菜单
	IsDisable int         `json:"isDisable" orm:"is_disable" description:"是否禁用: 0=否, 1=是"`       // 是否禁用: 0=否, 1=是
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`                 // 创建时间
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:"更新时间"`                 // 更新时间
}
