// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SysDictDao is the data access object for the table sys_dict.
type SysDictDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  SysDictColumns     // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// SysDictColumns defines and stores column names for the table sys_dict.
type SysDictColumns struct {
	Id        string //
	GroupId   string // 字典分组id
	Name      string // 字典名称
	Value     string // 字典值
	Code      string // 字典标识
	Sort      string // 排序
	IsDisable string // 是否禁用: 0=否, 1=是
	IsDelete  string // 是否删除: 0=否, 1=是
	IsSystem  string // 是否系统保留 1是 0否
	Remark    string // 备注
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
	DeletedAt string // 删除时间
}

// sysDictColumns holds the columns for the table sys_dict.
var sysDictColumns = SysDictColumns{
	Id:        "id",
	GroupId:   "group_id",
	Name:      "name",
	Value:     "value",
	Code:      "code",
	Sort:      "sort",
	IsDisable: "is_disable",
	IsDelete:  "is_delete",
	IsSystem:  "is_system",
	Remark:    "remark",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
	DeletedAt: "deleted_at",
}

// NewSysDictDao creates and returns a new DAO object for table data access.
func NewSysDictDao(handlers ...gdb.ModelHandler) *SysDictDao {
	return &SysDictDao{
		group:    "default",
		table:    "sys_dict",
		columns:  sysDictColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SysDictDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SysDictDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SysDictDao) Columns() SysDictColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SysDictDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SysDictDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SysDictDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
