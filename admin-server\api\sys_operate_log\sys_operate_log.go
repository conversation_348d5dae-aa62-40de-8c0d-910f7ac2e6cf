package sys_operate_log

import (
	v1 "admin-server/api/sys_operate_log/v1"
	"context"
)

type ISysOperateLogV1 interface {
	GetList(ctx context.Context, req *v1.GetListReq) (res *v1.GetListRes, err error)
	GetOne(ctx context.Context, req *v1.GetOneReq) (res *v1.GetOneRes, err error)
	Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error)
	Clear(ctx context.Context, req *v1.ClearReq) (res *v1.ClearRes, err error)
}
