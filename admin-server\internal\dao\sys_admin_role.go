// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-server/internal/dao/internal"
)

// sysAdminRoleDao is the data access object for the table sys_admin_role.
// You can define custom methods on it to extend its functionality as needed.
type sysAdminRoleDao struct {
	*internal.SysAdminRoleDao
}

var (
	// SysAdminRole is a globally accessible object for table sys_admin_role operations.
	SysAdminRole = sysAdminRoleDao{internal.NewSysAdminRoleDao()}
)

// Add your custom methods and functionality below.
