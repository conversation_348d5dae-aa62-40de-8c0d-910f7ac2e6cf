// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-server/internal/dao/internal"
)

// sysOperateLogDao is the data access object for the table sys_operate_log.
// You can define custom methods on it to extend its functionality as needed.
type sysOperateLogDao struct {
	*internal.SysOperateLogDao
}

var (
	// SysOperateLog is a globally accessible object for table sys_operate_log operations.
	SysOperateLog = sysOperateLogDao{internal.NewSysOperateLogDao()}
)

// Add your custom methods and functionality below.
