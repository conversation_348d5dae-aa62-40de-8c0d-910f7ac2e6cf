// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-server/internal/dao/internal"
)

// wechatConfigDao is the data access object for the table wechat_config.
// You can define custom methods on it to extend its functionality as needed.
type wechatConfigDao struct {
	*internal.WechatConfigDao
}

var (
	// WechatConfig is a globally accessible object for table wechat_config operations.
	WechatConfig = wechatConfigDao{internal.NewWechatConfigDao()}
)

// Add your custom methods and functionality below.
