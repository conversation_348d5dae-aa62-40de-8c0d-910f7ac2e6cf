<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>开通会员</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="http://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            -webkit-overflow-scrolling: touch;
        }
        .phone-container {
            min-height: 100vh;
            position: relative;
            background: white;
            -webkit-overflow-scrolling: touch;
        }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .gold-gradient { background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .mini-capsule {
            position: absolute;
            top: 8px;
            right: 12px;
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .price-card { transition: all 0.3s ease; }
        .price-card:hover { transform: translateY(-2px); }
        .price-card.selected { border-color: #8b5cf6; box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2); }

        /* 城市选择样式 - 更紧凑 */
        .city-option {
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
            padding: 8px 6px !important;
            font-size: 12px !important;
            min-height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        .city-option:hover {
            transform: translateY(-1px);
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
        }
        .city-option.selected {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            border-color: #8b5cf6;
            color: white;
            font-weight: 600;
        }
        .city-option.selected::after {
            content: '✓';
            position: absolute;
            top: 2px;
            right: 4px;
            font-size: 10px;
            font-weight: bold;
        }
        .city-option:active {
            transform: scale(0.98);
        }

        /* 加载动画 */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .animate-spin {
            animation: spin 1s linear infinite;
        }

        /* 登录弹窗样式 */
        .vip-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .vip-modal-content {
            background: white;
            border-radius: 20px;
            padding: 30px 20px;
            margin: 20px;
            text-align: center;
            max-width: 320px;
            width: 100%;
            animation: modalSlideIn 0.3s ease-out;
        }
        @keyframes modalSlideIn {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
    </style>
</head>
<body>
<div class="phone-container">
    <!-- 顶部区域 -->
    <div class="gradient-bg px-4 pt-4 pb-4">
        <!-- 会员特权展示 -->
        <div class="text-center text-white">
            <div class="w-20 h-20 gold-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-crown text-white text-2xl"></i>
            </div>
            <h2 class="text-xl font-bold mb-2">招标信息VIP会员</h2>
            <p class="text-white/80 text-sm">查看完整版招标信息，获取更多商机</p>
        </div>
    </div>

    <!-- 内容区域 -->
    <div class="bg-white flex-1 overflow-y-auto">
        <!-- 会员权益 -->
        <div class="px-4 py-6">
            <!-- 城市选择 -->
            <div class="mb-4">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-base font-bold text-gray-800">选择城市</h3>
                    <div class="flex space-x-2">
                        <button id="selectAllBtn" onclick="selectAllCities()" class="bg-purple-100 text-purple-600 px-3 py-1 rounded-md text-xs font-medium hover:bg-purple-200 transition-colors" style="display: none;">
                            全选
                        </button>
                        <button id="clearAllBtn" onclick="clearAllSelections()" class="bg-gray-100 text-gray-600 px-3 py-1 rounded-md text-xs font-medium hover:bg-gray-200 transition-colors" style="display: none;">
                            清空
                        </button>
                    </div>
                </div>

                <!-- 加载状态 -->
                <div id="cityLoading" class="text-center py-4">
                    <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-purple-500"></div>
                    <p class="text-xs text-gray-600 mt-1">加载中...</p>
                </div>

                <!-- 城市网格 - 更紧凑的布局 -->
                <div id="cityGrid" class="grid grid-cols-4 gap-2 mb-3" style="display: none;">
                    <!-- 动态生成的城市按钮将插入到这里 -->
                </div>

                <!-- 已选择城市提示 - 更简洁 -->
                <div id="selectedCitiesInfo" class="bg-blue-50 border border-blue-200 p-2 rounded text-xs" style="display: none;">
                    <span class="text-blue-700">
                        <i class="fas fa-map-marker-alt mr-1"></i>
                        已选：<span id="selectedCitiesText" class="font-medium"></span>
                    </span>
                </div>

                <!-- 错误状态 -->
                <div id="cityError" class="text-center py-4" style="display: none;">
                    <i class="fas fa-exclamation-triangle text-red-500 text-lg mb-1"></i>
                    <p class="text-xs text-gray-600 mb-2">加载失败</p>
                    <button onclick="loadCities()" class="bg-purple-500 text-white px-3 py-1 rounded text-xs">
                        重试
                    </button>
                </div>
            </div>

            <!-- 套餐选择 -->
            <div class="mb-6">
                <h3 class="text-base font-bold text-gray-800 mb-3">选择套餐</h3>

                <!-- 套餐加载状态 -->
                <div id="packageLoading" class="text-center py-6">
                    <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-purple-500"></div>
                    <p class="text-xs text-gray-600 mt-1">正在加载套餐...</p>
                </div>

                <!-- 套餐列表 -->
                <div id="packageList" class="space-y-3" style="display: none;">
                    <!-- 动态生成的套餐卡片将插入到这里 -->
                </div>

                <!-- 套餐错误状态 -->
                <div id="packageError" class="text-center py-6" style="display: none;">
                    <i class="fas fa-exclamation-triangle text-red-500 text-lg mb-1"></i>
                    <p class="text-xs text-gray-600 mb-2">套餐加载失败</p>
                    <button onclick="loadPackages()" class="bg-purple-500 text-white px-3 py-1 rounded text-xs">
                        重新加载
                    </button>
                </div>
            </div>

        </div>
    </div>

    {{if .is_logged_in}}
        <!-- 底部支付按钮 -->
        <div class="bg-white border-t border-gray-200 px-4 py-4">
            <div class="flex items-center justify-between mb-3">
                <span class="text-sm text-gray-600">应付金额</span>
                <span id="totalPrice" class="text-lg font-bold text-purple-600">¥0</span>
            </div>
            <button class="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-3 rounded-lg text-sm font-medium flex items-center justify-center space-x-2" onclick="handlePayment()">
                <i class="fab fa-weixin text-lg"></i>
                <span>立即支付</span>
            </button>
            <p class="text-xs text-gray-500 text-center mt-2">开通即表示同意《会员服务协议》</p>
        </div>
    {{end}}
</div>


{{if not .is_logged_in}}
    <!-- 登录弹窗 -->
    <div id="loginModal" class="vip-modal">
        <div class="vip-modal-content">
            <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-user text-white text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-3">请先登录</h3>
            <p class="text-gray-600 text-sm mb-6 leading-relaxed">
                登录后即可开通会员套餐<br>
                享受更多专业服务
            </p>

            <!-- 登录优势 -->
            <div class="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6">
                <div class="space-y-2">
                    <div class="flex items-center text-sm text-gray-700">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>专享会员特权</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-700">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>多城市招标信息</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-700">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        <span>个性化推荐服务</span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <button class="w-full bg-gradient-to-r from-purple-500 to-blue-600 text-white py-3 rounded-lg text-sm font-medium mb-3" onclick="goToLogin()">
                立即登录
            </button>
            <p class="text-xs text-gray-500">登录即可享受更多服务</p>
        </div>
    </div>
{{end}}

<script>
// 全局变量
let selectedCities = []; // 存储选中的城市
let allCities = []; // 存储所有城市数据
let selectedPackage = null; // 存储选中的套餐
let allPackages = []; // 存储所有套餐数据
var userInfo = {{json .user.openid}}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    loadCities();
    loadPackages();
    // 加载微信JSSDK配置
    loadJssdkConfig();

    // 调试用户信息
    debugUserInfo();

    console.log('页面加载完成');
});

// 加载城市列表
async function loadCities() {
    const loading = document.getElementById('cityLoading');
    const grid = document.getElementById('cityGrid');
    const error = document.getElementById('cityError');
    const info = document.getElementById('selectedCitiesInfo');

    // 显示加载状态
    loading.style.display = 'block';
    grid.style.display = 'none';
    error.style.display = 'none';
    info.style.display = 'none';

    try {
        const response = await fetch('/m/api/zb_city/tree');
        const result = await response.json();

        if (result.code === 0 && result.data && result.data.list) {
            allCities = result.data.list;
            renderCities(allCities);

            // 隐藏加载状态，显示城市网格
            loading.style.display = 'none';
            grid.style.display = 'grid';

            // 初始化价格显示
            updateTotalPrice();

            console.log('城市列表加载成功:', allCities);
        } else {
            throw new Error(result.message || '数据格式错误');
        }
    } catch (error) {
        console.error('加载城市列表失败:', error);

        // 显示错误状态
        loading.style.display = 'none';
        document.getElementById('cityError').style.display = 'block';
    }
}

// 渲染城市列表
function renderCities(cities) {
    const grid = document.getElementById('cityGrid');
    const selectAllBtn = document.getElementById('selectAllBtn');
    const clearAllBtn = document.getElementById('clearAllBtn');

    grid.innerHTML = '';

    cities.forEach(city => {
        const button = document.createElement('button');
        button.className = 'city-option bg-white border border-gray-200 text-gray-600 py-2 rounded text-xs font-medium';
        button.textContent = city.name;
        button.dataset.cityId = city.id;
        button.dataset.cityName = city.name;

        // 添加点击事件
        button.addEventListener('click', function() {
            toggleCitySelection(this);
        });

        grid.appendChild(button);
    });

    // 显示操作按钮
    selectAllBtn.style.display = 'inline-block';
    clearAllBtn.style.display = 'inline-block';
}

// 切换城市选择状态
function toggleCitySelection(button) {
    const cityId = parseInt(button.dataset.cityId);
    const cityName = button.dataset.cityName;

    if (button.classList.contains('selected')) {
        // 取消选择
        button.classList.remove('selected');
        button.classList.add('bg-white', 'border-gray-200', 'text-gray-600');
        button.classList.remove('bg-purple-500', 'border-purple-500', 'text-white');

        // 从选中列表中移除
        selectedCities = selectedCities.filter(city => city.id !== cityId);
    } else {
        // 选择城市
        button.classList.add('selected');
        button.classList.remove('bg-white', 'border-gray-200', 'text-gray-600');
        button.classList.add('bg-purple-500', 'border-purple-500', 'text-white');

        // 添加到选中列表
        selectedCities.push({ id: cityId, name: cityName });
    }

    // 更新选中城市显示和按钮状态
    updateSelectedCitiesDisplay();
    updateButtonStates();

    // 更新总价格
    updateTotalPrice();

    console.log('当前选中的城市:', selectedCities);
}

// 更新选中城市的显示
function updateSelectedCitiesDisplay() {
    const info = document.getElementById('selectedCitiesInfo');
    const text = document.getElementById('selectedCitiesText');

    if (selectedCities.length === 0) {
        info.style.display = 'none';
    } else {
        let displayText;
        if (selectedCities.length <= 3) {
            // 3个或以下直接显示名称
            displayText = selectedCities.map(city => city.name).join('、');
        } else {
            // 超过3个显示前2个+数量
            const firstTwo = selectedCities.slice(0, 2).map(city => city.name).join('、');
            displayText = `${firstTwo} 等${selectedCities.length}个城市`;
        }
        text.textContent = displayText;
        info.style.display = 'block';
    }
}

// 获取选中的城市（供其他功能使用）
function getSelectedCities() {
    return selectedCities;
}

// 获取选中城市的ID数组
function getSelectedCityIds() {
    return selectedCities.map(city => city.id);
}

// 获取选中城市的名称数组
function getSelectedCityNames() {
    return selectedCities.map(city => city.name);
}

// 全选城市
function selectAllCities() {
    selectedCities = [];
    const buttons = document.querySelectorAll('.city-option');

    buttons.forEach(button => {
        const cityId = parseInt(button.dataset.cityId);
        const cityName = button.dataset.cityName;

        // 设置为选中状态
        button.classList.add('selected');
        button.classList.remove('bg-white', 'border-gray-200', 'text-gray-600');
        button.classList.add('bg-purple-500', 'border-purple-500', 'text-white');

        // 添加到选中列表
        selectedCities.push({ id: cityId, name: cityName });
    });

    updateSelectedCitiesDisplay();
    updateButtonStates();
    updateTotalPrice(); // 更新总价格
    console.log('已全选所有城市:', selectedCities);
}

// 清空所有选择
function clearAllSelections() {
    selectedCities = [];
    const buttons = document.querySelectorAll('.city-option');
    buttons.forEach(button => {
        button.classList.remove('selected');
        button.classList.add('bg-white', 'border-gray-200', 'text-gray-600');
        button.classList.remove('bg-purple-500', 'border-purple-500', 'text-white');
    });
    updateSelectedCitiesDisplay();
    updateButtonStates();
    updateTotalPrice(); // 更新总价格
    console.log('已清空所有选择');
}

// 更新按钮状态
function updateButtonStates() {
    const selectAllBtn = document.getElementById('selectAllBtn');
    const clearAllBtn = document.getElementById('clearAllBtn');
    const totalCities = allCities.length;
    const selectedCount = selectedCities.length;

    if (selectedCount === 0) {
        // 没有选择任何城市
        selectAllBtn.style.display = 'inline-block';
        clearAllBtn.style.display = 'none';
        selectAllBtn.textContent = '全选';
    } else if (selectedCount === totalCities) {
        // 已全选
        selectAllBtn.style.display = 'none';
        clearAllBtn.style.display = 'inline-block';
    } else {
        // 部分选择
        selectAllBtn.style.display = 'inline-block';
        clearAllBtn.style.display = 'inline-block';
        selectAllBtn.textContent = `全选(${totalCities - selectedCount})`;
    }
}

// 设置默认选中的城市（可选功能）
function setDefaultSelectedCities(cityIds) {
    cityIds.forEach(cityId => {
        const button = document.querySelector(`[data-city-id="${cityId}"]`);
        if (button) {
            toggleCitySelection(button);
        }
    });
}

// ==================== 套餐相关功能 ====================

// 加载套餐列表
async function loadPackages() {
    const loading = document.getElementById('packageLoading');
    const list = document.getElementById('packageList');
    const error = document.getElementById('packageError');

    // 显示加载状态
    loading.style.display = 'block';
    list.style.display = 'none';
    error.style.display = 'none';

    try {
        const response = await fetch('/m/api/zb_good/all');
        const result = await response.json();

        if (result.code === 0 && result.data && result.data.list) {
            allPackages = result.data.list;
            renderPackages(allPackages);

            // 隐藏加载状态，显示套餐列表
            loading.style.display = 'none';
            list.style.display = 'block';

            console.log('套餐列表加载成功:', allPackages);
        } else {
            throw new Error(result.message || '数据格式错误');
        }
    } catch (error) {
        console.error('加载套餐列表失败:', error);

        // 显示错误状态
        loading.style.display = 'none';
        document.getElementById('packageError').style.display = 'block';
    }
}

// 渲染套餐列表
function renderPackages(packages) {
    const list = document.getElementById('packageList');
    list.innerHTML = '';

    // 找出折扣最大的套餐（折扣值最小，即优惠最大）
    let bestDiscountPackage = null;
    let bestDiscountValue = 1; // 初始值设为1（无折扣）

    packages.forEach(pkg => {
        // 只考虑有原价和现价的套餐，且现价小于原价
        if (pkg.original_price && pkg.price && pkg.original_price > pkg.price) {
            const discountValue = pkg.discount || (pkg.price / pkg.original_price);
            if (discountValue < bestDiscountValue) {
                bestDiscountValue = discountValue;
                bestDiscountPackage = pkg;
            }
        }
    });

    console.log('折扣最大的套餐:', {
        package: bestDiscountPackage,
        discountValue: bestDiscountValue,
        discountPercent: Math.round((1 - bestDiscountValue) * 100) + '%'
    });

    packages.forEach((pkg, index) => {
        const isRecommended = bestDiscountPackage && pkg.id === bestDiscountPackage.id; // 是否为推荐套餐
        const isDefault = isRecommended || (index === 0 && !bestDiscountPackage); // 推荐套餐优先，否则选第一个
        const card = createPackageCard(pkg, isDefault, isRecommended);
        list.appendChild(card);
    });

    // 默认选中推荐套餐，如果没有推荐套餐则选第一个
    if (packages.length > 0) {
        selectedPackage = bestDiscountPackage || packages[0];
        updateTotalPrice(); // 更新底部价格显示
        console.log('默认选中套餐:', selectedPackage, bestDiscountPackage ? '(推荐套餐)' : '(第一个套餐)');
    }
}

// 创建套餐卡片
function createPackageCard(pkg, isDefault = false, isRecommended = false) {
    const card = document.createElement('div');
    card.className = `price-card bg-white border-2 ${isDefault ? 'border-purple-500 selected' : 'border-gray-200'} rounded-xl p-4 cursor-pointer relative`;
    card.dataset.packageId = pkg.id;

    // 只为折扣最大的套餐添加推荐标签
    let recommendTag = '';
    if (isRecommended) {
        recommendTag = `
            <div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
                <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-medium">推荐</span>
            </div>
        `;
    }

    // 构建价格显示
    let priceDisplay = '';
    if (pkg.original_price && pkg.original_price > pkg.price) {
        const savings = pkg.original_price - pkg.price;
        priceDisplay = `
            <div class="flex items-center space-x-2 mb-1">
                <span class="text-xs text-gray-400 line-through">¥${pkg.original_price}</span>
                <span class="bg-red-100 text-red-600 px-2 py-1 rounded text-xs">${pkg.discount_text || '优惠'}</span>
            </div>
        `;
    }

    card.innerHTML = `
        ${recommendTag}
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="w-6 h-6 border-2 ${isDefault ? 'border-purple-500' : 'border-gray-300'} rounded-full flex items-center justify-center">
                    <div class="w-3 h-3 ${isDefault ? 'bg-purple-500' : 'bg-gray-300 hidden'} rounded-full"></div>
                </div>
                <div>
                    <h4 class="text-sm font-semibold text-gray-800">${pkg.name}</h4>
                    <p class="text-xs text-gray-600">${pkg.tag || '会员套餐'}</p>
                </div>
            </div>
            <div class="text-right">
                ${priceDisplay}
                <div class="text-lg font-bold ${isDefault ? 'text-purple-600' : 'text-gray-800'}">¥${pkg.price}</div>
                <div class="text-xs text-gray-500">${pkg.effective}个月</div>
            </div>
        </div>
    `;

    // 添加点击事件
    card.addEventListener('click', function() {
        selectPackage(this, pkg);
    });

    return card;
}

// 选择套餐
function selectPackage(cardElement, pkg) {
    // 移除所有套餐的选中状态
    const allCards = document.querySelectorAll('.price-card');
    allCards.forEach(card => {
        card.classList.remove('selected', 'border-purple-500');
        card.classList.add('border-gray-200');

        // 更新单选按钮状态
        const radio = card.querySelector('.w-6.h-6');
        const dot = card.querySelector('.w-3.h-3');
        radio.classList.remove('border-purple-500');
        radio.classList.add('border-gray-300');
        dot.classList.remove('bg-purple-500');
        dot.classList.add('bg-gray-300', 'hidden');

        // 更新价格颜色
        const price = card.querySelector('.text-lg.font-bold');
        price.classList.remove('text-purple-600');
        price.classList.add('text-gray-800');
    });

    // 设置当前套餐为选中状态
    cardElement.classList.add('selected', 'border-purple-500');
    cardElement.classList.remove('border-gray-200');

    // 更新单选按钮状态
    const radio = cardElement.querySelector('.w-6.h-6');
    const dot = cardElement.querySelector('.w-3.h-3');
    radio.classList.add('border-purple-500');
    radio.classList.remove('border-gray-300');
    dot.classList.add('bg-purple-500');
    dot.classList.remove('bg-gray-300', 'hidden');

    // 更新价格颜色
    const price = cardElement.querySelector('.text-lg.font-bold');
    price.classList.add('text-purple-600');
    price.classList.remove('text-gray-800');

    // 更新选中的套餐
    selectedPackage = pkg;

    // 更新底部价格显示
    updateTotalPrice();

    console.log('选中套餐:', selectedPackage);
}

// 获取选中的套餐（供其他功能使用）
function getSelectedPackage() {
    return selectedPackage;
}

// 获取选中套餐的ID
function getSelectedPackageId() {
    return selectedPackage ? selectedPackage.id : null;
}

// 获取选中套餐的价格
function getSelectedPackagePrice() {
    return selectedPackage ? selectedPackage.price : null;
}

// 更新底部总价显示
function updateTotalPrice() {
    const totalPriceElement = document.getElementById('totalPrice');
    if (selectedPackage && totalPriceElement) {
        const cityCount = selectedCities.length || 1; // 至少选择1个城市
        const totalPrice = selectedPackage.price * cityCount;
        totalPriceElement.textContent = `¥${totalPrice}`;

        console.log('价格计算:', {
            packagePrice: selectedPackage.price,
            cityCount: cityCount,
            totalPrice: totalPrice
        });
    }
}

// 获取当前总价格
function getTotalPrice() {
    if (selectedPackage) {
        const cityCount = selectedCities.length || 1;
        return selectedPackage.price * cityCount;
    }
    return 0;
}

// 获取价格计算详情
function getPriceDetails() {
    if (selectedPackage) {
        const cityCount = selectedCities.length || 1;
        return {
            packagePrice: selectedPackage.price,
            cityCount: cityCount,
            totalPrice: selectedPackage.price * cityCount,
            selectedCities: selectedCities.map(city => city.name),
            selectedPackage: selectedPackage.name
        };
    }
    return null;
}

// ==================== 微信分享功能 ====================

// 加载分享jssdk配置
async function loadJssdkConfig() {
    try {
        // 获取当前页面的URL，iPhone需要特殊处理
        const currentUrl = window.location.href;

        console.log('正在获取JSSDK配置，当前URL:', currentUrl);

        // 请求JSSDK配置
        const response = await fetch('/m/api/get_wechat_jssdk', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                url: currentUrl
            })
        });

        const result = await response.json();

        if (result.code === 0 && result.data && result.data.signature) {
            const config = result.data.signature;

            console.log('JSSDK配置获取成功:', config);

            // iPhone兼容性：扩展jsApiList包含旧版API
            let jsApiList = config.jsApiList || [
                'updateAppMessageShareData',
                'updateTimelineShareData'
            ];

            // 配置微信JSSDK
            wx.config({
                debug: config.debug || false,
                appId: config.appId,
                timestamp: config.timestamp,
                nonceStr: config.nonceStr,
                signature: config.signature,
                jsApiList: jsApiList,
                openTagList: config.openTagList || []
            });

            console.log('微信JSSDK配置完成');

        } else {
            console.error('JSSDK配置数据格式错误:', result);
            throw new Error(result.message || 'JSSDK配置获取失败');
        }

    } catch (error) {
        console.error('加载JSSDK配置失败:', error);
    }
}

// 微信JSSDK配置成功回调
wx.ready(function () {
    console.log('微信JSSDK初始化成功');

    // 获取当前页面URL，iPhone需要特殊处理
    let shareUrl = window.location.href;

    console.log('分享URL:', shareUrl);

    // 分享配置对象
    const shareConfig = {
        title: '会员套餐 - 招标信息服务',
        desc: '开通会员享受更多招标信息服务，把握商机',
        link: shareUrl,
        imgUrl: '', // 可以设置默认分享图片
        success: function () {
            console.log('分享配置成功');
        },
        fail: function (error) {
            console.error('分享配置失败:', error);
        }
    };

    // 设置分享到朋友圈的内容
    wx.updateTimelineShareData({
        title: shareConfig.title,
        link: shareConfig.link,
        imgUrl: shareConfig.imgUrl,
        success: function () {
            console.log('朋友圈分享配置成功');
        },
        fail: function (error) {
            console.error('朋友圈分享配置失败:', error);
        }
    });

    // 设置分享给朋友的内容
    wx.updateAppMessageShareData({
        title: shareConfig.title,
        desc: shareConfig.desc,
        link: shareConfig.link,
        imgUrl: shareConfig.imgUrl,
        success: function () {
            console.log('好友分享配置成功');
        },
        fail: function (error) {
            console.error('好友分享配置失败:', error);
        }
    });
});

// 微信JSSDK配置失败回调
wx.error(function (res) {
    console.error('微信JSSDK配置失败:', res);

    // 通用错误处理
    console.log('JSSDK配置失败，错误详情:', {
        errMsg: res.errMsg,
        userAgent: navigator.userAgent,
        url: window.location.href
    });
});

// ==================== 登录相关功能 ====================

// 微信登录
function goToLogin() {
    // 获取当前页面URL作为回调地址
    const currentURL = window.location.pathname + window.location.search;
    // 跳转到微信授权登录
    window.location.href = '/m/auth/login?callback=' + encodeURIComponent(currentURL);

    console.log('跳转到微信登录，回调URL:', currentURL);
}

// 处理支付按钮点击
async function handlePayment() {
    // 用户已登录，执行支付逻辑
    console.log('用户已登录，开始支付流程');

    // 检查是否选择了城市和套餐
    if (selectedCities.length === 0) {
        alert('请先选择城市');
        return;
    }

    if (!selectedPackage) {
        alert('请先选择套餐');
        return;
    }

    // 获取支付详情
    const paymentDetails = getPriceDetails();
    console.log('支付详情:', paymentDetails);

    // 显示加载状态
    const payButton = document.querySelector('button[onclick="handlePayment()"]');
    const originalText = payButton.innerHTML;
    payButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>创建订单中...';
    payButton.disabled = true;

    try {
        // 创建订单
        const orderResult = await createOrder(paymentDetails);
        console.log('订单创建成功:', orderResult);

        // 显示订单创建成功信息
        showOrderSuccess(orderResult, paymentDetails);

        // TODO: 这里可以添加实际的支付逻辑
        // 例如：调用微信支付API

    } catch (error) {
        console.error('创建订单失败:', error);
        showOrderError(error.message);
    } finally {
        // 恢复按钮状态
        payButton.innerHTML = originalText;
        payButton.disabled = false;
    }
}

// 创建订单
async function createOrder(paymentDetails) {
    // 获取用户openid
    const openid = getUserOpenId();
    if (!openid) {
        throw new Error('获取用户信息失败，请重新登录');
    }

    const orderData = {
        openid: openid,
        good_id: selectedPackage.id,
        good_name: selectedPackage.name,
        price: selectedPackage.price,
        city_ids: selectedCities.map(city => city.id),
        remark: `选择城市：${paymentDetails.selectedCities.join('、')}`
    };

    console.log('创建订单请求数据:', orderData);

    const response = await fetch('/m/api/zb_order/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderData)
    });

    const result = await response.json();
    console.log('创建订单响应:', result);

    if (result.code !== 0) {
        throw new Error(result.message || '创建订单失败');
    }

    return result.data;
}

// 获取用户OpenID
function getUserOpenId() {
    // 直接使用页面定义的userInfo（就是用户的openid）
    if (userInfo && userInfo.trim() !== '') {
        console.log('✅ 从页面变量获取OpenID:', userInfo);
        return userInfo;
    }

    console.error('❌ 无法获取用户OpenID，userInfo为空');
    return null;
}



// 显示订单创建成功
function showOrderSuccess(orderResult, paymentDetails) {
    const message = `
🎉 订单创建成功！

📋 订单信息：
• 订单号：${orderResult.order_sn}
• 套餐：${paymentDetails.selectedPackage}
• 城市：${paymentDetails.selectedCities.join('、')}
• 金额：¥${paymentDetails.totalPrice}

请保存订单号以便查询。
    `.trim();

    alert(message);

    // 可以考虑跳转到订单详情页面
    // window.location.href = `/m/order/detail?id=${orderResult.order_id}`;
}

// 显示订单创建失败
function showOrderError(errorMessage) {
    const message = `
❌ 订单创建失败

错误信息：${errorMessage}

请检查：
• 网络连接是否正常
• 是否已选择城市和套餐
• 用户信息是否有效

如问题持续存在，请联系客服。
    `.trim();

    alert(message);
}

// 调试函数：显示当前用户信息
function debugUserInfo() {
    console.log('🔍 用户信息调试:', {
        userInfo: userInfo || 'undefined',
        userInfo_type: typeof userInfo,
        userInfo_length: userInfo ? userInfo.length : 0
    });

    const openid = getUserOpenId();
    if (!openid) {
        console.warn('⚠️ 无法获取用户OpenID，订单创建可能失败');
        console.warn('请确保页面已正确设置 userInfo 变量');
    } else {
        console.log('✅ 用户OpenID获取成功:', openid);
    }
}
</script>

</body>
</html>
