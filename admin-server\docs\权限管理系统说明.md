# 权限管理系统说明

## 概述

本系统实现了基于角色的访问控制(RBAC)，支持自动权限验证，无需为每个API接口手动配置权限。

## 系统架构

### 1. 权限验证流程

```
用户请求 → JWT认证 → 自动权限验证 → 业务逻辑
```

### 2. 中间件层次

1. **Auth中间件** - JWT token验证，设置admin_id到上下文
2. **AutoPermission中间件** - 自动权限验证，根据路径和方法匹配权限

### 3. 权限配置

权限配置在 `internal/middleware/permission_config.go` 中维护：

```go
{
    Path: "/sys_admin",
    Methods: map[string]string{
        "GET":    consts.PermSysAdminList,
        "POST":   consts.PermSysAdminAdd,
        "PUT":    consts.PermSysAdminEdit,
        "DELETE": consts.PermSysAdminDelete,
    },
    SubPaths: map[string]string{
        "roles": consts.PermSysAdminRoles,
    },
}
```

## 权限匹配规则

### 1. 基础路径匹配

- `GET /sys_admin` → `system:admin:list`
- `POST /sys_admin/create` → `system:admin:add`
- `PUT /sys_admin/1` → `system:admin:edit`
- `DELETE /sys_admin/1` → `system:admin:delete`

### 2. 子路径匹配

- `PUT /sys_admin/1/roles` → `system:admin:roles`
- `GET /sys_admin/1/roles` → `system:admin:roles`

### 3. 排除路径

以下路径不需要权限验证：
- `/auth/login` - 登录
- `/auth/refresh` - 刷新token
- `/auth/permissions` - 获取权限
- `/auth/menus` - 获取菜单
- `/auth/userinfo` - 获取用户信息
- `/auth/logout` - 退出登录

## 权限验证逻辑

### 1. 超级管理员

- `is_super = 1` 的管理员拥有所有权限
- 跳过权限检查，直接允许访问

### 2. 普通管理员

1. 查询管理员的角色关联 (`sys_admin_role`)
2. 查询角色的菜单权限 (`sys_role_menu`)
3. 查询菜单的权限标识 (`sys_menu.perms`)
4. 匹配请求所需的权限

### 3. 权限匹配算法

支持三种匹配方式：

1. **精确匹配**: `system:admin:list` = `system:admin:list`
2. **通配符匹配**: `system:*` 匹配 `system:admin:list`
3. **父级权限匹配**: `system:admin` 匹配 `system:admin:list`

## 如何添加新的API权限

### 1. 定义权限常量

在 `internal/consts/permissions.go` 中添加：

```go
const (
    PermNewModuleList   = "system:newmodule:list"
    PermNewModuleAdd    = "system:newmodule:add"
    PermNewModuleEdit   = "system:newmodule:edit"
    PermNewModuleDelete = "system:newmodule:delete"
)
```

### 2. 配置权限映射

在 `internal/middleware/permission_config.go` 中添加：

```go
{
    Path: "/new_module",
    Methods: map[string]string{
        "GET":    consts.PermNewModuleList,
        "POST":   consts.PermNewModuleAdd,
        "PUT":    consts.PermNewModuleEdit,
        "DELETE": consts.PermNewModuleDelete,
    },
    Description: "新模块管理",
}
```

### 3. 创建菜单权限

在数据库 `sys_menu` 表中创建对应的菜单记录，设置 `perms` 字段。

### 4. 分配角色权限

通过角色管理界面或API，将菜单权限分配给相应的角色。

## 优势

### 1. 自动化

- 新增API接口时，只需在配置文件中添加权限映射
- 无需修改路由配置或控制器代码
- 减少人工错误和遗漏

### 2. 灵活性

- 支持多种权限匹配方式
- 可以轻松添加新的权限规则
- 支持子路径权限配置

### 3. 可维护性

- 权限配置集中管理
- 清晰的权限层次结构
- 易于理解和维护

### 4. 性能

- 权限映射在启动时初始化
- 使用map进行快速查找
- 避免重复的数据库查询

## 注意事项

1. **权限粒度**: 建议按照功能模块和操作类型设计权限
2. **命名规范**: 权限标识建议使用 `模块:功能:操作` 的格式
3. **缓存策略**: 可以考虑添加权限缓存来提高性能
4. **日志记录**: 系统会记录权限验证的详细日志，便于调试

## 示例

### 添加文章管理权限

1. 定义权限常量：
```go
const (
    PermArticleList   = "content:article:list"
    PermArticleAdd    = "content:article:add"
    PermArticleEdit   = "content:article:edit"
    PermArticleDelete = "content:article:delete"
)
```

2. 配置权限映射：
```go
{
    Path: "/article",
    Methods: map[string]string{
        "GET":    consts.PermArticleList,
        "POST":   consts.PermArticleAdd,
        "PUT":    consts.PermArticleEdit,
        "DELETE": consts.PermArticleDelete,
    },
    Description: "文章管理",
}
```

3. 创建菜单并分配给角色，系统会自动进行权限验证。

这样，整个权限管理系统就能自动工作，大大简化了开发和维护工作。
