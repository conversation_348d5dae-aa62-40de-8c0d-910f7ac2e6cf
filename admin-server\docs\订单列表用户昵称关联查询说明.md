# 订单列表用户昵称关联查询说明

## 📋 修改概述

为订单列表和详情接口添加用户昵称字段，通过关联查询 `zb_order.user_id = zb_user.id` 获取用户昵称信息。

## 🔧 修改内容

### 1. API响应结构修改

#### OrderInfo结构体
```go
// 修改前
type OrderInfo struct {
    Id            int64       `json:"id"`
    OrderSn       string      `json:"order_sn"`
    GoodId        int64       `json:"good_id"`
    GoodName      string      `json:"good_name"`
    CityCount     int         `json:"city_count"`
    UserId        int64       `json:"user_id"`        // 只有用户ID
    Price         float64     `json:"price"`
    // ... 其他字段
}

// 修改后
type OrderInfo struct {
    Id            int64       `json:"id"`
    OrderSn       string      `json:"order_sn"`
    GoodId        int64       `json:"good_id"`
    GoodName      string      `json:"good_name"`
    CityCount     int         `json:"city_count"`
    UserId        int64       `json:"user_id"`
    UserNickname  string      `json:"user_nickname"`  // 新增用户昵称
    Price         float64     `json:"price"`
    // ... 其他字段
}
```

### 2. 数据库关联查询

#### GetList方法修改
```go
// 修改前：只查询订单表
var orders []entity.ZbOrder
err = model.Page(req.Page, req.PageSize).
    Order("created_at DESC").
    Scan(&orders)

// 修改后：关联查询用户表
type OrderWithUser struct {
    entity.ZbOrder
    UserNickname string `json:"user_nickname"`
}

var ordersWithUser []OrderWithUser
err = model.Page(req.Page, req.PageSize).
    LeftJoin("zb_user u", "zb_order.user_id = u.id").
    Fields("zb_order.*, u.nickname as user_nickname").
    Order("zb_order.created_at DESC").
    Scan(&ordersWithUser)
```

#### GetDetail方法修改
```go
// 修改前：只查询订单表
var order entity.ZbOrder
err := dao.ZbOrder.Ctx(ctx).Where("id", id).Scan(&order)

// 修改后：关联查询用户表
type OrderWithUser struct {
    entity.ZbOrder
    UserNickname string `json:"user_nickname"`
}

var orderWithUser OrderWithUser
err := dao.ZbOrder.Ctx(ctx).
    LeftJoin("zb_user u", "zb_order.user_id = u.id").
    Fields("zb_order.*, u.nickname as user_nickname").
    Where("zb_order.id", id).
    Scan(&orderWithUser)
```

### 3. SQL查询分析

#### 生成的SQL语句
```sql
-- 订单列表查询
SELECT zb_order.*, u.nickname as user_nickname 
FROM zb_order 
LEFT JOIN zb_user u ON zb_order.user_id = u.id 
ORDER BY zb_order.created_at DESC 
LIMIT 10 OFFSET 0;

-- 订单详情查询
SELECT zb_order.*, u.nickname as user_nickname 
FROM zb_order 
LEFT JOIN zb_user u ON zb_order.user_id = u.id 
WHERE zb_order.id = 1;
```

#### 关联关系
- **主表**: `zb_order` (订单表)
- **关联表**: `zb_user` (用户表)
- **关联条件**: `zb_order.user_id = zb_user.id`
- **关联类型**: `LEFT JOIN` (左连接，确保即使用户不存在也能查到订单)

## 📊 API响应示例

### 1. 订单列表响应
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "list": [
            {
                "id": 1,
                "order_sn": "ZB20250123150405XXXX",
                "good_id": 1,
                "good_name": "VIP套餐",
                "city_count": 3,
                "user_id": 123,
                "user_nickname": "张三",
                "price": 300.00,
                "amount": 300.00,
                "pay_status": 1,
                "transaction_id": "wx_transaction_123",
                "trade_type": "JSAPI",
                "trade_state": "SUCCESS",
                "remark": "选择城市：北京、上海、深圳",
                "pay_at": "2025-01-23T15:30:00Z",
                "created_at": "2025-01-23T15:04:05Z",
                "updated_at": "2025-01-23T15:30:00Z",
                "cities": [
                    {"city_id": 1, "city_name": "北京"},
                    {"city_id": 2, "city_name": "上海"},
                    {"city_id": 3, "city_name": "深圳"}
                ]
            }
        ],
        "total": 1
    }
}
```

### 2. 订单详情响应
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "id": 1,
        "order_sn": "ZB20250123150405XXXX",
        "good_id": 1,
        "good_name": "VIP套餐",
        "city_count": 3,
        "user_id": 123,
        "user_nickname": "张三",
        "price": 300.00,
        "amount": 300.00,
        "pay_status": 1,
        "cities": [
            {"city_id": 1, "city_name": "北京"},
            {"city_id": 2, "city_name": "上海"},
            {"city_id": 3, "city_name": "深圳"}
        ]
    }
}
```

## 🧪 测试验证

### 1. 数据准备
确保测试数据中有用户信息：
```sql
-- 查看订单和用户关联数据
SELECT 
    o.id as order_id,
    o.order_sn,
    o.user_id,
    u.nickname as user_nickname
FROM zb_order o
LEFT JOIN zb_user u ON o.user_id = u.id
LIMIT 5;
```

### 2. API测试
```bash
# 测试订单列表
curl "http://localhost:8000/api/zb_order/list?page=1&page_size=10" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 测试订单详情
curl "http://localhost:8000/api/zb_order/detail?id=1" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 响应验证
检查响应中是否包含 `user_nickname` 字段：
```javascript
// 验证列表响应
response.data.list.forEach(order => {
    console.log('订单ID:', order.id);
    console.log('用户ID:', order.user_id);
    console.log('用户昵称:', order.user_nickname);
});

// 验证详情响应
console.log('订单详情用户昵称:', response.data.user_nickname);
```

## ⚠️ 注意事项

### 1. 数据完整性
- 使用 `LEFT JOIN` 确保即使用户被删除，订单仍能查询到
- 如果用户不存在，`user_nickname` 字段为 `null`

### 2. 性能考虑
- 关联查询会增加一定的查询开销
- 建议在 `zb_order.user_id` 和 `zb_user.id` 上建立索引
- 大数据量时可考虑分页优化

### 3. 索引建议
```sql
-- 确保有以下索引
ALTER TABLE zb_order ADD INDEX idx_user_id (user_id);
ALTER TABLE zb_user ADD INDEX idx_id (id); -- 主键默认有索引
```

### 4. 错误处理
- 处理用户表不存在的情况
- 处理关联查询失败的情况
- 记录查询异常日志

## 📈 性能优化建议

### 1. 查询优化
```sql
-- 添加复合索引优化排序和关联
ALTER TABLE zb_order ADD INDEX idx_created_user (created_at DESC, user_id);
```

### 2. 分页优化
对于大数据量，可以考虑：
- 使用游标分页替代OFFSET
- 缓存热门查询结果
- 异步加载用户信息

### 3. 缓存策略
```go
// 可以添加用户信息缓存
func (s *sZbOrder) getUserNicknameWithCache(ctx context.Context, userId int64) string {
    // 先从缓存获取
    // 缓存未命中时查询数据库
    // 设置缓存过期时间
}
```

## 🔄 后续扩展

### 1. 更多用户信息
可以根据需要添加更多用户字段：
```go
type OrderWithUser struct {
    entity.ZbOrder
    UserNickname string `json:"user_nickname"`
    UserAvatar   string `json:"user_avatar"`   // 用户头像
    UserPhone    string `json:"user_phone"`    // 用户手机号
}
```

### 2. 用户信息脱敏
对敏感信息进行脱敏处理：
```go
// 手机号脱敏
if orderWithUser.UserPhone != "" {
    orderInfo.UserPhone = maskPhone(orderWithUser.UserPhone)
}
```

### 3. 批量用户查询
对于大量订单，可以批量查询用户信息：
```go
// 先查询订单列表
// 提取所有user_id
// 批量查询用户信息
// 组装最终结果
```

---

**修改状态**: ✅ 已完成  
**关联查询**: zb_order.user_id = zb_user.id  
**新增字段**: user_nickname  
**查询类型**: LEFT JOIN  
**文档版本**: v1.0  
**修改时间**: 2025-01-23
