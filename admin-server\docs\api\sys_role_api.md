# 系统角色 API 文档

## 概述

系统角色模块提供了完整的角色管理功能，包括角色的增删改查以及角色菜单权限的分配管理。

## 基础信息

- **基础路径**: `/sys_role`
- **认证方式**: Bearer <PERSON>
- **数据格式**: JSON

## 数据类型说明

### RoleInfo 角色信息

```json
{
  "id": 1,                           // 角色ID
  "name": "管理员",                   // 角色名称
  "sort": 1,                         // 排序
  "remark": "系统管理员角色",          // 备注
  "is_disable": 0,                   // 是否禁用 (0=否, 1=是)
  "created_at": "2024-01-01T10:00:00Z", // 创建时间
  "updated_at": "2024-01-01T10:00:00Z", // 更新时间
  "menu_count": 8,                   // 权限数量
  "user_count": 3,                   // 用户数量
  "main_menus": ["系统管理", "用户管理", "角色管理", "菜单管理", "权限管理"] // 主要权限列表（最多5个）
}
```

### RoleMenuInfo 角色菜单权限信息

```json
{
  "role_id": 1,                      // 角色ID
  "role_name": "管理员",              // 角色名称
  "menu_ids": [1, 2, 3, 4]           // 菜单ID列表
}
```

## API 接口

### 1. 创建角色

**接口地址**: `POST /sys_role/create`

**接口描述**: 创建新的角色

**请求参数**:

```json
{
  "name": "管理员",                   // 必填，角色名称，长度1-30位，全局唯一
  "sort": 1,                         // 可选，排序，默认1，最小值0
  "remark": "系统管理员角色",          // 可选，备注，最大255位
  "is_disable": 0,                   // 可选，是否禁用 (0=否, 1=是)，默认0
  "menu_ids": [1, 2, 3, 4]           // 可选，菜单权限ID列表，不填则后续单独分配
}
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1                          // 新创建的角色ID
  }
}
```

**错误响应**:

```json
{
  "code": 1,
  "message": "角色名称已存在",
  "data": null
}
```

**菜单ID无效错误**:

```json
{
  "code": 1,
  "message": "存在无效的菜单ID",
  "data": null
}
```

### 2. 获取角色列表

**接口地址**: `GET /sys_role/list`

**接口描述**: 分页获取角色列表，支持条件筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1，最小值1 |
| page_size | int | 否 | 每页数量，默认10，范围1-100 |
| name | string | 否 | 角色名称（模糊搜索） |
| is_disable | int | 否 | 是否禁用 (0=否, 1=是) |

**请求示例**:

```
GET /sys_role/list?page=1&page_size=10&name=管理&is_disable=0
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "超级管理员",
        "sort": 1,
        "remark": "超级管理员角色",
        "is_disable": 0,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z",
        "menu_count": 8,
        "user_count": 1,
        "main_menus": ["系统管理", "用户管理", "角色管理", "菜单管理", "权限管理"]
      },
      {
        "id": 2,
        "name": "系统管理员",
        "sort": 2,
        "remark": "系统管理员角色",
        "is_disable": 0,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z",
        "menu_count": 4,
        "user_count": 3,
        "main_menus": ["系统管理", "用户管理", "角色管理"]
      },
      {
        "id": 3,
        "name": "内容编辑员",
        "sort": 3,
        "remark": "内容编辑员角色",
        "is_disable": 0,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z",
        "menu_count": 3,
        "user_count": 5,
        "main_menus": ["基本信息", "content:create", "content:update"]
      },
      {
        "id": 4,
        "name": "审核员",
        "sort": 4,
        "remark": "审核员角色",
        "is_disable": 1,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z",
        "menu_count": 2,
        "user_count": 0,
        "main_menus": ["基本信息", "审核权限"]
      }
    ],
    "total": 4,              // 总记录数
    "page": 1,               // 当前页码
    "page_size": 10          // 每页数量
  }
}
```

### 3. 获取单个角色信息

**接口地址**: `GET /sys_role/{id}`

**接口描述**: 根据ID获取单个角色的详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 角色ID |

**请求示例**:

```
GET /sys_role/1
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "管理员",
    "sort": 1,
    "remark": "系统管理员角色",
    "is_disable": 0,
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

**错误响应**:

```json
{
  "code": 1,
  "message": "角色不存在",
  "data": null
}
```

### 4. 更新角色信息

**接口地址**: `PUT /sys_role/{id}`

**接口描述**: 更新角色的基本信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 角色ID |

**请求参数**:

```json
{
  "name": "管理员",                   // 必填，角色名称，长度1-30位
  "sort": 1,                         // 可选，排序，最小值0
  "remark": "系统管理员角色",          // 可选，备注，最大255位
  "is_disable": 0                    // 可选，是否禁用 (0=否, 1=是)
}
```

**请求示例**:

```
PUT /sys_role/1
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

**错误响应**:

```json
{
  "code": 1,
  "message": "角色名称已被使用",
  "data": null
}
```

### 5. 删除角色

**接口地址**: `DELETE /sys_role/{id}`

**接口描述**: 软删除角色（逻辑删除）

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 角色ID |

**请求示例**:

```
DELETE /sys_role/1
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

**错误响应**:

```json
{
  "code": 1,
  "message": "该角色已被管理员使用，无法删除",
  "data": null
}
```

### 6. 分配角色菜单权限

**接口地址**: `PUT /sys_role/{id}/menus`

**接口描述**: 为角色分配菜单权限

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 角色ID |

**请求参数**:

```json
{
  "menu_ids": [1, 2, 3, 4, 5]        // 必填，菜单ID列表，空数组表示清空所有权限
}
```

**请求示例**:

```
PUT /sys_role/1/menus
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

**错误响应**:

```json
{
  "code": 1,
  "message": "存在无效的菜单ID",
  "data": null
}
```

### 7. 获取角色菜单权限

**接口地址**: `GET /sys_role/{id}/menus`

**接口描述**: 获取角色已分配的菜单权限

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 角色ID |

**请求示例**:

```
GET /sys_role/1/menus
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "role_id": 1,
    "role_name": "管理员",
    "menu_ids": [1, 2, 3, 4, 5]
  }
}
```

**错误响应**:

```json
{
  "code": 1,
  "message": "角色不存在",
  "data": null
}
```

### 8. 切换角色状态

**接口地址**: `PUT /sys_role/{id}/toggle`

**接口描述**: 切换角色的启用/禁用状态

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 角色ID |

**请求示例**:

```
PUT /sys_role/1/toggle
```

**响应示例**:

```json
{
  "code": 0,
  "message": "success",
  "data": {}
}
```

**错误响应**:

```json
{
  "code": 1,
  "message": "角色不存在",
  "data": null
}
```

**功能说明**:
- 如果角色当前是启用状态（is_disable=0），则切换为禁用状态（is_disable=1）
- 如果角色当前是禁用状态（is_disable=1），则切换为启用状态（is_disable=0）
- 无需传递任何参数，系统自动判断当前状态并切换

## API接口列表

| 方法 | 路径 | 功能 |
|------|------|------|
| POST | /sys_role/create | 创建角色 |
| GET | /sys_role/list | 获取角色列表 |
| GET | /sys_role/{id} | 获取单个角色信息 |
| PUT | /sys_role/{id} | 更新角色信息 |
| DELETE | /sys_role/{id} | 删除角色 |
| PUT | /sys_role/{id}/menus | 分配角色菜单权限 |
| GET | /sys_role/{id}/menus | 获取角色菜单权限 |
| PUT | /sys_role/{id}/toggle | 切换角色启用/禁用状态 |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1 | 业务错误（具体错误信息见message字段） |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有接口都需要在请求头中携带有效的认证Token
2. 角色名称在系统中必须唯一
3. 删除操作为软删除，不会真正删除数据库记录
4. 删除角色前需要确保没有管理员使用此角色
5. 角色按照sort字段升序排列，相同排序值按ID升序
6. 分配菜单权限时会先清空原有权限，再分配新权限
7. 菜单权限分配支持传入空数组，表示清空所有权限
8. 创建角色时可以同时分配菜单权限，menu_ids参数为可选
9. 如果创建时不提供menu_ids，则角色创建后无任何权限，需后续单独分配
10. 创建时提供的menu_ids会进行有效性验证，无效的菜单ID会导致创建失败

## 业务规则

### 角色管理
- 角色名称必须唯一
- 角色可以被禁用，但不能被物理删除
- 被管理员使用的角色不能被删除

### 权限分配
- 每个角色可以分配多个菜单权限
- 菜单权限包括目录、菜单和按钮三种类型
- 权限分配是覆盖式的，不是增量式的
- 分配权限时会验证菜单ID的有效性

### 数据完整性
- 删除角色时会同时删除相关的角色菜单关联记录
- 使用事务确保数据一致性
- 支持并发操作的安全性
