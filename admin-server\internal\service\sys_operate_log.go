package service

import (
	"admin-server/internal/dao"
	"admin-server/internal/model/entity"
	"context"
	"encoding/json"
	"net"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/google/uuid"
)

// 1.定义接口
type ISysOperateLog interface {
	RecordOperateLog(ctx context.Context, r *ghttp.Request, adminId int64, username, title string, startTime time.Time, status int, responseBody interface{}) error
	GetOperateLogList(ctx context.Context, page, pageSize int, username, title, method string, status int, startDate, endDate string) (list []entity.SysOperateLog, total int, err error)
	GetOperateLogDetail(ctx context.Context, id int64) (*entity.SysOperateLog, error)
	DeleteOperateLog(ctx context.Context, ids []int64) error
	ClearOperateLog(ctx context.Context) error
}

// 2.定义接口变量
var localSysOperateLog ISysOperateLog

// 3.定义一个获取接口实例的函数
func SysOperateLog() ISysOperateLog {
	if localSysOperateLog == nil {
		panic("ISysOperateLog接口未实现或未注册")
	}
	return localSysOperateLog
}

// 4.定义一个接口实现的注册方法
func RegisterSysOperateLog(i ISysOperateLog) {
	localSysOperateLog = i
}

type sSysOperateLog struct{}

func init() {
	RegisterSysOperateLog(NewOperateLog())
}

func NewOperateLog() *sSysOperateLog {
	return &sSysOperateLog{}
}

// RecordOperateLog 记录操作日志
func (s *sSysOperateLog) RecordOperateLog(ctx context.Context, r *ghttp.Request, adminId int64, username, title string, startTime time.Time, status int, responseBody interface{}) error {
	// 生成请求ID (UUID)
	requestId := uuid.New().String()

	// 获取客户端IP
	ip := s.getClientIP(r)

	// 获取请求头（过滤敏感信息）
	reqHeader := s.filterSensitiveHeaders(r.Header)

	// 获取请求体
	reqBody := s.getRequestBody(r)

	// 计算执行时间
	endTime := time.Now()
	taskTime := endTime.Sub(startTime).Milliseconds()

	// 过滤响应体中的敏感信息
	filteredResBody := s.filterSensitiveResponse(responseBody)

	// 将对象转换为JSON字符串，处理空值情况
	reqHeaderJson, _ := json.Marshal(reqHeader)
	if reqHeader == nil {
		reqHeaderJson = []byte("null")
	}

	reqBodyJson, _ := json.Marshal(reqBody)
	if reqBody == nil {
		reqBodyJson = []byte("null")
	}

	resBodyJson, _ := json.Marshal(filteredResBody)
	if filteredResBody == nil {
		resBodyJson = []byte("null")
	}

	// 创建操作日志记录
	operateLog := &entity.SysOperateLog{
		RequestId: requestId,
		AdminId:   adminId,
		Username:  username,
		Method:    r.Method,
		Title:     title,
		Ip:        ip,
		ReqHeader: string(reqHeaderJson),
		ReqBody:   string(reqBodyJson),
		ResHeader: "null", // 响应头设置为null而不是空字符串
		ResBody:   string(resBodyJson),
		Status:    status,
		StartTime: int(startTime.Unix()),
		EndTime:   int(endTime.Unix()),
		TaskTime:  int(taskTime),
		CreatedAt: gtime.Now(),
		UpdatedAt: gtime.Now(),
	}

	// 异步插入数据库，避免影响主业务流程
	go func() {
		_, err := dao.SysOperateLog.Ctx(context.Background()).Insert(operateLog)
		if err != nil {
			g.Log().Error(context.Background(), "记录操作日志失败:", err)
		}
	}()

	return nil
}

// GetOperateLogList 获取操作日志列表
func (s *sSysOperateLog) GetOperateLogList(ctx context.Context, page, pageSize int, username, title, method string, status int, startDate, endDate string) (list []entity.SysOperateLog, total int, err error) {
	query := dao.SysOperateLog.Ctx(ctx)

	// 按用户名筛选
	if username != "" {
		query = query.WhereLike("username", "%"+username+"%")
	}

	// 按操作标题筛选
	if title != "" {
		query = query.WhereLike("title", "%"+title+"%")
	}

	// 按请求方法筛选
	if method != "" {
		query = query.Where("method", method)
	}

	// 按状态筛选
	if status > 0 {
		query = query.Where("status", status)
	}

	// 按时间范围筛选
	if startDate != "" {
		query = query.WhereGTE("created_at", startDate)
	}
	if endDate != "" {
		query = query.WhereLTE("created_at", endDate+" 23:59:59")
	}

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.OrderDesc("created_at").Limit(offset, pageSize).Scan(&list)
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

// GetOperateLogDetail 获取操作日志详情
func (s *sSysOperateLog) GetOperateLogDetail(ctx context.Context, id int64) (*entity.SysOperateLog, error) {
	var log entity.SysOperateLog
	err := dao.SysOperateLog.Ctx(ctx).Where("id", id).Scan(&log)
	if err != nil {
		return nil, err
	}

	if log.Id == 0 {
		return nil, nil
	}

	return &log, nil
}

// DeleteOperateLog 删除操作日志
func (s *sSysOperateLog) DeleteOperateLog(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	_, err := dao.SysOperateLog.Ctx(ctx).WhereIn("id", ids).Delete()
	if err != nil {
		g.Log().Error(ctx, "删除操作日志失败:", err)
		return err
	}

	g.Log().Info(ctx, "删除操作日志成功:", "ids:", ids)
	return nil
}

// ClearOperateLog 清空操作日志
func (s *sSysOperateLog) ClearOperateLog(ctx context.Context) error {
	_, err := dao.SysOperateLog.Ctx(ctx).Delete()
	if err != nil {
		g.Log().Error(ctx, "清空操作日志失败:", err)
		return err
	}

	g.Log().Info(ctx, "清空操作日志成功")
	return nil
}

// getClientIP 获取客户端真实IP
func (s *sSysOperateLog) getClientIP(r *ghttp.Request) string {
	// 优先从代理头获取真实IP
	ip := r.Header.Get("X-Forwarded-For")
	if ip != "" {
		ips := strings.Split(ip, ",")
		if len(ips) > 0 {
			return strings.TrimSpace(ips[0])
		}
	}

	ip = r.Header.Get("X-Real-IP")
	if ip != "" {
		return ip
	}

	ip = r.GetClientIp()
	if ip != "" {
		return ip
	}

	host, _, err := net.SplitHostPort(r.RemoteAddr)
	if err != nil {
		return r.RemoteAddr
	}

	return host
}

// filterSensitiveHeaders 过滤敏感请求头
func (s *sSysOperateLog) filterSensitiveHeaders(headers map[string][]string) map[string]interface{} {
	filtered := make(map[string]interface{})
	sensitiveKeys := []string{"authorization", "cookie", "x-auth-token", "x-api-key"}

	for key, values := range headers {
		lowerKey := strings.ToLower(key)
		isSensitive := false

		for _, sensitiveKey := range sensitiveKeys {
			if strings.Contains(lowerKey, sensitiveKey) {
				isSensitive = true
				break
			}
		}

		if isSensitive {
			filtered[key] = "***"
		} else {
			if len(values) == 1 {
				filtered[key] = values[0]
			} else {
				filtered[key] = values
			}
		}
	}

	return filtered
}

// getRequestBody 获取请求体
func (s *sSysOperateLog) getRequestBody(r *ghttp.Request) interface{} {
	// 只记录POST、PUT、PATCH请求的请求体
	if r.Method != "POST" && r.Method != "PUT" && r.Method != "PATCH" {
		return nil
	}

	// 获取原始请求体
	bodyBytes := r.GetBody()
	if len(bodyBytes) == 0 {
		return nil
	}

	// 尝试解析为JSON
	var bodyJson interface{}
	if err := json.Unmarshal(bodyBytes, &bodyJson); err == nil {
		// 过滤敏感字段
		return s.filterSensitiveFields(bodyJson)
	}

	// 如果不是JSON，返回字符串（限制长度）
	bodyStr := string(bodyBytes)
	if len(bodyStr) > 1000 {
		bodyStr = bodyStr[:1000] + "..."
	}

	return bodyStr
}

// filterSensitiveFields 过滤敏感字段
func (s *sSysOperateLog) filterSensitiveFields(data interface{}) interface{} {
	sensitiveFields := []string{"password", "token", "secret", "key", "auth"}

	switch v := data.(type) {
	case map[string]interface{}:
		filtered := make(map[string]interface{})
		for key, value := range v {
			lowerKey := strings.ToLower(key)
			isSensitive := false

			for _, sensitiveField := range sensitiveFields {
				if strings.Contains(lowerKey, sensitiveField) {
					isSensitive = true
					break
				}
			}

			if isSensitive {
				filtered[key] = "***"
			} else {
				filtered[key] = s.filterSensitiveFields(value)
			}
		}
		return filtered
	case []interface{}:
		filtered := make([]interface{}, len(v))
		for i, item := range v {
			filtered[i] = s.filterSensitiveFields(item)
		}
		return filtered
	default:
		return v
	}
}

// filterSensitiveResponse 过滤响应体中的敏感信息
func (s *sSysOperateLog) filterSensitiveResponse(data interface{}) interface{} {
	return s.filterSensitiveFields(data)
}
