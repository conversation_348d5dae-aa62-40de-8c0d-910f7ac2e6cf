# 素材中心管理API文档

## 概述

素材中心管理系统提供完整的资源分组和资源文件管理功能，支持图片、视频等多媒体资源的上传、存储、分类管理，为系统提供统一的素材管理服务。

## 基础信息

- **Base URL**: `http://localhost:8000`
- **认证方式**: Bearer Token
- **Content-Type**: `application/json`

## 资源分组管理API

### 1. 获取资源分组列表

**接口地址**: `GET /sys_resources_group/list`

**接口描述**: 分页获取资源分组列表，支持多条件筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码，最小值为1 |
| page_size | int | 否 | 10 | 每页数量，范围1-100 |
| name | string | 否 | - | 分组名称（模糊搜索） |
| type | string | 否 | - | 资源类型：PIC=图片，VIDEO=视频 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/sys_resources_group/list?page=1&page_size=10&type=PIC" \
  -H "Authorization: Bearer your_token"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "产品图片",
        "type": "PIC",
        "is_delete": 0,
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 10:00:00"
      }
    ],
    "total": 1
  }
}
```

### 2. 获取资源分组详情

**接口地址**: `GET /sys_resources_group/get_one/{id}`

**接口描述**: 获取指定资源分组的详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 资源分组ID |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/sys_resources_group/get_one/1" \
  -H "Authorization: Bearer your_token"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "resources_group": {
      "id": 1,
      "name": "产品图片",
      "type": "PIC",
      "is_delete": 0,
      "created_at": "2024-01-01 10:00:00",
      "updated_at": "2024-01-01 10:00:00"
    }
  }
}
```

### 3. 创建资源分组

**接口地址**: `POST /sys_resources_group/create`

**接口描述**: 创建新的资源分组

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 分组名称，长度1-255个字符 |
| type | string | 是 | 资源类型：PIC=图片，VIDEO=视频 |

**请求示例**:
```bash
curl -X POST "http://localhost:8000/sys_resources_group/create" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "宣传视频",
    "type": "VIDEO"
  }'
```

**响应示例**:
```json
{
  "code": 0,
  "message": "创建成功",
  "data": {}
}
```

### 4. 更新资源分组

**接口地址**: `PUT /sys_resources_group/update/{id}`

**接口描述**: 更新指定的资源分组信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 资源分组ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 分组名称，长度1-255个字符 |
| type | string | 是 | 资源类型：PIC=图片，VIDEO=视频 |

**请求示例**:
```bash
curl -X PUT "http://localhost:8000/sys_resources_group/update/1" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "产品图片（已更新）",
    "type": "PIC"
  }'
```

### 5. 删除资源分组

**接口地址**: `DELETE /sys_resources_group/delete`

**接口描述**: 批量删除资源分组（软删除）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | array | 是 | 资源分组ID列表 |

**请求示例**:
```bash
curl -X DELETE "http://localhost:8000/sys_resources_group/delete" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [1, 2, 3]
  }'
```

### 6. 获取所有资源分组

**接口地址**: `GET /sys_resources_group/all`

**接口描述**: 获取所有资源分组（用于下拉选择）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | string | 否 | 资源类型：PIC=图片，VIDEO=视频 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/sys_resources_group/all?type=PIC" \
  -H "Authorization: Bearer your_token"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "产品图片",
        "type": "PIC"
      },
      {
        "id": 2,
        "name": "广告图片",
        "type": "PIC"
      }
    ]
  }
}
```

## 资源管理API

### 1. 获取资源列表

**接口地址**: `GET /sys_resources/list`

**接口描述**: 分页获取资源列表，支持多条件筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码，最小值为1 |
| page_size | int | 否 | 10 | 每页数量，范围1-100 |
| group_id | int64 | 否 | - | 资源分组ID |
| origin_name | string | 否 | - | 源文件名（模糊搜索） |
| mime_type | string | 否 | - | 资源类型 |
| storage_mode | int | 否 | - | 存储模式：1=本地，2=阿里云，3=七牛云，4=腾讯云 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/sys_resources/list?page=1&page_size=10&group_id=1" \
  -H "Authorization: Bearer your_token"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "group_id": 1,
        "storage_mode": 1,
        "origin_name": "product1.jpg",
        "object_name": "1640995200_abc123.jpg",
        "hash": "d41d8cd98f00b204e9800998ecf8427e",
        "mime_type": "image/jpeg",
        "storage_path": "./uploads/2024/01/01",
        "suffix": ".jpg",
        "size_byte": "102400",
        "size_info": "100.0 KB",
        "url": "/uploads/2024/01/01/1640995200_abc123.jpg",
        "remark": "产品图片",
        "is_delete": 0,
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 10:00:00"
      }
    ],
    "total": 1
  }
}
```

### 2. 获取资源详情

**接口地址**: `GET /sys_resources/get_one/{id}`

**接口描述**: 获取指定资源的详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 资源ID |

### 3. 创建资源

**接口地址**: `POST /sys_resources/create`

**接口描述**: 创建新的资源记录

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| group_id | int64 | 是 | 资源分组ID |
| storage_mode | int | 是 | 存储模式：1=本地，2=阿里云，3=七牛云，4=腾讯云 |
| origin_name | string | 是 | 源文件名 |
| object_name | string | 是 | 新文件名 |
| hash | string | 否 | 文件hash |
| mime_type | string | 否 | 资源类型 |
| storage_path | string | 否 | 存储目录 |
| suffix | string | 否 | 文件后缀 |
| size_byte | string | 否 | 字节数 |
| size_info | string | 否 | 文件大小 |
| url | string | 是 | url地址 |
| remark | string | 否 | 备注 |

### 4. 更新资源

**接口地址**: `PUT /sys_resources/update/{id}`

**接口描述**: 更新指定的资源信息

### 5. 删除资源

**接口地址**: `DELETE /sys_resources/delete`

**接口描述**: 批量删除资源（软删除）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | array | 是 | 资源ID列表 |

### 6. 根据分组获取资源

**接口地址**: `GET /sys_resources/group/{group_id}`

**接口描述**: 根据分组ID获取该分组下的所有资源

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| group_id | int64 | 是 | 资源分组ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码，最小值为1 |
| page_size | int | 否 | 10 | 每页数量，范围1-100 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/sys_resources/group/1?page=1&page_size=10" \
  -H "Authorization: Bearer your_token"
```

### 7. 文件上传

**接口地址**: `POST /sys_resources/upload`

**接口描述**: 上传文件到指定分组

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| group_id | int64 | 是 | 资源分组ID |
| file | file | 是 | 上传的文件 |

**请求示例**:
```bash
curl -X POST "http://localhost:8000/sys_resources/upload" \
  -H "Authorization: Bearer your_token" \
  -F "group_id=1" \
  -F "file=@/path/to/your/file.jpg"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "上传成功",
  "data": {
    "id": 1,
    "url": "/uploads/2024/01/01/1640995200_abc123.jpg"
  }
}
```

## 数据结构说明

### ResourcesGroupInfo (资源分组列表项)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 主键ID |
| name | string | 分组名称 |
| type | string | 资源类型：PIC=图片，VIDEO=视频 |
| is_delete | int | 是否删除：0=否，1=是 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

### ResourcesInfo (资源列表项)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 主键ID |
| group_id | int64 | 资源分组ID |
| storage_mode | int | 存储模式：1=本地，2=阿里云，3=七牛云，4=腾讯云 |
| origin_name | string | 源文件名 |
| object_name | string | 新文件名 |
| hash | string | 文件hash |
| mime_type | string | 资源类型 |
| storage_path | string | 存储目录 |
| suffix | string | 文件后缀 |
| size_byte | string | 字节数 |
| size_info | string | 文件大小 |
| url | string | url地址 |
| remark | string | 备注 |
| is_delete | int | 是否删除：0=否，1=是 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

## 权限说明

### 资源分组权限

| 权限标识 | 权限名称 | 说明 |
|----------|----------|------|
| system:resourcesgroup:list | 查看资源分组 | 获取资源分组列表 |
| system:resourcesgroup:view | 查看分组详情 | 获取资源分组详细信息 |
| system:resourcesgroup:create | 创建资源分组 | 新增资源分组 |
| system:resourcesgroup:update | 更新资源分组 | 修改资源分组信息 |
| system:resourcesgroup:delete | 删除资源分组 | 删除资源分组 |

### 资源权限

| 权限标识 | 权限名称 | 说明 |
|----------|----------|------|
| system:resources:list | 查看资源 | 获取资源列表 |
| system:resources:view | 查看资源详情 | 获取资源详细信息 |
| system:resources:create | 创建资源 | 新增资源 |
| system:resources:update | 更新资源 | 修改资源信息 |
| system:resources:delete | 删除资源 | 删除资源 |
| system:resources:upload | 上传文件 | 文件上传功能 |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 401 | 未登录或登录已过期 |
| 403 | 权限不足 |
| 404 | 记录不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. **文件上传**: 支持图片、视频等多媒体文件上传
2. **存储模式**: 目前主要支持本地存储，后续可扩展云存储
3. **文件去重**: 通过文件hash值进行去重检查
4. **分组管理**: 删除分组前需要先删除该分组下的所有资源
5. **权限控制**: 所有接口都需要相应的权限才能访问
6. **文件大小**: 建议限制单个文件大小，避免服务器压力过大

## 使用建议

1. **分组规划**: 合理规划资源分组，按业务模块或文件类型分类
2. **命名规范**: 建议使用有意义的文件名和分组名
3. **定期清理**: 定期清理无用的资源文件，释放存储空间
4. **备份策略**: 重要资源建议做好备份
5. **访问控制**: 根据业务需要设置合适的权限控制
