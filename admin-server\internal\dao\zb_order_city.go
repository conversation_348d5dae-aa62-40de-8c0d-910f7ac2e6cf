// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package dao

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// internalZbOrderCityDao is internal type for wrapping dao logic.
type internalZbOrderCityDao struct {
	table   string                 // table is the underlying table name of the DAO.
	group   string                 // group is the database configuration group name of current DAO.
	columns ZbOrderCityColumns     // columns contains all the column names of Table for convenient usage.
}

// ZbOrderCityColumns defines and stores column names for table zb_order_city.
type ZbOrderCityColumns struct {
	Id       string // 主键ID
	OrderId  string // 订单id
	CityId   string // 城市id
	CityName string // 城市名称
}

// zbOrderCityColumns holds the columns for table zb_order_city.
var zbOrderCityColumns = ZbOrderCityColumns{
	Id:       "id",
	OrderId:  "order_id",
	CityId:   "city_id",
	CityName: "city_name",
}

// NewZbOrderCityDao creates and returns a new DAO object for table data access.
func NewZbOrderCityDao() *internalZbOrderCityDao {
	return &internalZbOrderCityDao{
		group:   "default",
		table:   "zb_order_city",
		columns: zbOrderCityColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *internalZbOrderCityDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *internalZbOrderCityDao) Table() string {
	return dao.table
}

// Columns returns the columns of current dao.
func (dao *internalZbOrderCityDao) Columns() ZbOrderCityColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *internalZbOrderCityDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *internalZbOrderCityDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *internalZbOrderCityDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}

// ZbOrderCity is the dao object for table zb_order_city.
var ZbOrderCity = NewZbOrderCityDao()
