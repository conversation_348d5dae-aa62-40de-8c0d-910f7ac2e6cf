package wechat_menu

import (
	v1 "admin-server/api/wechat_menu/v1"
	"context"
)

type IWechatMenuV1 interface {
	Create(ctx context.Context, req *v1.WechatMenuCreateReq) (res *v1.WechatMenuCreateRes, err error)
	Update(ctx context.Context, req *v1.WechatMenuUpdateReq) (res *v1.WechatMenuUpdateRes, err error)
	Delete(ctx context.Context, req *v1.WechatMenuDeleteReq) (res *v1.WechatMenuDeleteRes, err error)
	GetOne(ctx context.Context, req *v1.WechatMenuGetOneReq) (res *v1.WechatMenuGetOneRes, err error)
	GetList(ctx context.Context, req *v1.WechatMenuGetListReq) (res *v1.WechatMenuGetListRes, err error)
	GetTree(ctx context.Context, req *v1.WechatMenuGetTreeReq) (res *v1.WechatMenuGetTreeRes, err error)
	UpdateSort(ctx context.Context, req *v1.WechatMenuUpdateSortReq) (res *v1.WechatMenuUpdateSortRes, err error)
	UpdateStatus(ctx context.Context, req *v1.WechatMenuUpdateStatusReq) (res *v1.WechatMenuUpdateStatusRes, err error)
	Publish(ctx context.Context, req *v1.WechatMenuPublishReq) (res *v1.WechatMenuPublishRes, err error)
}
