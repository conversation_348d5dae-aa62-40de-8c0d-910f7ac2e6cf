package middleware

import (
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/frame/g"
	"net/http"
	"strings"

	"github.com/gogf/gf/v2/net/ghttp"
)

func NotFound(r *ghttp.Request) {
	r.Middleware.Next()

	if r.Response.Status >= http.StatusNotFound {
		r.Response.ClearBuffer()

		// 判断是否为API请求
		if isVIEWRequest(r) {
			// 页面请求返回HTML错误页面
			err := r.Response.WriteTpl("mobile/404.html")
			if err != nil {
				r.Response.WriteStatus(http.StatusNotFound, "页面不存在")
			}
			return
		}

		// API请求返回JSON格式错误
		r.Response.WriteJson(g.Map{
			"code":    gcode.CodeNotFound.Code(),
			"message": "接口不存在",
			"data":    nil,
		})
	}
}

// 判断是否为view请求的辅助函数
func isVIEWRequest(r *ghttp.Request) bool {
	// 1. 检查URL路径
	path := r.URL.Path
	if strings.HasPrefix(path, "/m/") {
		return true
	}

	return false
}
