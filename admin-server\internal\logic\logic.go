package logic

import (
	_ "admin-server/internal/logic/search"
	_ "admin-server/internal/logic/sysAdmin"
	_ "admin-server/internal/logic/sysAuth"
	_ "admin-server/internal/logic/sysConfig"
	_ "admin-server/internal/logic/sysConfigGroup"
	_ "admin-server/internal/logic/sysDict"
	_ "admin-server/internal/logic/sysDictGroup"
	_ "admin-server/internal/logic/sysMenu"
	_ "admin-server/internal/logic/sysResources"
	_ "admin-server/internal/logic/sysResourcesGroup"
	_ "admin-server/internal/logic/sysRole"
	_ "admin-server/internal/logic/wechatConfig"
	_ "admin-server/internal/logic/wechatMenu"
	_ "admin-server/internal/logic/zbArticle"
	_ "admin-server/internal/logic/zbCate"
	_ "admin-server/internal/logic/zbCity"
	_ "admin-server/internal/logic/zbGood"
	_ "admin-server/internal/logic/zbUser"
	_ "admin-server/internal/logic/zb_order"
	_ "admin-server/internal/logic/zb_user_browser"
)
