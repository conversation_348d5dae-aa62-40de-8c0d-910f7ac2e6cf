// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SysConfig is the golang structure for table sys_config.
type SysConfig struct {
	Id               int64       `json:"id"               orm:"id"                 description:""`                                                //
	GroupId          int64       `json:"groupId"          orm:"group_id"           description:"系统配置组id"`                                         // 系统配置组id
	Key              string      `json:"key"              orm:"key"                description:"配置键名"`                                            // 配置键名
	Value            string      `json:"value"            orm:"value"              description:"配置键值"`                                            // 配置键值
	Name             string      `json:"name"             orm:"name"               description:"配置名称"`                                            // 配置名称
	Sort             int         `json:"sort"             orm:"sort"               description:"排序"`                                              // 排序
	InputType        string      `json:"inputType"        orm:"input_type"         description:"数据输入类型：input、textarea、select、radio、switch、image"` // 数据输入类型：input、textarea、select、radio、switch、image
	ConfigSelectData string      `json:"configSelectData" orm:"config_select_data" description:"配置项数据，select、radio、switch是一样的格式"`                 // 配置项数据，select、radio、switch是一样的格式
	IsSystem         int         `json:"isSystem"         orm:"is_system"          description:"是否系统保留 1是 0否"`                                    // 是否系统保留 1是 0否
	IsDelete         int         `json:"isDelete"         orm:"is_delete"          description:"是否删除: 0=否, 1=是"`                                  // 是否删除: 0=否, 1=是
	CreatedAt        *gtime.Time `json:"createdAt"        orm:"created_at"         description:"创建时间"`                                            // 创建时间
	UpdatedAt        *gtime.Time `json:"updatedAt"        orm:"updated_at"         description:"更新时间"`                                            // 更新时间
	DeletedAt        *gtime.Time `json:"deletedAt"        orm:"deleted_at"         description:"删除时间"`                                            // 删除时间
}
