package sys_dict

import (
	"context"

	v1 "admin-server/api/sys_dict/v1"
	"admin-server/internal/service"
)

func (c *ControllerV1) GetByGroupCode(ctx context.Context, req *v1.GetByGroupCodeReq) (res *v1.GetByGroupCodeRes, err error) {
	list, err := service.SysDict().GetDictsByGroupCode(ctx, req.GroupCode)
	if err != nil {
		return nil, err
	}

	// 转换为API响应格式
	options := make([]v1.DictOption, len(list))
	for i, dict := range list {
		options[i] = v1.DictOption{
			ID:    dict.Id,
			Name:  dict.Name,
			Value: dict.Value,
			Code:  dict.Code,
			Sort:  int(dict.Sort),
		}
	}

	return &v1.GetByGroupCodeRes{
		List: options,
	}, nil
}
