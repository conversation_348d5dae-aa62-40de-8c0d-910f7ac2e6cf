package service

import (
	v1 "admin-server/api/zb_order/v1"
	"admin-server/internal/model/entity"
	"context"
)

// IZbOrder 订单服务接口
type IZbOrder interface {
	Create(ctx context.Context, req *v1.CreateReq) (*v1.<PERSON>reate<PERSON><PERSON>, error)
	GetList(ctx context.Context, req *v1.GetListReq) (*v1.GetListRes, error)
	GetDetail(ctx context.Context, id int64) (*v1.GetDetailRes, error)
	UpdatePayStatus(ctx context.Context, req *v1.UpdatePayStatusReq) error
	Delete(ctx context.Context, req *v1.DeleteReq) (*v1.DeleteRes, error)
	GetMyList(ctx context.Context, req *v1.GetMyListReq) (*v1.GetMyListRes, error)
	GetMyStats(ctx context.Context, req *v1.GetMyStatsReq) (*v1.GetMyStatsRes, error)
	GetMySubscriptions(ctx context.Context, req *v1.GetMySubscriptionsReq) (*v1.GetMySubscriptionsRes, error)
	GenerateOrderSn() string
	GetOrderWithCities(ctx context.Context, orderId int64) (*entity.ZbOrder, []entity.ZbOrderCity, error)
}

var (
	localZbOrder IZbOrder
)

func ZbOrder() IZbOrder {
	if localZbOrder == nil {
		panic("implement not found for interface IZbOrder, forgot register?")
	}
	return localZbOrder
}

func RegisterZbOrder(i IZbOrder) {
	localZbOrder = i
}
