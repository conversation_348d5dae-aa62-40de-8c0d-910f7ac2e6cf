// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"admin-server/internal/dao/internal"
)

// zbGoodDao is the data access object for the table zb_good.
// You can define custom methods on it to extend its functionality as needed.
type zbGoodDao struct {
	*internal.ZbGoodDao
}

var (
	// ZbGood is a globally accessible object for table zb_good operations.
	ZbGood = zbGoodDao{internal.NewZbGoodDao()}
)

// Add your custom methods and functionality below.
