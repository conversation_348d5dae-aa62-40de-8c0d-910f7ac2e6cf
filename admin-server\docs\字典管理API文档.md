# 字典管理API文档

## 概述

字典管理系统提供完整的字典分组和字典项管理功能，支持分层管理、状态控制、排序等功能，为系统提供统一的数据字典服务。

## 基础信息

- **Base URL**: `http://localhost:8000`
- **认证方式**: Bearer Token
- **Content-Type**: `application/json`

## 字典分组管理API

### 1. 获取字典分组列表

**接口地址**: `GET /sys_dict_group/list`

**接口描述**: 分页获取字典分组列表，支持多条件筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码，最小值为1 |
| page_size | int | 否 | 10 | 每页数量，范围1-100 |
| name | string | 否 | - | 分组名称（模糊搜索） |
| code | string | 否 | - | 分组编码（模糊搜索） |
| is_disable | int | 否 | -1 | 是否禁用：0=否，1=是，-1=全部 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/sys_dict_group/list?page=1&page_size=10&name=用户" \
  -H "Authorization: Bearer your_token"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "用户状态",
        "code": "user_status",
        "is_disable": 0,
        "is_system": 0,
        "remark": "用户状态字典",
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 10:00:00"
      }
    ],
    "total": 1
  }
}
```

### 2. 获取字典分组详情

**接口地址**: `GET /sys_dict_group/{id}`

**接口描述**: 获取指定字典分组的详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 字典分组ID |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/sys_dict_group/1" \
  -H "Authorization: Bearer your_token"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "dict_group": {
      "id": 1,
      "name": "用户状态",
      "code": "user_status",
      "is_disable": 0,
      "is_system": 0,
      "remark": "用户状态字典",
      "created_at": "2024-01-01 10:00:00",
      "updated_at": "2024-01-01 10:00:00"
    }
  }
}
```

### 3. 创建字典分组

**接口地址**: `POST /sys_dict_group/create`

**接口描述**: 创建新的字典分组

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 分组名称，长度1-50个字符 |
| code | string | 是 | 分组编码，长度1-50个字符，唯一 |
| remark | string | 否 | 备注，最大255个字符 |

**请求示例**:
```bash
curl -X POST "http://localhost:8000/sys_dict_group/create" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "订单状态",
    "code": "order_status",
    "remark": "订单状态字典"
  }'
```

**响应示例**:
```json
{
  "code": 0,
  "message": "创建成功",
  "data": {}
}
```

### 4. 更新字典分组

**接口地址**: `PUT /sys_dict_group/update/{id}`

**接口描述**: 更新指定的字典分组信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 字典分组ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 分组名称，长度1-50个字符 |
| code | string | 是 | 分组编码，长度1-50个字符，唯一 |
| remark | string | 否 | 备注，最大255个字符 |

**请求示例**:
```bash
curl -X PUT "http://localhost:8000/sys_dict_group/update/1" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "用户状态",
    "code": "user_status",
    "remark": "用户状态字典（已更新）"
  }'
```

### 5. 删除字典分组

**接口地址**: `DELETE /sys_dict_group/delete`

**接口描述**: 批量删除字典分组（软删除）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | array | 是 | 字典分组ID列表 |

**请求示例**:
```bash
curl -X DELETE "http://localhost:8000/sys_dict_group/delete" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [1, 2, 3]
  }'
```

### 6. 设置字典分组状态

**接口地址**: `PUT /sys_dict_group/status/{id}`

**接口描述**: 设置字典分组的启用/禁用状态

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 字典分组ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| is_disable | int | 是 | 是否禁用：0=否，1=是 |

**请求示例**:
```bash
curl -X PUT "http://localhost:8000/sys_dict_group/status/1" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "is_disable": 1
  }'
```

### 7. 获取所有字典分组

**接口地址**: `GET /sys_dict_group/all`

**接口描述**: 获取所有启用的字典分组（用于下拉选择）

**请求示例**:
```bash
curl -X GET "http://localhost:8000/sys_dict_group/all" \
  -H "Authorization: Bearer your_token"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "用户状态",
        "code": "user_status"
      },
      {
        "id": 2,
        "name": "订单状态",
        "code": "order_status"
      }
    ]
  }
}
```

## 字典项管理API

### 1. 获取字典项列表

**接口地址**: `GET /sys_dict/list`

**接口描述**: 分页获取字典项列表，支持多条件筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码，最小值为1 |
| page_size | int | 否 | 10 | 每页数量，范围1-100 |
| group_id | int64 | 否 | - | 字典分组ID |
| name | string | 否 | - | 字典名称（模糊搜索） |
| code | string | 否 | - | 字典编码（模糊搜索） |
| is_disable | int | 否 | -1 | 是否禁用：0=否，1=是，-1=全部 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/sys_dict/list?page=1&page_size=10&group_id=1" \
  -H "Authorization: Bearer your_token"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "group_id": 1,
        "name": "正常",
        "value": "1",
        "code": "normal",
        "sort": 1,
        "is_disable": 0,
        "is_system": 0,
        "remark": "正常状态",
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 10:00:00"
      }
    ],
    "total": 1
  }
}
```

### 2. 获取字典项详情

**接口地址**: `GET /sys_dict/{id}`

**接口描述**: 获取指定字典项的详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 字典项ID |

### 3. 创建字典项

**接口地址**: `POST /sys_dict/create`

**接口描述**: 创建新的字典项

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| group_id | int64 | 是 | 字典分组ID |
| name | string | 是 | 字典名称，长度1-50个字符 |
| value | string | 否 | 字典值，最大255个字符 |
| code | string | 是 | 字典编码，长度1-255个字符 |
| sort | int | 否 | 排序，默认0 |
| remark | string | 否 | 备注，最大255个字符 |

**请求示例**:
```bash
curl -X POST "http://localhost:8000/sys_dict/create" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "group_id": 1,
    "name": "正常",
    "value": "1",
    "code": "normal",
    "sort": 1,
    "remark": "正常状态"
  }'
```

### 4. 更新字典项

**接口地址**: `PUT /sys_dict/update/{id}`

**接口描述**: 更新指定的字典项信息

### 5. 删除字典项

**接口地址**: `DELETE /sys_dict/delete`

**接口描述**: 批量删除字典项（软删除）

### 6. 设置字典项状态

**接口地址**: `PUT /sys_dict/status/{id}`

**接口描述**: 设置字典项的启用/禁用状态

### 7. 根据分组编码获取字典项

**接口地址**: `GET /sys_dict/group/{group_code}`

**接口描述**: 根据分组编码获取该分组下的所有启用字典项

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| group_code | string | 是 | 字典分组编码 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/sys_dict/group/user_status" \
  -H "Authorization: Bearer your_token"
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "正常",
        "value": "1",
        "code": "normal",
        "sort": 1
      },
      {
        "id": 2,
        "name": "禁用",
        "value": "0",
        "code": "disabled",
        "sort": 2
      }
    ]
  }
}
```

### 8. 根据分组ID获取字典项

**接口地址**: `GET /sys_dict/group_id/{group_id}`

**接口描述**: 根据分组ID获取该分组下的所有启用字典项

### 9. 更新字典项排序

**接口地址**: `PUT /sys_dict/sort/{id}`

**接口描述**: 更新字典项的排序值

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 字典项ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sort | int | 是 | 排序值 |

## 数据结构说明

### DictGroupInfo (字典分组列表项)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 主键ID |
| name | string | 分组名称 |
| code | string | 分组编码 |
| is_disable | int | 是否禁用：0=否，1=是 |
| is_system | int | 是否系统保留：0=否，1=是 |
| remark | string | 备注 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

### DictInfo (字典项列表项)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int64 | 主键ID |
| group_id | int64 | 字典分组ID |
| name | string | 字典名称 |
| value | string | 字典值 |
| code | string | 字典编码 |
| sort | int | 排序 |
| is_disable | int | 是否禁用：0=否，1=是 |
| is_system | int | 是否系统保留：0=否，1=是 |
| remark | string | 备注 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

## 权限说明

### 字典分组权限

| 权限标识 | 权限名称 | 说明 |
|----------|----------|------|
| system:dictgroup:list | 查看字典分组 | 获取字典分组列表 |
| system:dictgroup:view | 查看分组详情 | 获取字典分组详细信息 |
| system:dictgroup:create | 创建字典分组 | 新增字典分组 |
| system:dictgroup:update | 更新字典分组 | 修改字典分组信息 |
| system:dictgroup:delete | 删除字典分组 | 删除字典分组 |
| system:dictgroup:status | 设置分组状态 | 启用/禁用字典分组 |

### 字典项权限

| 权限标识 | 权限名称 | 说明 |
|----------|----------|------|
| system:dict:list | 查看字典项 | 获取字典项列表 |
| system:dict:view | 查看字典详情 | 获取字典项详细信息 |
| system:dict:create | 创建字典项 | 新增字典项 |
| system:dict:update | 更新字典项 | 修改字典项信息 |
| system:dict:delete | 删除字典项 | 删除字典项 |
| system:dict:status | 设置字典状态 | 启用/禁用字典项 |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 401 | 未登录或登录已过期 |
| 403 | 权限不足 |
| 404 | 记录不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. **系统保留**: 系统保留的字典分组和字典项不允许修改和删除
2. **编码唯一性**: 字典分组编码全局唯一，字典项编码在同一分组内唯一
3. **级联删除**: 删除字典分组前需要先删除该分组下的所有字典项
4. **排序规则**: 字典项按sort字段升序排列，相同sort值按创建时间降序
5. **状态控制**: 禁用的字典分组下的字典项在前端选择时不会显示
6. **权限控制**: 所有接口都需要相应的权限才能访问

## 使用建议

1. **分组规划**: 合理规划字典分组，避免过度细分或过于宽泛
2. **编码规范**: 建议使用下划线命名法，如：user_status、order_type
3. **排序管理**: 合理设置排序值，便于前端展示
4. **数据维护**: 定期清理无用的字典数据，保持数据整洁
