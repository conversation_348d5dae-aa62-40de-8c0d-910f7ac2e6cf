package search

import (
	"admin-server/internal/dao"
	"admin-server/internal/model/entity"
	"admin-server/internal/service"
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

type sSearch struct{}

func init() {
	service.RegisterSearch(&sSearch{})
}

// GetHotKeywords 获取热门搜索关键词
func (s *sSearch) GetHotKeywords(ctx context.Context, limit int) ([]entity.ZbSearchKeywords, error) {
	if limit <= 0 {
		limit = 10
	}
	if limit > 20 {
		limit = 20
	}

	var keywords []entity.ZbSearchKeywords
	err := dao.ZbSearchKeywords.Ctx(ctx).
		Order("search_count DESC, click_count DESC, updated_at DESC").
		Limit(limit).
		Scan(&keywords)

	if err != nil {
		g.Log().Error(ctx, "获取热门搜索关键词失败:", err)
		return nil, err
	}

	return keywords, nil
}

// TrackSearch 记录搜索统计
func (s *sSearch) TrackSearch(ctx context.Context, keyword string, cityId int64) error {
	if keyword == "" {
		return nil
	}

	// 使用事务确保数据一致性
	return dao.ZbSearchKeywords.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 检查关键词是否已存在
		count, err := dao.ZbSearchKeywords.Ctx(ctx).Where("keyword", keyword).Count()
		if err != nil {
			return err
		}

		if count > 0 {
			// 更新现有记录
			_, err = dao.ZbSearchKeywords.Ctx(ctx).
				Where("keyword", keyword).
				Data(g.Map{
					"search_count": gdb.Raw("search_count + 1"),
					"updated_at":   gdb.Raw("NOW()"),
				}).
				Update()
		} else {
			// 插入新记录
			_, err = dao.ZbSearchKeywords.Ctx(ctx).
				Data(g.Map{
					"keyword":      keyword,
					"search_count": 1,
					"click_count":  0,
					"trend":        "stable",
					"is_hot":       0,
					"is_new":       1, // 新增的关键词标记为新
				}).
				Insert()
		}

		return err
	})
}

// TrackClick 记录点击统计
func (s *sSearch) TrackClick(ctx context.Context, keyword string) error {
	if keyword == "" {
		return nil
	}

	// 更新点击次数
	_, err := dao.ZbSearchKeywords.Ctx(ctx).
		Where("keyword", keyword).
		Data(g.Map{
			"click_count": gdb.Raw("click_count + 1"),
			"updated_at":  gdb.Raw("NOW()"),
		}).
		Update()

	if err != nil {
		g.Log().Error(ctx, "更新点击统计失败:", err)
	}

	return err
}

// UpdateKeywordTrend 更新关键词趋势
func (s *sSearch) UpdateKeywordTrend(ctx context.Context) error {
	// 这里可以实现复杂的趋势计算逻辑
	// 比如：比较过去24小时和前24小时的搜索量变化
	// 暂时简化实现：根据搜索次数设置热门标记

	// 先重置所有关键词的热门标记（使用WHERE条件避免错误）
	_, err := dao.ZbSearchKeywords.Ctx(ctx).
		Where("is_hot", 1).
		Data(g.Map{"is_hot": 0}).
		Update()
	if err != nil {
		g.Log().Error(ctx, "重置热门标记失败:", err)
		return err
	}

	// 获取搜索次数前3的关键词，标记为热门
	var topKeywords []entity.ZbSearchKeywords
	err = dao.ZbSearchKeywords.Ctx(ctx).
		Order("search_count DESC").
		Limit(3).
		Scan(&topKeywords)
	if err != nil {
		g.Log().Error(ctx, "获取热门关键词失败:", err)
		return err
	}

	// 标记前3名为热门
	for _, keyword := range topKeywords {
		_, err = dao.ZbSearchKeywords.Ctx(ctx).
			Where("id", keyword.Id).
			Data(g.Map{"is_hot": 1}).
			Update()
		if err != nil {
			g.Log().Error(ctx, "更新热门标记失败:", err)
		} else {
			g.Log().Info(ctx, "标记热门关键词:", keyword.Keyword)
		}
	}

	g.Log().Info(ctx, "热门搜索趋势更新完成，共标记", len(topKeywords), "个热门关键词")
	return nil
}
